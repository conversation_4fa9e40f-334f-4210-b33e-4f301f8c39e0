package config

import (
	"bytes"
	"encoding/json"
	"strings"

	"github.com/joho/godotenv"
	"github.com/mitchellh/mapstructure"
	"github.com/spf13/viper"
	"gitlab.deepgate.io/apps/common/constants"
	"gitlab.deepgate.io/apps/common/log"
)

const (
	PlaceIndexName = "places"
)

type Schema struct {
	Env                     string   `json:"env"`
	LogLevel                string   `json:"log_level"`
	Port                    string   `json:"port"`
	HTTPPort                string   `json:"http_port"`
	WriteURL                string   `json:"write_url"`
	ReadURL                 string   `json:"read_url"`
	CommonReadURL           string   `json:"common_read_url"`
	MongoDB                 string   `json:"mongo_db"`
	CommonMongoDB           string   `json:"common_mongo_db"`
	RabbitmqURL             string   `json:"rabbitmq_url"`
	RabbitmqSuffix          string   `json:"rabbitmq_suffix"`
	RabbitmqQueueType       string   `json:"rabbitmq_queue_type"`
	RedisAddress            string   `json:"redis_address"`
	RedisPassword           string   `json:"redis_password"`
	RedisDatabase           int      `json:"redis_database"`
	RedisCommonDatabase     int      `json:"redis_common_database"`
	RedisSentinel           bool     `json:"redis_sentinel"`
	RedisSentinelMasterName string   `json:"redis_sentinel_master_name"`
	AuthSigningKey          string   `json:"auth_signing_key"`
	AuthTokenTTL            uint     `json:"auth_token_ttl"`
	PublicPaths             []string `json:"public_paths"`
	PublicMethods           []string `json:"public_methods"`
	InternalMethods         []string `json:"internal_methods"`
	InternalSecretToken     string   `json:"internal_secret_token"`
	DecryptKey              string   `json:"decrypt_key"`

	PartnerServiceEndpoint       string `json:"partner_service_endpoint"`
	AirplaneServiceEndpoint      string `json:"airplane_service_endpoint"`
	PartnershipServiceEndpoint   string `json:"partnership_service_endpoint"`
	DataWareHouseServiceEndpoint string `json:"data_warehouse_service_endpoint"`
	NotificationServiceEndpoint  string `json:"notification_service_endpoint"`
	PriceServiceEndpoint         string `json:"price_service_endpoint"`

	HubPartnershipID       string `json:"hub_partnership_id"`
	OrderServiceEndpoint   string `json:"order_service_endpoint"`
	WalletServiceEndpoint  string `json:"wallet_service_endpoint"`
	PaymentServiceEndpoint string `json:"payment_service_endpoint"`
	SearchHotelCtxTimeout  int    `json:"search_hotel_ctx_timeout"`

	TelegramURL      string `json:"telegram_url"`
	TelegramBotToken string `json:"telegram_bot_token"`

	ExpediaURL          string `json:"expedia_url"`
	ExpediaApiKey       string `json:"expedia_api_key"`
	ExpediaSharedSecret string `json:"expedia_shared_secret"`

	TourmindAgentCode string `json:"tour_mind_agent_code"`
	TourmindUsername  string `json:"tour_mind_username"`
	TourmindPassword  string `json:"tour_mind_password"`
	TourmindBaseURL   string `json:"tour_mind_base_url"`

	TelegramManualBookingChatID    string `json:"telegram_manual_booking_chat_id"`
	TelegramManualBookingBotToken  string `json:"telegram_manual_booking_bot_token"`
	TelegramBookingCancelingChatID string `json:"telegram_booking_canceling_chat_id"`
	UseExpediaFake                 bool   `json:"use_expedia_fake"`
	CronJobPendingBooking          string `json:"cron_job_pending_booking"`

	ElasticsearchAddress     string `json:"elasticsearch_address"`
	ElasticsearchUsername    string `json:"elasticsearch_username"`
	ElasticsearchPassword    string `json:"elasticsearch_password"`
	ElasticsearchIndexSuffix string `json:"elasticsearch_index_suffix"`

	ContentVersion string `json:"content_version"`

	RateHawKBaseURL    string `json:"rate_hawk_base_url"`
	RateHawkUsername   string `json:"rate_hawk_username"`
	RateHawkPassword   string `json:"rate_hawk_password"`
	WebhookTransaction string `json:"webhook_transaction"`
	EnableProvider     string `json:"enable_provider"`

	BTMTokenURL   string `json:"btm_token_url"`
	BTMWebhookURL string `json:"btm_webhook_url"`
	BTMClientID   string `json:"btm_client_id"`
	BTMSecretKey  string `json:"btm_secret_key"`
	BTMScope      string `json:"btm_scope"`

	TATokenURL          string `json:"ta_token_url"`
	TABookingURL        string `json:"ta_booking_url"`
	TAClientID          string `json:"ta_client_id"`
	TASecretKey         string `json:"ta_secret_key"`
	TAScope             string `json:"ta_scope"`
	TAWebHookKey        string `json:"ta_webhook_key"`
	TAIssuingActiveTime string `json:"ta_issuing_active_time"`

	CompanyEmail string `json:"company_email"`
	HubAppName   string `json:"hub_app_name"`

	RateLimitMaxRequests int64 `json:"rate_limit_max_requests"`
	RateLimitExpire      int64 `json:"rate_limit_expire"`
	SkipJob              bool  `json:"skip_job"`

	DidaBasicAuthUsename  string `json:"dida_basic_auth_username"`
	DidaBasicAuthPassword string `json:"dida_basic_auth_password"`
	DidaAPIURL            string `json:"dida_api_url"`
	DidaResFormat         string `json:"dida_res_format"`
	ContactFirstName      string `json:"contact_first_name"`
	ContactLastName       string `json:"contact_last_name"`
}

func New() (*Schema, error) {
	err := godotenv.Load()
	if err != nil {
		panic(err)
	}

	v := viper.New()
	v.SetEnvKeyReplacer(strings.NewReplacer(".", "__"))
	v.AutomaticEnv()
	v.SetConfigType("yaml")

	if err := v.ReadConfig(bytes.NewBuffer(defaultConfig)); err != nil {
		return nil, err
	}

	cfg := Schema{}
	err = v.Unmarshal(&cfg, func(c *mapstructure.DecoderConfig) {
		c.TagName = "json"
	})

	if cfg.Env != constants.ProductionEnvName {
		c, err := json.MarshalIndent(cfg, "", "\t")
		if err != nil {
			return nil, err
		}

		log.Info("Config", log.Any("", string(c)))
	}

	if cfg.AuthTokenTTL == 0 {
		const defaultTokenTTL = 86400
		cfg.AuthTokenTTL = defaultTokenTTL
	}

	return &cfg, err
}
