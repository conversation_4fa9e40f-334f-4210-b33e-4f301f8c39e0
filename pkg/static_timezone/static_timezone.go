package statictimezone

import (
	"strings"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/helpers"
)

func preprocessString(s string) string {
	s = strings.ToLower(s)
	return strings.ReplaceAll(s, " ", "")
}

func GetTimezone(city, countryCode string) string {
	try1City := preprocessString(city)
	countryCode = preprocessString(countryCode)

	out := ""

	// Try 1
	out = cityAsciiToTimezone[try1City]

	// Try 2
	if out == "" {
		try2City := helpers.RemoveAccents(try1City)
		out = cityAsciiToTimezone[try2City]

		// Try 3
		if out == "" {
			out = iso2ToTimezone[countryCode]

			// Try 4
			if out == "" {
				out = "UTC"
			}
		}
	}

	return out
}
