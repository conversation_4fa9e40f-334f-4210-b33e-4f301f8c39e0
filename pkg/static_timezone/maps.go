package statictimezone

var cityAsciiToTimezone = map[string]string{
	"qaleh-ye":                       "Asia/Kabul",
	"chaghcharan":                    "Asia/Kabul",
	"lashkargah":                     "Asia/Kabul",
	"zaranj":                         "Asia/Kabul",
	"tarinkowt":                      "Asia/Kabul",
	"zarehsharan":                    "Asia/Kabul",
	"asadabad":                       "Asia/Kabul",
	"taloqan":                        "Asia/Kabul",
	"mahmud-eeraqi":                  "Asia/Kabul",
	"mehtarlam":                      "Asia/Kabul",
	"barakibarak":                    "Asia/Kabul",
	"aybak":                          "Asia/Kabul",
	"maydashahr":                     "Asia/Kabul",
	"karokh":                         "Asia/Kabul",
	"sheberghan":                     "Asia/Kabul",
	"pol-ekhomri":                    "Asia/Kabul",
	"balkh":                          "Asia/Kabul",
	"meymaneh":                       "Asia/Kabul",
	"andkhvoy":                       "Asia/Kabul",
	"qalat":                          "Asia/Kabul",
	"ghazni":                         "Asia/Kabul",
	"feyzabad":                       "Asia/Kabul",
	"kondoz":                         "Asia/Kabul",
	"jalalabad":                      "Asia/Bishkek",
	"charikar":                       "Asia/Kabul",
	"gardiz":                         "Asia/Kabul",
	"bamian":                         "Asia/Kabul",
	"baghlan":                        "Asia/Kabul",
	"farah":                          "Asia/Kabul",
	"herat":                          "Asia/Kabul",
	"mazar-esharif":                  "Asia/Kabul",
	"kandahar":                       "Asia/Kabul",
	"kabul":                          "Asia/Kabul",
	"mariehamn":                      "Europe/Mariehamn",
	"kruje":                          "Europe/Tirane",
	"fier":                           "Europe/Tirane",
	"lushnje":                        "Europe/Tirane",
	"puke":                           "Europe/Tirane",
	"bajramcurri":                    "Europe/Tirane",
	"kukes":                          "Europe/Tirane",
	"sarande":                        "Europe/Tirane",
	"erseke":                         "Europe/Tirane",
	"pogradec":                       "Europe/Tirane",
	"korce":                          "Europe/Tirane",
	"berat":                          "Europe/Tirane",
	"corovode":                       "Europe/Tirane",
	"gramsh":                         "Europe/Tirane",
	"librazhd":                       "Europe/Tirane",
	"tepelene":                       "Europe/Tirane",
	"permet":                         "Europe/Tirane",
	"gjirokaster":                    "Europe/Tirane",
	"peshkopi":                       "Europe/Tirane",
	"burrel":                         "Europe/Tirane",
	"lezhe":                          "Europe/Tirane",
	"rreshen":                        "Europe/Tirane",
	"vlore":                          "Europe/Tirane",
	"elbasan":                        "Europe/Tirane",
	"durres":                         "Europe/Tirane",
	"shkoder":                        "Europe/Tirane",
	"tirana":                         "Europe/Tirane",
	"jijel":                          "Africa/Algiers",
	"tizi-ouzou":                     "Africa/Algiers",
	"bordjbouarreridj":               "Africa/Algiers",
	"m'sila":                         "Africa/Algiers",
	"guelma":                         "Africa/Algiers",
	"oumelbouaghi":                   "Africa/Algiers",
	"timimoun":                       "Africa/Algiers",
	"sidibelabbes":                   "Africa/Algiers",
	"tlimcen":                        "Africa/Algiers",
	"beniounif":                      "Africa/Algiers",
	"abadla":                         "Africa/Algiers",
	"sefra":                          "Africa/Algiers",
	"skikda":                         "Africa/Algiers",
	"djanet":                         "Africa/Algiers",
	"i-n-amenas":                     "Africa/Algiers",
	"inamguel":                       "Africa/Algiers",
	"elbayadh":                       "Africa/Algiers",
	"eloued":                         "Africa/Algiers",
	"hassimessaoud":                  "Africa/Algiers",
	"chlef":                          "Africa/Algiers",
	"mascara":                        "Africa/Algiers",
	"mostaganem":                     "Africa/Algiers",
	"saida":                          "Asia/Beirut",
	"tiarat":                         "Africa/Algiers",
	"bejaia":                         "Africa/Algiers",
	"blida":                          "Africa/Algiers",
	"bouira":                         "Africa/Algiers",
	"medea":                          "Africa/Algiers",
	"soukahras":                      "Africa/Algiers",
	"tebessa":                        "Africa/Algiers",
	"adrar":                          "Africa/Algiers",
	"reggane":                        "Africa/Algiers",
	"bechar":                         "Africa/Algiers",
	"tindouf":                        "Africa/Algiers",
	"illizi":                         "Africa/Algiers",
	"arak":                           "Asia/Tehran",
	"i-n-salah":                      "Africa/Algiers",
	"elgolea":                        "Africa/Algiers",
	"laghouat":                       "Africa/Algiers",
	"touggourt":                      "Africa/Algiers",
	"ouargla":                        "Africa/Algiers",
	"biskra":                         "Africa/Algiers",
	"djelfa":                         "Africa/Algiers",
	"setif":                          "Africa/Algiers",
	"batna":                          "Africa/Algiers",
	"annaba":                         "Africa/Algiers",
	"constantine":                    "Africa/Algiers",
	"oran":                           "Africa/Algiers",
	"tamanrasset":                    "Africa/Algiers",
	"ghardaia":                       "Africa/Algiers",
	"algiers":                        "Africa/Algiers",
	"pagopago":                       "Pacific/Pago_Pago",
	"andorra":                        "Europe/Andorra",
	"mucusso":                        "Africa/Luanda",
	"lucapa":                         "Africa/Luanda",
	"capenda-camulemba":              "Africa/Luanda",
	"saurimo":                        "Africa/Luanda",
	"muconda":                        "Africa/Luanda",
	"cacolo":                         "Africa/Luanda",
	"caxito":                         "Africa/Luanda",
	"camabatela":                     "Africa/Luanda",
	"ndalatando":                     "Africa/Luanda",
	"quibala":                        "Africa/Luanda",
	"calulo":                         "Africa/Luanda",
	"wakukungo":                      "Africa/Luanda",
	"songo":                          "Africa/Luanda",
	"mbanza-congo":                   "Africa/Luanda",
	"nzeto":                          "Africa/Luanda",
	"soyo":                           "Africa/Luanda",
	"cabinda":                        "Africa/Luanda",
	"calucinga":                      "Africa/Luanda",
	"camacupa":                       "Africa/Luanda",
	"cubal":                          "Africa/Luanda",
	"mavinga":                        "Africa/Luanda",
	"cuitocaunavale":                 "Africa/Luanda",
	"luiana":                         "Africa/Luanda",
	"ondjiva":                        "Africa/Luanda",
	"chitado":                        "Africa/Luanda",
	"chibemba":                       "Africa/Luanda",
	"chibia":                         "Africa/Luanda",
	"quipungo":                       "Africa/Luanda",
	"luau":                           "Africa/Luanda",
	"cangamba":                       "Africa/Luanda",
	"lumbalanguimbo":                 "Africa/Luanda",
	"cazombo":                        "Africa/Luanda",
	"dundo":                          "Africa/Luanda",
	"ambriz":                         "Africa/Luanda",
	"dondo":                          "Africa/Maputo",
	"sumbe":                          "Africa/Luanda",
	"uige":                           "Africa/Luanda",
	"kuito":                          "Africa/Luanda",
	"lobito":                         "Africa/Luanda",
	"xangongo":                       "Africa/Luanda",
	"luena":                          "Africa/Luanda",
	"tombua":                         "Africa/Luanda",
	"malanje":                        "Africa/Luanda",
	"benguela":                       "Africa/Luanda",
	"lubango":                        "Africa/Luanda",
	"namibe":                         "Africa/Luanda",
	"menongue":                       "Africa/Luanda",
	"huambo":                         "Africa/Luanda",
	"luanda":                         "Africa/Luanda",
	"saintjohn's":                    "America/Antigua",
	"28denoviembre":                  "America/Santiago",
	"gobernadorgregores":             "America/Argentina/Rio_Gallegos",
	"comondanteluispiedrabuena":      "America/Argentina/Rio_Gallegos",
	"pasoriomayo":                    "America/Argentina/Catamarca",
	"altoriosanguer":                 "America/Argentina/Catamarca",
	"elmaiten":                       "America/Argentina/Catamarca",
	"puertomadryn":                   "America/Argentina/Catamarca",
	"trelew":                         "America/Argentina/Catamarca",
	"lasheras":                       "America/Argentina/Mendoza",
	"sanmartin":                      "America/Bogota",
	"uspallata":                      "America/Argentina/Mendoza",
	"cutralco":                       "America/Argentina/Salta",
	"puntaalta":                      "America/Argentina/Buenos_Aires",
	"sannicolas":                     "America/Argentina/Buenos_Aires",
	"campana":                        "America/Argentina/Buenos_Aires",
	"chacabuco":                      "America/Argentina/Buenos_Aires",
	"mercedes":                       "America/Montevideo",
	"lincoln":                        "America/Chicago",
	"chivilcoy":                      "America/Argentina/Buenos_Aires",
	"veinticincodemayo":              "America/Argentina/Buenos_Aires",
	"nuevedejulio":                   "America/Argentina/Buenos_Aires",
	"dolores":                        "America/Argentina/Buenos_Aires",
	"pedroluro":                      "America/Argentina/Buenos_Aires",
	"tresarroyos":                    "America/Argentina/Buenos_Aires",
	"coronelsuarez":                  "America/Argentina/Buenos_Aires",
	"balcarce":                       "America/Argentina/Buenos_Aires",
	"25demayo":                       "America/Argentina/Salta",
	"generalroca":                    "America/Argentina/Salta",
	"comallo":                        "America/Argentina/Salta",
	"ingenierojacobacci":             "America/Argentina/Salta",
	"generalconesa":                  "America/Argentina/Salta",
	"choelechoel":                    "America/Argentina/Salta",
	"sanfrancisco":                   "America/Los_Angeles",
	"altagracia":                     "America/Argentina/Cordoba",
	"villamaria":                     "America/Argentina/Cordoba",
	"bellville":                      "America/Argentina/Cordoba",
	"villarumipal":                   "America/Argentina/Cordoba",
	"villacarlospaz":                 "America/Argentina/Cordoba",
	"chumbicha":                      "America/Argentina/Catamarca",
	"tinogasta":                      "America/Argentina/Catamarca",
	"abrapampa":                      "America/Argentina/Jujuy",
	"humahuaca":                      "America/Argentina/Jujuy",
	"susques":                        "America/Argentina/Jujuy",
	"chepes":                         "America/Argentina/La_Rioja",
	"yacuiba":                        "America/Argentina/Salta",
	"tartagal":                       "America/Argentina/Salta",
	"joaquinv.gonzalez":              "America/Argentina/Salta",
	"generalguemes":                  "America/Argentina/Salta",
	"trancas":                        "America/Argentina/Tucuman",
	"presidenciaroquesaenzpena":      "America/Argentina/Cordoba",
	"pampadelinfierno":               "America/Argentina/Cordoba",
	"villaangela":                    "America/Argentina/Cordoba",
	"ingenieroguillermon.juarez":     "America/Argentina/Cordoba",
	"comandantefontana":              "America/Argentina/Cordoba",
	"doctorpedrop.pena":              "America/Asuncion",
	"sanlorenzo":                     "America/Asuncion",
	"corrientes":                     "America/Argentina/Cordoba",
	"concepciondeluruguay":           "America/Argentina/Cordoba",
	"victoria":                       "America/Chicago",
	"gualeguay":                      "America/Argentina/Cordoba",
	"parana":                         "America/Argentina/Cordoba",
	"villaconstitucion":              "America/Argentina/Cordoba",
	"rafaela":                        "America/Argentina/Cordoba",
	"eldorado":                       "America/Caracas",
	"rodeo":                          "America/Argentina/San_Juan",
	"lasplumas":                      "America/Argentina/Catamarca",
	"gastre":                         "America/Argentina/Catamarca",
	"telsen":                         "America/Argentina/Catamarca",
	"malargue":                       "America/Argentina/Mendoza",
	"tunuyan":                        "America/Argentina/Mendoza",
	"lapaz":                          "America/Mazatlan",
	"chosmalal":                      "America/Argentina/Salta",
	"laslajas":                       "America/Argentina/Salta",
	"zarate":                         "America/Argentina/Buenos_Aires",
	"carhue":                         "America/Argentina/Buenos_Aires",
	"darregueira":                    "America/Argentina/Buenos_Aires",
	"juarez":                         "America/Argentina/Buenos_Aires",
	"mardeajo":                       "America/Argentina/Buenos_Aires",
	"lobos":                          "America/Argentina/Buenos_Aires",
	"chascomus":                      "America/Argentina/Buenos_Aires",
	"junin":                          "America/Lima",
	"laplata":                        "America/Argentina/Buenos_Aires",
	"pergamino":                      "America/Argentina/Buenos_Aires",
	"lujan":                          "America/Argentina/Buenos_Aires",
	"azul":                           "America/Argentina/Buenos_Aires",
	"villalonga":                     "America/Argentina/Buenos_Aires",
	"victorica":                      "America/Argentina/Salta",
	"generalpico":                    "America/Argentina/Salta",
	"sanantoniooeste":                "America/Argentina/Salta",
	"sierracolorado":                 "America/Argentina/Salta",
	"riotercero":                     "America/Argentina/Cordoba",
	"belen":                          "America/Asuncion",
	"rinconada":                      "America/Argentina/Jujuy",
	"sanpedro":                       "America/Asuncion",
	"libertadorgeneralsanmartin":     "America/Argentina/Jujuy",
	"chamical":                       "America/Argentina/La_Rioja",
	"losblancos":                     "America/Argentina/Salta",
	"cafayate":                       "America/Argentina/Salta",
	"cerrillos":                      "America/Argentina/Salta",
	"sanantoniodeloscobres":          "America/Argentina/Salta",
	"anatuya":                        "America/Argentina/Cordoba",
	"frias":                          "America/Argentina/Catamarca",
	"montequemado":                   "America/Argentina/Cordoba",
	"juanjosecastelli":               "America/Argentina/Cordoba",
	"charata":                        "America/Argentina/Cordoba",
	"laslomitas":                     "America/Argentina/Cordoba",
	"concordia":                      "America/Sao_Paulo",
	"sunchales":                      "America/Argentina/Cordoba",
	"sanjusto":                       "America/Argentina/Cordoba",
	"vera":                           "America/Argentina/Cordoba",
	"reconquista":                    "America/Argentina/Cordoba",
	"venadotuerto":                   "America/Argentina/Cordoba",
	"esquel":                         "America/Argentina/Catamarca",
	"zapala":                         "America/Argentina/Salta",
	"olavarria":                      "America/Argentina/Buenos_Aires",
	"tandil":                         "America/Argentina/Buenos_Aires",
	"viedma":                         "America/Argentina/Salta",
	"sanluis":                        "America/Guatemala",
	"riocuarto":                      "America/Argentina/Cordoba",
	"sansalvadordejujuy":             "America/Argentina/Jujuy",
	"sanramondelanuevaoran":          "America/Argentina/Salta",
	"goya":                           "America/Argentina/Cordoba",
	"puertosanjulian":                "America/Argentina/Rio_Gallegos",
	"peritomoreno":                   "America/Argentina/Rio_Gallegos",
	"riogrande":                      "America/Sao_Paulo",
	"ushuaia":                        "America/Argentina/Ushuaia",
	"sarmiento":                      "America/Argentina/Catamarca",
	"sanrafael":                      "America/La_Paz",
	"necochea":                       "America/Argentina/Buenos_Aires",
	"riocolorado":                    "America/Argentina/Salta",
	"catamarca":                      "America/Argentina/Catamarca",
	"larioja":                        "America/Argentina/La_Rioja",
	"santiagodelestero":              "America/Argentina/Cordoba",
	"resistencia":                    "America/Argentina/Cordoba",
	"gualeguaychu":                   "America/Argentina/Cordoba",
	"elcalafate":                     "America/Argentina/Rio_Gallegos",
	"sanjuan":                        "America/Puerto_Rico",
	"rawson":                         "America/Argentina/Catamarca",
	"neuquen":                        "America/Argentina/Salta",
	"santarosa":                      "America/Los_Angeles",
	"sancarlosdebariloche":           "America/Argentina/Salta",
	"salta":                          "America/Argentina/Salta",
	"sanmigueldetucuman":             "America/Argentina/Tucuman",
	"formosa":                        "America/Sao_Paulo",
	"santafe":                        "America/Denver",
	"rosario":                        "America/Asuncion",
	"puertodeseado":                  "America/Argentina/Rio_Gallegos",
	"riogallegos":                    "America/Argentina/Rio_Gallegos",
	"mendoza":                        "America/Argentina/Mendoza",
	"bahiablanca":                    "America/Argentina/Buenos_Aires",
	"mardelplata":                    "America/Argentina/Buenos_Aires",
	"cordoba":                        "Europe/Madrid",
	"posadas":                        "America/Argentina/Cordoba",
	"buenosaires":                    "America/Argentina/Buenos_Aires",
	"ashtarak":                       "Asia/Yerevan",
	"ijevan":                         "Asia/Yerevan",
	"artashat":                       "Asia/Yerevan",
	"gavarr":                         "Asia/Yerevan",
	"yeghegnadzor":                   "Asia/Yerevan",
	"gyumri":                         "Asia/Yerevan",
	"vanadzor":                       "Asia/Yerevan",
	"yerevan":                        "Asia/Yerevan",
	"oranjestad":                     "America/Aruba",
	"centralcoast":                   "Australia/Sydney",
	"sunshinecoast":                  "Australia/Brisbane",
	"bourke":                         "Australia/Sydney",
	"pinecreek":                      "Australia/Darwin",
	"adelaideriver":                  "Australia/Darwin",
	"mcminnslagoon":                  "Australia/Darwin",
	"newcastlewaters":                "Australia/Darwin",
	"ravensthorpe":                   "Australia/Perth",
	"wagin":                          "Australia/Perth",
	"roebourne":                      "Australia/Perth",
	"pannawonica":                    "Australia/Perth",
	"tomprice":                       "Australia/Perth",
	"kalbarri":                       "Australia/Perth",
	"mountmagnet":                    "Australia/Perth",
	"morawa":                         "Australia/Perth",
	"portdenison":                    "Australia/Perth",
	"merredin":                       "Australia/Perth",
	"mountbarker":                    "Australia/Perth",
	"katanning":                      "Australia/Perth",
	"narrogin":                       "Australia/Perth",
	"gingin":                         "Australia/Perth",
	"bunbury":                        "Australia/Perth",
	"kwinana":                        "Australia/Perth",
	"southerncross":                  "Australia/Perth",
	"kaltukatjara":                   "Australia/Perth",
	"queanbeyan":                     "Australia/Sydney",
	"tweedheads":                     "Australia/Sydney",
	"ivanhoe":                        "Australia/Sydney",
	"wilcannia":                      "Australia/Sydney",
	"merimbula":                      "Australia/Sydney",
	"echuca":                         "Australia/Melbourne",
	"deniliquin":                     "Australia/Sydney",
	"nowra":                          "Australia/Sydney",
	"ulladulla":                      "Australia/Sydney",
	"batemansbay":                    "Australia/Sydney",
	"cooma":                          "Australia/Sydney",
	"tumut":                          "Australia/Sydney",
	"leeton":                         "Australia/Sydney",
	"young":                          "Australia/Sydney",
	"cowra":                          "Australia/Sydney",
	"forbes":                         "Australia/Sydney",
	"goulburn":                       "Australia/Sydney",
	"kiama":                          "Australia/Sydney",
	"katoomba":                       "Australia/Sydney",
	"richmond":                       "America/New_York",
	"lithgow":                        "Australia/Sydney",
	"parkes":                         "Australia/Sydney",
	"bathurst":                       "America/Moncton",
	"maitland":                       "Australia/Sydney",
	"singleton":                      "Australia/Sydney",
	"mudgee":                         "Australia/Sydney",
	"muswellbrook":                   "Australia/Sydney",
	"taree":                          "Australia/Sydney",
	"kempsey":                        "Australia/Sydney",
	"gunnedah":                       "Australia/Sydney",
	"coffsharbour":                   "Australia/Sydney",
	"narrabri":                       "Australia/Sydney",
	"inverell":                       "Australia/Sydney",
	"yamba":                          "Australia/Sydney",
	"ballina":                        "Australia/Sydney",
	"waggawagga":                     "Australia/Sydney",
	"scone":                          "Australia/Sydney",
	"byronbay":                       "Australia/Sydney",
	"berri":                          "Australia/Adelaide",
	"peterborough":                   "Europe/London",
	"wallaroo":                       "Australia/Adelaide",
	"clare":                          "Australia/Adelaide",
	"meningie":                       "Australia/Adelaide",
	"kingstonsoutheast":              "Australia/Adelaide",
	"bordertown":                     "Australia/Adelaide",
	"penola":                         "Australia/Adelaide",
	"kingoonya":                      "Australia/Adelaide",
	"kimba":                          "Australia/Adelaide",
	"streakybay":                     "Australia/Adelaide",
	"cowell":                         "Australia/Adelaide",
	"tumbybay":                       "Australia/Adelaide",
	"andamooka":                      "Australia/Adelaide",
	"woomera":                        "Australia/Adelaide",
	"portpirie":                      "Australia/Adelaide",
	"gawler":                         "Australia/Adelaide",
	"murraybridge":                   "Australia/Adelaide",
	"victorharbor":                   "Australia/Adelaide",
	"hamilton":                       "Pacific/Auckland",
	"ouyen":                          "Australia/Melbourne",
	"colac":                          "Australia/Melbourne",
	"stawell":                        "Australia/Melbourne",
	"horsham":                        "Australia/Melbourne",
	"ararat":                         "Australia/Melbourne",
	"maryborough":                    "Australia/Brisbane",
	"bairnsdale":                     "Australia/Melbourne",
	"sale":                           "Australia/Melbourne",
	"traralgon":                      "Australia/Melbourne",
	"wonthaggi":                      "Australia/Melbourne",
	"cranbourne":                     "Australia/Melbourne",
	"ballarat":                       "Australia/Melbourne",
	"melton":                         "Australia/Melbourne",
	"seymour":                        "Australia/Melbourne",
	"shepparton":                     "Australia/Melbourne",
	"cobram":                         "Australia/Melbourne",
	"swanhill":                       "Australia/Melbourne",
	"sunbury":                        "Australia/Melbourne",
	"proserpine":                     "Australia/Brisbane",
	"theodore":                       "Australia/Brisbane",
	"eidsvold":                       "Australia/Brisbane",
	"barcaldine":                     "Australia/Brisbane",
	"winton":                         "Australia/Brisbane",
	"longreach":                      "Australia/Brisbane",
	"caboolture":                     "Australia/Brisbane",
	"warwick":                        "Australia/Brisbane",
	"kingaroy":                       "Australia/Brisbane",
	"dalby":                          "Australia/Brisbane",
	"bongaree":                       "Australia/Brisbane",
	"gympie":                         "Australia/Brisbane",
	"ingham":                         "Australia/Brisbane",
	"birdsville":                     "Australia/Brisbane",
	"bedourie":                       "Australia/Brisbane",
	"boulia":                         "Australia/Brisbane",
	"burketown":                      "Australia/Brisbane",
	"herveybay":                      "Australia/Brisbane",
	"biloela":                        "Australia/Brisbane",
	"yeppoon":                        "Australia/Brisbane",
	"emerald":                        "Australia/Brisbane",
	"moranbah":                       "Australia/Brisbane",
	"charterstowers":                 "Australia/Brisbane",
	"ayr":                            "Europe/London",
	"atherton":                       "Australia/Brisbane",
	"portdouglas":                    "Australia/Brisbane",
	"smithton":                       "Australia/Hobart",
	"scottsdale":                     "America/Phoenix",
	"bicheno":                        "Australia/Hobart",
	"oatlands":                       "Australia/Hobart",
	"queenstown":                     "Africa/Johannesburg",
	"kingston":                       "America/Jamaica",
	"tennantcreek":                   "Australia/Darwin",
	"yulara":                         "Australia/Darwin",
	"erldunda":                       "Australia/Darwin",
	"norseman":                       "Australia/Perth",
	"hallscreek":                     "Australia/Perth",
	"kununurra":                      "Australia/Perth",
	"derby":                          "Australia/Perth",
	"onslow":                         "Australia/Perth",
	"exmouth":                        "Australia/Perth",
	"carnarvon":                      "Africa/Johannesburg",
	"newman":                         "Australia/Perth",
	"meekatharra":                    "Australia/Perth",
	"threesprings":                   "Australia/Perth",
	"manjimup":                       "Australia/Perth",
	"northam":                        "Australia/Perth",
	"esperance":                      "Australia/Perth",
	"leonara":                        "Australia/Perth",
	"laverton":                       "Australia/Perth",
	"wyndham":                        "Australia/Perth",
	"albury":                         "Australia/Sydney",
	"forster-tuncurry":               "Australia/Sydney",
	"portmacquarie":                  "Australia/Sydney",
	"tamworth":                       "Australia/Sydney",
	"grafton":                        "Australia/Sydney",
	"moree":                          "Australia/Sydney",
	"goondiwindi":                    "Australia/Brisbane",
	"lismore":                        "Australia/Sydney",
	"wollongong":                     "Australia/Sydney",
	"ceduna":                         "Australia/Adelaide",
	"mountgambier":                   "Australia/Adelaide",
	"portaugusta":                    "Australia/Adelaide",
	"warrnambool":                    "Australia/Melbourne",
	"mildura":                        "Australia/Melbourne",
	"geelong":                        "Australia/Melbourne",
	"camooweal":                      "Australia/Brisbane",
	"quilpie":                        "Australia/Brisbane",
	"charleville":                    "Australia/Brisbane",
	"hughenden":                      "Australia/Brisbane",
	"caloundra":                      "Australia/Brisbane",
	"roma":                           "Australia/Brisbane",
	"toowoomba":                      "Australia/Brisbane",
	"georgetown":                     "Africa/Banjul",
	"thargomindah":                   "Australia/Brisbane",
	"weipa":                          "Australia/Brisbane",
	"karumba":                        "Australia/Brisbane",
	"cloncurry":                      "Australia/Brisbane",
	"bundaberg":                      "Australia/Brisbane",
	"gladstone":                      "Australia/Brisbane",
	"bowen":                          "Australia/Brisbane",
	"innisfail":                      "Australia/Brisbane",
	"mackay":                         "Australia/Brisbane",
	"burnie":                         "Australia/Hobart",
	"launceston":                     "Australia/Hobart",
	"katherine":                      "Australia/Darwin",
	"busselton":                      "Australia/Perth",
	"mandurah":                       "Australia/Perth",
	"broome":                         "Australia/Perth",
	"kalgoorlie":                     "Australia/Perth",
	"albany":                         "America/New_York",
	"porthedland":                    "Australia/Perth",
	"karratha":                       "Australia/Perth",
	"geraldton":                      "America/Toronto",
	"griffith":                       "Australia/Sydney",
	"orange":                         "Australia/Sydney",
	"dubbo":                          "Australia/Sydney",
	"armidale":                       "Australia/Sydney",
	"brokenhill":                     "Australia/Broken_Hill",
	"portlincoln":                    "Australia/Adelaide",
	"whyalla":                        "Australia/Adelaide",
	"portland":                       "America/Los_Angeles",
	"bendigo":                        "Australia/Melbourne",
	"wangaratta":                     "Australia/Melbourne",
	"windorah":                       "Australia/Brisbane",
	"mountisa":                       "Australia/Brisbane",
	"rockhampton":                    "Australia/Brisbane",
	"cairns":                         "Australia/Brisbane",
	"goldcoast":                      "Australia/Brisbane",
	"devonport":                      "Australia/Hobart",
	"darwin":                         "Australia/Darwin",
	"alicesprings":                   "Australia/Darwin",
	"canberra":                       "Australia/Sydney",
	"newcastle":                      "Europe/London",
	"adelaide":                       "Australia/Adelaide",
	"townsville":                     "Australia/Brisbane",
	"brisbane":                       "Australia/Brisbane",
	"hobart":                         "Australia/Hobart",
	"perth":                          "Europe/London",
	"melbourne":                      "America/New_York",
	"sydney":                         "America/Glace_Bay",
	"bregenz":                        "Europe/Vienna",
	"eisenstadt":                     "Europe/Vienna",
	"wienerneustadt":                 "Europe/Vienna",
	"graz":                           "Europe/Vienna",
	"klagenfurt":                     "Europe/Vienna",
	"linz":                           "Europe/Vienna",
	"passau":                         "Europe/Berlin",
	"salzburg":                       "Europe/Vienna",
	"innsbruck":                      "Europe/Vienna",
	"vienna":                         "Europe/Vienna",
	"gadabay":                        "Asia/Baku",
	"goranboy":                       "Asia/Baku",
	"tovuz":                          "Asia/Baku",
	"agdam":                          "Asia/Baku",
	"qabala":                         "Asia/Baku",
	"oguz":                           "Asia/Baku",
	"ganca":                          "Asia/Baku",
	"yevlax":                         "Asia/Baku",
	"sumqayt":                        "Asia/Baku",
	"alibayramli":                    "Asia/Baku",
	"goycay":                         "Asia/Baku",
	"lankaran":                       "Asia/Baku",
	"saki":                           "Asia/Baku",
	"stepanakert":                    "Asia/Baku",
	"kapan":                          "Asia/Yerevan",
	"naxcivan":                       "Asia/Baku",
	"baku":                           "Asia/Baku",
	"manama":                         "Asia/Bahrain",
	"tangail":                        "Asia/Dhaka",
	"sylhet":                         "Asia/Dhaka",
	"mymensingh":                     "Asia/Dhaka",
	"jamalpur":                       "Asia/Dhaka",
	"narayanganj":                    "Asia/Dhaka",
	"jessore":                        "Asia/Dhaka",
	"barisal":                        "Asia/Dhaka",
	"comilla":                        "Asia/Dhaka",
	"pabna":                          "Asia/Dhaka",
	"nawabganj":                      "Asia/Dhaka",
	"saidpur":                        "Asia/Dhaka",
	"rangpur":                        "Asia/Dhaka",
	"khulna":                         "Asia/Dhaka",
	"rajshahi":                       "Asia/Dhaka",
	"dhaka":                          "Asia/Dhaka",
	"chittagong":                     "Asia/Dhaka",
	"bridgetown":                     "America/Barbados",
	"baranavichy":                    "Europe/Minsk",
	"polatsk":                        "Europe/Minsk",
	"maladzyechna":                   "Europe/Minsk",
	"pinsk":                          "Europe/Minsk",
	"mazyr":                          "Europe/Minsk",
	"mahilyow":                       "Europe/Minsk",
	"babruysk":                       "Europe/Minsk",
	"orsha":                          "Europe/Minsk",
	"lida":                           "Europe/Minsk",
	"hrodna":                         "Europe/Minsk",
	"barysaw":                        "Europe/Minsk",
	"homyel":                         "Europe/Minsk",
	"vitsyebsk":                      "Europe/Minsk",
	"brest":                          "Europe/Paris",
	"minsk":                          "Europe/Minsk",
	"mons":                           "Europe/Brussels",
	"hasselt":                        "Europe/Brussels",
	"arlon":                          "Europe/Brussels",
	"gent":                           "Europe/Brussels",
	"liege":                          "Europe/Brussels",
	"brugge":                         "Europe/Brussels",
	"namur":                          "Europe/Brussels",
	"charleroi":                      "Europe/Brussels",
	"antwerpen":                      "Europe/Brussels",
	"brussels":                       "Europe/Brussels",
	"elcayo":                         "America/Belize",
	"corozal":                        "America/Belize",
	"dangriga":                       "America/Belize",
	"belizecity":                     "America/Belize",
	"orangewalk":                     "America/Belize",
	"puntagorda":                     "America/Belize",
	"belmopan":                       "America/Belize",
	"lokossa":                        "Africa/Porto-Novo",
	"kandi":                          "Africa/Porto-Novo",
	"ouidah":                         "Africa/Porto-Novo",
	"abomey":                         "Africa/Porto-Novo",
	"natitingou":                     "Africa/Porto-Novo",
	"djougou":                        "Africa/Porto-Novo",
	"parakou":                        "Africa/Porto-Novo",
	"porto-novo":                     "Africa/Porto-Novo",
	"cotonou":                        "Africa/Porto-Novo",
	"paro":                           "Asia/Thimphu",
	"punakha":                        "Asia/Thimphu",
	"wangdueprodrang":                "Asia/Thimphu",
	"thimphu":                        "Asia/Thimphu",
	"punata":                         "America/La_Paz",
	"cliza":                          "America/La_Paz",
	"quillacollo":                    "America/La_Paz",
	"puertovillarroel":               "America/La_Paz",
	"tarabuco":                       "America/La_Paz",
	"guayaramerin":                   "America/La_Paz",
	"santaana":                       "America/El_Salvador",
	"baures":                         "America/La_Paz",
	"sicasica":                       "America/La_Paz",
	"rurrenabaque":                   "America/La_Paz",
	"sorata":                         "America/La_Paz",
	"achacachi":                      "America/La_Paz",
	"viacha":                         "America/La_Paz",
	"quime":                          "America/La_Paz",
	"llallagua":                      "America/La_Paz",
	"uncia":                          "America/La_Paz",
	"uyuni":                          "America/La_Paz",
	"villamartin":                    "America/La_Paz",
	"betanzos":                       "America/La_Paz",
	"portachuelo":                    "America/La_Paz",
	"samaipata":                      "America/La_Paz",
	"cuevo":                          "America/La_Paz",
	"sancarlos":                      "America/Caracas",
	"entrerios":                      "America/La_Paz",
	"aiquile":                        "America/La_Paz",
	"padilla":                        "America/La_Paz",
	"camargo":                        "America/La_Paz",
	"reyes":                          "America/La_Paz",
	"sanborja":                       "America/La_Paz",
	"magdalena":                      "America/Hermosillo",
	"sanramon":                       "America/Lima",
	"puertoheath":                    "America/Lima",
	"charana":                        "America/La_Paz",
	"puertoacosta":                   "America/La_Paz",
	"apolo":                          "America/La_Paz",
	"coroico":                        "America/La_Paz",
	"corocoro":                       "America/La_Paz",
	"sabaya":                         "America/La_Paz",
	"challapata":                     "America/La_Paz",
	"llica":                          "America/La_Paz",
	"potosi":                         "America/La_Paz",
	"villazon":                       "America/La_Paz",
	"tupiza":                         "America/La_Paz",
	"montero":                        "America/La_Paz",
	"pisofirme":                      "America/La_Paz",
	"robore":                         "America/La_Paz",
	"puertoquijarro":                 "America/La_Paz",
	"sanignacio":                     "America/La_Paz",
	"ascension":                      "America/Ojinaga",
	"sanjavier":                      "America/La_Paz",
	"vallegrande":                    "America/La_Paz",
	"puertosuarez":                   "America/La_Paz",
	"charagua":                       "America/La_Paz",
	"villamontes":                    "America/La_Paz",
	"bermejo":                        "America/La_Paz",
	"cochabamba":                     "America/La_Paz",
	"oruro":                          "America/La_Paz",
	"camiri":                         "America/La_Paz",
	"cobija":                         "America/Rio_Branco",
	"sanmatias":                      "America/La_Paz",
	"sanjose":                        "America/Los_Angeles",
	"trinidad":                       "America/Montevideo",
	"tarija":                         "America/La_Paz",
	"sucre":                          "America/La_Paz",
	"riberalta":                      "America/La_Paz",
	"santacruz":                      "America/Los_Angeles",
	"zenica":                         "Europe/Sarajevo",
	"mostar":                         "Europe/Sarajevo",
	"tuzla":                          "Europe/Sarajevo",
	"prijedor":                       "Europe/Sarajevo",
	"banjaluka":                      "Europe/Sarajevo",
	"sarajevo":                       "Europe/Sarajevo",
	"mochudi":                        "Africa/Gaborone",
	"ghanzi":                         "Africa/Gaborone",
	"lokhwabe":                       "Africa/Gaborone",
	"lehututu":                       "Africa/Gaborone",
	"tshabong":                       "Africa/Gaborone",
	"tsau":                           "Africa/Gaborone",
	"nokaneng":                       "Africa/Gaborone",
	"mohembo":                        "Africa/Gaborone",
	"maun":                           "Africa/Gaborone",
	"kasane":                         "Africa/Gaborone",
	"nata":                           "Africa/Gaborone",
	"mopipi":                         "Africa/Gaborone",
	"palapye":                        "Africa/Gaborone",
	"lobatse":                        "Africa/Gaborone",
	"kanye":                          "Africa/Gaborone",
	"molepolole":                     "Africa/Gaborone",
	"francistown":                    "Africa/Gaborone",
	"mahalapye":                      "Africa/Gaborone",
	"serowe":                         "Africa/Gaborone",
	"gaborone":                       "Africa/Gaborone",
	"grajau":                         "America/Fortaleza",
	"presidentedutra":                "America/Fortaleza",
	"itapecurumirim":                 "America/Fortaleza",
	"saojosederibamar":               "America/Fortaleza",
	"santaines":                      "America/Fortaleza",
	"timon":                          "America/Fortaleza",
	"capanema":                       "America/Belem",
	"portel":                         "America/Belem",
	"itupiranga":                     "America/Belem",
	"pimentabueno":                   "America/Porto_Velho",
	"pontapora":                      "America/Campo_Grande",
	"maracaju":                       "America/Campo_Grande",
	"jardim":                         "America/Campo_Grande",
	"treslagoas":                     "America/Campo_Grande",
	"guanhaes":                       "America/Sao_Paulo",
	"leopoldina":                     "America/Sao_Paulo",
	"novalima":                       "America/Sao_Paulo",
	"pousoalegre":                    "America/Sao_Paulo",
	"itauna":                         "America/Sao_Paulo",
	"caratinga":                      "America/Sao_Paulo",
	"diamantina":                     "America/Sao_Paulo",
	"nanuque":                        "America/Sao_Paulo",
	"barbacena":                      "America/Sao_Paulo",
	"pocosdecaldas":                  "America/Sao_Paulo",
	"guaxupe":                        "America/Sao_Paulo",
	"saojoaodelrei":                  "America/Sao_Paulo",
	"muriae":                         "America/Sao_Paulo",
	"passos":                         "America/Sao_Paulo",
	"conselheirolafaiete":            "America/Sao_Paulo",
	"formiga":                        "America/Sao_Paulo",
	"frutal":                         "America/Sao_Paulo",
	"iturama":                        "America/Sao_Paulo",
	"ituiutaba":                      "America/Sao_Paulo",
	"araguari":                       "America/Sao_Paulo",
	"almenara":                       "America/Sao_Paulo",
	"varzeagrande":                   "America/Cuiaba",
	"caceres":                        "America/Cuiaba",
	"santanadolivramento":            "America/Sao_Paulo",
	"canoas":                         "America/Sao_Paulo",
	"quarai":                         "America/Sao_Paulo",
	"santavitoriadopalmar":           "America/Sao_Paulo",
	"saolourencodosul":               "America/Sao_Paulo",
	"canela":                         "America/Sao_Paulo",
	"saogabriel":                     "America/Sao_Paulo",
	"rosariodosul":                   "America/Sao_Paulo",
	"cachoeiradosul":                 "America/Sao_Paulo",
	"osorio":                         "America/Sao_Paulo",
	"santacruzdosul":                 "America/Sao_Paulo",
	"saoluizgonzaga":                 "America/Sao_Paulo",
	"santoangelo":                    "America/Sao_Paulo",
	"carazinho":                      "America/Sao_Paulo",
	"erechim":                        "America/Sao_Paulo",
	"guaira":                         "America/Sao_Paulo",
	"palmas":                         "America/Araguaina",
	"arapongas":                      "America/Sao_Paulo",
	"paranagua":                      "America/Sao_Paulo",
	"saojosedospinhais":              "America/Sao_Paulo",
	"guarapuava":                     "America/Sao_Paulo",
	"rionegro":                       "America/Sao_Paulo",
	"apucarana":                      "America/Sao_Paulo",
	"lapa":                           "America/Sao_Paulo",
	"irati":                          "America/Sao_Paulo",
	"castro":                         "America/Santiago",
	"telemacoborba":                  "America/Sao_Paulo",
	"jacarezinho":                    "America/Sao_Paulo",
	"blumenau":                       "America/Sao_Paulo",
	"brusque":                        "America/Sao_Paulo",
	"ararangua":                      "America/Sao_Paulo",
	"jaraguadosul":                   "America/Sao_Paulo",
	"tubarao":                        "America/Sao_Paulo",
	"laguna":                         "America/Sao_Paulo",
	"joacaba":                        "America/Sao_Paulo",
	"cacador":                        "America/Sao_Paulo",
	"canoinhas":                      "America/Sao_Paulo",
	"camocim":                        "America/Fortaleza",
	"russas":                         "America/Fortaleza",
	"sobral":                         "America/Fortaleza",
	"iguatu":                         "America/Fortaleza",
	"quixada":                        "America/Fortaleza",
	"caninde":                        "America/Fortaleza",
	"campomaior":                     "America/Fortaleza",
	"barras":                         "America/Fortaleza",
	"riolargo":                       "America/Maceio",
	"palmeiradosindios":              "America/Maceio",
	"santacruzcabralia":              "America/Bahia",
	"pauloafonso":                    "America/Bahia",
	"brumado":                        "America/Bahia",
	"jaguaquara":                     "America/Bahia",
	"itapetinga":                     "America/Bahia",
	"ubaitaba":                       "America/Bahia",
	"cachoeirodeitapemirim":          "America/Sao_Paulo",
	"barramansa":                     "America/Sao_Paulo",
	"novaiguacu":                     "America/Sao_Paulo",
	"duquedecaxias":                  "America/Sao_Paulo",
	"niteroi":                        "America/Sao_Paulo",
	"cabofrio":                       "America/Sao_Paulo",
	"macae":                          "America/Sao_Paulo",
	"miracema":                       "America/Sao_Paulo",
	"apodi":                          "America/Fortaleza",
	"morrinhos":                      "America/Sao_Paulo",
	"ceres":                          "America/Sao_Paulo",
	"catalao":                        "America/Sao_Paulo",
	"cristalina":                     "America/Sao_Paulo",
	"trindade":                       "America/Sao_Paulo",
	"ipora":                          "America/Sao_Paulo",
	"inhumas":                        "America/Sao_Paulo",
	"itaberai":                       "America/Sao_Paulo",
	"santoandre":                     "America/Sao_Paulo",
	"pindamonhangaba":                "America/Sao_Paulo",
	"rioclaro":                       "America/Sao_Paulo",
	"ourinhos":                       "America/Sao_Paulo",
	"itanhaem":                       "America/Sao_Paulo",
	"jaboticabal":                    "America/Sao_Paulo",
	"bragancapaulista":               "America/Sao_Paulo",
	"jundiai":                        "America/Sao_Paulo",
	"saojosedoscampos":               "America/Sao_Paulo",
	"guaratingueta":                  "America/Sao_Paulo",
	"pirassununga":                   "America/Sao_Paulo",
	"americana":                      "America/Sao_Paulo",
	"piracicaba":                     "America/Sao_Paulo",
	"saojoaodaboavista":              "America/Sao_Paulo",
	"saocarlos":                      "America/Sao_Paulo",
	"tupa":                           "America/Sao_Paulo",
	"penapolis":                      "America/Sao_Paulo",
	"presidenteprudente":             "America/Sao_Paulo",
	"registro":                       "America/Sao_Paulo",
	"tatui":                          "America/Sao_Paulo",
	"avare":                          "America/Sao_Paulo",
	"garca":                          "America/Sao_Paulo",
	"catanduva":                      "America/Sao_Paulo",
	"batatais":                       "America/Sao_Paulo",
	"barretos":                       "America/Sao_Paulo",
	"marilia":                        "America/Sao_Paulo",
	"itu":                            "America/Sao_Paulo",
	"itapetininga":                   "America/Sao_Paulo",
	"jaboatao":                       "America/Recife",
	"olinda":                         "America/Recife",
	"cabodesantoagostinho":           "America/Recife",
	"carpina":                        "America/Recife",
	"arcoverde":                      "America/Recife",
	"manacapuru":                     "America/Manaus",
	"maues":                          "America/Manaus",
	"pedreiras":                      "America/Fortaleza",
	"codo":                           "America/Fortaleza",
	"coroata":                        "America/Fortaleza",
	"chapadinha":                     "America/Fortaleza",
	"pinheiro":                       "America/Fortaleza",
	"barradocorda":                   "America/Fortaleza",
	"viana":                          "America/Fortaleza",
	"colinas":                        "America/Fortaleza",
	"viseu":                          "Europe/Lisbon",
	"capitaopoco":                    "America/Belem",
	"castanhal":                      "America/Belem",
	"salinopolis":                    "America/Belem",
	"alenquer":                       "America/Santarem",
	"oriximina":                      "America/Santarem",
	"xinguara":                       "America/Belem",
	"jacunda":                        "America/Belem",
	"uruara":                         "America/Santarem",
	"altamira":                       "America/Santarem",
	"paragominas":                    "America/Belem",
	"cameta":                         "America/Belem",
	"rolimdemoura":                   "America/Porto_Velho",
	"ariquemes":                      "America/Porto_Velho",
	"abuna":                          "America/Porto_Velho",
	"tocantinopolis":                 "America/Araguaina",
	"gurupi":                         "America/Araguaina",
	"aquidauana":                     "America/Campo_Grande",
	"paranaiba":                      "America/Campo_Grande",
	"setelagoas":                     "America/Sao_Paulo",
	"divinopolis":                    "America/Sao_Paulo",
	"ipatinga":                       "America/Sao_Paulo",
	"araxa":                          "America/Sao_Paulo",
	"lavras":                         "America/Sao_Paulo",
	"uba":                            "America/Sao_Paulo",
	"campobelo":                      "America/Sao_Paulo",
	"pontenova":                      "America/Sao_Paulo",
	"curvelo":                        "America/Sao_Paulo",
	"paracatu":                       "America/Sao_Paulo",
	"bocaiuva":                       "America/Sao_Paulo",
	"aracuai":                        "America/Sao_Paulo",
	"janauba":                        "America/Sao_Paulo",
	"juina":                          "America/Cuiaba",
	"barradogarcas":                  "America/Cuiaba",
	"ponteselacerda":                 "America/Cuiaba",
	"barradobugres":                  "America/Cuiaba",
	"rondonopolis":                   "America/Cuiaba",
	"uruguaiana":                     "America/Sao_Paulo",
	"saoborja":                       "America/Sao_Paulo",
	"novohamburgo":                   "America/Sao_Paulo",
	"camaqua":                        "America/Sao_Paulo",
	"bentogoncalves":                 "America/Sao_Paulo",
	"vacaria":                        "America/Sao_Paulo",
	"ijui":                           "America/Sao_Paulo",
	"maringa":                        "America/Sao_Paulo",
	"cascavel":                       "America/Sao_Paulo",
	"campomurao":                     "America/Sao_Paulo",
	"fozdoiguacu":                    "America/Sao_Paulo",
	"saofranciscodosul":              "America/Sao_Paulo",
	"portouniao":                     "America/Sao_Paulo",
	"itajai":                         "America/Sao_Paulo",
	"imbituba":                       "America/Sao_Paulo",
	"lajes":                          "America/Sao_Paulo",
	"granja":                         "America/Fortaleza",
	"crato":                          "America/Manaus",
	"itapipoca":                      "America/Fortaleza",
	"paracuru":                       "America/Fortaleza",
	"acarau":                         "America/Fortaleza",
	"taua":                           "America/Fortaleza",
	"crateus":                        "America/Fortaleza",
	"baturite":                       "America/Fortaleza",
	"ipu":                            "America/Fortaleza",
	"floriano":                       "America/Fortaleza",
	"piripiri":                       "America/Fortaleza",
	"penedo":                         "America/Maceio",
	"itabuna":                        "America/Bahia",
	"itamaraju":                      "America/Bahia",
	"guanambi":                       "America/Bahia",
	"portoseguro":                    "America/Bahia",
	"valenca":                        "America/Bahia",
	"serrinha":                       "America/Bahia",
	"tucano":                         "America/Bahia",
	"senhordobonfim":                 "America/Bahia",
	"remanso":                        "America/Bahia",
	"itambe":                         "America/Bahia",
	"bomjesusdalapa":                 "America/Bahia",
	"itaberaba":                      "America/Bahia",
	"saomateus":                      "America/Sao_Paulo",
	"patos":                          "America/Fortaleza",
	"voltaredonda":                   "America/Sao_Paulo",
	"petropolis":                     "America/Sao_Paulo",
	"novacruz":                       "America/Fortaleza",
	"caico":                          "America/Fortaleza",
	"acu":                            "America/Fortaleza",
	"estancia":                       "America/Maceio",
	"caracarai":                      "America/Boa_Vista",
	"portosantana":                   "America/Belem",
	"rioverde":                       "America/Mexico_City",
	"piresdorio":                     "America/Sao_Paulo",
	"anapolis":                       "America/Sao_Paulo",
	"goianesia":                      "America/Sao_Paulo",
	"niquelandia":                    "America/Sao_Paulo",
	"itumbiara":                      "America/Sao_Paulo",
	"jatai":                          "America/Sao_Paulo",
	"mineiros":                       "America/Sao_Paulo",
	"saojosedoriopreto":              "America/Sao_Paulo",
	"limeira":                        "America/Sao_Paulo",
	"taubate":                        "America/Sao_Paulo",
	"jau":                            "America/Sao_Paulo",
	"assis":                          "America/Sao_Paulo",
	"itapeva":                        "America/Sao_Paulo",
	"botucatu":                       "America/Sao_Paulo",
	"novohorizonte":                  "America/Sao_Paulo",
	"andradina":                      "America/Sao_Paulo",
	"fernandopolis":                  "America/Sao_Paulo",
	"barreiros":                      "America/Recife",
	"salgueiro":                      "America/Recife",
	"goiana":                         "America/Recife",
	"timbauba":                       "America/Recife",
	"bacabal":                        "America/Fortaleza",
	"braganca":                       "Europe/Lisbon",
	"obidos":                         "America/Santarem",
	"guajara-miram":                  "America/La_Paz",
	"portonacional":                  "America/Araguaina",
	"dourados":                       "America/Campo_Grande",
	"governadorvaladares":            "America/Sao_Paulo",
	"pirapora":                       "America/Sao_Paulo",
	"juizdefora":                     "America/Sao_Paulo",
	"santamaria":                     "America/Los_Angeles",
	"passofundo":                     "America/Sao_Paulo",
	"xapeco":                         "America/Sao_Paulo",
	"joinville":                      "America/Sao_Paulo",
	"juazeirodonorte":                "America/Fortaleza",
	"novavicosa":                     "America/Bahia",
	"alagoinhas":                     "America/Bahia",
	"juazeiro":                       "America/Bahia",
	"vitoria":                        "Europe/Madrid",
	"joaopessoa":                     "America/Fortaleza",
	"campinagrande":                  "America/Fortaleza",
	"novafriburgo":                   "America/Sao_Paulo",
	"aracatuba":                      "America/Sao_Paulo",
	"senamadureira":                  "America/Rio_Branco",
	"fonteboa":                       "America/Manaus",
	"eirunepe":                       "America/Eirunepe",
	"manicore":                       "America/Manaus",
	"barcelos":                       "America/Manaus",
	"tonantins":                      "America/Manaus",
	"tefe":                           "America/Manaus",
	"coari":                          "America/Manaus",
	"saocabrieldacachoeira":          "America/Manaus",
	"novoairao":                      "America/Manaus",
	"itacoatiara":                    "America/Manaus",
	"parintins":                      "America/Manaus",
	"natal":                          "America/Fortaleza",
	"imperatriz":                     "America/Fortaleza",
	"balsas":                         "America/Fortaleza",
	"breves":                         "America/Belem",
	"jacareacanga":                   "America/Santarem",
	"tucurui":                        "America/Belem",
	"itaituba":                       "America/Santarem",
	"conceicaodoaraguaia":            "America/Belem",
	"abaetetuba":                     "America/Belem",
	"principedabeira":                "America/Porto_Velho",
	"araguaina":                      "America/Araguaina",
	"teofilootoni":                   "America/Sao_Paulo",
	"uberaba":                        "America/Sao_Paulo",
	"januaria":                       "America/Sao_Paulo",
	"matogrosso":                     "America/Cuiaba",
	"aripuana":                       "America/Cuiaba",
	"sinop":                          "Europe/Istanbul",
	"jaguarao":                       "America/Sao_Paulo",
	"bage":                           "America/Sao_Paulo",
	"londrina":                       "America/Sao_Paulo",
	"criciuma":                       "America/Sao_Paulo",
	"aracati":                        "America/Fortaleza",
	"ico":                            "America/Fortaleza",
	"parnaiba":                       "America/Fortaleza",
	"picos":                          "America/Fortaleza",
	"arapiraca":                      "America/Maceio",
	"jequie":                         "America/Bahia",
	"ilheus":                         "America/Bahia",
	"canavieiras":                    "America/Bahia",
	"santamariadavitoria":            "America/Bahia",
	"irece":                          "America/Bahia",
	"xique-xique":                    "America/Bahia",
	"linhares":                       "America/Sao_Paulo",
	"campos":                         "America/Sao_Paulo",
	"mossoro":                        "America/Fortaleza",
	"aracaju":                        "America/Maceio",
	"laranjaldojari":                 "America/Santarem",
	"amapa":                          "America/Belem",
	"vilavelha":                      "America/Sao_Paulo",
	"santos":                         "America/Sao_Paulo",
	"bauru":                          "America/Sao_Paulo",
	"iguape":                         "America/Sao_Paulo",
	"franca":                         "America/Sao_Paulo",
	"garanhuns":                      "America/Recife",
	"caruaru":                        "America/Recife",
	"riobranco":                      "America/Rio_Branco",
	"saoluis":                        "America/Fortaleza",
	"portovelho":                     "America/Porto_Velho",
	"alvorada":                       "America/Araguaina",
	"corumba":                        "America/Campo_Grande",
	"belohorizonte":                  "America/Sao_Paulo",
	"montesclaros":                   "America/Sao_Paulo",
	"uberlandia":                     "America/Sao_Paulo",
	"colider":                        "America/Cuiaba",
	"altafloresta":                   "America/Cuiaba",
	"cuiaba":                         "America/Cuiaba",
	"pelotas":                        "America/Sao_Paulo",
	"caxiasdosul":                    "America/Sao_Paulo",
	"pontagrossa":                    "America/Sao_Paulo",
	"teresina":                       "America/Fortaleza",
	"maceio":                         "America/Maceio",
	"vitoriadaconquista":             "America/Bahia",
	"barreiras":                      "America/Bahia",
	"campinas":                       "America/Sao_Paulo",
	"sorocaba":                       "America/Sao_Paulo",
	"ribeiraopreto":                  "America/Sao_Paulo",
	"petrolina":                      "America/Recife",
	"cruzeirodosul":                  "America/Rio_Branco",
	"manaus":                         "America/Manaus",
	"caxias":                         "America/Fortaleza",
	"santarem":                       "Europe/Lisbon",
	"maraba":                         "America/Belem",
	"vilhena":                        "America/Porto_Velho",
	"ji-parana":                      "America/Porto_Velho",
	"campogrande":                    "America/Campo_Grande",
	"florianopolis":                  "America/Sao_Paulo",
	"feiradesantana":                 "America/Bahia",
	"boavista":                       "America/Boa_Vista",
	"macapa":                         "America/Belem",
	"belem":                          "America/Belem",
	"brasilia":                       "America/Sao_Paulo",
	"portoalegre":                    "America/Sao_Paulo",
	"curitiba":                       "America/Sao_Paulo",
	"fortaleza":                      "America/Fortaleza",
	"salvador":                       "America/Bahia",
	"goiania":                        "America/Sao_Paulo",
	"recife":                         "America/Recife",
	"riodejaneiro":                   "America/Sao_Paulo",
	"saopaulo":                       "America/Sao_Paulo",
	"bandarseribegawan":              "Asia/Brunei",
	"lovec":                          "Europe/Sofia",
	"montana":                        "America/Anchorage",
	"razgrad":                        "Europe/Sofia",
	"sliven":                         "Europe/Sofia",
	"plovdiv":                        "Europe/Sofia",
	"pernik":                         "Europe/Sofia",
	"vratsa":                         "Europe/Sofia",
	"shumen":                         "Europe/Sofia",
	"khaskovo":                       "Europe/Sofia",
	"starazagora":                    "Europe/Sofia",
	"pleven":                         "Europe/Sofia",
	"turnovo":                        "Europe/Sofia",
	"kyustendil":                     "Europe/Sofia",
	"dobrich":                        "Europe/Sofia",
	"varna":                          "Europe/Sofia",
	"ruse":                           "Europe/Sofia",
	"burgas":                         "Europe/Sofia",
	"sofia":                          "Europe/Sofia",
	"fadangourma":                    "Africa/Ouagadougou",
	"orodara":                        "Africa/Ouagadougou",
	"solenzo":                        "Africa/Ouagadougou",
	"nouna":                          "Africa/Ouagadougou",
	"dedougou":                       "Africa/Ouagadougou",
	"goromgorom":                     "Africa/Ouagadougou",
	"djibo":                          "Africa/Ouagadougou",
	"tougan":                         "Africa/Ouagadougou",
	"kombissiri":                     "Africa/Ouagadougou",
	"ziniare":                        "Africa/Ouagadougou",
	"yako":                           "Africa/Ouagadougou",
	"reo":                            "Africa/Ouagadougou",
	"leo":                            "Africa/Ouagadougou",
	"sapouy":                         "Africa/Ouagadougou",
	"boulsa":                         "Africa/Ouagadougou",
	"zorgo":                          "Africa/Ouagadougou",
	"koupela":                        "Africa/Ouagadougou",
	"po":                             "Africa/Ouagadougou",
	"manga":                          "Africa/Ouagadougou",
	"diebougou":                      "Africa/Ouagadougou",
	"gaoua":                          "Africa/Ouagadougou",
	"bogande":                        "Africa/Ouagadougou",
	"dori":                           "Africa/Ouagadougou",
	"sebba":                          "Africa/Ouagadougou",
	"diapaga":                        "Africa/Ouagadougou",
	"koudougou":                      "Africa/Ouagadougou",
	"ouahigouya":                     "Africa/Ouagadougou",
	"kaya":                           "Africa/Ouagadougou",
	"tenkodogo":                      "Africa/Ouagadougou",
	"banfora":                        "Africa/Ouagadougou",
	"bobodioulasso":                  "Africa/Ouagadougou",
	"ouagadougou":                    "Africa/Ouagadougou",
	"cankuzo":                        "Africa/Bujumbura",
	"karusi":                         "Africa/Bujumbura",
	"rutana":                         "Africa/Bujumbura",
	"ruyigi":                         "Africa/Bujumbura",
	"bubanza":                        "Africa/Bujumbura",
	"kayanza":                        "Africa/Bujumbura",
	"makamba":                        "Africa/Bujumbura",
	"ngozi":                          "Africa/Bujumbura",
	"kirundo":                        "Africa/Bujumbura",
	"muramvya":                       "Africa/Bujumbura",
	"bururi":                         "Africa/Bujumbura",
	"muyinga":                        "Africa/Bujumbura",
	"gitega":                         "Africa/Bujumbura",
	"bujumbura":                      "Africa/Bujumbura",
	"kampongspoe":                    "Asia/Phnom_Penh",
	"kampongthum":                    "Asia/Phnom_Penh",
	"preyveng":                       "Asia/Phnom_Penh",
	"phnumtbengmeanchey":             "Asia/Phnom_Penh",
	"stoengtreng":                    "Asia/Phnom_Penh",
	"kracheh":                        "Asia/Phnom_Penh",
	"senmonorom":                     "Asia/Phnom_Penh",
	"lumphat":                        "Asia/Phnom_Penh",
	"svayrieng":                      "Asia/Phnom_Penh",
	"sisophon":                       "Asia/Phnom_Penh",
	"krongkohkong":                   "Asia/Phnom_Penh",
	"pursat":                         "Asia/Phnom_Penh",
	"kampongcham":                    "Asia/Phnom_Penh",
	"kompongchhnang":                 "Asia/Phnom_Penh",
	"kampot":                         "Asia/Phnom_Penh",
	"takeo":                          "Asia/Phnom_Penh",
	"battambang":                     "Asia/Phnom_Penh",
	"siemreap":                       "Asia/Phnom_Penh",
	"phnompenh":                      "Asia/Phnom_Penh",
	"buea":                           "Africa/Douala",
	"bafang":                         "Africa/Douala",
	"foumban":                        "Africa/Douala",
	"bafoussam":                      "Africa/Douala",
	"kumba":                          "Africa/Douala",
	"eyumojok":                       "Africa/Douala",
	"limbe":                          "Africa/Douala",
	"kribi":                          "Africa/Douala",
	"nkongsamba":                     "Africa/Douala",
	"edea":                           "Africa/Douala",
	"wum":                            "Africa/Douala",
	"kumbo":                          "Africa/Douala",
	"bafia":                          "Africa/Douala",
	"mbalmayo":                       "Africa/Douala",
	"eseka":                          "Africa/Douala",
	"bertoua":                        "Africa/Douala",
	"abongmbang":                     "Africa/Douala",
	"batouri":                        "Africa/Douala",
	"belabo":                         "Africa/Douala",
	"meiganga":                       "Africa/Douala",
	"ngaoundere":                     "Africa/Douala",
	"tibati":                         "Africa/Douala",
	"kontcha":                        "Africa/Douala",
	"guider":                         "Africa/Douala",
	"mbe":                            "Africa/Douala",
	"douala":                         "Africa/Douala",
	"ebolowa":                        "Africa/Douala",
	"bamenda":                        "Africa/Douala",
	"garoua":                         "Africa/Douala",
	"maroua":                         "Africa/Douala",
	"yaounde":                        "Africa/Douala",
	"selkirk":                        "America/Winnipeg",
	"berensriver":                    "America/Winnipeg",
	"pukatawagan":                    "America/Winnipeg",
	"gimli":                          "America/Winnipeg",
	"islandlake":                     "America/Winnipeg",
	"melville":                       "America/Regina",
	"weyburn":                        "America/Regina",
	"laronge":                        "America/Regina",
	"stonyrapids":                    "America/Regina",
	"camrose":                        "America/Edmonton",
	"hinton":                         "America/Edmonton",
	"vegreville":                     "America/Edmonton",
	"stettler":                       "America/Edmonton",
	"laclabiche":                     "America/Edmonton",
	"wetaskiwin":                     "America/Edmonton",
	"meanderriver":                   "America/Edmonton",
	"creston":                        "America/Creston",
	"cranbrook":                      "America/Edmonton",
	"terrace":                        "America/Vancouver",
	"chilliwack":                     "America/Vancouver",
	"hallbeach":                      "America/Iqaluit",
	"lutselke":                       "America/Yellowknife",
	"hayriver":                       "America/Yellowknife",
	"deline":                         "America/Yellowknife",
	"paulatuk":                       "America/Yellowknife",
	"tsiigehtchic":                   "America/Yellowknife",
	"owensound":                      "America/Toronto",
	"orillia":                        "America/Toronto",
	"kapuskasing":                    "America/Toronto",
	"thessalon":                      "America/Toronto",
	"belleville":                     "America/Chicago",
	"sarnia":                         "America/Toronto",
	"oshawa":                         "America/Toronto",
	"london":                         "America/New_York",
	"kitchener":                      "America/Toronto",
	"newliskeard":                    "America/Toronto",
	"brockville":                     "America/Toronto",
	"bigbeaverhouse":                 "America/Winnipeg",
	"port-menier":                    "America/Montreal",
	"riviere-du-loup":                "America/Montreal",
	"drummondville":                  "America/Montreal",
	"sherbrooke":                     "America/Montreal",
	"cap-chat":                       "America/Montreal",
	"baie-comeau":                    "America/Montreal",
	"natashquan":                     "America/Montreal",
	"eastmain":                       "America/Montreal",
	"schefferville":                  "America/Montreal",
	"salluit":                        "America/Montreal",
	"amos":                           "America/Montreal",
	"joliette":                       "America/Montreal",
	"st.-jerome":                     "America/Montreal",
	"st-augustin":                    "America/Blanc-Sablon",
	"rouyn-noranda":                  "America/Montreal",
	"lasarre":                        "America/Montreal",
	"newglasgow":                     "America/Halifax",
	"liverpool":                      "Europe/London",
	"amherst":                        "America/Halifax",
	"baddeck":                        "America/Halifax",
	"deerlake":                       "America/Winnipeg",
	"lascie":                         "America/St_Johns",
	"hopedale":                       "America/Goose_Bay",
	"happyvalley-goosebay":           "America/Goose_Bay",
	"porthopesimpson":                "America/St_Johns",
	"tofino":                         "America/Vancouver",
	"steinbach":                      "America/Winnipeg",
	"nelsonhouse":                    "America/Winnipeg",
	"shamattawa":                     "America/Winnipeg",
	"oxfordhouse":                    "America/Winnipeg",
	"yorkton":                        "America/Regina",
	"swiftcurrent":                   "America/Regina",
	"biggar":                         "America/Regina",
	"kindersley":                     "America/Regina",
	"meadowlake":                     "America/Regina",
	"hudsonbay":                      "America/Regina",
	"lethbridge":                     "America/Edmonton",
	"brooks":                         "America/Edmonton",
	"lakelouise":                     "America/Edmonton",
	"athabasca":                      "America/Edmonton",
	"fortchipewyan":                  "America/Edmonton",
	"bellabella":                     "America/Vancouver",
	"sandspit":                       "America/Vancouver",
	"campbellriver":                  "America/Vancouver",
	"porthardy":                      "America/Vancouver",
	"nanaimo":                        "America/Vancouver",
	"quesnel":                        "America/Vancouver",
	"abbotsford":                     "America/Vancouver",
	"dawsoncreek":                    "America/Dawson_Creek",
	"penticton":                      "America/Vancouver",
	"nelson":                         "Pacific/Auckland",
	"lillooet":                       "America/Vancouver",
	"powellriver":                    "America/Vancouver",
	"revelstoke":                     "America/Vancouver",
	"burnslake":                      "America/Vancouver",
	"deaselake":                      "America/Vancouver",
	"coralharbour":                   "America/Coral_Harbour",
	"bakerlake":                      "America/Rankin_Inlet",
	"normanwells":                    "America/Yellowknife",
	"fortmcpherson":                  "America/Yellowknife",
	"burwashlanding":                 "America/Whitehorse",
	"orangeville":                    "America/Toronto",
	"littlecurrent":                  "America/Toronto",
	"chapleau":                       "America/Toronto",
	"wawa":                           "America/Toronto",
	"hearst":                         "America/Toronto",
	"marathon":                       "America/Toronto",
	"siouxlookout":                   "America/Winnipeg",
	"redlake":                        "America/Winnipeg",
	"catlake":                        "America/Winnipeg",
	"cornwall":                       "America/Toronto",
	"barrie":                         "America/Toronto",
	"parrysound":                     "America/Toronto",
	"wiarton":                        "America/Toronto",
	"cobalt":                         "America/Toronto",
	"cochrane":                       "America/Santiago",
	"nipigon":                        "America/Nipigon",
	"atikokan":                       "America/Atikokan",
	"rimouski":                       "America/Montreal",
	"saint-georges":                  "America/Cayenne",
	"victoriaville":                  "America/Los_Angeles",
	"chevery":                        "America/Blanc-Sablon",
	"mistassini":                     "America/Montreal",
	"kangirsuk":                      "America/Montreal",
	"shawinigan":                     "America/Montreal",
	"matagami":                       "America/Montreal",
	"mont-laurier":                   "America/Montreal",
	"pembroke":                       "America/Montreal",
	"radisson":                       "America/Montreal",
	"saintjohn":                      "America/Moncton",
	"edmundston":                     "America/Moncton",
	"shelburne":                      "America/Halifax",
	"antigonish":                     "America/Halifax",
	"windsor":                        "America/Detroit",
	"digby":                          "America/Halifax",
	"stephenville":                   "America/St_Johns",
	"argentia":                       "America/St_Johns",
	"st.anthony":                     "America/St_Johns",
	"channel-portauxbasques":         "America/St_Johns",
	"buchans":                        "America/St_Johns",
	"troutriver":                     "America/St_Johns",
	"churchillfalls":                 "America/Goose_Bay",
	"forteau":                        "America/St_Johns",
	"trepassey":                      "America/St_Johns",
	"brochet":                        "America/Winnipeg",
	"lynnlake":                       "America/Winnipeg",
	"gillam":                         "America/Winnipeg",
	"northbattleford":                "America/Regina",
	"princealbert":                   "America/Regina",
	"courtenay":                      "America/Vancouver",
	"kelowna":                        "America/Vancouver",
	"pangnirtung":                    "America/Pangnirtung",
	"holman":                         "America/Yellowknife",
	"dryden":                         "America/Winnipeg",
	"attawapiskat":                   "America/Toronto",
	"troisrivieres":                  "America/Montreal",
	"sept-iles":                      "America/Montreal",
	"cornerbrook":                    "America/St_Johns",
	"norwayhouse":                    "America/Winnipeg",
	"flinflon":                       "America/Winnipeg",
	"dauphin":                        "America/Winnipeg",
	"thepas":                         "America/Winnipeg",
	"uraniumcity":                    "America/Regina",
	"moosejaw":                       "America/Regina",
	"jasper":                         "America/Edmonton",
	"medicinehat":                    "America/Edmonton",
	"reddeer":                        "America/Edmonton",
	"banff":                          "America/Edmonton",
	"grandprairie":                   "America/Chicago",
	"smithers":                       "America/Vancouver",
	"kamloops":                       "America/Vancouver",
	"williamslake":                   "America/Vancouver",
	"princegeorge":                   "America/Vancouver",
	"fortnelson":                     "America/Fort_Nelson",
	"pondinlet":                      "America/Iqaluit",
	"capedorset":                     "America/Iqaluit",
	"kimmirut":                       "America/Iqaluit",
	"gjoahaven":                      "America/Cambridge_Bay",
	"grisefiord":                     "America/Iqaluit",
	"alert":                          "America/Pangnirtung",
	"ennadai":                        "America/Rankin_Inlet",
	"rankininlet":                    "America/Rankin_Inlet",
	"fortresolution":                 "America/Yellowknife",
	"fortsimpson":                    "America/Yellowknife",
	"inuvik":                         "America/Inuvik",
	"tuktoyaktuk":                    "America/Yellowknife",
	"watsonlake":                     "America/Whitehorse",
	"lansdownehouse":                 "America/Toronto",
	"moosonee":                       "America/Toronto",
	"sudbury":                        "America/Toronto",
	"kenora":                         "America/Winnipeg",
	"gaspe":                          "America/Montreal",
	"mingan":                         "America/Montreal",
	"dolbeau":                        "America/Montreal",
	"vald'or":                        "America/Montreal",
	"ivugivik":                       "America/Montreal",
	"inukjuak":                       "America/Montreal",
	"chicoutimi":                     "America/Montreal",
	"moncton":                        "America/Moncton",
	"fredericton":                    "America/Moncton",
	"yarmouth":                       "America/Halifax",
	"gander":                         "America/St_Johns",
	"cartwright":                     "America/Goose_Bay",
	"rigolet":                        "America/Goose_Bay",
	"portburwell":                    "America/Goose_Bay",
	"thompson":                       "America/Winnipeg",
	"brandon":                        "America/Winnipeg",
	"fortsmith":                      "America/Chicago",
	"fortmcmurray":                   "America/Edmonton",
	"peaceriver":                     "America/Edmonton",
	"iqaluit":                        "America/Iqaluit",
	"cambridgebay":                   "America/Cambridge_Bay",
	"kugluktuk":                      "America/Cambridge_Bay",
	"chesterfieldinlet":              "America/Rankin_Inlet",
	"arviat":                         "America/Rankin_Inlet",
	"taloyoak":                       "America/Cambridge_Bay",
	"igloolik":                       "America/Iqaluit",
	"dawson":                         "America/Dawson",
	"timmins":                        "America/Toronto",
	"northbay":                       "America/Toronto",
	"kuujjuarapik":                   "America/Montreal",
	"kuujjuaq":                       "America/Montreal",
	"labradorcity":                   "America/Goose_Bay",
	"winnipeg":                       "America/Winnipeg",
	"churchill":                      "America/Winnipeg",
	"regina":                         "America/Regina",
	"saskatoon":                      "America/Regina",
	"calgary":                        "America/Edmonton",
	"princerupert":                   "America/Vancouver",
	"arcticbay":                      "America/Rankin_Inlet",
	"resolute":                       "America/Resolute",
	"repulsebay":                     "America/Rankin_Inlet",
	"yellowknife":                    "America/Yellowknife",
	"fortgoodhope":                   "America/Yellowknife",
	"whitehorse":                     "America/Whitehorse",
	"ottawa":                         "America/Toronto",
	"fortsevern":                     "America/Toronto",
	"thunderbay":                     "America/Thunder_Bay",
	"quebec":                         "America/Montreal",
	"halifax":                        "America/Halifax",
	"st.john's":                      "America/St_Johns",
	"nain":                           "America/Goose_Bay",
	"charlottetown":                  "America/Halifax",
	"edmonton":                       "America/Edmonton",
	"montréal":                       "America/Montreal",
	"vancouver":                      "America/Los_Angeles",
	"toronto":                        "America/Toronto",
	"mindelo":                        "Atlantic/Cape_Verde",
	"praia":                          "Atlantic/Cape_Verde",
	"mobaye":                         "Africa/Bangui",
	"mbaiki":                         "Africa/Bangui",
	"carnot":                         "Africa/Bangui",
	"bozoum":                         "Africa/Bangui",
	"kagabandoro":                    "Africa/Bangui",
	"zemio":                          "Africa/Bangui",
	"yakossi":                        "Africa/Bangui",
	"nola":                           "Africa/Bangui",
	"sibut":                          "Africa/Bangui",
	"bossangoa":                      "Africa/Bangui",
	"birao":                          "Africa/Bangui",
	"ouadda":                         "Africa/Bangui",
	"bangassou":                      "Africa/Bangui",
	"bossembele":                     "Africa/Bangui",
	"berberati":                      "Africa/Bangui",
	"bria":                           "Africa/Bangui",
	"bouar":                          "Africa/Bangui",
	"bambari":                        "Africa/Bangui",
	"ndele":                          "Africa/Bangui",
	"obo":                            "Africa/Bangui",
	"bangui":                         "Africa/Bangui",
	"lai":                            "Africa/Ndjamena",
	"zouar":                          "Africa/Ndjamena",
	"bol":                            "Africa/Ndjamena",
	"ati":                            "Africa/Ndjamena",
	"oumhadjer":                      "Africa/Ndjamena",
	"mongo":                          "Africa/Ndjamena",
	"doba":                           "Africa/Ndjamena",
	"pala":                           "Africa/Ndjamena",
	"bongor":                         "Africa/Ndjamena",
	"kelo":                           "Africa/Ndjamena",
	"fada":                           "Africa/Ndjamena",
	"fayalargeau":                    "Africa/Ndjamena",
	"mao":                            "America/Santo_Domingo",
	"biltine":                        "Africa/Ndjamena",
	"sarh":                           "Africa/Ndjamena",
	"amtiman":                        "Africa/Ndjamena",
	"moundou":                        "Africa/Ndjamena",
	"ndjamena":                       "Africa/Ndjamena",
	"abeche":                         "Africa/Ndjamena",
	"cuya":                           "America/Santiago",
	"chuquicamata":                   "America/Santiago",
	"mariaelena":                     "America/Santiago",
	"tierraamarilla":                 "America/Santiago",
	"combarbala":                     "America/Santiago",
	"sanbernardo":                    "America/Santiago",
	"sanfelipe":                      "America/Caracas",
	"vinadelmar":                     "America/Santiago",
	"laligua":                        "America/Santiago",
	"quillota":                       "America/Santiago",
	"nuevaimperial":                  "America/Santiago",
	"loncoche":                       "America/Santiago",
	"villarica":                      "America/Santiago",
	"tolten":                         "America/Santiago",
	"lonquimay":                      "America/Santiago",
	"angol":                          "America/Santiago",
	"collipulli":                     "America/Santiago",
	"launion":                        "America/El_Salvador",
	"riobueno":                       "America/Santiago",
	"coronel":                        "America/Santiago",
	"talcahuano":                     "America/Santiago",
	"quirihue":                       "America/Santiago",
	"curanilahue":                    "America/Santiago",
	"santabarbara":                   "America/Los_Angeles",
	"pichilemu":                      "America/Santiago",
	"sanfernando":                    "America/Port_of_Spain",
	"puertovaras":                    "America/Santiago",
	"calbuco":                        "America/Santiago",
	"chonchi":                        "America/Santiago",
	"linares":                        "Europe/Madrid",
	"cauquenes":                      "America/Santiago",
	"lagunas":                        "America/Santiago",
	"pozoalmonte":                    "America/Santiago",
	"toconao":                        "America/Santiago",
	"huasco":                         "America/Santiago",
	"diegodealmagro":                 "America/Santiago",
	"caldera":                        "America/Santiago",
	"coquimbo":                       "America/Santiago",
	"vicuna":                         "America/Santiago",
	"illapel":                        "America/Santiago",
	"salamanca":                      "Europe/Madrid",
	"losandes":                       "America/Santiago",
	"sanantonio":                     "America/Chicago",
	"carahue":                        "America/Santiago",
	"loslagos":                       "America/Santiago",
	"lota":                           "America/Santiago",
	"lebu":                           "America/Santiago",
	"quellon":                        "America/Santiago",
	"constitucion":                   "America/Santiago",
	"villao'higgins":                 "America/Santiago",
	"puertoaisen":                    "America/Santiago",
	"puertonatales":                  "America/Santiago",
	"puertowilliams":                 "America/Santiago",
	"temuco":                         "America/Santiago",
	"tocopilla":                      "America/Santiago",
	"calama":                         "America/Santiago",
	"mejillones":                     "America/Santiago",
	"taltal":                         "America/Santiago",
	"vallenar":                       "America/Santiago",
	"chanaral":                       "America/Santiago",
	"ovalle":                         "America/Santiago",
	"chillan":                        "America/Santiago",
	"rancagua":                       "America/Santiago",
	"osorno":                         "America/Santiago",
	"ancud":                          "America/Santiago",
	"talca":                          "America/Santiago",
	"curico":                         "America/Santiago",
	"coihaique":                      "America/Santiago",
	"arica":                          "America/Santiago",
	"copiapo":                        "America/Santiago",
	"laserena":                       "America/Santiago",
	"losangeles":                     "America/Los_Angeles",
	"puntaarenas":                    "America/Santiago",
	"iquique":                        "America/Santiago",
	"antofagasta":                    "America/Santiago",
	"valparaiso":                     "America/Mexico_City",
	"valdivia":                       "America/Santiago",
	"concepcion":                     "America/Asuncion",
	"puertomontt":                    "America/Santiago",
	"santiago":                       "America/Lima",
	"yumen":                          "Asia/Urumqi",
	"linxia":                         "Asia/Chongqing",
	"zuozhou":                        "Asia/Shanghai",
	"sanming":                        "Asia/Shanghai",
	"huizhou":                        "Asia/Shanghai",
	"chaozhou":                       "Asia/Shanghai",
	"gyangze":                        "Asia/Urumqi",
	"dali":                           "Asia/Chongqing",
	"yangquan":                       "Asia/Shanghai",
	"shiyan":                         "Asia/Shanghai",
	"danjiangkou":                    "Asia/Shanghai",
	"shashi":                         "Asia/Shanghai",
	"anlu":                           "Asia/Shanghai",
	"zixing":                         "Asia/Shanghai",
	"deyang":                         "Asia/Chongqing",
	"tengchong":                      "Asia/Chongqing",
	"mengzi":                         "Asia/Chongqing",
	"chuxiong":                       "Asia/Chongqing",
	"hengshui":                       "Asia/Shanghai",
	"xuanhua":                        "Asia/Shanghai",
	"luohe":                          "Asia/Shanghai",
	"beipiao":                        "Asia/Shanghai",
	"wafangdian":                     "Asia/Shanghai",
	"zhucheng":                       "Asia/Shanghai",
	"hangu":                          "Asia/Shanghai",
	"xinyi":                          "Asia/Shanghai",
	"yangzhou":                       "Asia/Shanghai",
	"linhai":                         "Asia/Shanghai",
	"huangyan":                       "Asia/Shanghai",
	"daan":                           "Asia/Harbin",
	"changling":                      "Asia/Harbin",
	"tonghua":                        "Asia/Harbin",
	"baishan":                        "Asia/Harbin",
	"yanji":                          "Asia/Harbin",
	"ergunzuoqi":                     "Asia/Shanghai",
	"shangdu":                        "Asia/Chongqing",
	"orongenzizhiqi":                 "Asia/Shanghai",
	"zalantun":                       "Asia/Shanghai",
	"wuchuan":                        "Asia/Chongqing",
	"hangginhouqi":                   "Asia/Chongqing",
	"anda":                           "Asia/Harbin",
	"qinggang":                       "Asia/Harbin",
	"angangxi":                       "Asia/Harbin",
	"hulanergi":                      "Asia/Harbin",
	"qingan":                         "Asia/Harbin",
	"baiquan":                        "Asia/Harbin",
	"suileng":                        "Asia/Harbin",
	"linkou":                         "Asia/Harbin",
	"longxi":                         "Asia/Chongqing",
	"pingliang":                      "Asia/Chongqing",
	"anxi":                           "Asia/Urumqi",
	"minxian":                        "Asia/Chongqing",
	"jinchang":                       "Asia/Chongqing",
	"guide":                          "Asia/Chongqing",
	"qinzhou":                        "Asia/Chongqing",
	"pingxiang":                      "Asia/Shanghai",
	"yishan":                         "Asia/Chongqing",
	"beihai":                         "Asia/Chongqing",
	"hechi":                          "Asia/Chongqing",
	"tongren":                        "Asia/Chongqing",
	"fengjie":                        "Asia/Chongqing",
	"changping":                      "Asia/Shanghai",
	"shaowu":                         "Asia/Shanghai",
	"longyan":                        "Asia/Shanghai",
	"zhangzhou":                      "Asia/Shanghai",
	"putian":                         "Asia/Shanghai",
	"fuan":                           "Asia/Shanghai",
	"changting":                      "Asia/Shanghai",
	"nanping":                        "Asia/Shanghai",
	"ninde":                          "Asia/Shanghai",
	"jieshou":                        "Asia/Shanghai",
	"tongling":                       "Asia/Shanghai",
	"maanshan":                       "Asia/Shanghai",
	"fuyang":                         "Asia/Shanghai",
	"yangjiang":                      "Asia/Chongqing",
	"meizhou":                        "Asia/Shanghai",
	"heyuan":                         "Asia/Shanghai",
	"qingyuan":                       "Asia/Shanghai",
	"zhaoqing":                       "Asia/Shanghai",
	"lianxian":                       "Asia/Shanghai",
	"jiangmen":                       "Asia/Shanghai",
	"maoming":                        "Asia/Urumqi",
	"gar":                            "Asia/Kashgar",
	"turpan":                         "Asia/Urumqi",
	"quiemo":                         "Asia/Urumqi",
	"koktokay":                       "Asia/Urumqi",
	"hancheng":                       "Asia/Chongqing",
	"weinan":                         "Asia/Chongqing",
	"shuozhou":                       "Asia/Shanghai",
	"xinzhou":                        "Asia/Shanghai",
	"jincheng":                       "Asia/Shanghai",
	"jiexiu":                         "Asia/Shanghai",
	"changzhi":                       "Asia/Shanghai",
	"guangshui":                      "Asia/Shanghai",
	"jingmen":                        "Asia/Shanghai",
	"zicheng":                        "Asia/Shanghai",
	"shishou":                        "Asia/Shanghai",
	"xiaogan":                        "Asia/Shanghai",
	"puqi":                           "Asia/Shanghai",
	"yunxian":                        "Asia/Shanghai",
	"jinshi":                         "Asia/Shanghai",
	"chenzhou":                       "Asia/Shanghai",
	"zhijiang":                       "Asia/Shanghai",
	"xiangtan":                       "Asia/Shanghai",
	"zigong":                         "Asia/Chongqing",
	"yaan":                           "Asia/Chongqing",
	"langzhong":                      "Asia/Chongqing",
	"rongzhag":                       "Asia/Chongqing",
	"simao":                          "Asia/Chongqing",
	"wenshan":                        "Asia/Chongqing",
	"zhanyi":                         "Asia/Chongqing",
	"huize":                          "Asia/Chongqing",
	"chengde":                        "Asia/Shanghai",
	"cangzhou":                       "Asia/Shanghai",
	"baoding":                        "Asia/Shanghai",
	"hunanghua":                      "Asia/Shanghai",
	"dingzhou":                       "Asia/Shanghai",
	"nangong":                        "Asia/Shanghai",
	"linqing":                        "Asia/Shanghai",
	"xiangtai":                       "Asia/Shanghai",
	"puyang":                         "Asia/Shanghai",
	"hebi":                           "Asia/Shanghai",
	"xuchang":                        "Asia/Shanghai",
	"zhoukou":                        "Asia/Shanghai",
	"dengzhou":                       "Asia/Shanghai",
	"tieling":                        "Asia/Shanghai",
	"chaoyang":                       "Asia/Shanghai",
	"huanren":                        "Asia/Shanghai",
	"zhuanghe":                       "Asia/Shanghai",
	"yishui":                         "Asia/Shanghai",
	"shanxian":                       "Asia/Shanghai",
	"pingyi":                         "Asia/Shanghai",
	"pingdu":                         "Asia/Shanghai",
	"laiwu":                          "Asia/Shanghai",
	"buizhou":                        "Asia/Shanghai",
	"liaocheng":                      "Asia/Shanghai",
	"rizhao":                         "Asia/Shanghai",
	"dezhou":                         "Asia/Shanghai",
	"linchuan":                       "Asia/Shanghai",
	"fengcheng":                      "Asia/Shanghai",
	"jian":                           "Asia/Shanghai",
	"shangrao":                       "Asia/Shanghai",
	"jingdezhen":                     "Asia/Shanghai",
	"taizhou":                        "Asia/Shanghai",
	"shuyang":                        "Asia/Shanghai",
	"lianyungang":                    "Asia/Shanghai",
	"lishui":                         "Asia/Shanghai",
	"jiaojing":                       "Asia/Shanghai",
	"quzhou":                         "Asia/Shanghai",
	"fuyu":                           "Asia/Harbin",
	"dunhua":                         "Asia/Harbin",
	"nongan":                         "Asia/Harbin",
	"taonan":                         "Asia/Harbin",
	"liuhe":                          "Asia/Harbin",
	"huinan":                         "Asia/Harbin",
	"panshi":                         "Asia/Harbin",
	"jiaohe":                         "Asia/Harbin",
	"linjiang":                       "Asia/Harbin",
	"wangqing":                       "Asia/Harbin",
	"helong":                         "Asia/Harbin",
	"shulan":                         "Asia/Harbin",
	"jiutai":                         "Asia/Harbin",
	"alxazuoqi":                      "Asia/Chongqing",
	"linxi":                          "Asia/Shanghai",
	"kailu":                          "Asia/Shanghai",
	"bairinzuoqi":                    "Asia/Shanghai",
	"yitulihe":                       "Asia/Shanghai",
	"yakeshi":                        "Asia/Shanghai",
	"bugt":                           "Asia/Shanghai",
	"wuyuan":                         "Asia/Chongqing",
	"bayanobo":                       "Asia/Chongqing",
	"fengzhen":                       "Asia/Chongqing",
	"suihua":                         "Asia/Harbin",
	"shuangyashan":                   "Asia/Harbin",
	"shangzhi":                       "Asia/Harbin",
	"fujin":                          "Asia/Harbin",
	"yian":                           "Asia/Harbin",
	"tailai":                         "Asia/Harbin",
	"longjiang":                      "Asia/Harbin",
	"gannan":                         "Asia/Harbin",
	"hailun":                         "Asia/Harbin",
	"mishan":                         "Asia/Harbin",
	"tieli":                          "Asia/Harbin",
	"shuangcheng":                    "Asia/Harbin",
	"zhaodong":                       "Asia/Harbin",
	"lanxi":                          "Asia/Harbin",
	"keshan":                         "Asia/Harbin",
	"nancha":                         "Asia/Harbin",
	"xinqing":                        "Asia/Harbin",
	"hulin":                          "Asia/Harbin",
	"boli":                           "Asia/Harbin",
	"ningan":                         "Asia/Harbin",
	"jyekundo":                       "Asia/Urumqi",
	"liuzhou":                        "Asia/Chongqing",
	"xingyi":                         "Asia/Chongqing",
	"anshun":                         "Asia/Chongqing",
	"zunyi":                          "Asia/Chongqing",
	"wanxian":                        "Asia/Chongqing",
	"huaibei":                        "Asia/Shanghai",
	"wuhu":                           "Asia/Shanghai",
	"luan":                           "Asia/Shanghai",
	"bengbu":                         "Asia/Shanghai",
	"anqing":                         "Asia/Shanghai",
	"foshan":                         "Asia/Shanghai",
	"nagchu":                         "Asia/Urumqi",
	"nyingchi":                       "Asia/Urumqi",
	"chamdo":                         "Asia/Urumqi",
	"korla":                          "Asia/Urumqi",
	"kuqa":                           "Asia/Urumqi",
	"tacheng":                        "Asia/Urumqi",
	"shihezi":                        "Asia/Urumqi",
	"karamay":                        "Asia/Urumqi",
	"aksu":                           "Asia/Kashgar",
	"sanya":                          "Asia/Chongqing",
	"haikou":                         "Asia/Chongqing",
	"hanzhong":                       "Asia/Chongqing",
	"baoji":                          "Asia/Chongqing",
	"tongchuan":                      "Asia/Chongqing",
	"linfen":                         "Asia/Shanghai",
	"yuci":                           "Asia/Shanghai",
	"datong":                         "Asia/Shanghai",
	"jianmen":                        "Asia/Shanghai",
	"yichang":                        "Asia/Shanghai",
	"xiantao":                        "Asia/Shanghai",
	"macheng":                        "Asia/Shanghai",
	"huangshi":                       "Asia/Shanghai",
	"zhuzhou":                        "Asia/Shanghai",
	"yongzhou":                       "Asia/Shanghai",
	"yiyang":                         "Asia/Shanghai",
	"changde":                        "Asia/Shanghai",
	"shaoyang":                       "Asia/Shanghai",
	"leshan":                         "Asia/Chongqing",
	"panzhihua":                      "Asia/Chongqing",
	"fulin":                          "Asia/Chongqing",
	"guangyuan":                      "Asia/Chongqing",
	"luzhou":                         "Asia/Chongqing",
	"yibin":                          "Asia/Chongqing",
	"zhaotang":                       "Asia/Chongqing",
	"lijiang":                        "Asia/Chongqing",
	"yuxi":                           "Asia/Chongqing",
	"qinhuangdao":                    "Asia/Shanghai",
	"langfang":                       "Asia/Shanghai",
	"zhangjiakou":                    "Asia/Shanghai",
	"tangshan":                       "Asia/Shanghai",
	"anyang":                         "Asia/Shanghai",
	"jiaozuo":                        "Asia/Shanghai",
	"kaifeng":                        "Asia/Shanghai",
	"shangqiu":                       "Asia/Shanghai",
	"pingdingshan":                   "Asia/Shanghai",
	"xinyang":                        "Asia/Shanghai",
	"xinxiang":                       "Asia/Shanghai",
	"luoyang":                        "Asia/Shanghai",
	"liaoyang":                       "Asia/Shanghai",
	"dandong":                        "Asia/Shanghai",
	"yingkow":                        "Asia/Shanghai",
	"jinzhou":                        "Asia/Shanghai",
	"fuxin":                          "Asia/Shanghai",
	"benxi":                          "Asia/Shanghai",
	"fushun":                         "Asia/Shanghai",
	"jining":                         "Asia/Chongqing",
	"weifang":                        "Asia/Shanghai",
	"taian":                          "Asia/Shanghai",
	"heze":                           "Asia/Shanghai",
	"laiyang":                        "Asia/Shanghai",
	"xinyu":                          "Asia/Shanghai",
	"ganzhou":                        "Asia/Shanghai",
	"jiujiang":                       "Asia/Shanghai",
	"changzhou":                      "Asia/Shanghai",
	"zhenjiang":                      "Asia/Shanghai",
	"nantong":                        "Asia/Shanghai",
	"jiaxing":                        "Asia/Shanghai",
	"huzhou":                         "Asia/Shanghai",
	"shaoxing":                       "Asia/Shanghai",
	"jinhua":                         "Asia/Shanghai",
	"liaoyuan":                       "Asia/Harbin",
	"tumen":                          "Asia/Harbin",
	"siping":                         "Asia/Harbin",
	"baicheng":                       "Asia/Harbin",
	"wuhai":                          "Asia/Chongqing",
	"erenhot":                        "Asia/Shanghai",
	"arxan":                          "Asia/Shanghai",
	"manzhouli":                      "Asia/Shanghai",
	"xilinhot":                       "Asia/Shanghai",
	"heihe":                          "Asia/Harbin",
	"qitaihe":                        "Asia/Harbin",
	"yichun":                         "Asia/Shanghai",
	"hegang":                         "Asia/Harbin",
	"nenjiang":                       "Asia/Harbin",
	"nehe":                           "Asia/Harbin",
	"mudangiang":                     "Asia/Harbin",
	"xuanzhou":                       "Asia/Shanghai",
	"zhuhai":                         "Asia/Shanghai",
	"xianyang":                       "Asia/Chongqing",
	"xiangfan":                       "Asia/Shanghai",
	"suining":                        "Asia/Chongqing",
	"lingyuan":                       "Asia/Shanghai",
	"weihai":                         "Asia/Shanghai",
	"yancheng":                       "Asia/Shanghai",
	"xiamen":                         "Asia/Shanghai",
	"nanchong":                       "Asia/Chongqing",
	"neijiang":                       "Asia/Chongqing",
	"nanyang":                        "Asia/Shanghai",
	"jinxi":                          "Asia/Shanghai",
	"yantai":                         "Asia/Shanghai",
	"zaozhuang":                      "Asia/Shanghai",
	"suzhou":                         "Asia/Shanghai",
	"xuzhou":                         "Asia/Shanghai",
	"wuxi":                           "Asia/Shanghai",
	"jilin":                          "Asia/Harbin",
	"zhangye":                        "Asia/Chongqing",
	"wuwei":                          "Asia/Chongqing",
	"dunhuang":                       "Asia/Urumqi",
	"tianshui":                       "Asia/Chongqing",
	"dulan":                          "Asia/Urumqi",
	"golmud":                         "Asia/Urumqi",
	"yulin":                          "Asia/Chongqing",
	"bose":                           "Asia/Chongqing",
	"wuzhou":                         "Asia/Chongqing",
	"lupanshui":                      "Asia/Chongqing",
	"quanzhou":                       "Asia/Shanghai",
	"hefei":                          "Asia/Shanghai",
	"zhanjiang":                      "Asia/Urumqi",
	"shaoguan":                       "Asia/Shanghai",
	"xigaze":                         "Asia/Urumqi",
	"shache":                         "Asia/Kashgar",
	"yining":                         "Asia/Kashgar",
	"altay":                          "Asia/Hovd",
	"shizuishan":                     "Asia/Chongqing",
	"ankang":                         "Asia/Chongqing",
	"houma":                          "America/Chicago",
	"yueyang":                        "Asia/Shanghai",
	"hengyang":                       "Asia/Shanghai",
	"mianyang":                       "Asia/Chongqing",
	"xichang":                        "Asia/Chongqing",
	"baoshan":                        "Asia/Chongqing",
	"gejiu":                          "Asia/Chongqing",
	"shijianzhuang":                  "Asia/Shanghai",
	"handan":                         "Asia/Shanghai",
	"anshan":                         "Asia/Shanghai",
	"dalian":                         "Asia/Shanghai",
	"qingdao":                        "Asia/Shanghai",
	"linyi":                          "Asia/Shanghai",
	"huaiyin":                        "Asia/Shanghai",
	"wenzhou":                        "Asia/Shanghai",
	"ningbo":                         "Asia/Shanghai",
	"tongliao":                       "Asia/Shanghai",
	"hohhot":                         "Asia/Chongqing",
	"chifeng":                        "Asia/Shanghai",
	"ulanhot":                        "Asia/Shanghai",
	"hailar":                         "Asia/Shanghai",
	"jiamusi":                        "Asia/Harbin",
	"beian":                          "Asia/Harbin",
	"daqing":                         "Asia/Harbin",
	"jixi":                           "Asia/Harbin",
	"jiayuguan":                      "Asia/Chongqing",
	"xining":                         "Asia/Chongqing",
	"guilin":                         "Asia/Chongqing",
	"huainan":                        "Asia/Shanghai",
	"shantou":                        "Asia/Shanghai",
	"lhasa":                          "Asia/Urumqi",
	"hami":                           "Asia/Urumqi",
	"hotan":                          "Asia/Kashgar",
	"kashi":                          "Asia/Kashgar",
	"yinchuan":                       "Asia/Chongqing",
	"qiqihar":                        "Asia/Harbin",
	"shenzhen":                       "Asia/Shanghai",
	"zibo":                           "Asia/Shanghai",
	"lanzhou":                        "Asia/Chongqing",
	"nanning":                        "Asia/Chongqing",
	"guiyang":                        "Asia/Chongqing",
	"chongqing":                      "Asia/Chongqing",
	"fuzhou":                         "Asia/Shanghai",
	"guangzhou":                      "Asia/Shanghai",
	"dongguan":                       "Asia/Shanghai",
	"xian":                           "Asia/Chongqing",
	"taiyuan":                        "Asia/Shanghai",
	"wuhan":                          "Asia/Shanghai",
	"changsha":                       "Asia/Shanghai",
	"kunming":                        "Asia/Chongqing",
	"zhengzhou":                      "Asia/Shanghai",
	"shenyeng":                       "Asia/Shanghai",
	"jinan":                          "Asia/Shanghai",
	"tianjin":                        "Asia/Shanghai",
	"nanchang":                       "Asia/Shanghai",
	"nanjing":                        "Asia/Shanghai",
	"hangzhou":                       "Asia/Shanghai",
	"changchun":                      "Asia/Harbin",
	"baotou":                         "Asia/Chongqing",
	"harbin":                         "Asia/Harbin",
	"urumqi":                         "Asia/Urumqi",
	"chengdu":                        "Asia/Chongqing",
	"beijing":                        "Asia/Shanghai",
	"shanghai":                       "Asia/Shanghai",
	"yopal":                          "America/Bogota",
	"sanandres":                      "America/Bogota",
	"sonson":                         "America/Bogota",
	"sogamoso":                       "America/Bogota",
	"barrancabermeja":                "America/Bogota",
	"girardot":                       "America/Bogota",
	"campoalegre":                    "America/Bogota",
	"tuquerres":                      "America/Bogota",
	"mocoa":                          "America/Bogota",
	"cartago":                        "America/Costa_Rica",
	"soledad":                        "America/Bogota",
	"sabanalarga":                    "America/Bogota",
	"arjona":                         "America/Bogota",
	"magangue":                       "America/Bogota",
	"valledupar":                     "America/Bogota",
	"sanjosedelguaviare":             "America/Bogota",
	"puertolopez":                    "America/Bogota",
	"yarumal":                        "America/Bogota",
	"puertoberrio":                   "America/Bogota",
	"turbo":                          "America/Bogota",
	"tunja":                          "America/Bogota",
	"chiquinquira":                   "America/Bogota",
	"duitama":                        "America/Bogota",
	"ayapel":                         "America/Bogota",
	"lorica":                         "America/Bogota",
	"socorro":                        "America/Denver",
	"riohacha":                       "America/Bogota",
	"armenia":                        "America/Bogota",
	"pereira":                        "America/Bogota",
	"honda":                          "America/Bogota",
	"sanvicentedelcaguan":            "America/Bogota",
	"florencia":                      "America/Bogota",
	"guapi":                          "America/Bogota",
	"neiva":                          "America/Bogota",
	"garzon":                         "America/Bogota",
	"ipiales":                        "America/Bogota",
	"buenaventura":                   "America/Bogota",
	"tulua":                          "America/Bogota",
	"elcarmendebolivar":              "America/Bogota",
	"jurado":                         "America/Bogota",
	"nuqui":                          "America/Bogota",
	"quibdo":                         "America/Bogota",
	"elbanco":                        "America/Bogota",
	"cienaga":                        "America/Bogota",
	"sincelejo":                      "America/Bogota",
	"tolu":                           "America/Bogota",
	"arauca":                         "America/Bogota",
	"tame":                           "America/Bogota",
	"pamplona":                       "Europe/Madrid",
	"ocana":                          "America/Bogota",
	"orocue":                         "America/Bogota",
	"obando":                         "America/Bogota",
	"puertocarreno":                  "America/Bogota",
	"bello":                          "America/Bogota",
	"monteria":                       "America/Bogota",
	"bucaramanga":                    "America/Bogota",
	"ibague":                         "America/Bogota",
	"popayan":                        "America/Bogota",
	"santamarta":                     "America/Bogota",
	"cucuta":                         "America/Bogota",
	"villavicencio":                  "America/Bogota",
	"tumaco":                         "America/Bogota",
	"manizales":                      "America/Bogota",
	"pasto":                          "America/Bogota",
	"barranquilla":                   "America/Bogota",
	"cartagena":                      "Europe/Madrid",
	"mitu":                           "America/Bogota",
	"leticia":                        "America/Bogota",
	"medellin":                       "America/Bogota",
	"cali":                           "America/Bogota",
	"bogota":                         "America/Bogota",
	"moroni":                         "Indian/Comoro",
	"madingou":                       "Africa/Brazzaville",
	"kinkala":                        "Africa/Brazzaville",
	"ewo":                            "Africa/Brazzaville",
	"impfondo":                       "Africa/Brazzaville",
	"sembe":                          "Africa/Brazzaville",
	"moloundou":                      "Africa/Brazzaville",
	"owando":                         "Africa/Brazzaville",
	"makoua":                         "Africa/Brazzaville",
	"sibiti":                         "Africa/Brazzaville",
	"mossendjo":                      "Africa/Brazzaville",
	"loubomo":                        "Africa/Brazzaville",
	"gamboma":                        "Africa/Brazzaville",
	"djambala":                       "Africa/Brazzaville",
	"ouesso":                         "Africa/Brazzaville",
	"kayes":                          "Africa/Bamako",
	"pointe-noire":                   "Africa/Brazzaville",
	"brazzaville":                    "Africa/Brazzaville",
	"buluko":                         "Africa/Lubumbashi",
	"zongo":                          "Africa/Kinshasa",
	"libenge":                        "Africa/Kinshasa",
	"bongandanga":                    "Africa/Kinshasa",
	"ikela":                          "Africa/Kinshasa",
	"binga":                          "Africa/Kinshasa",
	"basankusu":                      "Africa/Kinshasa",
	"boende":                         "Africa/Kinshasa",
	"gbadolite":                      "Africa/Kinshasa",
	"businga":                        "Africa/Kinshasa",
	"bosobolo":                       "Africa/Kinshasa",
	"yangambi":                       "Africa/Lubumbashi",
	"aketi":                          "Africa/Lubumbashi",
	"mongbwalu":                      "Africa/Lubumbashi",
	"bafwasende":                     "Africa/Lubumbashi",
	"bunia":                          "Africa/Lubumbashi",
	"wamba":                          "Africa/Lubumbashi",
	"basoko":                         "Africa/Lubumbashi",
	"kenge":                          "Africa/Kinshasa",
	"bolobo":                         "Africa/Kinshasa",
	"kahemba":                        "Africa/Kinshasa",
	"bulungu":                        "Africa/Kinshasa",
	"lusanga":                        "Africa/Kinshasa",
	"mangai":                         "Africa/Kinshasa",
	"kasongo-lunda":                  "Africa/Kinshasa",
	"mushie":                         "Africa/Kinshasa",
	"dibaya":                         "Africa/Lubumbashi",
	"mweka":                          "Africa/Lubumbashi",
	"luebo":                          "Africa/Lubumbashi",
	"demba":                          "Africa/Lubumbashi",
	"ilebo":                          "Africa/Lubumbashi",
	"moanda":                         "Africa/Libreville",
	"kimpese":                        "Africa/Kinshasa",
	"kasangulu":                      "Africa/Kinshasa",
	"mbanza-ngungu":                  "Africa/Kinshasa",
	"tshela":                         "Africa/Kinshasa",
	"mwenga":                         "Africa/Lubumbashi",
	"kampene":                        "Africa/Lubumbashi",
	"kalima":                         "Africa/Lubumbashi",
	"lubutu":                         "Africa/Lubumbashi",
	"kabinda":                        "Africa/Lubumbashi",
	"lubao":                          "Africa/Lubumbashi",
	"lusambo":                        "Africa/Lubumbashi",
	"gandajika":                      "Africa/Lubumbashi",
	"lodja":                          "Africa/Lubumbashi",
	"dilolo":                         "Africa/Lubumbashi",
	"nyunzu":                         "Africa/Lubumbashi",
	"kasaji":                         "Africa/Lubumbashi",
	"luanza":                         "Africa/Lubumbashi",
	"moba":                           "Africa/Lubumbashi",
	"bukama":                         "Africa/Lubumbashi",
	"kaniama":                        "Africa/Lubumbashi",
	"kipushi":                        "Africa/Lubumbashi",
	"kambove":                        "Africa/Lubumbashi",
	"kongolo":                        "Africa/Lubumbashi",
	"kabalo":                         "Africa/Lubumbashi",
	"beni":                           "Africa/Lubumbashi",
	"lisala":                         "Africa/Kinshasa",
	"gemena":                         "Africa/Kinshasa",
	"buta":                           "Africa/Lubumbashi",
	"watsa":                          "Africa/Lubumbashi",
	"isiro":                          "Africa/Lubumbashi",
	"bondo":                          "Africa/Lubumbashi",
	"inongo":                         "Africa/Kinshasa",
	"tshikapa":                       "Africa/Lubumbashi",
	"boma":                           "Africa/Kinshasa",
	"bukavu":                         "Africa/Lubumbashi",
	"uvira":                          "Africa/Lubumbashi",
	"kindu":                          "Africa/Lubumbashi",
	"mwene-ditu":                     "Africa/Lubumbashi",
	"likasi":                         "Africa/Lubumbashi",
	"manono":                         "Africa/Lubumbashi",
	"kamina":                         "Africa/Lubumbashi",
	"mbandaka":                       "Africa/Kinshasa",
	"kisangani":                      "Africa/Lubumbashi",
	"bandundu":                       "Africa/Kinshasa",
	"kananga":                        "Africa/Lubumbashi",
	"kasongo":                        "Africa/Lubumbashi",
	"mbuji-mayi":                     "Africa/Lubumbashi",
	"kalemie":                        "Africa/Lubumbashi",
	"butembo":                        "Africa/Lubumbashi",
	"goma":                           "Africa/Lubumbashi",
	"bumba":                          "Africa/Kinshasa",
	"kikwit":                         "Africa/Kinshasa",
	"matadi":                         "Africa/Kinshasa",
	"kolwezi":                        "Africa/Lubumbashi",
	"lubumbashi":                     "Africa/Lubumbashi",
	"kinshasa":                       "Africa/Kinshasa",
	"rarotonga":                      "Pacific/Rarotonga",
	"heredia":                        "America/Costa_Rica",
	"golfito":                        "America/Costa_Rica",
	"alajuela":                       "America/Costa_Rica",
	"canas":                          "America/Costa_Rica",
	"sixaola":                        "America/Costa_Rica",
	"puntarenas":                     "America/Costa_Rica",
	"ciudadcortes":                   "America/Costa_Rica",
	"quesada":                        "America/Costa_Rica",
	"liberia":                        "America/Costa_Rica",
	"lacruz":                         "America/Mazatlan",
	"puertolimon":                    "America/Costa_Rica",
	"sibenik":                        "Europe/Zagreb",
	"karlovac":                       "Europe/Zagreb",
	"rijeka":                         "Europe/Zagreb",
	"slavonskibrod":                  "Europe/Zagreb",
	"dubrovnik":                      "Europe/Zagreb",
	"split":                          "Europe/Zagreb",
	"zadar":                          "Europe/Zagreb",
	"pula":                           "Europe/Zagreb",
	"osijek":                         "Europe/Zagreb",
	"zagreb":                         "Europe/Zagreb",
	"ciegodeavila":                   "America/Havana",
	"palmasoriano":                   "America/Havana",
	"sanantoniodelosbanos":           "America/Havana",
	"guines":                         "America/Havana",
	"caibarien":                      "America/Havana",
	"placetas":                       "America/Havana",
	"cienfuegos":                     "America/Havana",
	"nuevagerona":                    "America/Havana",
	"sanctispiritus":                 "America/Havana",
	"moron":                          "Asia/Ulaanbaatar",
	"nuevitas":                       "America/Havana",
	"manzanillo":                     "America/Mexico_City",
	"bayamo":                         "America/Havana",
	"banes":                          "America/Havana",
	"lastunas":                       "America/Havana",
	"artemisa":                       "America/Havana",
	"matanzas":                       "America/Havana",
	"colon":                          "America/Panama",
	"sagualagrande":                  "America/Havana",
	"pinardelrio":                    "America/Havana",
	"camaguey":                       "America/Havana",
	"guantanamo":                     "America/Havana",
	"holguin":                        "America/Havana",
	"santaclara":                     "America/Havana",
	"santiagodecuba":                 "America/Havana",
	"havana":                         "America/Havana",
	"willemstad":                     "America/Curacao",
	"larnaka":                        "Asia/Nicosia",
	"paphos":                         "Asia/Nicosia",
	"lemosos":                        "Asia/Nicosia",
	"nicosia":                        "Asia/Nicosia",
	"ustinadlabem":                   "Europe/Prague",
	"hradeckralove":                  "Europe/Prague",
	"ceskebudejovice":                "Europe/Prague",
	"liberec":                        "Europe/Prague",
	"olomouc":                        "Europe/Prague",
	"pizen":                          "Europe/Prague",
	"jihlava":                        "Europe/Prague",
	"zlin":                           "Europe/Prague",
	"brno":                           "Europe/Prague",
	"pardubice":                      "Europe/Prague",
	"ostrava":                        "Europe/Prague",
	"prague":                         "Europe/Prague",
	"vejle":                          "Europe/Copenhagen",
	"hillerod":                       "Europe/Copenhagen",
	"soro":                           "Europe/Copenhagen",
	"viborg":                         "Europe/Copenhagen",
	"roskilde":                       "Europe/Copenhagen",
	"svendborg":                      "Europe/Copenhagen",
	"odense":                         "Europe/Copenhagen",
	"esbjerg":                        "Europe/Copenhagen",
	"frederikshavn":                  "Europe/Copenhagen",
	"aalborg":                        "Europe/Copenhagen",
	"aarhus":                         "Europe/Copenhagen",
	"kobenhavn":                      "Europe/Copenhagen",
	"dikhil":                         "Africa/Djibouti",
	"tadjoura":                       "Africa/Djibouti",
	"alisabih":                       "Africa/Djibouti",
	"obock":                          "Africa/Djibouti",
	"djibouti":                       "Africa/Djibouti",
	"roseau":                         "America/Dominica",
	"sabaneta":                       "America/Santo_Domingo",
	"cotui":                          "America/Santo_Domingo",
	"puertoplata":                    "America/Santo_Domingo",
	"dajabon":                        "America/Santo_Domingo",
	"moca":                           "America/Santo_Domingo",
	"salcedo":                        "America/Santo_Domingo",
	"jimani":                         "America/Santo_Domingo",
	"eliaspina":                      "America/Santo_Domingo",
	"pedernales":                     "America/Santo_Domingo",
	"azua":                           "America/Santo_Domingo",
	"bonao":                          "America/Santo_Domingo",
	"bani":                           "America/Santo_Domingo",
	"hatomayor":                      "America/Santo_Domingo",
	"monteplata":                     "America/Santo_Domingo",
	"nagua":                          "America/Santo_Domingo",
	"samana":                         "America/Santo_Domingo",
	"sancristobal":                   "America/Caracas",
	"elseibo":                        "America/Santo_Domingo",
	"higuey":                         "America/Santo_Domingo",
	"neiba":                          "America/Santo_Domingo",
	"lavega":                         "America/Santo_Domingo",
	"sanfranciscodemacoris":          "America/Santo_Domingo",
	"sanpedrodemacoris":              "America/Santo_Domingo",
	"montecristi":                    "America/Santo_Domingo",
	"barahona":                       "America/Santo_Domingo",
	"bavaro":                         "America/Santo_Domingo",
	"laromana":                       "America/Santo_Domingo",
	"santodomingo":                   "America/Santo_Domingo",
	"dili":                           "Asia/Dili",
	"puyo":                           "America/Guayaquil",
	"tulcan":                         "America/Guayaquil",
	"pinas":                          "America/Guayaquil",
	"puertovillamil":                 "Pacific/Galapagos",
	"puertobaquerizomoreno":          "Pacific/Galapagos",
	"guaranda":                       "America/Guayaquil",
	"azogues":                        "America/Guayaquil",
	"salinas":                        "America/Los_Angeles",
	"alausi":                         "America/Guayaquil",
	"sangolqui":                      "America/Guayaquil",
	"muisne":                         "America/Guayaquil",
	"sangabriel":                     "America/Guayaquil",
	"macara":                         "America/Guayaquil",
	"zamora":                         "America/Mexico_City",
	"latacunga":                      "America/Guayaquil",
	"milagro":                        "America/Guayaquil",
	"babahoyo":                       "America/Guayaquil",
	"chone":                          "America/Guayaquil",
	"jipijapa":                       "America/Guayaquil",
	"yaupi":                          "America/Guayaquil",
	"macas":                          "America/Guayaquil",
	"cayambe":                        "America/Guayaquil",
	"ambato":                         "America/Guayaquil",
	"tena":                           "America/Guayaquil",
	"valdez":                         "America/Anchorage",
	"esmeraldas":                     "America/Guayaquil",
	"ibarra":                         "America/Guayaquil",
	"portoviejo":                     "America/Guayaquil",
	"machala":                        "America/Guayaquil",
	"loja":                           "America/Guayaquil",
	"manta":                          "America/Guayaquil",
	"riobamba":                       "America/Guayaquil",
	"cuenca":                         "America/Guayaquil",
	"quito":                          "America/Guayaquil",
	"guayaquil":                      "America/Guayaquil",
	"shibinelkom":                    "Africa/Cairo",
	"benha":                          "Africa/Cairo",
	"zagazig":                        "Africa/Cairo",
	"kafrelsheikh":                   "Africa/Cairo",
	"tanta":                          "Africa/Cairo",
	"ismailia":                       "Africa/Cairo",
	"elmansura":                      "Africa/Cairo",
	"dumyat":                         "Africa/Cairo",
	"matruh":                         "Africa/Cairo",
	"elalamein":                      "Africa/Cairo",
	"eldaba":                         "Africa/Cairo",
	"salum":                          "Africa/Cairo",
	"damanhur":                       "Africa/Cairo",
	"samalut":                        "Africa/Cairo",
	"mallawi":                        "Africa/Cairo",
	"benimazar":                      "Africa/Cairo",
	"benisuef":                       "Africa/Cairo",
	"rashid":                         "Africa/Cairo",
	"qasrfarafra":                    "Africa/Cairo",
	"elqasr":                         "Africa/Cairo",
	"isna":                           "Africa/Cairo",
	"qena":                           "Africa/Cairo",
	"girga":                          "Africa/Cairo",
	"sohag":                          "Africa/Cairo",
	"berenice":                       "Africa/Cairo",
	"bursafaga":                      "Africa/Cairo",
	"eltur":                          "Africa/Cairo",
	"elarish":                        "Africa/Cairo",
	"elgiza":                         "Africa/Cairo",
	"siwa":                           "Africa/Cairo",
	"elminya":                        "Africa/Cairo",
	"komombo":                        "Africa/Cairo",
	"elkharga":                       "Africa/Cairo",
	"hurghada":                       "Africa/Cairo",
	"suez":                           "Africa/Cairo",
	"bursaid":                        "Africa/Cairo",
	"elfaiyum":                       "Africa/Cairo",
	"aswan":                          "Africa/Cairo",
	"asyut":                          "Africa/Cairo",
	"luxor":                          "Africa/Cairo",
	"alexandria":                     "America/Chicago",
	"cairo":                          "Africa/Cairo",
	"ahuachapan":                     "America/El_Salvador",
	"cojutepeque":                    "America/El_Salvador",
	"nuevasansalvador":               "America/El_Salvador",
	"zacatecoluca":                   "America/El_Salvador",
	"sanfranciscogotera":             "America/El_Salvador",
	"sanvicente":                     "America/El_Salvador",
	"usulutan":                       "America/El_Salvador",
	"chalatenango":                   "America/El_Salvador",
	"sensuntepeque":                  "America/El_Salvador",
	"sonsonate":                      "America/El_Salvador",
	"sanmiguel":                      "America/El_Salvador",
	"sansalvador":                    "America/El_Salvador",
	"evinayong":                      "Africa/Malabo",
	"luba":                           "Africa/Malabo",
	"calatrava":                      "Africa/Malabo",
	"mongomo":                        "Africa/Malabo",
	"bata":                           "Africa/Malabo",
	"malabo":                         "Africa/Malabo",
	"tessenei":                       "Africa/Asmara",
	"agordat":                        "Africa/Asmara",
	"massawa":                        "Africa/Asmara",
	"keren":                          "Africa/Asmara",
	"adiugri":                        "Africa/Asmara",
	"assab":                          "Africa/Asmara",
	"asmara":                         "Africa/Asmara",
	"haapsalu":                       "Europe/Tallinn",
	"viljandi":                       "Europe/Tallinn",
	"kohtla-jarve":                   "Europe/Tallinn",
	"narva":                          "Europe/Tallinn",
	"tartu":                          "Europe/Tallinn",
	"parnu":                          "Europe/Tallinn",
	"tallinn":                        "Europe/Tallinn",
	"awasa":                          "Africa/Addis_Ababa",
	"gore":                           "Africa/Addis_Ababa",
	"debrebirhan":                    "Africa/Addis_Ababa",
	"bati":                           "Africa/Addis_Ababa",
	"adigrat":                        "Africa/Addis_Ababa",
	"aksum":                          "Africa/Addis_Ababa",
	"yirgaalem":                      "Africa/Addis_Ababa",
	"hosaina":                        "Africa/Addis_Ababa",
	"dila":                           "Africa/Addis_Ababa",
	"giyon":                          "Africa/Addis_Ababa",
	"hagerehiywet":                   "Africa/Addis_Ababa",
	"nekemte":                        "Africa/Addis_Ababa",
	"asela":                          "Africa/Addis_Ababa",
	"shashemene":                     "Africa/Addis_Ababa",
	"dembidolo":                      "Africa/Addis_Ababa",
	"gimbi":                          "Africa/Addis_Ababa",
	"asosa":                          "Africa/Addis_Ababa",
	"jijiga":                         "Africa/Addis_Ababa",
	"debremarkos":                    "Africa/Addis_Ababa",
	"dese":                           "Africa/Addis_Ababa",
	"sodo":                           "Africa/Addis_Ababa",
	"arbaminch":                      "Africa/Addis_Ababa",
	"harar":                          "Africa/Addis_Ababa",
	"goba":                           "Africa/Addis_Ababa",
	"jima":                           "Africa/Addis_Ababa",
	"nazret":                         "Africa/Addis_Ababa",
	"nagele":                         "Africa/Addis_Ababa",
	"gode":                           "Africa/Addis_Ababa",
	"dolobay":                        "Africa/Addis_Ababa",
	"bahirdar":                       "Africa/Addis_Ababa",
	"mekele":                         "Africa/Addis_Ababa",
	"diredawa":                       "Africa/Addis_Ababa",
	"gonder":                         "Africa/Addis_Ababa",
	"addisababa":                     "Africa/Addis_Ababa",
	"foxbay":                         "Atlantic/Stanley",
	"stanley":                        "Atlantic/Stanley",
	"klaksvik":                       "Atlantic/Faroe",
	"torshavn":                       "Atlantic/Faroe",
	"palikir":                        "Pacific/Pohnpei",
	"nandi":                          "Pacific/Fiji",
	"lautoka":                        "Pacific/Fiji",
	"labasa":                         "Pacific/Fiji",
	"suva":                           "Pacific/Fiji",
	"hameenlinna":                    "Europe/Helsinki",
	"kouvola":                        "Europe/Helsinki",
	"mikkeli":                        "Europe/Helsinki",
	"savonlinna":                     "Europe/Helsinki",
	"pori":                           "Europe/Helsinki",
	"sodankyla":                      "Europe/Helsinki",
	"jyvaskyla":                      "Europe/Helsinki",
	"kuopio":                         "Europe/Helsinki",
	"lappeenranta":                   "Europe/Helsinki",
	"porvoo":                         "Europe/Helsinki",
	"kemijarvi":                      "Europe/Helsinki",
	"kokkola":                        "Europe/Helsinki",
	"lahti":                          "Europe/Helsinki",
	"joensuu":                        "Europe/Helsinki",
	"turku":                          "Europe/Helsinki",
	"kemi":                           "Europe/Helsinki",
	"oulu":                           "Europe/Helsinki",
	"rovaniemi":                      "Europe/Helsinki",
	"vaasa":                          "Europe/Helsinki",
	"tampere":                        "Europe/Helsinki",
	"helsinki":                       "Europe/Helsinki",
	"annecy":                         "Europe/Paris",
	"roanne":                         "Europe/Paris",
	"roura":                          "America/Cayenne",
	"sinnamary":                      "America/Cayenne",
	"st.-brieuc":                     "Europe/Paris",
	"poitier":                        "Europe/Paris",
	"angers":                         "Europe/Paris",
	"biarritz":                       "Europe/Paris",
	"aix-en-provence":                "Europe/Paris",
	"perpignan":                      "Europe/Paris",
	"tarbes":                         "Europe/Paris",
	"clermont-ferrand":               "Europe/Paris",
	"melun":                          "Europe/Paris",
	"arras":                          "Europe/Paris",
	"besancon":                       "Europe/Paris",
	"saint-etienne":                  "Europe/Paris",
	"grenoble":                       "Europe/Paris",
	"fort-de-france":                 "America/Martinique",
	"saint-laurent-du-maroni":        "America/Cayenne",
	"iracoubo":                       "America/Cayenne",
	"cherbourg":                      "Europe/Paris",
	"caen":                           "Europe/Paris",
	"lorient":                        "Europe/Paris",
	"lemans":                         "Europe/Paris",
	"nantes":                         "Europe/Paris",
	"agen":                           "Europe/Paris",
	"ajaccio":                        "Europe/Paris",
	"bastia":                         "Europe/Paris",
	"toulon":                         "Europe/Paris",
	"beziers":                        "Europe/Paris",
	"montpellier":                    "Europe/Paris",
	"nimes":                          "Europe/Paris",
	"vichy":                          "Europe/Paris",
	"nevers":                         "Europe/Paris",
	"auxerre":                        "Europe/Paris",
	"dijon":                          "Europe/Paris",
	"bourges":                        "Europe/Paris",
	"tours":                          "Europe/Paris",
	"orleans":                        "Europe/Paris",
	"dieppe":                         "Europe/Paris",
	"rouen":                          "Europe/Paris",
	"versailles":                     "Europe/Paris",
	"brive":                          "Europe/Paris",
	"troyes":                         "Europe/Paris",
	"reims":                          "Europe/Paris",
	"calais":                         "America/New_York",
	"amiens":                         "Europe/Paris",
	"mulhouse":                       "Europe/Paris",
	"nancy":                          "Europe/Paris",
	"metz":                           "Europe/Paris",
	"pointe-a-pitre":                 "America/Guadeloupe",
	"basse-terre":                    "America/Guadeloupe",
	"st.-benoit":                     "Indian/Reunion",
	"dzaoudzi":                       "Indian/Mayotte",
	"rennes":                         "Europe/Paris",
	"nice":                           "Europe/Paris",
	"toulouse":                       "Europe/Paris",
	"limoges":                        "Europe/Paris",
	"lille":                          "Europe/Paris",
	"strasbourg":                     "Europe/Paris",
	"kourou":                         "America/Cayenne",
	"larochelle":                     "Europe/Paris",
	"bordeaux":                       "Europe/Paris",
	"marseille":                      "Europe/Paris",
	"lehavre":                        "Europe/Paris",
	"st.-denis":                      "Indian/Reunion",
	"lyon":                           "Europe/Paris",
	"cayenne":                        "America/Cayenne",
	"paris":                          "Europe/Paris",
	"papeete":                        "Pacific/Tahiti",
	"ebebiyin":                       "Africa/Malabo",
	"tchibanga":                      "Africa/Libreville",
	"mekambo":                        "Africa/Libreville",
	"makokou":                        "Africa/Libreville",
	"mitzik":                         "Africa/Libreville",
	"bitam":                          "Africa/Libreville",
	"lambarene":                      "Africa/Libreville",
	"bifoum":                         "Africa/Libreville",
	"ndende":                         "Africa/Libreville",
	"mouila":                         "Africa/Libreville",
	"omboue":                         "Africa/Libreville",
	"okandja":                        "Africa/Libreville",
	"koulamoutou":                    "Africa/Libreville",
	"oyem":                           "Africa/Libreville",
	"mayumba":                        "Africa/Libreville",
	"gamba":                          "Africa/Libreville",
	"franceville":                    "Africa/Libreville",
	"libreville":                     "Africa/Libreville",
	"port-gentil":                    "Africa/Libreville",
	"kutaisi":                        "Asia/Tbilisi",
	"tskhinvali":                     "Asia/Tbilisi",
	"poti":                           "Asia/Tbilisi",
	"rustavi":                        "Asia/Tbilisi",
	"batumi":                         "Asia/Tbilisi",
	"sukhumi":                        "Asia/Tbilisi",
	"tbilisi":                        "Asia/Tbilisi",
	"mainz":                          "Europe/Berlin",
	"schwerin":                       "Europe/Berlin",
	"bielefeld":                      "Europe/Berlin",
	"dortmund":                       "Europe/Berlin",
	"duisburg":                       "Europe/Berlin",
	"wuppertal":                      "Europe/Berlin",
	"essen":                          "Europe/Berlin",
	"karlsruhe":                      "Europe/Berlin",
	"heidelberg":                     "Europe/Berlin",
	"kassel":                         "Europe/Berlin",
	"oldenburg":                      "Europe/Berlin",
	"emden":                          "Europe/Berlin",
	"braunschweig":                   "Europe/Berlin",
	"erfurt":                         "Europe/Berlin",
	"coburg":                         "Europe/Berlin",
	"augsburg":                       "Europe/Berlin",
	"furth":                          "Europe/Berlin",
	"chemnitz":                       "Europe/Berlin",
	"bonn":                           "Europe/Berlin",
	"munster":                        "Europe/Berlin",
	"dusseldorf":                     "Europe/Berlin",
	"ulm":                            "Europe/Berlin",
	"mannheim":                       "Europe/Berlin",
	"freiburg":                       "Europe/Berlin",
	"giessen":                        "Europe/Berlin",
	"wiesbaden":                      "Europe/Berlin",
	"bremerhaven":                    "Europe/Berlin",
	"osnabruck":                      "Europe/Berlin",
	"hannover":                       "Europe/Berlin",
	"gottingen":                      "Europe/Berlin",
	"gera":                           "Europe/Berlin",
	"jena":                           "Europe/Berlin",
	"flensburg":                      "Europe/Berlin",
	"lubeck":                         "Europe/Berlin",
	"kiel":                           "Europe/Berlin",
	"koblenz":                        "Europe/Berlin",
	"saarbrucken":                    "Europe/Berlin",
	"regensburg":                     "Europe/Berlin",
	"rosenheim":                      "Europe/Berlin",
	"hof":                            "Europe/Berlin",
	"wurzburg":                       "Europe/Berlin",
	"ingolstadt":                     "Europe/Berlin",
	"cottbus":                        "Europe/Berlin",
	"potsdam":                        "Europe/Berlin",
	"magdeburg":                      "Europe/Berlin",
	"leipzig":                        "Europe/Berlin",
	"stralsund":                      "Europe/Berlin",
	"rostock":                        "Europe/Berlin",
	"stuttgart":                      "Europe/Berlin",
	"bremen":                         "Europe/Berlin",
	"nurnberg":                       "Europe/Berlin",
	"cologne":                        "Europe/Berlin",
	"dresden":                        "Europe/Berlin",
	"frankfurt":                      "Europe/Berlin",
	"hamburg":                        "Europe/Berlin",
	"munich":                         "Europe/Berlin",
	"berlin":                         "Europe/Berlin",
	"sunyani":                        "Africa/Accra",
	"tamale":                         "Africa/Accra",
	"yendi":                          "Africa/Accra",
	"bolgatanga":                     "Africa/Accra",
	"bawku":                          "Africa/Accra",
	"wa":                             "Africa/Accra",
	"obuasi":                         "Africa/Accra",
	"berekum":                        "Africa/Accra",
	"winneba":                        "Africa/Accra",
	"capecoast":                      "Africa/Accra",
	"nkawkaw":                        "Africa/Accra",
	"koforidua":                      "Africa/Accra",
	"tema":                           "Africa/Accra",
	"ho":                             "Africa/Accra",
	"kumasi":                         "Africa/Accra",
	"sekondi":                        "Africa/Accra",
	"accra":                          "Africa/Accra",
	"gibraltar":                      "Europe/Gibraltar",
	"lamia":                          "Europe/Athens",
	"polygyros":                      "Europe/Athens",
	"komatini":                       "Europe/Athens",
	"piraievs":                       "Europe/Athens",
	"volos":                          "Europe/Athens",
	"hania":                          "Europe/Athens",
	"kavala":                         "Europe/Athens",
	"alexandroupoli":                 "Europe/Athens",
	"kerkira":                        "Europe/Athens",
	"tripoli":                        "Africa/Tripoli",
	"sparti":                         "Europe/Athens",
	"agrinio":                        "Europe/Athens",
	"pirgos":                         "Europe/Athens",
	"larissa":                        "Europe/Athens",
	"ioanina":                        "Europe/Athens",
	"mitilini":                       "Europe/Athens",
	"hios":                           "Europe/Athens",
	"chalkida":                       "Europe/Athens",
	"sitia":                          "Europe/Athens",
	"katerini":                       "Europe/Athens",
	"seres":                          "Europe/Athens",
	"xanthi":                         "Europe/Athens",
	"ermoupoli":                      "Europe/Athens",
	"kos":                            "Europe/Athens",
	"rodos":                          "Europe/Athens",
	"patra":                          "Europe/Athens",
	"kalamata":                       "Europe/Athens",
	"iraklio":                        "Europe/Athens",
	"thessaloniki":                   "Europe/Athens",
	"athens":                         "America/New_York",
	"qasigiannguit":                  "America/Godthab",
	"kullorsuaq":                     "America/Godthab",
	"tasiusaq":                       "America/Godthab",
	"kulusuk":                        "America/Godthab",
	"paamiut":                        "America/Godthab",
	"ittoqqortoormiit":               "America/Danmarkshavn",
	"timmiarmiut":                    "America/Godthab",
	"qaqortoq":                       "America/Godthab",
	"kangerlussuaq":                  "America/Godthab",
	"nord":                           "America/Godthab",
	"qeqertasuaq":                    "America/Godthab",
	"nuussuaq":                       "America/Godthab",
	"ilulissat":                      "America/Godthab",
	"tasiilaq":                       "America/Godthab",
	"savissivik":                     "America/Thule",
	"kangersuatsiaq":                 "America/Godthab",
	"uummannaq":                      "America/Godthab",
	"narsarsuaq":                     "America/Godthab",
	"sisimiut":                       "America/Godthab",
	"upernavik":                      "America/Godthab",
	"qaanaaq":                        "America/Thule",
	"nuuk":                           "America/Godthab",
	"saintgeorge's":                  "America/Grenada",
	"agana":                          "Pacific/Guam",
	"salama":                         "America/Guatemala",
	"retalhuleu":                     "America/Guatemala",
	"sanmarcos":                      "America/Chicago",
	"chimaltenango":                  "America/Guatemala",
	"antiguaguatemala":               "America/Guatemala",
	"solola":                         "America/Guatemala",
	"totonicapan":                    "America/Guatemala",
	"elprogreso":                     "America/Guatemala",
	"cuilapa":                        "America/Guatemala",
	"chiquimula":                     "America/Guatemala",
	"jalapa":                         "America/Guatemala",
	"zacapa":                         "America/Guatemala",
	"santacruzdelquiche":             "America/Guatemala",
	"coban":                          "America/Guatemala",
	"livingston":                     "America/Guatemala",
	"jutiapa":                        "America/Guatemala",
	"huehuetenango":                  "America/Guatemala",
	"flores":                         "America/Guatemala",
	"lalibertad":                     "America/Guatemala",
	"escuintla":                      "America/Mexico_City",
	"mazatenango":                    "America/Guatemala",
	"puertobarrios":                  "America/Guatemala",
	"quetzaltenango":                 "America/Guatemala",
	"guatemala":                      "America/Guatemala",
	"mali":                           "Africa/Conakry",
	"tongue":                         "Africa/Conakry",
	"kouroussa":                      "Africa/Conakry",
	"pita":                           "Africa/Conakry",
	"dalaba":                         "Africa/Conakry",
	"boffa":                          "Africa/Conakry",
	"koundara":                       "Africa/Conakry",
	"gaoual":                         "Africa/Conakry",
	"telimele":                       "Africa/Conakry",
	"forecariah":                     "Africa/Conakry",
	"beyla":                          "Africa/Conakry",
	"gueckedou":                      "Africa/Conakry",
	"dinguiraye":                     "Africa/Conakry",
	"dabola":                         "Africa/Conakry",
	"kerouane":                       "Africa/Conakry",
	"siguiri":                        "Africa/Conakry",
	"mamou":                          "Africa/Conakry",
	"kamsar":                         "Africa/Conakry",
	"fria":                           "Africa/Conakry",
	"macenta":                        "Africa/Conakry",
	"yomou":                          "Africa/Conakry",
	"faranah":                        "Africa/Conakry",
	"kissidougou":                    "Africa/Conakry",
	"labe":                           "Africa/Conakry",
	"boke":                           "Africa/Conakry",
	"kindia":                         "Africa/Conakry",
	"kankan":                         "Africa/Conakry",
	"nzerekore":                      "Africa/Conakry",
	"conakry":                        "Africa/Conakry",
	"cacheu":                         "Africa/Bissau",
	"farim":                          "Africa/Bissau",
	"fulacunda":                      "Africa/Bissau",
	"gabu":                           "Africa/Bissau",
	"catio":                          "Africa/Bissau",
	"bolama":                         "Africa/Bissau",
	"bafata":                         "Africa/Bissau",
	"bissau":                         "Africa/Bissau",
	"corriverton":                    "America/Guyana",
	"ituni":                          "America/Guyana",
	"lethem":                         "America/Guyana",
	"kumaka":                         "America/Guyana",
	"bartica":                        "America/Guyana",
	"annaregina":                     "America/Guyana",
	"linden":                         "America/Guyana",
	"mabaruma":                       "America/Guyana",
	"newamsterdam":                   "America/Guyana",
	"jeremie":                        "America/Port-au-Prince",
	"port-de-paix":                   "America/Port-au-Prince",
	"hinche":                         "America/Port-au-Prince",
	"fort-liberte":                   "America/Port-au-Prince",
	"jacmel":                         "America/Port-au-Prince",
	"lescayes":                       "America/Port-au-Prince",
	"gonaives":                       "America/Port-au-Prince",
	"cap-haitien":                    "America/Port-au-Prince",
	"port-au-prince":                 "America/Port-au-Prince",
	"yoro":                           "America/Tegucigalpa",
	"laesperanza":                    "America/Tegucigalpa",
	"gracias":                        "America/Tegucigalpa",
	"nuevaocotepeque":                "America/Tegucigalpa",
	"yuscaran":                       "America/Tegucigalpa",
	"roatan":                         "America/Tegucigalpa",
	"nacaome":                        "America/Tegucigalpa",
	"santarosadecopan":               "America/Tegucigalpa",
	"trujillo":                       "America/Caracas",
	"bruslaguna":                     "America/Tegucigalpa",
	"puertolempira":                  "America/Tegucigalpa",
	"juticalpa":                      "America/Tegucigalpa",
	"comayagua":                      "America/Tegucigalpa",
	"choluteca":                      "America/Tegucigalpa",
	"laceiba":                        "America/Tegucigalpa",
	"sanpedrosula":                   "America/Tegucigalpa",
	"tegucigalpa":                    "America/Tegucigalpa",
	"hongkong":                       "Asia/Hong_Kong",
	"veszprem":                       "Europe/Budapest",
	"zalaegerszeg":                   "Europe/Budapest",
	"tatabanya":                      "Europe/Budapest",
	"szekszard":                      "Europe/Budapest",
	"salgotarjan":                    "Europe/Budapest",
	"bekescsaba":                     "Europe/Budapest",
	"eger":                           "Europe/Budapest",
	"szombathely":                    "Europe/Budapest",
	"kecskemet":                      "Europe/Budapest",
	"szekesfehervar":                 "Europe/Budapest",
	"nyiregyhaza":                    "Europe/Budapest",
	"pecs":                           "Europe/Budapest",
	"gyor":                           "Europe/Budapest",
	"kaposvar":                       "Europe/Budapest",
	"vac":                            "Europe/Budapest",
	"miskolc":                        "Europe/Budapest",
	"szeged":                         "Europe/Budapest",
	"debrecen":                       "Europe/Budapest",
	"szolnok":                        "Europe/Budapest",
	"budapest":                       "Europe/Budapest",
	"borgarnes":                      "Atlantic/Reykjavik",
	"egilsstadir":                    "Atlantic/Reykjavik",
	"saudarkrokur":                   "Atlantic/Reykjavik",
	"selfoss":                        "Atlantic/Reykjavik",
	"hofn":                           "Atlantic/Reykjavik",
	"isafjordur":                     "Atlantic/Reykjavik",
	"akureyi":                        "Atlantic/Reykjavik",
	"keflavik":                       "Atlantic/Reykjavik",
	"reykjavik":                      "Atlantic/Reykjavik",
	"panaji":                         "Asia/Kolkata",
	"simla":                          "Asia/Kolkata",
	"gurgaon":                        "Asia/Kolkata",
	"sonipat":                        "Asia/Kolkata",
	"rohtak":                         "Asia/Kolkata",
	"hisar":                          "Asia/Kolkata",
	"bhiwani":                        "Asia/Kolkata",
	"ambala":                         "Asia/Kolkata",
	"sopur":                          "Asia/Kolkata",
	"silvassa":                       "Asia/Kolkata",
	"kalyan":                         "Asia/Kolkata",
	"bhusawal":                       "Asia/Kolkata",
	"jorhat":                         "Asia/Kolkata",
	"hoshiarpur":                     "Asia/Kolkata",
	"ajmer":                          "Asia/Kolkata",
	"hathras":                        "Asia/Kolkata",
	"sitapur":                        "Asia/Kolkata",
	"pilibhit":                       "Asia/Kolkata",
	"budaun":                         "Asia/Kolkata",
	"firozabad":                      "Asia/Kolkata",
	"mathura":                        "Asia/Kolkata",
	"bulandshahr":                    "Asia/Kolkata",
	"hapur":                          "Asia/Kolkata",
	"muzaffarnagar":                  "Asia/Kolkata",
	"gangtok":                        "Asia/Kolkata",
	"diu":                            "Asia/Kolkata",
	"pathankot":                      "Asia/Kolkata",
	"sirsa":                          "Asia/Kolkata",
	"panipat":                        "Asia/Kolkata",
	"karnal":                         "Asia/Kolkata",
	"baramula":                       "Asia/Kolkata",
	"proddatur":                      "Asia/Kolkata",
	"nandyal":                        "Asia/Kolkata",
	"hindupur":                       "Asia/Kolkata",
	"tirupati":                       "Asia/Kolkata",
	"ongole":                         "Asia/Kolkata",
	"vizianagaram":                   "Asia/Kolkata",
	"rajahmundry":                    "Asia/Kolkata",
	"machilipatnam":                  "Asia/Kolkata",
	"khammam":                        "Asia/Kolkata",
	"chirala":                        "Asia/Kolkata",
	"karimnagar":                     "Asia/Kolkata",
	"nizamabad":                      "Asia/Kolkata",
	"kollam":                         "Asia/Kolkata",
	"alappuzha":                      "Asia/Kolkata",
	"puri":                           "Asia/Kolkata",
	"sambalpur":                      "Asia/Kolkata",
	"raurkela":                       "Asia/Kolkata",
	"kavaratti":                      "Asia/Kolkata",
	"mandya":                         "Asia/Kolkata",
	"kolar":                          "Asia/Kolkata",
	"shimoga":                        "Asia/Kolkata",
	"raichur":                        "Asia/Kolkata",
	"hospet":                         "Asia/Kolkata",
	"bidar":                          "Asia/Kolkata",
	"sangli":                         "Asia/Kolkata",
	"parbhani":                       "Asia/Kolkata",
	"malegaon":                       "Asia/Kolkata",
	"portblair":                      "Asia/Kolkata",
	"tezpur":                         "Asia/Kolkata",
	"silchar":                        "Asia/Kolkata",
	"kohima":                         "Asia/Kolkata",
	"shillong":                       "Asia/Kolkata",
	"abohar":                         "Asia/Kolkata",
	"patiala":                        "Asia/Kolkata",
	"bhilwara":                       "Asia/Kolkata",
	"pali":                           "Asia/Kolkata",
	"tonk":                           "Asia/Kolkata",
	"sikar":                          "Asia/Kolkata",
	"bikaner":                        "Asia/Kolkata",
	"bharatpur":                      "Asia/Kolkata",
	"alwar":                          "Asia/Kolkata",
	"fatehpur":                       "Asia/Kolkata",
	"faizabad":                       "Asia/Kolkata",
	"bahraich":                       "Asia/Kolkata",
	"mirzapur":                       "Asia/Kolkata",
	"jhansi":                         "Asia/Kolkata",
	"shahjahanpur":                   "Asia/Kolkata",
	"rampur":                         "Asia/Kolkata",
	"bareilly":                       "Asia/Kolkata",
	"etawah":                         "Asia/Kolkata",
	"dehradun":                       "Asia/Kolkata",
	"haora":                          "Asia/Kolkata",
	"alipurduar":                     "Asia/Kolkata",
	"haldia":                         "Asia/Kolkata",
	"bhatpara":                       "Asia/Kolkata",
	"medinipur":                      "Asia/Kolkata",
	"siliguri":                       "Asia/Kolkata",
	"purnia":                         "Asia/Kolkata",
	"muzaffarpur":                    "Asia/Kolkata",
	"aurangabad":                     "Asia/Kolkata",
	"bilaspur":                       "Asia/Kolkata",
	"burhanpur":                      "Asia/Kolkata",
	"ujjain":                         "Asia/Kolkata",
	"ratlam":                         "Asia/Kolkata",
	"sagar":                          "Asia/Kolkata",
	"vellore":                        "Asia/Kolkata",
	"tiruvannamalai":                 "Asia/Kolkata",
	"rajapalaiyam":                   "Asia/Kolkata",
	"cuddalore":                      "Asia/Kolkata",
	"karur":                          "Asia/Kolkata",
	"kanchipuram":                    "Asia/Kolkata",
	"tirunelveli":                    "Asia/Kolkata",
	"nagercoil":                      "Asia/Kolkata",
	"thanjavur":                      "Asia/Kolkata",
	"kumbakonam":                     "Asia/Kolkata",
	"valparai":                       "Asia/Kolkata",
	"tiruppur":                       "Asia/Kolkata",
	"daman":                          "Asia/Kolkata",
	"navsari":                        "Asia/Kolkata",
	"bhuj":                           "Asia/Kolkata",
	"bhavnagar":                      "Asia/Kolkata",
	"gandhinagar":                    "Asia/Kolkata",
	"itanagar":                       "Asia/Kolkata",
	"aizawl":                         "Asia/Kolkata",
	"agartala":                       "Asia/Kolkata",
	"kakinada":                       "Asia/Kolkata",
	"warangal":                       "Asia/Kolkata",
	"brahmapur":                      "Asia/Kolkata",
	"bijapur":                        "Asia/Kolkata",
	"bhiwandi":                       "Asia/Kolkata",
	"latur":                          "Asia/Kolkata",
	"ahmednagar":                     "Asia/Kolkata",
	"chandrapur":                     "Asia/Kolkata",
	"amravati":                       "Asia/Kolkata",
	"dhule":                          "Asia/Kolkata",
	"dibrugarh":                      "Asia/Kolkata",
	"imphal":                         "Asia/Kolkata",
	"udaipur":                        "Asia/Kolkata",
	"gorakhpur":                      "Asia/Kolkata",
	"barddhaman":                     "Asia/Kolkata",
	"krishnanagar":                   "Asia/Kolkata",
	"gaya":                           "Africa/Niamey",
	"porbandar":                      "Asia/Kolkata",
	"nellore":                        "Asia/Kolkata",
	"kurnool":                        "Asia/Kolkata",
	"guntur":                         "Asia/Kolkata",
	"tumkur":                         "Asia/Kolkata",
	"davangere":                      "Asia/Kolkata",
	"bellary":                        "Asia/Kolkata",
	"belgaum":                        "Asia/Kolkata",
	"tuticorin":                      "Asia/Kolkata",
	"dindigul":                       "Asia/Kolkata",
	"chandigarh":                     "Asia/Kolkata",
	"jammu":                          "Asia/Kolkata",
	"sholapur":                       "Asia/Kolkata",
	"nasik":                          "Asia/Kolkata",
	"dispur":                         "Asia/Kolkata",
	"jullundur":                      "Asia/Kolkata",
	"allahabad":                      "Asia/Kolkata",
	"moradabad":                      "Asia/Kolkata",
	"ghaziabad":                      "Asia/Kolkata",
	"agra":                           "Asia/Kolkata",
	"aligarh":                        "Asia/Kolkata",
	"meerut":                         "Asia/Kolkata",
	"dhanbad":                        "Asia/Kolkata",
	"gwalior":                        "Asia/Kolkata",
	"vadodara":                       "Asia/Kolkata",
	"rajkot":                         "Asia/Kolkata",
	"faridabad":                      "Asia/Kolkata",
	"srinagar":                       "Asia/Kolkata",
	"vijayawada":                     "Asia/Kolkata",
	"thiruvananthapuram":             "Asia/Kolkata",
	"kochi":                          "Asia/Tokyo",
	"cuttack":                        "Asia/Kolkata",
	"hubli":                          "Asia/Kolkata",
	"mangalore":                      "Asia/Kolkata",
	"mysore":                         "Asia/Kolkata",
	"gulbarga":                       "Asia/Kolkata",
	"kolhapur":                       "Asia/Kolkata",
	"nanded":                         "Asia/Kolkata",
	"akola":                          "Asia/Kolkata",
	"guwahati":                       "Asia/Kolkata",
	"ludhiana":                       "Asia/Kolkata",
	"kota":                           "Asia/Kolkata",
	"jodhpur":                        "Asia/Kolkata",
	"lucknow":                        "Asia/Kolkata",
	"saharanpur":                     "Asia/Kolkata",
	"ranchi":                         "Asia/Kolkata",
	"bhagalpur":                      "Asia/Kolkata",
	"raipur":                         "Asia/Kolkata",
	"jabalpur":                       "Asia/Kolkata",
	"indore":                         "Asia/Kolkata",
	"pondicherry":                    "Asia/Kolkata",
	"salem":                          "America/Los_Angeles",
	"tiruchirappalli":                "Asia/Kolkata",
	"kozhikode":                      "Asia/Kolkata",
	"bhubaneshwar":                   "Asia/Kolkata",
	"jamshedpur":                     "Asia/Kolkata",
	"vishakhapatnam":                 "Asia/Kolkata",
	"amritsar":                       "Asia/Kolkata",
	"varanasi":                       "Asia/Kolkata",
	"asansol":                        "Asia/Kolkata",
	"bhilai":                         "Asia/Kolkata",
	"bhopal":                         "Asia/Kolkata",
	"madurai":                        "Asia/Kolkata",
	"coimbatore":                     "Asia/Kolkata",
	"delhi":                          "Asia/Kolkata",
	"hyderabad":                      "Asia/Karachi",
	"pune":                           "Asia/Kolkata",
	"nagpur":                         "Asia/Kolkata",
	"jaipur":                         "Asia/Kolkata",
	"kanpur":                         "Asia/Kolkata",
	"patna":                          "Asia/Kolkata",
	"chennai":                        "Asia/Kolkata",
	"ahmedabad":                      "Asia/Kolkata",
	"surat":                          "Asia/Kolkata",
	"newdelhi":                       "Asia/Kolkata",
	"bangalore":                      "Asia/Kolkata",
	"mumbai":                         "Asia/Kolkata",
	"kolkata":                        "Asia/Kolkata",
	"binjai":                         "Asia/Jakarta",
	"padangsidempuan":                "Asia/Jakarta",
	"tarutung":                       "Asia/Jakarta",
	"tebingtinggi":                   "Asia/Jakarta",
	"tidore":                         "Asia/Jayapura",
	"bukittinggi":                    "Asia/Jakarta",
	"sawahlunto":                     "Asia/Jakarta",
	"padangpanjang":                  "Asia/Jakarta",
	"amahai":                         "Asia/Jayapura",
	"mataram":                        "Asia/Makassar",
	"praya":                          "Asia/Makassar",
	"baubau":                         "Asia/Makassar",
	"luwuk":                          "Asia/Makassar",
	"poso":                           "Asia/Makassar",
	"biak":                           "Asia/Jayapura",
	"timika":                         "Asia/Jayapura",
	"langsa":                         "Asia/Jakarta",
	"indramayu":                      "Asia/Jakarta",
	"sukabumi":                       "Asia/Jakarta",
	"cilacap":                        "Asia/Jakarta",
	"pati":                           "Asia/Jakarta",
	"pakalongan":                     "Asia/Jakarta",
	"tegal":                          "Asia/Jakarta",
	"salatiga":                       "Asia/Jakarta",
	"magelang":                       "Asia/Jakarta",
	"serang":                         "Asia/Jakarta",
	"bekasi":                         "Asia/Jakarta",
	"singkawang":                     "Asia/Pontianak",
	"tanjungkarang-telubketung":      "Asia/Jakarta",
	"perabumulih":                    "Asia/Jakarta",
	"kuta":                           "Asia/Makassar",
	"singaraja":                      "Asia/Makassar",
	"sumenep":                        "Asia/Jakarta",
	"banyuwangi":                     "Asia/Jakarta",
	"tuban":                          "Asia/Jakarta",
	"probolinggo":                    "Asia/Jakarta",
	"pasuruan":                       "Asia/Jakarta",
	"mojokerto":                      "Asia/Jakarta",
	"madiun":                         "Asia/Jakarta",
	"kediri":                         "Asia/Jakarta",
	"blitar":                         "Asia/Jakarta",
	"waingapu":                       "Asia/Makassar",
	"maumere":                        "Asia/Makassar",
	"ende":                           "Asia/Makassar",
	"makale":                         "Asia/Makassar",
	"palopo":                         "Asia/Makassar",
	"watampone":                      "Asia/Makassar",
	"pinrang":                        "Asia/Makassar",
	"majene":                         "Asia/Makassar",
	"tanjungpinang":                  "Asia/Jakarta",
	"telukbutun":                     "Asia/Jakarta",
	"sungaipenuh":                    "Asia/Jakarta",
	"sampit":                         "Asia/Pontianak",
	"kualakapuas":                    "Asia/Pontianak",
	"palangkaraya":                   "Asia/Pontianak",
	"bontang":                        "Asia/Makassar",
	"denpasar":                       "Asia/Makassar",
	"sorong":                         "Asia/Jayapura",
	"sibolga":                        "Asia/Jakarta",
	"pematangsiantar":                "Asia/Jakarta",
	"pekanbaru":                      "Asia/Jakarta",
	"manado":                         "Asia/Makassar",
	"yogyakarta":                     "Asia/Jakarta",
	"kendari":                        "Asia/Makassar",
	"palu":                           "Asia/Makassar",
	"nabire":                         "Asia/Jayapura",
	"merauke":                        "Asia/Jayapura",
	"lhokseumawe":                    "Asia/Jakarta",
	"samarinda":                      "Asia/Makassar",
	"cirebon":                        "Asia/Jakarta",
	"tasikmalaya":                    "Asia/Jakarta",
	"bogor":                          "Asia/Jakarta",
	"bengkulu":                       "Asia/Jakarta",
	"pontianak":                      "Asia/Pontianak",
	"kotabumi":                       "Asia/Jakarta",
	"lahat":                          "Asia/Jakarta",
	"pangkalpinang":                  "Asia/Jakarta",
	"jember":                         "Asia/Jakarta",
	"martapura":                      "Asia/Makassar",
	"ruteng":                         "Asia/Makassar",
	"jambi":                          "Asia/Jakarta",
	"manokwari":                      "Asia/Jayapura",
	"ternate":                        "Asia/Jayapura",
	"ambon":                          "Asia/Jayapura",
	"raba":                           "Asia/Makassar",
	"jayapura":                       "Asia/Jayapura",
	"bandaaceh":                      "Asia/Jakarta",
	"balikpapan":                     "Asia/Makassar",
	"surakarta":                      "Asia/Jakarta",
	"bandarlampung":                  "Asia/Jakarta",
	"tanjungpandan":                  "Asia/Jakarta",
	"malang":                         "Asia/Jakarta",
	"kupang":                         "Asia/Makassar",
	"parepare":                       "Asia/Makassar",
	"gorontalo":                      "Asia/Makassar",
	"padang":                         "Asia/Jakarta",
	"tarakan":                        "Asia/Makassar",
	"semarang":                       "Asia/Jakarta",
	"palembang":                      "Asia/Jakarta",
	"bandjarmasin":                   "Asia/Makassar",
	"ujungpandang":                   "Asia/Makassar",
	"medan":                          "Asia/Jakarta",
	"bandung":                        "Asia/Jakarta",
	"surabaya":                       "Asia/Jakarta",
	"jakarta":                        "Asia/Jakarta",
	"yasuj":                          "Asia/Tehran",
	"sharekord":                      "Asia/Tehran",
	"marvdasht":                      "Asia/Tehran",
	"shahrud":                        "Asia/Tehran",
	"varamin":                        "Asia/Tehran",
	"masjedsoleyman":                 "Asia/Tehran",
	"borujerd":                       "Asia/Tehran",
	"malayer":                        "Asia/Tehran",
	"zanjan":                         "Asia/Tehran",
	"ahar":                           "Asia/Tehran",
	"sanandaj":                       "Asia/Tehran",
	"neyshabur":                      "Asia/Tehran",
	"bojnurd":                        "Asia/Tehran",
	"sirjan":                         "Asia/Tehran",
	"qomsheh":                        "Asia/Tehran",
	"kashan":                         "Asia/Tehran",
	"khomeinishahr":                  "Asia/Tehran",
	"fasa":                           "Asia/Tehran",
	"gonbad-ekavus":                  "Asia/Tehran",
	"gorgan":                         "Asia/Tehran",
	"amol":                           "Asia/Tehran",
	"sari":                           "Asia/Tehran",
	"semnan":                         "Asia/Tehran",
	"karaj":                          "Asia/Tehran",
	"behbehan":                       "Asia/Tehran",
	"dezful":                         "Asia/Tehran",
	"khorramabad":                    "Asia/Tehran",
	"ilam":                           "Asia/Kathmandu",
	"saveh":                          "Asia/Tehran",
	"mahabad":                        "Asia/Tehran",
	"khvoy":                          "Asia/Tehran",
	"maragheh":                       "Asia/Tehran",
	"qasr-eshirin":                   "Asia/Tehran",
	"bijar":                          "Asia/Tehran",
	"yazdan":                         "Asia/Tehran",
	"torbat-ejam":                    "Asia/Tehran",
	"quchan":                         "Asia/Tehran",
	"chabahar":                       "Asia/Tehran",
	"kashmar":                        "Asia/Tehran",
	"bam":                            "Asia/Tehran",
	"kerman":                         "Asia/Tehran",
	"bandar-ebushehr":                "Asia/Tehran",
	"abadan":                         "Asia/Tehran",
	"ardabil":                        "Asia/Tehran",
	"qom":                            "Asia/Tehran",
	"qazvin":                         "Asia/Tehran",
	"kermanshah":                     "Asia/Tehran",
	"rasht":                          "Asia/Tehran",
	"birjand":                        "Asia/Tehran",
	"sabzewar":                       "Asia/Tehran",
	"zabol":                          "Asia/Tehran",
	"zahedan":                        "Asia/Tehran",
	"yazd":                           "Asia/Tehran",
	"ahvaz":                          "Asia/Tehran",
	"bandar-e-abbas":                 "Asia/Tehran",
	"hamadan":                        "Asia/Tehran",
	"tabriz":                         "Asia/Tehran",
	"isfahan":                        "Asia/Tehran",
	"shiraz":                         "Asia/Tehran",
	"mashhad":                        "Asia/Tehran",
	"tehran":                         "Asia/Tehran",
	"dahuk":                          "Asia/Baghdad",
	"samarra":                        "Asia/Baghdad",
	"azaubayr":                       "Asia/Baghdad",
	"addiwaniyah":                    "Asia/Baghdad",
	"ashshatrah":                     "Asia/Baghdad",
	"mandali":                        "Asia/Baghdad",
	"arramadi":                       "Asia/Baghdad",
	"almusayyib":                     "Asia/Baghdad",
	"zakho":                          "Asia/Baghdad",
	"tallafar":                       "Asia/Baghdad",
	"tikrit":                         "Asia/Baghdad",
	"karbala":                        "Asia/Baghdad",
	"assamawah":                      "Asia/Baghdad",
	"annasiriyah":                    "Asia/Baghdad",
	"alamarah":                       "Asia/Baghdad",
	"alkut":                          "Asia/Baghdad",
	"assulaymaniyah":                 "Asia/Baghdad",
	"baqubah":                        "Asia/Baghdad",
	"arrutbah":                       "Asia/Baghdad",
	"alfallujah":                     "Asia/Baghdad",
	"alhillah":                       "Asia/Riyadh",
	"irbil":                          "Asia/Baghdad",
	"kirkuk":                         "Asia/Baghdad",
	"mosul":                          "Asia/Baghdad",
	"annajaf":                        "Asia/Baghdad",
	"basra":                          "Asia/Baghdad",
	"baghdad":                        "Asia/Baghdad",
	"roscomain":                      "Europe/Dublin",
	"muineachan":                     "Europe/Dublin",
	"shannon":                        "Europe/Dublin",
	"waterford":                      "Europe/Dublin",
	"tralee":                         "Europe/Dublin",
	"donegal":                        "Europe/Dublin",
	"drogheda":                       "Europe/Dublin",
	"dundalk":                        "Europe/Dublin",
	"galway":                         "Europe/Dublin",
	"kilkenny":                       "Europe/Dublin",
	"killarney":                      "Europe/Dublin",
	"sligo":                          "Europe/Dublin",
	"cork":                           "Europe/Dublin",
	"limerick":                       "Europe/Dublin",
	"dublin":                         "America/New_York",
	"douglas":                        "America/Phoenix",
	"ramla":                          "Asia/Jerusalem",
	"beersheva":                      "Asia/Jerusalem",
	"haifa":                          "Asia/Jerusalem",
	"nazareth":                       "Asia/Jerusalem",
	"jerusalem":                      "Asia/Jerusalem",
	"telaviv-yafo":                   "Asia/Jerusalem",
	"potenza":                        "Europe/Rome",
	"campobasso":                     "Europe/Rome",
	"aosta":                          "Europe/Rome",
	"modena":                         "Europe/Rome",
	"crotone":                        "Europe/Rome",
	"vibovalentia":                   "Europe/Rome",
	"reggiodicalabria":               "Europe/Rome",
	"caserta":                        "Europe/Rome",
	"barletta":                       "Europe/Rome",
	"ragusa":                         "Europe/Rome",
	"asti":                           "Europe/Rome",
	"novara":                         "Europe/Rome",
	"como":                           "Europe/Rome",
	"udine":                          "Europe/Rome",
	"treviso":                        "Europe/Rome",
	"parma":                          "Europe/Rome",
	"ravenna":                        "Europe/Rome",
	"ferrara":                        "Europe/Rome",
	"bologna":                        "Europe/Rome",
	"olbia":                          "Europe/Rome",
	"cagliari":                       "Europe/Rome",
	"pisa":                           "Europe/Rome",
	"livorno":                        "Europe/Rome",
	"siena":                          "Europe/Rome",
	"arezzo":                         "Europe/Rome",
	"catanzaro":                      "Europe/Rome",
	"salerno":                        "Europe/Rome",
	"benevento":                      "Europe/Rome",
	"bari":                           "Europe/Rome",
	"foggia":                         "Europe/Rome",
	"lecce":                          "Europe/Rome",
	"brindisi":                       "Europe/Rome",
	"taranto":                        "Europe/Rome",
	"messina":                        "Europe/Rome",
	"marsala":                        "Europe/Rome",
	"siracusa":                       "Europe/Rome",
	"pescara":                        "Europe/Rome",
	"l'aquila":                       "Europe/Rome",
	"civitavecchia":                  "Europe/Rome",
	"ancona":                         "Europe/Rome",
	"perugia":                        "Europe/Rome",
	"bergamo":                        "Europe/Rome",
	"trieste":                        "Europe/Rome",
	"bolzano":                        "Europe/Rome",
	"trento":                         "Europe/Rome",
	"verona":                         "Europe/Rome",
	"sassari":                        "Europe/Rome",
	"turin":                          "Europe/Rome",
	"genoa":                          "Europe/Rome",
	"florence":                       "America/New_York",
	"catania":                        "Europe/Rome",
	"venice":                         "Europe/Rome",
	"palermo":                        "Europe/Rome",
	"naples":                         "America/New_York",
	"milan":                          "Europe/Rome",
	"rome":                           "Europe/Rome",
	"touba":                          "Africa/Abidjan",
	"bouafle":                        "Africa/Abidjan",
	"divo":                           "Africa/Abidjan",
	"toumodi":                        "Africa/Abidjan",
	"aboisso":                        "Africa/Abidjan",
	"ferkessedougou":                 "Africa/Abidjan",
	"odienne":                        "Africa/Abidjan",
	"man":                            "Africa/Abidjan",
	"seguela":                        "Africa/Abidjan",
	"gagnoa":                         "Africa/Abidjan",
	"soubre":                         "Africa/Abidjan",
	"san-pedro":                      "Africa/Abidjan",
	"sassandra":                      "Africa/Abidjan",
	"bondoukou":                      "Africa/Abidjan",
	"agboville":                      "Africa/Abidjan",
	"dimbokro":                       "Africa/Abidjan",
	"grandbassam":                    "Africa/Abidjan",
	"dabou":                          "Africa/Abidjan",
	"guiglo":                         "Africa/Abidjan",
	"abengourou":                     "Africa/Abidjan",
	"korhogo":                        "Africa/Abidjan",
	"daloa":                          "Africa/Abidjan",
	"bouake":                         "Africa/Abidjan",
	"yamoussoukro":                   "Africa/Abidjan",
	"abidjan":                        "Africa/Abidjan",
	"lucea":                          "America/Jamaica",
	"mandeville":                     "America/Jamaica",
	"blackriver":                     "America/Jamaica",
	"falmouth":                       "America/Jamaica",
	"savannalamar":                   "America/Jamaica",
	"portantonio":                    "America/Jamaica",
	"st.annsbay":                     "America/Jamaica",
	"portmaria":                      "America/Jamaica",
	"halfwaytree":                    "America/Jamaica",
	"portmorant":                     "America/Jamaica",
	"maypen":                         "America/Jamaica",
	"spanishtown":                    "America/Jamaica",
	"montegobay":                     "America/Jamaica",
	"okayama":                        "Asia/Tokyo",
	"shimonoseki":                    "Asia/Tokyo",
	"kanoya":                         "Asia/Tokyo",
	"takamatsu":                      "Asia/Tokyo",
	"tokushima":                      "Asia/Tokyo",
	"toyama":                         "Asia/Tokyo",
	"takaoka":                        "Asia/Tokyo",
	"otsu":                           "Asia/Tokyo",
	"maebashi":                       "Asia/Tokyo",
	"kawasaki":                       "Asia/Tokyo",
	"kawagoe":                        "Asia/Tokyo",
	"utsunomiya":                     "Asia/Tokyo",
	"hachioji":                       "Asia/Tokyo",
	"koriyama":                       "Asia/Tokyo",
	"kure":                           "Asia/Tokyo",
	"matsue":                         "Asia/Tokyo",
	"tottori":                        "Asia/Tokyo",
	"sasebo":                         "Asia/Tokyo",
	"kitakyushu":                     "Asia/Tokyo",
	"kumamoto":                       "Asia/Tokyo",
	"oita":                           "Asia/Tokyo",
	"gifu":                           "Asia/Tokyo",
	"tsu":                            "Asia/Tokyo",
	"matsumoto":                      "Asia/Tokyo",
	"shizuoka":                       "Asia/Tokyo",
	"hamamatsu":                      "Asia/Tokyo",
	"obihiro":                        "Asia/Tokyo",
	"tomakomai":                      "Asia/Tokyo",
	"kitami":                         "Asia/Tokyo",
	"otaru":                          "Asia/Tokyo",
	"fukui":                          "Asia/Tokyo",
	"maizuru":                        "Asia/Tokyo",
	"wakayama":                       "Asia/Tokyo",
	"mito":                           "Asia/Tokyo",
	"kofu":                           "Asia/Tokyo",
	"iwaki":                          "Asia/Tokyo",
	"nagaoka":                        "Asia/Tokyo",
	"yamagata":                       "Asia/Tokyo",
	"tsuruoka":                       "Asia/Tokyo",
	"kagoshima":                      "Asia/Tokyo",
	"matsuyama":                      "Asia/Tokyo",
	"kanazawa":                       "Asia/Tokyo",
	"muroran":                        "Asia/Tokyo",
	"asahikawa":                      "Asia/Tokyo",
	"kobe":                           "Asia/Tokyo",
	"yokohama":                       "Asia/Tokyo",
	"akita":                          "Asia/Tokyo",
	"aomori":                         "Asia/Tokyo",
	"hirosaki":                       "Asia/Tokyo",
	"hachinohe":                      "Asia/Tokyo",
	"fukushima":                      "Asia/Tokyo",
	"morioka":                        "Asia/Tokyo",
	"niigata":                        "Asia/Tokyo",
	"fukuoka":                        "Asia/Tokyo",
	"miyazaki":                       "Asia/Tokyo",
	"naha":                           "Asia/Tokyo",
	"nagoya":                         "Asia/Tokyo",
	"nagano":                         "Asia/Tokyo",
	"kushiro":                        "Asia/Tokyo",
	"hakodate":                       "Asia/Tokyo",
	"kyoto":                          "Asia/Tokyo",
	"sendai":                         "Asia/Tokyo",
	"sakata":                         "Asia/Tokyo",
	"nagasaki":                       "Asia/Tokyo",
	"hiroshima":                      "Asia/Tokyo",
	"sapporo":                        "Asia/Tokyo",
	"osaka":                          "Asia/Tokyo",
	"tokyo":                          "Asia/Tokyo",
	"almafraq":                       "Asia/Amman",
	"attafilah":                      "Asia/Amman",
	"ma'an":                          "Asia/Amman",
	"irbid":                          "Asia/Amman",
	"assalt":                         "Asia/Amman",
	"azzarqa":                        "Asia/Amman",
	"alaqabah":                       "Asia/Amman",
	"alkarak":                        "Asia/Amman",
	"amman":                          "Asia/Amman",
	"turgay":                         "Asia/Qyzylorda",
	"mangyshlak":                     "Asia/Aqtau",
	"maqat":                          "Asia/Atyrau",
	"bestobe":                        "Asia/Almaty",
	"osakarovka":                     "Asia/Almaty",
	"aqadyr":                         "Asia/Almaty",
	"sharbaqty":                      "Asia/Almaty",
	"shemonaikha":                    "Asia/Almaty",
	"serebryansk":                    "Asia/Almaty",
	"boralday":                       "Asia/Almaty",
	"zharkent":                       "Asia/Almaty",
	"esik":                           "Asia/Almaty",
	"lenger":                         "Asia/Almaty",
	"kentau":                         "Asia/Almaty",
	"zhosaly":                        "Asia/Qyzylorda",
	"oktyabrsk":                      "Asia/Aqtobe",
	"algha":                          "Asia/Aqtobe",
	"bayghanin":                      "Asia/Aqtobe",
	"embi":                           "Asia/Aqtobe",
	"zhetiqara":                      "Asia/Qyzylorda",
	"komsomolets":                    "Asia/Qyzylorda",
	"tobol":                          "Asia/Qyzylorda",
	"qusmuryn":                       "Asia/Qyzylorda",
	"shieli":                         "Asia/Qyzylorda",
	"makhambet":                      "Asia/Atyrau",
	"chapaev":                        "Asia/Oral",
	"zhanibek":                       "Asia/Oral",
	"aqsay":                          "Asia/Oral",
	"esil":                           "Asia/Almaty",
	"derzhavinsk":                    "Asia/Almaty",
	"zhaltyr":                        "Asia/Almaty",
	"makinsk":                        "Asia/Almaty",
	"aqsu":                           "Asia/Almaty",
	"zholymbet":                      "Asia/Almaty",
	"erymentau":                      "Asia/Almaty",
	"saryshaghan":                    "Asia/Almaty",
	"qarazhal":                       "Asia/Almaty",
	"atasu":                          "Asia/Almaty",
	"kishkenekol":                    "Asia/Almaty",
	"tayynsha":                       "Asia/Almaty",
	"bulaevo":                        "Asia/Almaty",
	"ertis":                          "Asia/Almaty",
	"kachiry":                        "Asia/Almaty",
	"zaysan":                         "Asia/Almaty",
	"zyryanovsk":                     "Asia/Almaty",
	"ridder":                         "Asia/Almaty",
	"shar":                           "Asia/Almaty",
	"urzhar":                         "Asia/Almaty",
	"sarqan":                         "Asia/Almaty",
	"ushtobe":                        "Asia/Almaty",
	"shonzhy":                        "Asia/Almaty",
	"qapshaghay":                     "Asia/Almaty",
	"otar":                           "Asia/Almaty",
	"fortshevchenko":                 "Asia/Aqtau",
	"zhangaozen":                     "Asia/Aqtau",
	"arys":                           "Asia/Almaty",
	"burylbaytal":                    "Asia/Almaty",
	"shu":                            "Asia/Almaty",
	"qulan":                          "Asia/Almaty",
	"oytal":                          "Asia/Almaty",
	"qaratau":                        "Asia/Almaty",
	"khromtau":                       "Asia/Aqtobe",
	"arqalyq":                        "Asia/Qyzylorda",
	"oostanay":                       "Asia/Qyzylorda",
	"baykonur":                       "Asia/Qyzylorda",
	"balyqshy":                       "Asia/Atyrau",
	"atbasar":                        "Asia/Almaty",
	"kokshetau":                      "Asia/Almaty",
	"temirtau":                       "Asia/Almaty",
	"zhezqazghan":                    "Asia/Almaty",
	"qarqaraly":                      "Asia/Almaty",
	"balqash":                        "Asia/Almaty",
	"petropavlovsk":                  "Asia/Almaty",
	"ayakoz":                         "Asia/Almaty",
	"taldyqorghan":                   "Asia/Almaty",
	"turkistan":                      "Asia/Almaty",
	"shalqar":                        "Asia/Aqtobe",
	"qazaly":                         "Asia/Qyzylorda",
	"aral":                           "Asia/Qyzylorda",
	"qulsary":                        "Asia/Atyrau",
	"oral":                           "Asia/Oral",
	"beyneu":                         "Asia/Aqtau",
	"aktau":                          "Asia/Aqtau",
	"aktobe":                         "Asia/Aqtobe",
	"rudny":                          "Asia/Qyzylorda",
	"qyzylorda":                      "Asia/Qyzylorda",
	"atyrau":                         "Asia/Atyrau",
	"ekibastuz":                      "Asia/Almaty",
	"pavlodar":                       "Asia/Almaty",
	"semey":                          "Asia/Almaty",
	"oskemen":                        "Asia/Almaty",
	"shymkent":                       "Asia/Almaty",
	"taraz":                          "Asia/Almaty",
	"astana":                         "Asia/Almaty",
	"qaraghandy":                     "Asia/Almaty",
	"almaty":                         "Asia/Almaty",
	"nyeri":                          "Africa/Nairobi",
	"mwingi":                         "Africa/Nairobi",
	"embu":                           "Africa/Nairobi",
	"machakos":                       "Africa/Nairobi",
	"nanyuki":                        "Africa/Nairobi",
	"maralal":                        "Africa/Nairobi",
	"konza":                          "Africa/Nairobi",
	"lodwar":                         "Africa/Nairobi",
	"eldamaravine":                   "Africa/Nairobi",
	"sotik":                          "Africa/Nairobi",
	"namanga":                        "Africa/Nairobi",
	"naivasha":                       "Africa/Nairobi",
	"kericho":                        "Africa/Nairobi",
	"kitale":                         "Africa/Nairobi",
	"bungoma":                        "Africa/Nairobi",
	"kakamega":                       "Africa/Nairobi",
	"wajir":                          "Africa/Nairobi",
	"garissa":                        "Africa/Nairobi",
	"witu":                           "Africa/Nairobi",
	"tsavo":                          "Africa/Nairobi",
	"voi":                            "Africa/Nairobi",
	"kilifi":                         "Africa/Nairobi",
	"thika":                          "Africa/Nairobi",
	"kendubay":                       "Africa/Nairobi",
	"karungu":                        "Africa/Nairobi",
	"kisii":                          "Africa/Nairobi",
	"marsabit":                       "Africa/Nairobi",
	"moyale":                         "Africa/Nairobi",
	"nakuru":                         "Africa/Nairobi",
	"lamu":                           "Africa/Nairobi",
	"malindi":                        "Africa/Nairobi",
	"kisumu":                         "Africa/Nairobi",
	"meru":                           "Africa/Nairobi",
	"eldoret":                        "Africa/Nairobi",
	"mombasa":                        "Africa/Nairobi",
	"nairobi":                        "Africa/Nairobi",
	"tarawa":                         "Pacific/Tarawa",
	"prizren":                        "Europe/Belgrade",
	"pec":                            "Europe/Belgrade",
	"pristina":                       "Europe/Belgrade",
	"hawalli":                        "Asia/Kuwait",
	"alahmadi":                       "Asia/Kuwait",
	"aljahra":                        "Asia/Kuwait",
	"kuwait":                         "Asia/Kuwait",
	"tokmak":                         "Asia/Bishkek",
	"karabalta":                      "Asia/Bishkek",
	"cholponata":                     "Asia/Bishkek",
	"naryn":                          "Asia/Bishkek",
	"kokyangak":                      "Asia/Bishkek",
	"balykchy":                       "Asia/Bishkek",
	"atbashy":                        "Asia/Bishkek",
	"toktogul":                       "Asia/Bishkek",
	"tashkomur":                      "Asia/Bishkek",
	"talas":                          "Asia/Bishkek",
	"osh":                            "Asia/Bishkek",
	"karakol":                        "Asia/Bishkek",
	"bishkek":                        "Asia/Bishkek",
	"banhouayxay":                    "Asia/Vientiane",
	"louangnamtha":                   "Asia/Vientiane",
	"champasak":                      "Asia/Vientiane",
	"saravan":                        "Asia/Vientiane",
	"xamnua":                         "Asia/Vientiane",
	"phongsali":                      "Asia/Vientiane",
	"attapu":                         "Asia/Vientiane",
	"xaignabouri":                    "Asia/Vientiane",
	"pakxe":                          "Asia/Vientiane",
	"xiangkhoang":                    "Asia/Vientiane",
	"louangphrabang":                 "Asia/Vientiane",
	"thakhek":                        "Asia/Vientiane",
	"savannakhet":                    "Asia/Vientiane",
	"vientiane":                      "Asia/Vientiane",
	"rezekne":                        "Europe/Riga",
	"ventspils":                      "Europe/Riga",
	"jelgava":                        "Europe/Riga",
	"liepaga":                        "Europe/Riga",
	"daugavpils":                     "Europe/Riga",
	"riga":                           "Europe/Riga",
	"b'abda":                         "Asia/Beirut",
	"nabatiyeettahta":                "Asia/Beirut",
	"zahle":                          "Asia/Beirut",
	"trablous":                       "Asia/Beirut",
	"beirut":                         "Asia/Beirut",
	"teyateyaneng":                   "Africa/Maseru",
	"mohaleshoek":                    "Africa/Maseru",
	"moyeni":                         "Africa/Maseru",
	"hlotse":                         "Africa/Maseru",
	"butha-buthe":                    "Africa/Maseru",
	"mokhotlong":                     "Africa/Maseru",
	"mafetang":                       "Africa/Maseru",
	"maseru":                         "Africa/Maseru",
	"barclayville":                   "Africa/Monrovia",
	"voinjama":                       "Africa/Monrovia",
	"bentol":                         "Africa/Monrovia",
	"kakata":                         "Africa/Monrovia",
	"sanniquellie":                   "Africa/Monrovia",
	"rivercess":                      "Africa/Monrovia",
	"harper":                         "Africa/Monrovia",
	"gbarnga":                        "Africa/Monrovia",
	"zwedru":                         "Africa/Monrovia",
	"greenville":                     "America/New_York",
	"buchanan":                       "Africa/Monrovia",
	"robertsport":                    "Africa/Monrovia",
	"monrovia":                       "Africa/Monrovia",
	"dirj":                           "Africa/Tripoli",
	"nalut":                          "Africa/Tripoli",
	"zillah":                         "Africa/Tripoli",
	"alkhums":                        "Africa/Tripoli",
	"tajarhi":                        "Africa/Tripoli",
	"ummalabid":                      "Africa/Tripoli",
	"azzawiyah":                      "Africa/Tripoli",
	"gharyan":                        "Africa/Tripoli",
	"mizdah":                         "Africa/Tripoli",
	"baniwalid":                      "Africa/Tripoli",
	"almarj":                         "Africa/Tripoli",
	"albayda":                        "Asia/Aden",
	"shahhat":                        "Africa/Tripoli",
	"elagheila":                      "Africa/Tripoli",
	"maradah":                        "Africa/Tripoli",
	"qaminis":                        "Africa/Tripoli",
	"assidr":                         "Africa/Tripoli",
	"aljaghbub":                      "Africa/Tripoli",
	"ghadamis":                       "Africa/Tripoli",
	"hun":                            "Africa/Tripoli",
	"birak":                          "Africa/Tripoli",
	"ghat":                           "Africa/Tripoli",
	"marzuq":                         "Africa/Tripoli",
	"ajdabiya":                       "Africa/Tripoli",
	"awjilah":                        "Africa/Tripoli",
	"surt":                           "Africa/Tripoli",
	"darnah":                         "Africa/Tripoli",
	"tubruq":                         "Africa/Tripoli",
	"aljawf":                         "Africa/Tripoli",
	"tmassah":                        "Africa/Tripoli",
	"misratah":                       "Africa/Tripoli",
	"zuwarah":                        "Africa/Tripoli",
	"sabha":                          "Africa/Tripoli",
	"banghazi":                       "Africa/Tripoli",
	"vaduz":                          "Europe/Vaduz",
	"panevezys":                      "Europe/Vilnius",
	"siauliai":                       "Europe/Vilnius",
	"klaipeda":                       "Europe/Vilnius",
	"kaunas":                         "Europe/Vilnius",
	"vilnius":                        "Europe/Vilnius",
	"diekirch":                       "Europe/Luxembourg",
	"grevenmacher":                   "Europe/Luxembourg",
	"luxembourg":                     "Europe/Luxembourg",
	"macau":                          "Asia/Macau",
	"tetovo":                         "Europe/Skopje",
	"bitola":                         "Europe/Skopje",
	"skopje":                         "Europe/Skopje",
	"sambava":                        "Indian/Antananarivo",
	"ambanja":                        "Indian/Antananarivo",
	"ihosy":                          "Indian/Antananarivo",
	"mandritsara":                    "Indian/Antananarivo",
	"besalampy":                      "Indian/Antananarivo",
	"marovoay":                       "Indian/Antananarivo",
	"antsohihy":                      "Indian/Antananarivo",
	"ambatondrazaka":                 "Indian/Antananarivo",
	"bekiy":                          "Indian/Antananarivo",
	"manja":                          "Indian/Antananarivo",
	"miandrivazo":                    "Indian/Antananarivo",
	"antsirabe":                      "Indian/Antananarivo",
	"antalaha":                       "Indian/Antananarivo",
	"andoany":                        "Indian/Antananarivo",
	"farafangana":                    "Indian/Antananarivo",
	"mananjary":                      "Indian/Antananarivo",
	"maintirano":                     "Indian/Antananarivo",
	"toamasina":                      "Indian/Antananarivo",
	"maroantsetra":                   "Indian/Antananarivo",
	"tolanaro":                       "Indian/Antananarivo",
	"morombe":                        "Indian/Antananarivo",
	"androka":                        "Indian/Antananarivo",
	"morondava":                      "Indian/Antananarivo",
	"antsiranana":                    "Indian/Antananarivo",
	"fianarantsoa":                   "Indian/Antananarivo",
	"mahajanga":                      "Indian/Antananarivo",
	"toliara":                        "Indian/Antananarivo",
	"antananarivo":                   "Indian/Antananarivo",
	"mzimba":                         "Africa/Blantyre",
	"machinga":                       "Africa/Blantyre",
	"dedza":                          "Africa/Blantyre",
	"mchinji":                        "Africa/Blantyre",
	"ntcheu":                         "Africa/Blantyre",
	"chiradzulu":                     "Africa/Blantyre",
	"nsanje":                         "Africa/Blantyre",
	"mwanza":                         "Africa/Dar_es_Salaam",
	"mulanje":                        "Africa/Blantyre",
	"karonga":                        "Africa/Blantyre",
	"chitipa":                        "Africa/Blantyre",
	"nkhatabay":                      "Africa/Blantyre",
	"nkhotakota":                     "Africa/Blantyre",
	"mangochi":                       "Africa/Blantyre",
	"salima":                         "Africa/Blantyre",
	"chiromo":                        "Africa/Blantyre",
	"zomba":                          "Africa/Blantyre",
	"mzuzu":                          "Africa/Blantyre",
	"blantyre":                       "Africa/Blantyre",
	"lilongwe":                       "Africa/Blantyre",
	"kangar":                         "Asia/Kuala_Lumpur",
	"kualalipis":                     "Asia/Kuala_Lumpur",
	"shahalam":                       "Asia/Kuala_Lumpur",
	"telukintan":                     "Asia/Kuala_Lumpur",
	"butterworth":                    "Asia/Kuala_Lumpur",
	"sungaipetani":                   "Asia/Kuala_Lumpur",
	"alorsetar":                      "Asia/Kuala_Lumpur",
	"muar":                           "Asia/Kuala_Lumpur",
	"batupahat":                      "Asia/Kuala_Lumpur",
	"keluang":                        "Asia/Kuala_Lumpur",
	"seremban":                       "Asia/Kuala_Lumpur",
	"raub":                           "Asia/Kuala_Lumpur",
	"chukai":                         "Asia/Kuala_Lumpur",
	"kualaterengganu":                "Asia/Kuala_Lumpur",
	"lahaddatu":                      "Asia/Kuching",
	"bintulu":                        "Asia/Kuching",
	"miri":                           "Asia/Kuching",
	"johorbahru":                     "Asia/Kuala_Lumpur",
	"kelang":                         "Asia/Kuala_Lumpur",
	"taiping":                        "Asia/Kuala_Lumpur",
	"ipoh":                           "Asia/Kuala_Lumpur",
	"kotabaharu":                     "Asia/Kuala_Lumpur",
	"malacca":                        "Asia/Kuala_Lumpur",
	"kuantan":                        "Asia/Kuala_Lumpur",
	"tawau":                          "Asia/Kuching",
	"sandakan":                       "Asia/Kuching",
	"kotakinabalu":                   "Asia/Kuching",
	"sibu":                           "Asia/Kuching",
	"kuching":                        "Asia/Kuching",
	"putrajaya":                      "Asia/Kuala_Lumpur",
	"kualalumpur":                    "Asia/Kuala_Lumpur",
	"male":                           "Indian/Maldives",
	"goundam":                        "Africa/Bamako",
	"aguelhok":                       "Africa/Bamako",
	"bourem":                         "Africa/Bamako",
	"kati":                           "Africa/Bamako",
	"banamba":                        "Africa/Bamako",
	"kangaba":                        "Africa/Bamako",
	"niorodusahel":                   "Africa/Bamako",
	"bafoulabe":                      "Africa/Bamako",
	"satadougou":                     "Africa/Bamako",
	"yelimane":                       "Africa/Bamako",
	"kita":                           "Africa/Bamako",
	"koutiala":                       "Africa/Bamako",
	"sikasso":                        "Africa/Bamako",
	"bougouni":                       "Africa/Bamako",
	"markala":                        "Africa/Bamako",
	"sokolo":                         "Africa/Bamako",
	"san":                            "Africa/Bamako",
	"taoudenni":                      "Africa/Bamako",
	"araouane":                       "Africa/Bamako",
	"tessalit":                       "Africa/Bamako",
	"menaka":                         "Africa/Bamako",
	"nara":                           "Africa/Bamako",
	"koulikoro":                      "Africa/Bamako",
	"mopti":                          "Africa/Bamako",
	"gao":                            "Africa/Bamako",
	"segou":                          "Africa/Bamako",
	"timbuktu":                       "Africa/Bamako",
	"bamako":                         "Africa/Bamako",
	"djenne":                         "Africa/Bamako",
	"valletta":                       "Europe/Malta",
	"majuro":                         "Pacific/Majuro",
	"fderik":                         "Africa/Nouakchott",
	"aleg":                           "Africa/Nouakchott",
	"akjoujt":                        "Africa/Nouakchott",
	"zouirat":                        "Africa/Nouakchott",
	"chegga":                         "Africa/Nouakchott",
	"magtalajar":                     "Africa/Nouakchott",
	"bogue":                          "Africa/Nouakchott",
	"boutilimit":                     "Africa/Nouakchott",
	"selibaby":                       "Africa/Nouakchott",
	"timbedra":                       "Africa/Nouakchott",
	"nema":                           "Africa/Nouakchott",
	"saint-louis":                    "Africa/Dakar",
	"tidjikdja":                      "Africa/Nouakchott",
	"birmogrein":                     "Africa/Nouakchott",
	"rosso":                          "Africa/Nouakchott",
	"kiffa":                          "Africa/Nouakchott",
	"nouadhibou":                     "Africa/Nouakchott",
	"ayounelatrous":                  "Africa/Nouakchott",
	"nouakchott":                     "Africa/Nouakchott",
	"atar":                           "Africa/Nouakchott",
	"curepipe":                       "Indian/Mauritius",
	"portlouis":                      "Indian/Mauritius",
	"vicenteguerrero":                "America/Tijuana",
	"loreto":                         "America/Mazatlan",
	"ciudadconstitucion":             "America/Mazatlan",
	"allende":                        "America/Monterrey",
	"nuevarosita":                    "America/Monterrey",
	"hidalgodelparral":               "America/Chihuahua",
	"gomezpalacio":                   "America/Monterrey",
	"canatlan":                       "America/Monterrey",
	"villaunion":                     "America/Mazatlan",
	"altata":                         "America/Mazatlan",
	"esperanza":                      "America/Hermosillo",
	"nacozariviejo":                  "America/Hermosillo",
	"villanueva":                     "America/Mexico_City",
	"montemorelos":                   "America/Monterrey",
	"sabinashidalgo":                 "America/Monterrey",
	"cardenas":                       "America/Mexico_City",
	"ciudadvalles":                   "America/Mexico_City",
	"ciudadmante":                    "America/Monterrey",
	"reynosa":                        "America/Matamoros",
	"ciudadmadero":                   "America/Monterrey",
	"autlan":                         "America/Mexico_City",
	"ciudadhidalgo":                  "America/Mexico_City",
	"apatzingan":                     "America/Mexico_City",
	"santiagoixcuintla":              "America/Mazatlan",
	"juchitan":                       "America/Mexico_City",
	"miahuatlan":                     "America/Mexico_City",
	"atlixco":                        "America/Mexico_City",
	"acatlan":                        "America/Mexico_City",
	"paraiso":                        "America/Mexico_City",
	"balancan":                       "America/Mexico_City",
	"tlaxcala":                       "America/Mexico_City",
	"irapuato":                       "America/Mexico_City",
	"celaya":                         "America/Mexico_City",
	"chilpancingo":                   "America/Mexico_City",
	"iguala":                         "America/Mexico_City",
	"tecpan":                         "America/Mexico_City",
	"atoyac":                         "America/Mexico_City",
	"nezahualcoyotl":                 "America/Mexico_City",
	"sanjuandelrio":                  "America/Mexico_City",
	"jaltipan":                       "America/Mexico_City",
	"orizaba":                        "America/Mexico_City",
	"xalapa":                         "America/Mexico_City",
	"nautla":                         "America/Mexico_City",
	"sancristobaldelascasas":         "America/Mexico_City",
	"motul":                          "America/Merida",
	"tekax":                          "America/Merida",
	"peto":                           "America/Merida",
	"halacho":                        "America/Merida",
	"sanquintin":                     "America/Tijuana",
	"puntaprieta":                    "America/Tijuana",
	"santarosalia":                   "America/Mazatlan",
	"guerreronegro":                  "America/Mazatlan",
	"piedrasnegras":                  "America/Matamoros",
	"sanpedrodelascolonias":          "America/Monterrey",
	"sierramojada":                   "America/Matamoros",
	"parras":                         "America/Monterrey",
	"cuauhtemoc":                     "America/Chihuahua",
	"nuevocasasgrandes":              "America/Chihuahua",
	"ojinaga":                        "America/Ojinaga",
	"villaahumada":                   "America/Chihuahua",
	"ciudadcamargo":                  "America/Chihuahua",
	"cuencame":                       "America/Monterrey",
	"papasquiaro":                    "America/Monterrey",
	"escuinapa":                      "America/Mazatlan",
	"guamuchil":                      "America/Mazatlan",
	"guasave":                        "America/Mazatlan",
	"elfuerte":                       "America/Mazatlan",
	"aguaprieta":                     "America/Hermosillo",
	"ciudadobregon":                  "America/Hermosillo",
	"navajoa":                        "America/Hermosillo",
	"caborca":                        "America/Hermosillo",
	"mazatlan":                       "America/Mazatlan",
	"cananea":                        "America/Hermosillo",
	"huatabampo":                     "America/Hermosillo",
	"zacatecas":                      "America/Mexico_City",
	"juanaldama":                     "America/Mexico_City",
	"fresnillo":                      "America/Mexico_City",
	"matehuala":                      "America/Mexico_City",
	"tamuin":                         "America/Mexico_City",
	"tamazunchale":                   "America/Mexico_City",
	"tula":                           "Europe/Moscow",
	"aldama":                         "America/Monterrey",
	"tecoman":                        "America/Mexico_City",
	"puertovallarta":                 "America/Mexico_City",
	"labarca":                        "America/Mexico_City",
	"ciudadguzman":                   "America/Mexico_City",
	"lagosdemoreno":                  "America/Mexico_City",
	"morelia":                        "America/Mexico_City",
	"lazarocardenas":                 "America/Mexico_City",
	"coalcoman":                      "America/Mexico_City",
	"uruapan":                        "America/Mexico_City",
	"tuxpan":                         "America/Mazatlan",
	"tepic":                          "America/Mazatlan",
	"compostela":                     "America/Mazatlan",
	"tecuala":                        "America/Mazatlan",
	"ciudaddelcarmen":                "America/Merida",
	"champoton":                      "America/Merida",
	"salinacruz":                     "America/Mexico_City",
	"puertoescondido":                "America/Mexico_City",
	"pochutla":                       "America/Mexico_City",
	"mitla":                          "America/Mexico_City",
	"tlaxiaco":                       "America/Mexico_City",
	"huajuapandeleon":                "America/Mexico_City",
	"tehuacan":                       "America/Mexico_City",
	"teziutlan":                      "America/Mexico_City",
	"frontera":                       "America/Mexico_City",
	"tenosique":                      "America/Mexico_City",
	"guanajuato":                     "America/Mexico_City",
	"taxco":                          "America/Mexico_City",
	"ayutla":                         "America/Mexico_City",
	"ciudadaltamirano":               "America/Mexico_City",
	"petatlan":                       "America/Mexico_City",
	"pachuca":                        "America/Mexico_City",
	"toluca":                         "America/Mexico_City",
	"zumpango":                       "America/Mexico_City",
	"minatitlan":                     "America/Mexico_City",
	"coatzacoalcos":                  "America/Mexico_City",
	"pozaricadehidalgo":              "America/Mexico_City",
	"santiagotuxtla":                 "America/Mexico_City",
	"tuxpam":                         "America/Mexico_City",
	"panuco":                         "America/Mexico_City",
	"pijijiapan":                     "America/Mexico_City",
	"islamujeres":                    "America/Cancun",
	"felipecarrillopuerto":           "America/Cancun",
	"tizimin":                        "America/Merida",
	"valladolid":                     "Europe/Madrid",
	"izamal":                         "America/Merida",
	"ticul":                          "America/Merida",
	"ensenada":                       "America/Tijuana",
	"saltillo":                       "America/Monterrey",
	"ciudadjuarez":                   "America/Ojinaga",
	"delicias":                       "America/Chihuahua",
	"durango":                        "America/Denver",
	"losmochis":                      "America/Mazatlan",
	"ciudadvictoria":                 "America/Monterrey",
	"aguascalientes":                 "America/Mexico_City",
	"tehuantepec":                    "America/Mexico_City",
	"villahermosa":                   "America/Mexico_City",
	"cuernavaca":                     "America/Mexico_City",
	"queretaro":                      "America/Mexico_City",
	"tapachula":                      "America/Mexico_City",
	"chetumal":                       "America/Cancun",
	"progreso":                       "America/Merida",
	"cabosanlucas":                   "America/Mazatlan",
	"monclova":                       "America/Monterrey",
	"ometepec":                       "America/Mexico_City",
	"cozumel":                        "America/Cancun",
	"mexicali":                       "America/Tijuana",
	"torreon":                        "America/Monterrey",
	"culiacan":                       "America/Mazatlan",
	"nogales":                        "America/Phoenix",
	"hermosillo":                     "America/Hermosillo",
	"guaymas":                        "America/Hermosillo",
	"sanluispotosi":                  "America/Mexico_City",
	"matamoros":                      "America/Matamoros",
	"nuevolaredo":                    "America/Matamoros",
	"colima":                         "America/Mexico_City",
	"campeche":                       "America/Merida",
	"oaxaca":                         "America/Mexico_City",
	"leon":                           "Europe/Madrid",
	"tijuana":                        "America/Tijuana",
	"chihuahua":                      "America/Chihuahua",
	"tampico":                        "America/Monterrey",
	"acapulco":                       "America/Mexico_City",
	"veracruz":                       "America/Mexico_City",
	"tuxtlagutierrez":                "America/Mexico_City",
	"cancun":                         "America/Cancun",
	"merida":                         "America/Caracas",
	"guadalajara":                    "Europe/Madrid",
	"puebla":                         "America/Mexico_City",
	"monterrey":                      "America/Monterrey",
	"mexicocity":                     "America/Mexico_City",
	"dubasari":                       "Europe/Chisinau",
	"balti":                          "Europe/Chisinau",
	"cahul":                          "Europe/Chisinau",
	"tiraspol":                       "Europe/Chisinau",
	"chisinau":                       "Europe/Chisinau",
	"monaco":                         "Europe/Paris",
	"suchboatar":                     "Asia/Ulaanbaatar",
	"dzuunmod":                       "Asia/Ulaanbaatar",
	"tsetserleg":                     "Asia/Ulaanbaatar",
	"olgiy":                          "Asia/Hovd",
	"ulaan-uul":                      "Asia/Ulaanbaatar",
	"hodrogo":                        "Asia/Hovd",
	"buyant-uhaa":                    "Asia/Ulaanbaatar",
	"ondorhaan":                      "Asia/Ulaanbaatar",
	"bayankhongor":                   "Asia/Ulaanbaatar",
	"uliastay":                       "Asia/Hovd",
	"ulaangom":                       "Asia/Hovd",
	"bulgan":                         "Asia/Ulaanbaatar",
	"mandalgovi":                     "Asia/Ulaanbaatar",
	"darhan":                         "Asia/Ulaanbaatar",
	"dzuunharaa":                     "Asia/Ulaanbaatar",
	"arvayheer":                      "Asia/Ulaanbaatar",
	"baruunurt":                      "Asia/Choibalsan",
	"dalandzadgad":                   "Asia/Ulaanbaatar",
	"dund-us":                        "Asia/Hovd",
	"choybalsan":                     "Asia/Choibalsan",
	"erdenet":                        "Asia/Ulaanbaatar",
	"ulaanbaatar":                    "Asia/Ulaanbaatar",
	"podgorica":                      "Europe/Podgorica",
	"ksarelkebir":                    "Africa/Casablanca",
	"larache":                        "Africa/Casablanca",
	"taza":                           "Africa/Casablanca",
	"ouezzane":                       "Africa/Casablanca",
	"kenitra":                        "Africa/Casablanca",
	"settat":                         "Africa/Casablanca",
	"errachidia":                     "Africa/Casablanca",
	"meknes":                         "Africa/Casablanca",
	"tiznit":                         "Africa/Casablanca",
	"eljadida":                       "Africa/Casablanca",
	"dawra":                          "Africa/El_Aaiun",
	"lemsid":                         "Africa/El_Aaiun",
	"tantan":                         "Africa/Casablanca",
	"biranzarane":                    "Africa/El_Aaiun",
	"tangier":                        "Africa/Casablanca",
	"agadir":                         "Africa/Casablanca",
	"goulimine":                      "Africa/Casablanca",
	"smara":                          "Africa/El_Aaiun",
	"addakhla":                       "Africa/El_Aaiun",
	"oujda":                          "Africa/Casablanca",
	"safi":                           "Africa/Casablanca",
	"laayoune":                       "Africa/El_Aaiun",
	"fez":                            "Africa/Casablanca",
	"rabat":                          "Africa/Casablanca",
	"marrakesh":                      "Africa/Casablanca",
	"casablanca":                     "Africa/Casablanca",
	"moatize":                        "Africa/Maputo",
	"luangwa":                        "Africa/Lusaka",
	"manica":                         "Africa/Maputo",
	"espungabera":                    "Africa/Maputo",
	"montepuez":                      "Africa/Maputo",
	"mocimboa":                       "Africa/Maputo",
	"marrupa":                        "Africa/Maputo",
	"cuamba":                         "Africa/Maputo",
	"ligonha":                        "Africa/Maputo",
	"macia":                          "Africa/Maputo",
	"massangena":                     "Africa/Maputo",
	"mapai":                          "Africa/Maputo",
	"chiramba":                       "Africa/Maputo",
	"mocuba":                         "Africa/Maputo",
	"nicuadala":                      "Africa/Maputo",
	"maxixe":                         "Africa/Maputo",
	"panda":                          "Africa/Maputo",
	"quissico":                       "Africa/Maputo",
	"vilanculos":                     "Africa/Maputo",
	"matola":                         "Africa/Maputo",
	"chimoio":                        "Africa/Maputo",
	"lichinga":                       "Africa/Maputo",
	"angoche":                        "Africa/Maputo",
	"mocambique":                     "Africa/Maputo",
	"inhambane":                      "Africa/Maputo",
	"tete":                           "Africa/Maputo",
	"pemba":                          "Africa/Maputo",
	"nampula":                        "Africa/Maputo",
	"xai-xai":                        "Africa/Maputo",
	"quelimane":                      "Africa/Maputo",
	"nacala":                         "Africa/Maputo",
	"beira":                          "Africa/Maputo",
	"maputo":                         "Africa/Maputo",
	"loikaw":                         "Asia/Rangoon",
	"pa-an":                          "Asia/Rangoon",
	"haka":                           "Asia/Rangoon",
	"taunggyi":                       "Asia/Rangoon",
	"sagaing":                        "Asia/Rangoon",
	"myingyan":                       "Asia/Rangoon",
	"letpadan":                       "Asia/Rangoon",
	"taungoo":                        "Asia/Rangoon",
	"thongwa":                        "Asia/Rangoon",
	"mudon":                          "Asia/Rangoon",
	"ye":                             "Asia/Rangoon",
	"mawlamyine":                     "Asia/Rangoon",
	"kyaukphyu":                      "Asia/Rangoon",
	"wakema":                         "Asia/Rangoon",
	"labutta":                        "Asia/Rangoon",
	"phyarpon":                       "Asia/Rangoon",
	"yandoon":                        "Asia/Rangoon",
	"hinthada":                       "Asia/Rangoon",
	"pathein":                        "Asia/Rangoon",
	"allanmyo":                       "Asia/Rangoon",
	"yaynangyoung":                   "Asia/Rangoon",
	"chauk":                          "Asia/Rangoon",
	"pakokku":                        "Asia/Rangoon",
	"namtu":                          "Asia/Rangoon",
	"dawei":                          "Asia/Rangoon",
	"shwebo":                         "Asia/Rangoon",
	"bago":                           "Asia/Rangoon",
	"pyu":                            "Asia/Rangoon",
	"pyay":                           "Asia/Rangoon",
	"magway":                         "Asia/Rangoon",
	"myitkyina":                      "Asia/Rangoon",
	"monywa":                         "Asia/Rangoon",
	"myeik":                          "Asia/Rangoon",
	"mandalay":                       "Asia/Rangoon",
	"sittwe":                         "Asia/Rangoon",
	"naypyidaw":                      "Asia/Rangoon",
	"rangoon":                        "Asia/Rangoon",
	"omaruru":                        "Africa/Windhoek",
	"karibib":                        "Africa/Windhoek",
	"otavi":                          "Africa/Windhoek",
	"gobabis":                        "Africa/Windhoek",
	"karasburg":                      "Africa/Windhoek",
	"bethanie":                       "Africa/Windhoek",
	"oranjemund":                     "Africa/Windhoek",
	"mariental":                      "Africa/Windhoek",
	"rehoboth":                       "Africa/Windhoek",
	"outjo":                          "Africa/Windhoek",
	"opuwo":                          "Africa/Windhoek",
	"usakos":                         "Africa/Windhoek",
	"okahandja":                      "Africa/Windhoek",
	"otjiwarongo":                    "Africa/Windhoek",
	"oshikango":                      "Africa/Windhoek",
	"cuangar":                        "Africa/Luanda",
	"katimamulilo":                   "Africa/Windhoek",
	"keetmanshoop":                   "Africa/Windhoek",
	"maltahohe":                      "Africa/Windhoek",
	"swakopmund":                     "Africa/Windhoek",
	"ongwediva":                      "Africa/Windhoek",
	"rundu":                          "Africa/Windhoek",
	"tsumeb":                         "Africa/Windhoek",
	"luderitz":                       "Africa/Windhoek",
	"walvisbay":                      "Africa/Windhoek",
	"windhoek":                       "Africa/Windhoek",
	"grootfontein":                   "Africa/Windhoek",
	"sallyan":                        "Asia/Kathmandu",
	"baglung":                        "Asia/Kathmandu",
	"jumla":                          "Asia/Kathmandu",
	"bhairawa":                       "Asia/Kathmandu",
	"dandeldhura":                    "Asia/Kathmandu",
	"dhangarhi":                      "Asia/Kathmandu",
	"ramechhap":                      "Asia/Kathmandu",
	"bhimphedi":                      "Asia/Kathmandu",
	"rajbiraj":                       "Asia/Kathmandu",
	"lalitpur":                       "Asia/Kathmandu",
	"hetauda":                        "Asia/Kathmandu",
	"nepalganj":                      "Asia/Kathmandu",
	"birganj":                        "Asia/Kathmandu",
	"biratnagar":                     "Asia/Kathmandu",
	"pokhara":                        "Asia/Kathmandu",
	"kathmandu":                      "Asia/Kathmandu",
	"assen":                          "Europe/Amsterdam",
	"arnhem":                         "Europe/Amsterdam",
	"maastricht":                     "Europe/Amsterdam",
	"zwolle":                         "Europe/Amsterdam",
	"middelburg":                     "Africa/Johannesburg",
	"'s-hertogenbosch":               "Europe/Amsterdam",
	"eindhoven":                      "Europe/Amsterdam",
	"leeuwarden":                     "Europe/Amsterdam",
	"groningen":                      "America/Paramaribo",
	"utrecht":                        "Europe/Amsterdam",
	"haarlem":                        "Europe/Amsterdam",
	"rotterdam":                      "Europe/Amsterdam",
	"thehague":                       "Europe/Amsterdam",
	"amsterdam":                      "Europe/Amsterdam",
	"noumea":                         "Pacific/Noumea",
	"greymouth":                      "Pacific/Auckland",
	"upperhutt":                      "Pacific/Auckland",
	"masterton":                      "Pacific/Auckland",
	"levin":                          "Pacific/Auckland",
	"waitakere":                      "Pacific/Auckland",
	"takapuna":                       "Pacific/Auckland",
	"whakatane":                      "Pacific/Auckland",
	"ashburton":                      "Pacific/Auckland",
	"kaiapoi":                        "Pacific/Auckland",
	"newplymouth":                    "Pacific/Auckland",
	"westport":                       "Pacific/Auckland",
	"hokitika":                       "Pacific/Auckland",
	"oamaru":                         "Pacific/Auckland",
	"palmerstonnorth":                "Pacific/Auckland",
	"wanganui":                       "Pacific/Auckland",
	"hastings":                       "Pacific/Auckland",
	"gisborne":                       "Pacific/Auckland",
	"rotorua":                        "Pacific/Auckland",
	"taupo":                          "Pacific/Auckland",
	"tauranga":                       "Pacific/Auckland",
	"timaru":                         "Pacific/Auckland",
	"whangarei":                      "Pacific/Auckland",
	"invercargill":                   "Pacific/Auckland",
	"napier":                         "Pacific/Auckland",
	"manukau":                        "Pacific/Auckland",
	"blenheim":                       "Pacific/Auckland",
	"dunedin":                        "Pacific/Auckland",
	"wellington":                     "Pacific/Auckland",
	"christchurch":                   "Pacific/Auckland",
	"auckland":                       "Pacific/Auckland",
	"somoto":                         "America/Managua",
	"ocotal":                         "America/Managua",
	"jinotepe":                       "America/Managua",
	"jinotega":                       "America/Managua",
	"masaya":                         "America/Managua",
	"esteli":                         "America/Managua",
	"boaco":                          "America/Managua",
	"juigalpa":                       "America/Managua",
	"rivas":                          "America/Managua",
	"sanjuandenicaragua":             "America/Managua",
	"granada":                        "Europe/Madrid",
	"chinandega":                     "America/Managua",
	"matagalpa":                      "America/Managua",
	"puertocabezas":                  "America/Managua",
	"bluefields":                     "America/Managua",
	"managua":                        "America/Managua",
	"goure":                          "Africa/Niamey",
	"tillaberi":                      "Africa/Niamey",
	"ayorou":                         "Africa/Niamey",
	"birninkonni":                    "Africa/Niamey",
	"madaoua":                        "Africa/Niamey",
	"diffa":                          "Africa/Niamey",
	"nguigmi":                        "Africa/Niamey",
	"dosso":                          "Africa/Niamey",
	"arlit":                          "Africa/Niamey",
	"djado":                          "Africa/Niamey",
	"maradi":                         "Africa/Niamey",
	"tahoua":                         "Africa/Niamey",
	"zinder":                         "Africa/Niamey",
	"niamey":                         "Africa/Niamey",
	"agadez":                         "Africa/Niamey",
	"umuahia":                        "Africa/Lagos",
	"uyo":                            "Africa/Lagos",
	"owerri":                         "Africa/Lagos",
	"dutse":                          "Africa/Lagos",
	"damaturu":                       "Africa/Lagos",
	"iwo":                            "Africa/Lagos",
	"iseyin":                         "Africa/Lagos",
	"biu":                            "Africa/Lagos",
	"bama":                           "Africa/Lagos",
	"aba":                            "Africa/Lagos",
	"opobo":                          "Africa/Lagos",
	"orlu":                           "Africa/Lagos",
	"oturkpo":                        "Africa/Lagos",
	"calabar":                        "Africa/Lagos",
	"wukari":                         "Africa/Lagos",
	"jalingo":                        "Africa/Lagos",
	"kontagora":                      "Africa/Lagos",
	"bida":                           "Africa/Lagos",
	"abeokuta":                       "Africa/Lagos",
	"ijebuode":                       "Africa/Lagos",
	"akure":                          "Africa/Lagos",
	"ikare":                          "Africa/Lagos",
	"owo":                            "Africa/Lagos",
	"ondo":                           "Africa/Lagos",
	"adoekiti":                       "Africa/Lagos",
	"ife":                            "Africa/Lagos",
	"oshogbo":                        "Africa/Lagos",
	"oyo":                            "Africa/Lagos",
	"awka":                           "Africa/Lagos",
	"onitsha":                        "Africa/Lagos",
	"azare":                          "Africa/Lagos",
	"bauchi":                         "Africa/Lagos",
	"gombe":                          "Africa/Lagos",
	"kumo":                           "Africa/Lagos",
	"sapele":                         "Africa/Lagos",
	"nsukka":                         "Africa/Lagos",
	"lokoja":                         "Africa/Lagos",
	"idah":                           "Africa/Lagos",
	"lafia":                          "Africa/Lagos",
	"keffi":                          "Africa/Lagos",
	"funtua":                         "Africa/Lagos",
	"katsina":                        "Africa/Lagos",
	"gusau":                          "Africa/Lagos",
	"nguru":                          "Africa/Lagos",
	"gashua":                         "Africa/Lagos",
	"potiskum":                       "Africa/Lagos",
	"birninkebbi":                    "Africa/Lagos",
	"koko":                           "Africa/Lagos",
	"mubi":                           "Africa/Lagos",
	"numan":                          "Africa/Lagos",
	"ilorin":                         "Africa/Lagos",
	"minna":                          "Africa/Lagos",
	"zaria":                          "Africa/Lagos",
	"jos":                            "Africa/Lagos",
	"yola":                           "Africa/Lagos",
	"benincity":                      "Africa/Lagos",
	"maiduguri":                      "Africa/Lagos",
	"portharcourt":                   "Africa/Lagos",
	"makurdi":                        "Africa/Lagos",
	"ibadan":                         "Africa/Lagos",
	"ogbomosho":                      "Africa/Lagos",
	"warri":                          "Africa/Lagos",
	"kaduna":                         "Africa/Lagos",
	"enugu":                          "Africa/Lagos",
	"sokoto":                         "Africa/Lagos",
	"abuja":                          "Africa/Lagos",
	"kano":                           "Africa/Lagos",
	"lagos":                          "Africa/Lagos",
	"sariwon":                        "Asia/Pyongyang",
	"sin-ni":                         "Asia/Pyongyang",
	"changyon":                       "Asia/Pyongyang",
	"anbyon":                         "Asia/Pyongyang",
	"munchon":                        "Asia/Pyongyang",
	"kaesong":                        "Asia/Pyongyang",
	"chosan":                         "Asia/Pyongyang",
	"manpo":                          "Asia/Pyongyang",
	"sunchon":                        "Asia/Pyongyang",
	"kimhyonggwon":                   "Asia/Pyongyang",
	"pyongsan":                       "Asia/Pyongyang",
	"ongjin":                         "Asia/Pyongyang",
	"haeju":                          "Asia/Pyongyang",
	"kilchu":                         "Asia/Pyongyang",
	"musan":                          "Asia/Pyongyang",
	"sonbong":                        "Asia/Pyongyang",
	"kanggye":                        "Asia/Pyongyang",
	"hungnam":                        "Asia/Pyongyang",
	"taedong":                        "Asia/Pyongyang",
	"chongju":                        "Asia/Pyongyang",
	"hyeson":                         "Asia/Pyongyang",
	"nampo":                          "Asia/Pyongyang",
	"chongjin":                       "Asia/Pyongyang",
	"kimchaek":                       "Asia/Pyongyang",
	"hamhung":                        "Asia/Pyongyang",
	"wonsan":                         "Asia/Pyongyang",
	"sinuiju":                        "Asia/Pyongyang",
	"pyongyang":                      "Asia/Pyongyang",
	"kyrenia":                        "Asia/Famagusta",
	"ammochostos":                    "Asia/Famagusta",
	"capitolhill":                    "Pacific/Saipan",
	"arendal":                        "Europe/Oslo",
	"vossavangen":                    "Europe/Oslo",
	"hermansverk":                    "Europe/Oslo",
	"baerum":                         "Europe/Oslo",
	"hamar":                          "Europe/Oslo",
	"tonsberg":                       "Europe/Oslo",
	"finnsnes":                       "Europe/Oslo",
	"gjovik":                         "Europe/Oslo",
	"rorvik":                         "Europe/Oslo",
	"harstad":                        "Europe/Oslo",
	"alesund":                        "Europe/Oslo",
	"sandnes":                        "Europe/Oslo",
	"drammen":                        "Europe/Oslo",
	"moss":                           "Europe/Oslo",
	"steinkjer":                      "Europe/Oslo",
	"svolvaer":                       "Europe/Oslo",
	"moirana":                        "Europe/Oslo",
	"narvik":                         "Europe/Oslo",
	"bodo":                           "Europe/Oslo",
	"haugesund":                      "Europe/Oslo",
	"stavanger":                      "Europe/Oslo",
	"skien":                          "Europe/Oslo",
	"namsos":                         "Europe/Oslo",
	"alta":                           "Europe/Oslo",
	"vadso":                          "Europe/Oslo",
	"molde":                          "Europe/Oslo",
	"lillehammer":                    "Europe/Oslo",
	"kirkenes":                       "Europe/Oslo",
	"kristiansand":                   "Europe/Oslo",
	"hammerfest":                     "Europe/Oslo",
	"tromso":                         "Europe/Oslo",
	"trondheim":                      "Europe/Oslo",
	"bergen":                         "Europe/Oslo",
	"oslo":                           "Europe/Oslo",
	"alayatsamail":                   "Asia/Muscat",
	"dawwah":                         "Asia/Muscat",
	"mirbat":                         "Asia/Muscat",
	"ibri":                           "Asia/Muscat",
	"salalah":                        "Asia/Muscat",
	"suhar":                          "Asia/Muscat",
	"assib":                          "Asia/Muscat",
	"nizwa":                          "Asia/Muscat",
	"sur":                            "Asia/Muscat",
	"muscat":                         "Asia/Muscat",
	"parachinar":                     "Asia/Karachi",
	"sialkote":                       "Asia/Karachi",
	"sheikhupura":                    "Asia/Karachi",
	"gujrat":                         "Asia/Karachi",
	"sahiwal":                        "Asia/Karachi",
	"chiniot":                        "Asia/Karachi",
	"rahimyarkhan":                   "Asia/Karachi",
	"mansehra":                       "Asia/Karachi",
	"kohat":                          "Asia/Karachi",
	"abbottabad":                     "Asia/Karachi",
	"mardan":                         "Asia/Karachi",
	"gwadar":                         "Asia/Karachi",
	"zhob":                           "Asia/Karachi",
	"gilgit":                         "Asia/Karachi",
	"kasur":                          "Asia/Karachi",
	"kundian":                        "Asia/Karachi",
	"okara":                          "Asia/Karachi",
	"jhang":                          "Asia/Karachi",
	"sargodha":                       "Asia/Karachi",
	"deraghazikhan":                  "Asia/Karachi",
	"sadiqabad":                      "Asia/Karachi",
	"nawabshah":                      "Asia/Karachi",
	"bannu":                          "Asia/Karachi",
	"deraismailkhan":                 "Asia/Karachi",
	"chaman":                         "Asia/Karachi",
	"turbat":                         "Asia/Karachi",
	"faisalabad":                     "Asia/Karachi",
	"rawalpindi":                     "Asia/Karachi",
	"bahawalpur":                     "Asia/Karachi",
	"mirputkhas":                     "Asia/Karachi",
	"sukkur":                         "Asia/Karachi",
	"saidu":                          "Asia/Karachi",
	"gujranwala":                     "Asia/Karachi",
	"quetta":                         "Asia/Karachi",
	"larkana":                        "Asia/Karachi",
	"islamabad":                      "Asia/Karachi",
	"multan":                         "Asia/Karachi",
	"peshawar":                       "Asia/Karachi",
	"lahore":                         "Asia/Karachi",
	"karachi":                        "Asia/Karachi",
	"koror":                          "Pacific/Palau",
	"melekeok":                       "Pacific/Palau",
	"ramallah":                       "Asia/Hebron",
	"alkhalil":                       "Asia/Hebron",
	"nablus":                         "Asia/Hebron",
	"gaza":                           "Asia/Gaza",
	"elporvenir":                     "America/Panama",
	"penonome":                       "America/Panama",
	"chitre":                         "America/Panama",
	"jaque":                          "America/Panama",
	"bocasdeltoro":                   "America/Panama",
	"almirante":                      "America/Panama",
	"lastablas":                      "America/Panama",
	"lapalma":                        "America/Panama",
	"balboa":                         "America/Panama",
	"puertoarmuelles":                "America/Panama",
	"david":                          "America/Panama",
	"panamacity":                     "America/Chicago",
	"wabag":                          "Pacific/Port_Moresby",
	"vanimo":                         "Pacific/Port_Moresby",
	"kundiawa":                       "Pacific/Port_Moresby",
	"kerema":                         "Pacific/Port_Moresby",
	"arawa":                          "Pacific/Bougainville",
	"lorengau":                       "Pacific/Port_Moresby",
	"kimbe":                          "Pacific/Port_Moresby",
	"daru":                           "Pacific/Port_Moresby",
	"sohano":                         "Pacific/Bougainville",
	"kieta":                          "Pacific/Bougainville",
	"mendi":                          "Pacific/Port_Moresby",
	"abau":                           "Pacific/Port_Moresby",
	"alotau":                         "Pacific/Port_Moresby",
	"popondetta":                     "Pacific/Port_Moresby",
	"hoskins":                        "Pacific/Port_Moresby",
	"wewak":                          "Pacific/Port_Moresby",
	"madang":                         "Pacific/Port_Moresby",
	"kavieng":                        "Pacific/Port_Moresby",
	"goroka":                         "Pacific/Port_Moresby",
	"mt.hagen":                       "Pacific/Port_Moresby",
	"rabaul":                         "Pacific/Port_Moresby",
	"lae":                            "Pacific/Port_Moresby",
	"portmoresby":                    "Pacific/Port_Moresby",
	"mariscaljosef.estigarribia":     "America/Asuncion",
	"caacupe":                        "America/Asuncion",
	"generaleugenioalejandrinogaray": "America/Asuncion",
	"arroyosyesteros":                "America/Asuncion",
	"villahayes":                     "America/Asuncion",
	"fortinfalcon":                   "America/Asuncion",
	"puertopinasco":                  "America/Asuncion",
	"pozocolorado":                   "America/Asuncion",
	"ypacarai":                       "America/Asuncion",
	"sanjuanbautista":                "America/Asuncion",
	"paraguari":                      "America/Asuncion",
	"nacunday":                       "America/Asuncion",
	"coroneloviedo":                  "America/Asuncion",
	"caazapa":                        "America/Asuncion",
	"ypejhu":                         "America/Asuncion",
	"encarnacion":                    "America/Asuncion",
	"coronelbogado":                  "America/Asuncion",
	"fuerteolimpo":                   "America/Asuncion",
	"capitanpablolagerenza":          "America/Asuncion",
	"lavictoria":                     "America/Asuncion",
	"horqueta":                       "America/Asuncion",
	"ita":                            "America/Asuncion",
	"pilar":                          "America/Asuncion",
	"pedrojuancaballero":             "America/Asuncion",
	"bellavista":                     "America/Asuncion",
	"abai":                           "America/Asuncion",
	"ygatimi":                        "America/Asuncion",
	"hohenau":                        "America/Asuncion",
	"villarrica":                     "America/Asuncion",
	"filadelfia":                     "America/Asuncion",
	"ciudaddeleste":                  "America/Asuncion",
	"asuncion":                       "America/Asuncion",
	"ferrenafe":                      "America/Lima",
	"motupe":                         "America/Lima",
	"mollendo":                       "America/Lima",
	"urubamba":                       "America/Lima",
	"santotomas":                     "America/Lima",
	"putina":                         "America/Lima",
	"casma":                          "America/Lima",
	"tournavista":                    "America/Lima",
	"huamachuco":                     "America/Lima",
	"otuzco":                         "America/Lima",
	"lamas":                          "America/Lima",
	"nauta":                          "America/Lima",
	"puquio":                         "America/Lima",
	"chosica":                        "America/Lima",
	"satipo":                         "America/Lima",
	"tarma":                          "America/Lima",
	"laoroya":                        "America/Lima",
	"huaura":                         "America/Lima",
	"huacho":                         "America/Lima",
	"pimentel":                       "America/Lima",
	"olmos":                          "America/Lima",
	"sechura":                        "America/Lima",
	"chulucanas":                     "America/Lima",
	"sullana":                        "America/Lima",
	"abancay":                        "America/Lima",
	"camana":                         "America/Lima",
	"sicuani":                        "America/Lima",
	"puno":                           "America/Lima",
	"ayaviri":                        "America/Lima",
	"ilave":                          "America/Lima",
	"desaguadero":                    "America/Lima",
	"huarmey":                        "America/Lima",
	"cajabamba":                      "America/Lima",
	"jaen":                           "Europe/Madrid",
	"chota":                          "America/Lima",
	"tingomaria":                     "America/Lima",
	"moyobamba":                      "America/Lima",
	"juanjui":                        "America/Lima",
	"tocache":                        "America/Lima",
	"chachapoyas":                    "America/Lima",
	"caballococha":                   "America/Lima",
	"pucaurco":                       "America/Lima",
	"andoas":                         "America/Lima",
	"soldadobartra":                  "America/Lima",
	"nuevorocafuerte":                "America/Guayaquil",
	"requena":                        "America/Lima",
	"huanta":                         "America/Lima",
	"coracora":                       "America/Lima",
	"chinchaalta":                    "America/Lima",
	"jauja":                          "America/Lima",
	"pativilca":                      "America/Lima",
	"chancay":                        "America/Lima",
	"chilca":                         "America/Lima",
	"chiclayo":                       "America/Lima",
	"juliaca":                        "America/Lima",
	"cerrodepasco":                   "America/Lima",
	"tarapoto":                       "America/Lima",
	"ayacucho":                       "America/Lima",
	"callao":                         "America/Lima",
	"paita":                          "America/Lima",
	"talara":                         "America/Lima",
	"tumbes":                         "America/Lima",
	"puertomaldonado":                "America/Lima",
	"ilo":                            "America/Lima",
	"moquegua":                       "America/Lima",
	"huaraz":                         "America/Lima",
	"cajamarca":                      "America/Lima",
	"huanuco":                        "America/Lima",
	"pacasmayo":                      "America/Lima",
	"salaverry":                      "America/Lima",
	"gueppi":                         "America/Lima",
	"contamana":                      "America/Lima",
	"huancavelica":                   "America/Lima",
	"pisco":                          "America/Lima",
	"nasca":                          "America/Lima",
	"piura":                          "America/Lima",
	"arequipa":                       "America/Lima",
	"chimbote":                       "America/Lima",
	"pucallpa":                       "America/Lima",
	"iquitos":                        "America/Lima",
	"huancayo":                       "America/Lima",
	"cusco":                          "America/Lima",
	"tacna":                          "America/Lima",
	"ica":                            "America/Lima",
	"lima":                           "America/New_York",
	"cadiz":                          "Europe/Madrid",
	"pagadian":                       "Asia/Manila",
	"ozamis":                         "Asia/Manila",
	"tarlac":                         "Asia/Manila",
	"cabanatuan":                     "Asia/Manila",
	"olongapo":                       "Asia/Manila",
	"dagupan":                        "Asia/Manila",
	"sanpablo":                       "Asia/Manila",
	"quezoncity":                     "Asia/Manila",
	"pasaycity":                      "Asia/Manila",
	"iligan":                         "Asia/Manila",
	"ormac":                          "Asia/Manila",
	"tacloban":                       "Asia/Manila",
	"butuan":                         "Asia/Manila",
	"tagum":                          "Asia/Manila",
	"surigao":                        "Asia/Manila",
	"gingoog":                        "Asia/Manila",
	"legazpi":                        "Asia/Manila",
	"tuguegarao":                     "Asia/Manila",
	"vigan":                          "Asia/Manila",
	"bacolod":                        "Asia/Manila",
	"roxas":                          "Asia/Manila",
	"puertoprincesa":                 "Asia/Manila",
	"naga":                           "Asia/Manila",
	"angeles":                        "Asia/Manila",
	"batangas":                       "Asia/Manila",
	"cotabato":                       "Asia/Manila",
	"calbayog":                       "Asia/Manila",
	"cagayandeoro":                   "Asia/Manila",
	"zamboanga":                      "Asia/Manila",
	"laoag":                          "Asia/Manila",
	"baguiocity":                     "Asia/Manila",
	"generalsantos":                  "Asia/Manila",
	"cebu":                           "Asia/Manila",
	"iloilo":                         "Asia/Manila",
	"davao":                          "Asia/Manila",
	"manila":                         "Asia/Manila",
	"olsztyn":                        "Europe/Warsaw",
	"elblag":                         "Europe/Warsaw",
	"inowroclaw":                     "Europe/Warsaw",
	"bytom":                          "Europe/Warsaw",
	"opole":                          "Europe/Warsaw",
	"koszalin":                       "Europe/Warsaw",
	"elk":                            "Europe/Warsaw",
	"gdynia":                         "Europe/Warsaw",
	"wroclaw":                        "Europe/Warsaw",
	"szczecin":                       "Europe/Warsaw",
	"zielonagora":                    "Europe/Warsaw",
	"poznan":                         "Europe/Warsaw",
	"grudziadz":                      "Europe/Warsaw",
	"bydgoszcz":                      "Europe/Warsaw",
	"katowice":                       "Europe/Warsaw",
	"gliwice":                        "Europe/Warsaw",
	"kielce":                         "Europe/Warsaw",
	"bialystok":                      "Europe/Warsaw",
	"lublin":                         "Europe/Warsaw",
	"rzeszow":                        "Europe/Warsaw",
	"lodz":                           "Europe/Warsaw",
	"gdansk":                         "Europe/Warsaw",
	"krakow":                         "Europe/Warsaw",
	"warsaw":                         "Europe/Warsaw",
	"aveiro":                         "Europe/Lisbon",
	"leiria":                         "Europe/Lisbon",
	"vianadocastelo":                 "Europe/Lisbon",
	"beja":                           "Africa/Tunis",
	"evora":                          "Europe/Lisbon",
	"portalegre":                     "Europe/Lisbon",
	"castelobranco":                  "Europe/Lisbon",
	"guarda":                         "Europe/Lisbon",
	"vilareal":                       "Europe/Lisbon",
	"braga":                          "Europe/Lisbon",
	"covilha":                        "Europe/Lisbon",
	"horta":                          "Atlantic/Azores",
	"angradoheroismo":                "Atlantic/Azores",
	"portimao":                       "Europe/Lisbon",
	"faro":                           "Europe/Lisbon",
	"coimbra":                        "Europe/Lisbon",
	"setubal":                        "Europe/Lisbon",
	"porto":                          "Europe/Lisbon",
	"funchal":                        "Atlantic/Madeira",
	"pontadelgada":                   "Atlantic/Azores",
	"lisbon":                         "Europe/Lisbon",
	"ponce":                          "America/Puerto_Rico",
	"mayaguez":                       "America/Puerto_Rico",
	"arecibo":                        "America/Puerto_Rico",
	"doha":                           "Asia/Qatar",
	"targujiu":                       "Europe/Bucharest",
	"slatina":                        "Europe/Bucharest",
	"targoviste":                     "Europe/Bucharest",
	"giurgiu":                        "Europe/Bucharest",
	"slobozia":                       "Europe/Bucharest",
	"albalulia":                      "Europe/Bucharest",
	"bistrita":                       "Europe/Bucharest",
	"deva":                           "Europe/Bucharest",
	"zalau":                          "Europe/Bucharest",
	"satumare":                       "Europe/Bucharest",
	"rimnicuvilcea":                  "Europe/Bucharest",
	"sfintu-gheorghe":                "Europe/Bucharest",
	"miercurea-cuic":                 "Europe/Bucharest",
	"piatra-neamt":                   "Europe/Bucharest",
	"braila":                         "Europe/Bucharest",
	"vaslui":                         "Europe/Bucharest",
	"drobeta-turnuseverin":           "Europe/Bucharest",
	"tulcea":                         "Europe/Bucharest",
	"arad":                           "Europe/Bucharest",
	"oradea":                         "Europe/Bucharest",
	"sibiu":                          "Europe/Bucharest",
	"suceava":                        "Europe/Bucharest",
	"buzau":                          "Europe/Bucharest",
	"galati":                         "Europe/Bucharest",
	"focsani":                        "Europe/Bucharest",
	"craiova":                        "Europe/Bucharest",
	"calarasi":                       "Europe/Bucharest",
	"resita":                         "Europe/Bucharest",
	"timisoara":                      "Europe/Bucharest",
	"botosani":                       "Europe/Bucharest",
	"baiamare":                       "Europe/Bucharest",
	"tirgumures":                     "Europe/Bucharest",
	"pitesti":                        "Europe/Bucharest",
	"brasov":                         "Europe/Bucharest",
	"ploiesti":                       "Europe/Bucharest",
	"bacau":                          "Europe/Bucharest",
	"cluj-napoca":                    "Europe/Bucharest",
	"constanta":                      "Europe/Bucharest",
	"iasi":                           "Europe/Bucharest",
	"bucharest":                      "Europe/Bucharest",
	"nazran":                         "Europe/Moscow",
	"ust'ordynskiy":                  "Asia/Irkutsk",
	"maykop":                         "Europe/Moscow",
	"mozdok":                         "Europe/Moscow",
	"georgievsk":                     "Europe/Moscow",
	"pyatigorsk":                     "Europe/Moscow",
	"kislovodsk":                     "Europe/Moscow",
	"nevinnomyssk":                   "Europe/Moscow",
	"enurmino":                       "Asia/Anadyr",
	"lavrentiya":                     "Asia/Anadyr",
	"zvezdnyy":                       "Asia/Anadyr",
	"mikhalkino":                     "Asia/Srednekolymsk",
	"chernyakhovsk":                  "Europe/Kaliningrad",
	"severomorsk":                    "Europe/Moscow",
	"apatity":                        "Europe/Moscow",
	"polyarnyy":                      "Europe/Moscow",
	"slantsy":                        "Europe/Moscow",
	"kolpino":                        "Europe/Moscow",
	"novozybkov":                     "Europe/Moscow",
	"dyatkovo":                       "Europe/Moscow",
	"shuya":                          "Europe/Moscow",
	"kineshma":                       "Europe/Moscow",
	"balakhna":                       "Europe/Moscow",
	"arzamas":                        "Europe/Moscow",
	"rzhev":                          "Europe/Moscow",
	"vyshnniyvolochek":               "Europe/Moscow",
	"uglich":                         "Europe/Moscow",
	"yelets":                         "Europe/Moscow",
	"orekhovo-zuevo":                 "Europe/Moscow",
	"klin":                           "Europe/Moscow",
	"sergiyevposad":                  "Europe/Moscow",
	"kolomna":                        "Europe/Moscow",
	"bataysk":                        "Europe/Moscow",
	"taganrog":                       "Europe/Moscow",
	"novocherkassk":                  "Europe/Moscow",
	"kamenskshakhtinskiy":            "Europe/Moscow",
	"novoshakhtinsk":                 "Europe/Moscow",
	"aleksin":                        "Europe/Moscow",
	"novomoskovsk":                   "Europe/Moscow",
	"shchekino":                      "Europe/Moscow",
	"nikolayevsk":                    "Europe/Volgograd",
	"shebekino":                      "Europe/Moscow",
	"gubkin":                         "Europe/Moscow",
	"apsheronsk":                     "Europe/Moscow",
	"kropotkin":                      "Europe/Moscow",
	"ruzayevka":                      "Europe/Moscow",
	"kirsanov":                       "Europe/Moscow",
	"michurinsk":                     "Europe/Moscow",
	"borisoglebsk":                   "Europe/Moscow",
	"oktyabrskiy":                    "Asia/Kamchatka",
	"plast":                          "Asia/Yekaterinburg",
	"bakal":                          "Asia/Yekaterinburg",
	"verkhniyufaley":                 "Asia/Yekaterinburg",
	"severnyy":                       "Europe/Moscow",
	"kirovo-chepetsk":                "Europe/Kirov",
	"krasnoturinsk":                  "Asia/Yekaterinburg",
	"asbest":                         "Asia/Yekaterinburg",
	"alapayevsk":                     "Asia/Yekaterinburg",
	"krasnouralsk":                   "Asia/Yekaterinburg",
	"severouralsk":                   "Asia/Yekaterinburg",
	"novotroitsk":                    "Asia/Yekaterinburg",
	"buguruslan":                     "Asia/Yekaterinburg",
	"chapayevsk":                     "Europe/Samara",
	"syzran":                         "Europe/Samara",
	"novokuybishevsk":                "Europe/Samara",
	"naberezhnyyechelny":             "Europe/Moscow",
	"zelenodolsk":                    "Europe/Moscow",
	"leninogorsk":                    "Europe/Moscow",
	"bugulma":                        "Europe/Moscow",
	"nefteyugansk":                   "Asia/Yekaterinburg",
	"leninskkuznetsky":               "Asia/Novokuznetsk",
	"anzherosudzhensk":               "Asia/Novokuznetsk",
	"kiselevsk":                      "Asia/Novokuznetsk",
	"mundybash":                      "Asia/Novokuznetsk",
	"chernogorsk":                    "Asia/Krasnoyarsk",
	"abaza":                          "Asia/Krasnoyarsk",
	"iskitim":                        "Asia/Novosibirsk",
	"toguchin":                       "Asia/Novosibirsk",
	"kupina":                         "Asia/Novosibirsk",
	"zaozernyy":                      "Asia/Krasnoyarsk",
	"bogotol":                        "Asia/Krasnoyarsk",
	"shilka":                         "Asia/Chita",
	"sherlovayagora":                 "Asia/Chita",
	"petrovskzabaykalskiy":           "Asia/Chita",
	"arsenyev":                       "Asia/Vladivostok",
	"partizansk":                     "Asia/Vladivostok",
	"dalnerechensk":                  "Asia/Vladivostok",
	"zemlyabunge":                    "Asia/Vladivostok",
	"khorgo":                         "Asia/Yakutsk",
	"putlenina":                      "Asia/Yakutsk",
	"obluchye":                       "Asia/Vladivostok",
	"vanino":                         "Asia/Vladivostok",
	"omchak":                         "Asia/Magadan",
	"uglegorsk":                      "Asia/Sakhalin",
	"kholmsk":                        "Asia/Sakhalin",
	"solikamsk":                      "Asia/Yekaterinburg",
	"kizel":                          "Asia/Yekaterinburg",
	"pakhachi":                       "Asia/Kamchatka",
	"timiryazevskiy":                 "Asia/Tomsk",
	"asino":                          "Asia/Tomsk",
	"strezhevoy":                     "Asia/Tomsk",
	"cherkessk":                      "Europe/Moscow",
	"vladikavkaz":                    "Europe/Moscow",
	"blagodarnyy":                    "Europe/Moscow",
	"zelenokumsk":                    "Europe/Moscow",
	"mukhomornoye":                   "Asia/Anadyr",
	"beringovskiy":                   "Asia/Anadyr",
	"bilibino":                       "Asia/Anadyr",
	"mysshmidta":                     "Asia/Anadyr",
	"egvekinot":                      "Asia/Anadyr",
	"sovetsk":                        "Europe/Kirov",
	"nikel":                          "Europe/Moscow",
	"monchegorsk":                    "Europe/Moscow",
	"kirovsk":                        "Europe/Moscow",
	"borovichi":                      "Europe/Moscow",
	"starayarussa":                   "Europe/Moscow",
	"volkhov":                        "Europe/Moscow",
	"tikhvin":                        "Europe/Moscow",
	"svetogorsk":                     "Europe/Moscow",
	"gatchina":                       "Europe/Moscow",
	"luga":                           "Europe/Moscow",
	"klintsy":                        "Europe/Moscow",
	"roslavl":                        "Europe/Moscow",
	"safonovo":                       "Europe/Moscow",
	"vyazma":                         "Europe/Moscow",
	"segezha":                        "Europe/Moscow",
	"vichuga":                        "Europe/Moscow",
	"sharya":                         "Europe/Moscow",
	"buy":                            "Europe/Moscow",
	"dzerzhinsk":                     "Europe/Moscow",
	"vyska":                          "Europe/Moscow",
	"kimry":                          "Europe/Moscow",
	"bezhetsk":                       "Europe/Moscow",
	"nelidovo":                       "Europe/Moscow",
	"bologoye":                       "Europe/Moscow",
	"torzhok":                        "Europe/Moscow",
	"sokol":                          "Europe/Moscow",
	"cherepovets":                    "Europe/Moscow",
	"rybinsk":                        "Europe/Moscow",
	"rostov":                         "Europe/Moscow",
	"kaluga":                         "Europe/Moscow",
	"kirov":                          "Europe/Kirov",
	"obninsk":                        "Europe/Moscow",
	"lgov":                           "Europe/Moscow",
	"zheleznogorsk":                  "Europe/Moscow",
	"gryazi":                         "Europe/Moscow",
	"yegoryevsk":                     "Europe/Moscow",
	"podolsk":                        "Europe/Moscow",
	"solnechnogorsk":                 "Europe/Moscow",
	"noginsk":                        "Asia/Krasnoyarsk",
	"serpukhov":                      "Europe/Moscow",
	"livny":                          "Europe/Moscow",
	"mtsensk":                        "Europe/Moscow",
	"salsk":                          "Europe/Moscow",
	"belayakalitva":                  "Europe/Moscow",
	"shakhty":                        "Europe/Moscow",
	"millerovo":                      "Europe/Moscow",
	"yefremov":                       "Europe/Moscow",
	"bogoroditsk":                    "Europe/Moscow",
	"kamyshin":                       "Europe/Volgograd",
	"pallasovka":                     "Europe/Volgograd",
	"frolovo":                        "Europe/Volgograd",
	"volzhskiy":                      "Europe/Volgograd",
	"mikhaylovka":                    "Europe/Volgograd",
	"uryupinsk":                      "Europe/Volgograd",
	"starsyoskol":                    "Europe/Moscow",
	"alekseyevka":                    "Europe/Moscow",
	"valuyki":                        "Europe/Moscow",
	"tuapse":                         "Europe/Moscow",
	"gelendzhik":                     "Europe/Moscow",
	"labinsk":                        "Europe/Moscow",
	"armavir":                        "Europe/Moscow",
	"timashevsk":                     "Europe/Moscow",
	"tikhoretsk":                     "Europe/Moscow",
	"yeysk":                          "Europe/Moscow",
	"saransk":                        "Europe/Moscow",
	"kamenka":                        "Europe/Moscow",
	"kuznetsk":                       "Europe/Moscow",
	"serdobsk":                       "Europe/Moscow",
	"kasimov":                        "Europe/Moscow",
	"sasovo":                         "Europe/Moscow",
	"kotovsk":                        "Europe/Moscow",
	"morshansk":                      "Europe/Moscow",
	"kovrov":                         "Europe/Moscow",
	"murom":                          "Europe/Moscow",
	"rayevskiy":                      "Asia/Yekaterinburg",
	"sibay":                          "Asia/Yekaterinburg",
	"kumertau":                       "Asia/Yekaterinburg",
	"salavat":                        "Asia/Yekaterinburg",
	"belebey":                        "Asia/Yekaterinburg",
	"tuymazy":                        "Asia/Yekaterinburg",
	"neftekamsk":                     "Asia/Yekaterinburg",
	"troitsk":                        "Asia/Yekaterinburg",
	"yemanzhelinsk":                  "Asia/Yekaterinburg",
	"kartaly":                        "Asia/Yekaterinburg",
	"asha":                           "Asia/Yekaterinburg",
	"miass":                          "Asia/Yekaterinburg",
	"kyshtym":                        "Asia/Yekaterinburg",
	"kurtamysh":                      "Asia/Yekaterinburg",
	"shadrinsk":                      "Asia/Yekaterinburg",
	"varnek":                         "Europe/Moscow",
	"bugrino":                        "Europe/Moscow",
	"yamburg":                        "Asia/Yekaterinburg",
	"nakhodka":                       "Asia/Vladivostok",
	"sosnogorsk":                     "Europe/Moscow",
	"slobodskoy":                     "Europe/Kirov",
	"kirs":                           "Europe/Kirov",
	"omutninsk":                      "Europe/Kirov",
	"kotelnich":                      "Europe/Kirov",
	"yoshkarola":                     "Europe/Moscow",
	"kamenskuralskiy":                "Asia/Yekaterinburg",
	"polevskoy":                      "Asia/Yekaterinburg",
	"tavda":                          "Asia/Yekaterinburg",
	"artemovskiy":                    "Asia/Yekaterinburg",
	"nevyansk":                       "Asia/Yekaterinburg",
	"verkhnyayasalda":                "Asia/Yekaterinburg",
	"nizhnyayatura":                  "Asia/Yekaterinburg",
	"karpinsk":                       "Asia/Yekaterinburg",
	"ivdel":                          "Asia/Yekaterinburg",
	"krasnoufimsk":                   "Asia/Yekaterinburg",
	"sarapul":                        "Europe/Samara",
	"mozhga":                         "Europe/Samara",
	"votkinsk":                       "Europe/Samara",
	"glazov":                         "Europe/Samara",
	"kanash":                         "Europe/Moscow",
	"shumerlya":                      "Europe/Moscow",
	"alatyr":                         "Europe/Moscow",
	"sol-lletsk":                     "Asia/Yekaterinburg",
	"dombarovskiy":                   "Asia/Yekaterinburg",
	"mednogorsk":                     "Asia/Yekaterinburg",
	"gay":                            "Asia/Yekaterinburg",
	"buzuluk":                        "Asia/Yekaterinburg",
	"otradnyy":                       "Europe/Samara",
	"tolyatti":                       "Europe/Samara",
	"engels":                         "Europe/Saratov",
	"pugachev":                       "Europe/Saratov",
	"volsk":                          "Europe/Saratov",
	"atkarsk":                        "Europe/Saratov",
	"balashov":                       "Europe/Saratov",
	"almetyevsk":                     "Europe/Moscow",
	"chistopol":                      "Europe/Moscow",
	"nizhnekamsk":                    "Europe/Moscow",
	"dimitrovgrad":                   "Europe/Ulyanovsk",
	"peregrebnoye":                   "Asia/Yekaterinburg",
	"saranpaul":                      "Asia/Yekaterinburg",
	"uray":                           "Asia/Yekaterinburg",
	"laryak":                         "Asia/Yekaterinburg",
	"kogalym":                        "Asia/Yekaterinburg",
	"megion":                         "Asia/Yekaterinburg",
	"cherlak":                        "Asia/Omsk",
	"kalachinsk":                     "Asia/Omsk",
	"nazyvayevsk":                    "Asia/Omsk",
	"isikul":                         "Asia/Omsk",
	"ishim":                          "Asia/Yekaterinburg",
	"golyshmanovo":                   "Asia/Yekaterinburg",
	"yalutorovsk":                    "Asia/Yekaterinburg",
	"biysk":                          "Asia/Barnaul",
	"zmeinogorsk":                    "Asia/Barnaul",
	"aleysk":                         "Asia/Barnaul",
	"novoaltaysk":                    "Asia/Barnaul",
	"kamennaobi":                     "Asia/Barnaul",
	"gornyak":                        "Asia/Barnaul",
	"kulunda":                        "Asia/Barnaul",
	"slavgorod":                      "Asia/Barnaul",
	"tashtagol":                      "Asia/Novokuznetsk",
	"guryevsk":                       "Asia/Novokuznetsk",
	"yurga":                          "Asia/Novokuznetsk",
	"topki":                          "Asia/Novokuznetsk",
	"mariinsk":                       "Asia/Novokuznetsk",
	"shira":                          "Asia/Krasnoyarsk",
	"cherepanovo":                    "Asia/Novosibirsk",
	"kargat":                         "Asia/Novosibirsk",
	"ob":                             "Asia/Novosibirsk",
	"karasuk":                        "Asia/Novosibirsk",
	"barabinsk":                      "Asia/Novosibirsk",
	"tatarsk":                        "Asia/Novosibirsk",
	"kaspiysk":                       "Europe/Moscow",
	"derbent":                        "Europe/Moscow",
	"buynaksk":                       "Europe/Moscow",
	"yessey":                         "Asia/Krasnoyarsk",
	"ulkan":                          "Asia/Irkutsk",
	"kirensk":                        "Asia/Irkutsk",
	"zheleznogorskilimskiy":          "Asia/Irkutsk",
	"vikhorevka":                     "Asia/Irkutsk",
	"biryusinsk":                     "Asia/Irkutsk",
	"kodinskiy":                      "Asia/Krasnoyarsk",
	"artemovsk":                      "Asia/Krasnoyarsk",
	"uyar":                           "Asia/Krasnoyarsk",
	"uzhur":                          "Asia/Krasnoyarsk",
	"sayanogorsk":                    "Asia/Krasnoyarsk",
	"podkamennaya":                   "Asia/Krasnoyarsk",
	"igarka":                         "Asia/Krasnoyarsk",
	"agapa":                          "Asia/Krasnoyarsk",
	"boyarka":                        "Asia/Krasnoyarsk",
	"nordvik":                        "Asia/Krasnoyarsk",
	"chelyuskin":                     "Asia/Krasnoyarsk",
	"taksimo":                        "Asia/Irkutsk",
	"gusinoozyorsk":                  "Asia/Irkutsk",
	"aginskoye":                      "Asia/Chita",
	"progress":                       "Asia/Yakutsk",
	"belogorsk":                      "Asia/Yakutsk",
	"nyukzha":                        "Asia/Yakutsk",
	"nerchinsk":                      "Asia/Chita",
	"kavalerovo":                     "Asia/Vladivostok",
	"spasskdalniy":                   "Asia/Vladivostok",
	"shalaurova":                     "Asia/Vladivostok",
	"logashkino":                     "Asia/Srednekolymsk",
	"ustkuyga":                       "Asia/Vladivostok",
	"pokrovsk":                       "Asia/Yakutsk",
	"verkhnevilyuysk":                "Asia/Yakutsk",
	"vitim":                          "Asia/Yakutsk",
	"olyokminsk":                     "Asia/Yakutsk",
	"tunguskhaya":                    "Asia/Yakutsk",
	"natara":                         "Asia/Yakutsk",
	"zhilinda":                       "Asia/Yakutsk",
	"trofimovsk":                     "Asia/Yakutsk",
	"tukchi":                         "Asia/Vladivostok",
	"amursk":                         "Asia/Vladivostok",
	"bikin":                          "Asia/Vladivostok",
	"vyazemskiy":                     "Asia/Vladivostok",
	"chegdomyn":                      "Asia/Vladivostok",
	"siglan":                         "Asia/Magadan",
	"karamken":                       "Asia/Magadan",
	"strelka":                        "Asia/Magadan",
	"severokurilsk":                  "Asia/Srednekolymsk",
	"krasnogorsk":                    "Asia/Sakhalin",
	"poronaysk":                      "Asia/Sakhalin",
	"makarov":                        "Asia/Sakhalin",
	"dolinsk":                        "Asia/Sakhalin",
	"nevelsk":                        "Asia/Sakhalin",
	"kudymkar":                       "Asia/Yekaterinburg",
	"kungur":                         "Asia/Yekaterinburg",
	"krasnokamsk":                    "Asia/Yekaterinburg",
	"chusovoy":                       "Asia/Yekaterinburg",
	"gubakha":                        "Asia/Yekaterinburg",
	"utkholok":                       "Asia/Kamchatka",
	"bol'sheretsk":                   "Asia/Kamchatka",
	"il'pyrskiy":                     "Asia/Kamchatka",
	"korf":                           "Asia/Kamchatka",
	"kolpashevo":                     "Asia/Tomsk",
	"omolon":                         "Asia/Anadyr",
	"pevek":                          "Asia/Anadyr",
	"umba":                           "Europe/Moscow",
	"kovda":                          "Europe/Moscow",
	"velikiynovgorod":                "Europe/Moscow",
	"velikiyeluki":                   "Europe/Moscow",
	"belomorsk":                      "Europe/Moscow",
	"kem":                            "Europe/Moscow",
	"krasino":                        "Europe/Moscow",
	"matochkinshar":                  "Europe/Moscow",
	"severodvinsk":                   "Europe/Moscow",
	"kursk":                          "Europe/Moscow",
	"tambov":                         "Europe/Moscow",
	"sterlitamak":                    "Asia/Yekaterinburg",
	"kurgan":                         "Asia/Yekaterinburg",
	"indiga":                         "Europe/Moscow",
	"shoyna":                         "Europe/Moscow",
	"novyyport":                      "Asia/Yekaterinburg",
	"salekhard":                      "Asia/Yekaterinburg",
	"gyda":                           "Asia/Yekaterinburg",
	"tazovskiy":                      "Asia/Yekaterinburg",
	"novyurengoy":                    "Asia/Yekaterinburg",
	"nadym":                          "Asia/Yekaterinburg",
	"noyabrsk":                       "Asia/Yekaterinburg",
	"syktyvkar":                      "Europe/Moscow",
	"ukhta":                          "Europe/Moscow",
	"serov":                          "Asia/Yekaterinburg",
	"cheboksary":                     "Europe/Moscow",
	"orsk":                           "Asia/Yekaterinburg",
	"balakovo":                       "Europe/Saratov",
	"igrim":                          "Asia/Yekaterinburg",
	"nyagan":                         "Asia/Yekaterinburg",
	"khantymansiysk":                 "Asia/Yekaterinburg",
	"nizhenvartovsk":                 "Asia/Yekaterinburg",
	"numto":                          "Asia/Yekaterinburg",
	"tara":                           "Asia/Omsk",
	"tobolsk":                        "Asia/Yekaterinburg",
	"rubtsovsk":                      "Asia/Barnaul",
	"gornoaltaysk":                   "Asia/Barnaul",
	"prokopyevsk":                    "Asia/Novokuznetsk",
	"makhachkala":                    "Europe/Moscow",
	"tura":                           "Asia/Krasnoyarsk",
	"yerema":                         "Asia/Irkutsk",
	"tayshet":                        "Asia/Irkutsk",
	"usolyesibirskoye":               "Asia/Irkutsk",
	"slyudyanka":                     "Asia/Irkutsk",
	"cheremkhovo":                    "Asia/Irkutsk",
	"zima":                           "Asia/Irkutsk",
	"tulun":                          "Asia/Irkutsk",
	"nizhneudinsk":                   "Asia/Irkutsk",
	"ustkut":                         "Asia/Irkutsk",
	"bodaybo":                        "Asia/Irkutsk",
	"komsa":                          "Asia/Krasnoyarsk",
	"kansk":                          "Asia/Krasnoyarsk",
	"achinsk":                        "Asia/Krasnoyarsk",
	"yeniseysk":                      "Asia/Krasnoyarsk",
	"lesosibirsk":                    "Asia/Krasnoyarsk",
	"turukhansk":                     "Asia/Krasnoyarsk",
	"vorontsovo":                     "Asia/Krasnoyarsk",
	"starorybnoye":                   "Asia/Krasnoyarsk",
	"mikhaylova":                     "Asia/Krasnoyarsk",
	"dudinka":                        "Asia/Krasnoyarsk",
	"teli":                           "Asia/Krasnoyarsk",
	"novyyuoyin":                     "Asia/Irkutsk",
	"bagdarin":                       "Asia/Irkutsk",
	"severobaykalsk":                 "Asia/Irkutsk",
	"kyakhta":                        "Asia/Irkutsk",
	"svobodnyy":                      "Asia/Yakutsk",
	"zeya":                           "Asia/Yakutsk",
	"magdagachi":                     "Asia/Yakutsk",
	"shimanovsk":                     "Asia/Yakutsk",
	"skovorodino":                    "Asia/Yakutsk",
	"tynda":                          "Asia/Yakutsk",
	"olovyannaya":                    "Asia/Chita",
	"mogocha":                        "Asia/Chita",
	"krasnokamensk":                  "Asia/Chita",
	"borzya":                         "Asia/Chita",
	"khilok":                         "Asia/Chita",
	"ussuriysk":                      "Asia/Vladivostok",
	"lesozavodsk":                    "Asia/Vladivostok",
	"kavache":                        "Asia/Vladivostok",
	"verkhoyansk":                    "Asia/Vladivostok",
	"cherskiy":                       "Asia/Srednekolymsk",
	"srednekolymsk":                  "Asia/Srednekolymsk",
	"zyryanka":                       "Asia/Srednekolymsk",
	"eldikan":                        "Asia/Khandyga",
	"chagda":                         "Asia/Khandyga",
	"khandyga":                       "Asia/Khandyga",
	"ustmaya":                        "Asia/Khandyga",
	"neryungri":                      "Asia/Yakutsk",
	"chernyshevskiy":                 "Asia/Yakutsk",
	"terbyas":                        "Asia/Yakutsk",
	"vilyuysk":                       "Asia/Yakutsk",
	"sangar":                         "Asia/Yakutsk",
	"menkere":                        "Asia/Yakutsk",
	"saskylakh":                      "Asia/Yakutsk",
	"govorovo":                       "Asia/Yakutsk",
	"sagastyr":                       "Asia/Yakutsk",
	"ustolensk":                      "Asia/Yakutsk",
	"suntar":                         "Asia/Yakutsk",
	"olenek":                         "Asia/Yakutsk",
	"udachnyy":                       "Asia/Yakutsk",
	"birobidzhan":                    "Asia/Vladivostok",
	"khakhar":                        "Asia/Vladivostok",
	"dekastri":                       "Asia/Vladivostok",
	"chumikan":                       "Asia/Vladivostok",
	"komsomolsknaamure":              "Asia/Vladivostok",
	"ayan":                           "Asia/Vladivostok",
	"nikolayevsknaamure":             "Asia/Vladivostok",
	"savetskayagavan":                "Asia/Vladivostok",
	"evensk":                         "Asia/Magadan",
	"palatka":                        "America/New_York",
	"omsukchan":                      "Asia/Magadan",
	"susuman":                        "Asia/Magadan",
	"nogliki":                        "Asia/Sakhalin",
	"aleksandrovsksakhalinskiy":      "Asia/Sakhalin",
	"korsakov":                       "Asia/Sakhalin",
	"manily":                         "Asia/Kamchatka",
	"klyuchi":                        "Asia/Kamchatka",
	"ustkamchatsk":                   "Asia/Kamchatka",
	"provideniya":                    "Asia/Anadyr",
	"uelen":                          "Asia/Anadyr",
	"kandalaksha":                    "Europe/Moscow",
	"vyborg":                         "Europe/Moscow",
	"kondopoga":                      "Europe/Moscow",
	"rusanovo":                       "Europe/Moscow",
	"mezen":                          "Europe/Moscow",
	"velsk":                          "Europe/Moscow",
	"kotlas":                         "Europe/Moscow",
	"onega":                          "Europe/Moscow",
	"ivanovo":                        "Europe/Moscow",
	"kostroma":                       "Europe/Moscow",
	"velikiyustyug":                  "Europe/Moscow",
	"lipetsk":                        "Europe/Moscow",
	"orel":                           "Europe/Moscow",
	"volgodonsk":                     "Europe/Moscow",
	"belgorod":                       "Europe/Moscow",
	"novorossiysk":                   "Europe/Moscow",
	"vladimir":                       "Europe/Moscow",
	"birsk":                          "Asia/Yekaterinburg",
	"zlatoust":                       "Asia/Yekaterinburg",
	"amderma":                        "Europe/Moscow",
	"naryanmar":                      "Europe/Moscow",
	"inta":                           "Europe/Moscow",
	"usinsk":                         "Europe/Moscow",
	"pechora":                        "Europe/Moscow",
	"pervouralsk":                    "Asia/Yekaterinburg",
	"izhevsk":                        "Europe/Samara",
	"akhtubinsk":                     "Europe/Astrakhan",
	"elista":                         "Europe/Moscow",
	"krasnoarmeysk":                  "Europe/Saratov",
	"berezniki":                      "Asia/Yekaterinburg",
	"naltchik":                       "Europe/Moscow",
	"stavropol":                      "Europe/Moscow",
	"ugolnyyekopi":                   "Asia/Anadyr",
	"kaliningrad":                    "Europe/Kaliningrad",
	"pskov":                          "Europe/Moscow",
	"bryansk":                        "Europe/Moscow",
	"smolensk":                       "Europe/Moscow",
	"petrozavodsk":                   "Europe/Moscow",
	"tver":                           "Europe/Moscow",
	"vologda":                        "Europe/Moscow",
	"yaroslavl":                      "Europe/Moscow",
	"sochi":                          "Europe/Moscow",
	"krasnodar":                      "Europe/Moscow",
	"ryazan":                         "Europe/Moscow",
	"voronezh":                       "Europe/Moscow",
	"magnitogorsk":                   "Asia/Yekaterinburg",
	"chelyabinsk":                    "Asia/Yekaterinburg",
	"vorkuta":                        "Europe/Moscow",
	"nizhnytagil":                    "Asia/Yekaterinburg",
	"astrakhan":                      "Europe/Astrakhan",
	"orenburg":                       "Asia/Yekaterinburg",
	"saratov":                        "Europe/Saratov",
	"ulyanovsk":                      "Europe/Ulyanovsk",
	"omsk":                           "Asia/Omsk",
	"tyumen":                         "Asia/Yekaterinburg",
	"novokuznetsk":                   "Asia/Novokuznetsk",
	"kemerovo":                       "Asia/Novokuznetsk",
	"groznyy":                        "Europe/Moscow",
	"ust-ulimsk":                     "Asia/Irkutsk",
	"angarsk":                        "Asia/Irkutsk",
	"abakan":                         "Asia/Krasnoyarsk",
	"norilsk":                        "Asia/Krasnoyarsk",
	"khatanga":                       "Asia/Krasnoyarsk",
	"kyzyl":                          "Asia/Krasnoyarsk",
	"ulanude":                        "Asia/Irkutsk",
	"blagoveshchensk":                "Asia/Yakutsk",
	"bukachacha":                     "Asia/Chita",
	"dalnegorsk":                     "Asia/Vladivostok",
	"ambarchik":                      "Asia/Srednekolymsk",
	"batagay":                        "Asia/Vladivostok",
	"chokurdakh":                     "Asia/Srednekolymsk",
	"ustnera":                        "Asia/Ust-Nera",
	"lensk":                          "Asia/Yakutsk",
	"aldan":                          "Asia/Yakutsk",
	"mirnyy":                         "Asia/Yakutsk",
	"zhigansk":                       "Asia/Yakutsk",
	"okhotsk":                        "Asia/Vladivostok",
	"khabarovsk":                     "Asia/Vladivostok",
	"okha":                           "Asia/Sakhalin",
	"yuzhnosakhalinsk":               "Asia/Sakhalin",
	"tomsk":                          "Asia/Tomsk",
	"anadyr":                         "Asia/Anadyr",
	"murmansk":                       "Europe/Moscow",
	"archangel":                      "Europe/Moscow",
	"nizhnynovgorod":                 "Europe/Moscow",
	"volgograd":                      "Europe/Volgograd",
	"ufa":                            "Asia/Yekaterinburg",
	"yekaterinburg":                  "Asia/Yekaterinburg",
	"samara":                         "Europe/Samara",
	"kazan":                          "Europe/Moscow",
	"surgut":                         "Asia/Yekaterinburg",
	"barnaul":                        "Asia/Barnaul",
	"novosibirsk":                    "Asia/Novosibirsk",
	"bratsk":                         "Asia/Irkutsk",
	"irkutsk":                        "Asia/Irkutsk",
	"krasnoyarsk":                    "Asia/Krasnoyarsk",
	"dickson":                        "Asia/Krasnoyarsk",
	"chita":                          "Asia/Chita",
	"vladivostok":                    "Asia/Vladivostok",
	"nizhneyansk":                    "Asia/Vladivostok",
	"yakutsk":                        "Asia/Yakutsk",
	"tiksi":                          "Asia/Yakutsk",
	"magadan":                        "Asia/Magadan",
	"palana":                         "Asia/Kamchatka",
	"petropavlovskkamchatskiy":       "Asia/Kamchatka",
	"st.petersburg":                  "America/New_York",
	"moscow":                         "Europe/Moscow",
	"gikongoro":                      "Africa/Kigali",
	"kibuye":                         "Africa/Kigali",
	"kibungo":                        "Africa/Kigali",
	"nyanza":                         "Africa/Kigali",
	"gitarama":                       "Africa/Kigali",
	"butare":                         "Africa/Kigali",
	"gisenyi":                        "Africa/Kigali",
	"cyangugu":                       "Africa/Kigali",
	"byumba":                         "Africa/Kigali",
	"ruhengeri":                      "Africa/Kigali",
	"kigali":                         "Africa/Kigali",
	"basseterre":                     "America/St_Kitts",
	"castries":                       "America/St_Lucia",
	"kingstown":                      "America/St_Vincent",
	"apia":                           "Pacific/Apia",
	"sanmarino":                      "Europe/San_Marino",
	"santoantonio":                   "Africa/Sao_Tome",
	"saotome":                        "Africa/Sao_Tome",
	"annabk":                         "Asia/Riyadh",
	"sakakah":                        "Asia/Riyadh",
	"yanbualbahr":                    "Asia/Riyadh",
	"dawmataljandal":                 "Asia/Riyadh",
	"qalatbishah":                    "Asia/Riyadh",
	"attaif":                         "Asia/Riyadh",
	"najran":                         "Asia/Riyadh",
	"alquwayiyah":                    "Asia/Riyadh",
	"almubarraz":                     "Asia/Riyadh",
	"al-qatif":                       "Asia/Riyadh",
	"azzahran":                       "Asia/Riyadh",
	"buraydah":                       "Asia/Riyadh",
	"hail":                           "Asia/Riyadh",
	"arar":                           "Asia/Riyadh",
	"rafha":                          "Asia/Riyadh",
	"alkharj":                        "Asia/Riyadh",
	"addamman":                       "Asia/Riyadh",
	"hafaralbatin":                   "Asia/Riyadh",
	"aljubayl":                       "Asia/Riyadh",
	"alqunfudhah":                    "Asia/Riyadh",
	"alhufuf":                        "Asia/Riyadh",
	"alwajh":                         "Asia/Riyadh",
	"abha":                           "Asia/Riyadh",
	"jizan":                          "Asia/Riyadh",
	"assulayyil":                     "Asia/Riyadh",
	"medina":                         "Asia/Riyadh",
	"tabuk":                          "Asia/Riyadh",
	"jeddah":                         "Asia/Riyadh",
	"makkah":                         "Asia/Riyadh",
	"riyadh":                         "Asia/Riyadh",
	"fatick":                         "Africa/Dakar",
	"diourbel":                       "Africa/Dakar",
	"louga":                          "Africa/Dakar",
	"thies":                          "Africa/Dakar",
	"kolda":                          "Africa/Dakar",
	"tambacounda":                    "Africa/Dakar",
	"kedougou":                       "Africa/Dakar",
	"ziguinchor":                     "Africa/Dakar",
	"kaolack":                        "Africa/Dakar",
	"kaedi":                          "Africa/Nouakchott",
	"dakar":                          "Africa/Dakar",
	"subotica":                       "Europe/Belgrade",
	"kragujevac":                     "Europe/Belgrade",
	"zrenjanin":                      "Europe/Belgrade",
	"nis":                            "Europe/Belgrade",
	"novisad":                        "Europe/Belgrade",
	"belgrade":                       "Europe/Belgrade",
	"makeni":                         "Africa/Freetown",
	"koidu":                          "Africa/Freetown",
	"kenema":                         "Africa/Freetown",
	"bo":                             "Africa/Freetown",
	"freetown":                       "Africa/Freetown",
	"singapore":                      "Asia/Singapore",
	"banskabystrica":                 "Europe/Bratislava",
	"trnava":                         "Europe/Bratislava",
	"zvolen":                         "Europe/Bratislava",
	"zilina":                         "Europe/Bratislava",
	"kosice":                         "Europe/Bratislava",
	"presov":                         "Europe/Bratislava",
	"bratislava":                     "Europe/Bratislava",
	"maribor":                        "Europe/Ljubljana",
	"ljubljana":                      "Europe/Ljubljana",
	"gizo":                           "Pacific/Guadalcanal",
	"lata":                           "Pacific/Guadalcanal",
	"honiara":                        "Pacific/Guadalcanal",
	"xuddur":                         "Africa/Mogadishu",
	"garbahaarey":                    "Africa/Mogadishu",
	"bu'aale":                        "Africa/Mogadishu",
	"dhuusamareeb":                   "Africa/Mogadishu",
	"buurhakaba":                     "Africa/Mogadishu",
	"luuq":                           "Africa/Mogadishu",
	"mandera":                        "Africa/Nairobi",
	"ferfer":                         "Africa/Mogadishu",
	"jawhar":                         "Africa/Mogadishu",
	"hurdiyo":                        "Africa/Mogadishu",
	"qardho":                         "Africa/Mogadishu",
	"caluula":                        "Africa/Mogadishu",
	"buurgaabo":                      "Africa/Mogadishu",
	"baydhabo":                       "Africa/Mogadishu",
	"marka":                          "Africa/Mogadishu",
	"mereeg":                         "Africa/Mogadishu",
	"beledweyne":                     "Africa/Mogadishu",
	"boosaaso":                       "Africa/Mogadishu",
	"bandarbeyla":                    "Africa/Mogadishu",
	"gaalkacyo":                      "Africa/Mogadishu",
	"eyl":                            "Africa/Mogadishu",
	"garoowe":                        "Africa/Mogadishu",
	"jamaame":                        "Africa/Mogadishu",
	"kismaayo":                       "Africa/Mogadishu",
	"mogadishu":                      "Africa/Mogadishu",
	"laascaanood":                    "Africa/Mogadishu",
	"ceerigaabo":                     "Africa/Mogadishu",
	"boorama":                        "Africa/Mogadishu",
	"burco":                          "Africa/Mogadishu",
	"maydh":                          "Africa/Mogadishu",
	"berbera":                        "Africa/Mogadishu",
	"hargeysa":                       "Africa/Mogadishu",
	"qacha'snek":                     "Africa/Maseru",
	"colesberg":                      "Africa/Johannesburg",
	"poffader":                       "Africa/Johannesburg",
	"prieska":                        "Africa/Johannesburg",
	"kuruman":                        "Africa/Johannesburg",
	"knysna":                         "Africa/Johannesburg",
	"swellendam":                     "Africa/Johannesburg",
	"hermanus":                       "Africa/Johannesburg",
	"paarl":                          "Africa/Johannesburg",
	"bredasdorp":                     "Africa/Johannesburg",
	"beaufortwest":                   "Africa/Johannesburg",
	"brits":                          "Africa/Johannesburg",
	"bloemhof":                       "Africa/Johannesburg",
	"potchefstroom":                  "Africa/Johannesburg",
	"brandfort":                      "Africa/Johannesburg",
	"bethlehem":                      "Africa/Johannesburg",
	"springs":                        "Africa/Johannesburg",
	"volksrust":                      "Africa/Johannesburg",
	"nelspruit":                      "Africa/Johannesburg",
	"komatipoort":                    "Africa/Johannesburg",
	"bethal":                         "Africa/Johannesburg",
	"standerton":                     "Africa/Johannesburg",
	"lebowakgomo":                    "Africa/Johannesburg",
	"tzaneen":                        "Africa/Johannesburg",
	"ulundi":                         "Africa/Johannesburg",
	"ladysmith":                      "Africa/Johannesburg",
	"portshepstone":                  "Africa/Johannesburg",
	"ubomba":                         "Africa/Johannesburg",
	"cradock":                        "Africa/Johannesburg",
	"uitenhage":                      "Africa/Johannesburg",
	"portalfred":                     "Africa/Johannesburg",
	"grahamstown":                    "Africa/Johannesburg",
	"portst.johns":                   "Africa/Johannesburg",
	"aliwalnorth":                    "Africa/Johannesburg",
	"benoni":                         "Africa/Johannesburg",
	"vereeniging":                    "Africa/Johannesburg",
	"deaar":                          "Africa/Johannesburg",
	"alexanderbay":                   "Africa/Johannesburg",
	"kimberley":                      "Africa/Johannesburg",
	"oudtshoorn":                     "Africa/Johannesburg",
	"vanhynsdorp":                    "Africa/Johannesburg",
	"saldanha":                       "Africa/Johannesburg",
	"mosselbay":                      "Africa/Johannesburg",
	"vryburg":                        "Africa/Johannesburg",
	"rustenburg":                     "Africa/Johannesburg",
	"mmabatho":                       "Africa/Johannesburg",
	"klerksdorp":                     "Africa/Johannesburg",
	"kroonstad":                      "Africa/Johannesburg",
	"polokwane":                      "Africa/Johannesburg",
	"thohoyandou":                    "Africa/Johannesburg",
	"musina":                         "Africa/Johannesburg",
	"vryheid":                        "Africa/Johannesburg",
	"pietermaritzburg":               "Africa/Johannesburg",
	"umtata":                         "Africa/Johannesburg",
	"graaffreinet":                   "Africa/Johannesburg",
	"bhisho":                         "Africa/Johannesburg",
	"springbok":                      "Africa/Johannesburg",
	"upington":                       "Africa/Johannesburg",
	"worcester":                      "America/New_York",
	"george":                         "Africa/Johannesburg",
	"welkom":                         "Africa/Johannesburg",
	"eastlondon":                     "Africa/Johannesburg",
	"bloemfontein":                   "Africa/Johannesburg",
	"pretoria":                       "Africa/Johannesburg",
	"portelizabeth":                  "Africa/Johannesburg",
	"durban":                         "Africa/Johannesburg",
	"johannesburg":                   "Africa/Johannesburg",
	"capetown":                       "Africa/Johannesburg",
	"grytviken":                      "Atlantic/South_Georgia",
	"eumseong":                       "Asia/Seoul",
	"cheongju":                       "Asia/Seoul",
	"wonju":                          "Asia/Seoul",
	"chuncheon":                      "Asia/Seoul",
	"ansan":                          "Asia/Seoul",
	"iksan":                          "Asia/Seoul",
	"gyeongju":                       "Asia/Seoul",
	"masan":                          "Asia/Seoul",
	"yeosu":                          "Asia/Seoul",
	"andong":                         "Asia/Seoul",
	"jeju":                           "Asia/Seoul",
	"gangneung":                      "Asia/Seoul",
	"sokcho":                         "Asia/Seoul",
	"jeonju":                         "Asia/Seoul",
	"gunsan":                         "Asia/Seoul",
	"mokpo":                          "Asia/Seoul",
	"puch'on":                        "Asia/Seoul",
	"songnam":                        "Asia/Seoul",
	"goyang":                         "Asia/Seoul",
	"suwon":                          "Asia/Seoul",
	"pohang":                         "Asia/Seoul",
	"ulsan":                          "Asia/Seoul",
	"daegu":                          "Asia/Seoul",
	"incheon":                        "Asia/Seoul",
	"daejeon":                        "Asia/Seoul",
	"gwangju":                        "Asia/Seoul",
	"busan":                          "Asia/Seoul",
	"seoul":                          "Asia/Seoul",
	"bentiu":                         "Africa/Juba",
	"maridi":                         "Africa/Juba",
	"yei":                            "Africa/Juba",
	"melut":                          "Africa/Juba",
	"nasir":                          "Africa/Juba",
	"gogrial":                        "Africa/Juba",
	"kapoeta":                        "Africa/Juba",
	"aweil":                          "Africa/Juba",
	"rumbek":                         "Africa/Juba",
	"yambio":                         "Africa/Juba",
	"bor":                            "Africa/Juba",
	"nimule":                         "Africa/Juba",
	"juba":                           "Africa/Juba",
	"malakal":                        "Africa/Juba",
	"wau":                            "Africa/Juba",
	"marbella":                       "Europe/Madrid",
	"algeciras":                      "Europe/Madrid",
	"mataro":                         "Europe/Madrid",
	"gijon":                          "Europe/Madrid",
	"almeria":                        "Europe/Madrid",
	"malaga":                         "Europe/Madrid",
	"huelva":                         "Europe/Madrid",
	"albacete":                       "Europe/Madrid",
	"toledo":                         "America/New_York",
	"santander":                      "Europe/Madrid",
	"burgos":                         "Europe/Madrid",
	"tarragona":                      "Europe/Madrid",
	"lorca":                          "Europe/Madrid",
	"oviedo":                         "Europe/Madrid",
	"santiagodecompostela":           "Europe/Madrid",
	"badajoz":                        "Europe/Madrid",
	"logrono":                        "Europe/Madrid",
	"sansebastian":                   "Europe/Madrid",
	"alicante":                       "Europe/Madrid",
	"castello":                       "Europe/Madrid",
	"arrecife":                       "Atlantic/Canary",
	"murcia":                         "Europe/Madrid",
	"ceuta":                          "Africa/Ceuta",
	"lacoruna":                       "Europe/Madrid",
	"ourense":                        "Europe/Madrid",
	"melilla":                        "Africa/Ceuta",
	"palma":                          "Europe/Madrid",
	"zaragoza":                       "Europe/Madrid",
	"santacruzdetenerife":            "Atlantic/Canary",
	"vigo":                           "Europe/Madrid",
	"bilbao":                         "Europe/Madrid",
	"laspalmas":                      "Atlantic/Canary",
	"seville":                        "Europe/Madrid",
	"valencia":                       "America/Caracas",
	"barcelona":                      "America/Caracas",
	"madrid":                         "Europe/Madrid",
	"trincomalee":                    "Asia/Colombo",
	"puttalan":                       "Asia/Colombo",
	"ratnapura":                      "Asia/Colombo",
	"batticaloa":                     "Asia/Colombo",
	"kilinochchi":                    "Asia/Colombo",
	"matara":                         "Asia/Colombo",
	"badulla":                        "Asia/Colombo",
	"moratuwa":                       "Asia/Colombo",
	"galle":                          "Asia/Colombo",
	"anuradhapura":                   "Asia/Colombo",
	"jaffna":                         "Asia/Colombo",
	"kandy":                          "Asia/Colombo",
	"srijawewardenepurakotte":        "Asia/Colombo",
	"colombo":                        "Asia/Colombo",
	"eddamazin":                      "Africa/Khartoum",
	"haiya":                          "Africa/Khartoum",
	"elmanaqil":                      "Africa/Khartoum",
	"shendi":                         "Africa/Khartoum",
	"berber":                         "Africa/Khartoum",
	"kerma":                          "Africa/Khartoum",
	"eddueim":                        "Africa/Khartoum",
	"ummruwaba":                      "Africa/Khartoum",
	"ennuhud":                        "Africa/Khartoum",
	"muglad":                         "Africa/Khartoum",
	"tokar":                          "Africa/Khartoum",
	"medani":                         "Africa/Khartoum",
	"gedaref":                        "Africa/Khartoum",
	"eddamer":                        "Africa/Khartoum",
	"atbara":                         "Africa/Khartoum",
	"wadihalfa":                      "Africa/Khartoum",
	"merowe":                         "Africa/Khartoum",
	"kosti":                          "Africa/Khartoum",
	"sennar":                         "Africa/Khartoum",
	"elfasher":                       "Africa/Khartoum",
	"kadugli":                        "Africa/Khartoum",
	"babanusa":                       "Africa/Khartoum",
	"geneina":                        "Africa/Khartoum",
	"omdurman":                       "Africa/Khartoum",
	"elobeid":                        "Africa/Khartoum",
	"portsudan":                      "Africa/Khartoum",
	"niyala":                         "Africa/Khartoum",
	"dongola":                        "Africa/Khartoum",
	"kassala":                        "Africa/Khartoum",
	"khartoum":                       "Africa/Khartoum",
	"onverwacht":                     "America/Paramaribo",
	"brownsweg":                      "America/Paramaribo",
	"moengo":                         "America/Paramaribo",
	"nieuwamsterdam":                 "America/Paramaribo",
	"nieuwnickerie":                  "America/Paramaribo",
	"brokopondo":                     "America/Paramaribo",
	"totness":                        "America/Paramaribo",
	"cottica":                        "America/Paramaribo",
	"paramaribo":                     "America/Paramaribo",
	"longyearbyen":                   "Arctic/Longyearbyen",
	"piggspeak":                      "Africa/Mbabane",
	"siteki":                         "Africa/Mbabane",
	"manzini":                        "Africa/Mbabane",
	"hlatikulu":                      "Africa/Mbabane",
	"golela":                         "Africa/Mbabane",
	"lobamba":                        "Africa/Mbabane",
	"mbabane":                        "Africa/Mbabane",
	"falun":                          "Europe/Stockholm",
	"nykoping":                       "Europe/Stockholm",
	"harnosand":                      "Europe/Stockholm",
	"karlskrona":                     "Europe/Stockholm",
	"mariestad":                      "Europe/Stockholm",
	"vannersborg":                    "Europe/Stockholm",
	"borlange":                       "Europe/Stockholm",
	"vasteraas":                      "Europe/Stockholm",
	"bollnas":                        "Europe/Stockholm",
	"gavle":                          "Europe/Stockholm",
	"kalmar":                         "Europe/Stockholm",
	"vaxjo":                          "Europe/Stockholm",
	"orebro":                         "Europe/Stockholm",
	"norrkoping":                     "Europe/Stockholm",
	"halmstad":                       "Europe/Stockholm",
	"karlstad":                       "Europe/Stockholm",
	"skelleftea":                     "Europe/Stockholm",
	"visby":                          "Europe/Stockholm",
	"trollhattan":                    "Europe/Stockholm",
	"boras":                          "Europe/Stockholm",
	"kristianstad":                   "Europe/Stockholm",
	"helsingborg":                    "Europe/Stockholm",
	"jonkoping":                      "Europe/Stockholm",
	"ornskoldsvik":                   "Europe/Stockholm",
	"linkoping":                      "Europe/Stockholm",
	"ostersund":                      "Europe/Stockholm",
	"kiruna":                         "Europe/Stockholm",
	"umea":                           "Europe/Stockholm",
	"uppsala":                        "Europe/Stockholm",
	"goteborg":                       "Europe/Stockholm",
	"lulea":                          "Europe/Stockholm",
	"sundsvall":                      "Europe/Stockholm",
	"malmo":                          "Europe/Stockholm",
	"stockholm":                      "Europe/Stockholm",
	"delemont":                       "Europe/Zurich",
	"neuchatel":                      "Europe/Zurich",
	"aarau":                          "Europe/Zurich",
	"stans":                          "Europe/Zurich",
	"sion":                           "Europe/Zurich",
	"herisau":                        "Europe/Zurich",
	"saintgallen":                    "Europe/Zurich",
	"bellinzona":                     "Europe/Zurich",
	"glarus":                         "Europe/Zurich",
	"schaffhausen":                   "Europe/Zurich",
	"schwyz":                         "Europe/Zurich",
	"frauenfeld":                     "Europe/Zurich",
	"altdorf":                        "Europe/Zurich",
	"zug":                            "Europe/Zurich",
	"fribourg":                       "Europe/Zurich",
	"liestal":                        "Europe/Zurich",
	"solothurn":                      "Europe/Zurich",
	"sarnen":                         "Europe/Zurich",
	"appenzell":                      "Europe/Zurich",
	"chur":                           "Europe/Zurich",
	"biel":                           "Europe/Zurich",
	"luzern":                         "Europe/Zurich",
	"lugano":                         "Europe/Zurich",
	"lausanne":                       "Europe/Zurich",
	"basel":                          "Europe/Zurich",
	"bern":                           "Europe/Zurich",
	"zurich":                         "Europe/Zurich",
	"geneva":                         "Europe/Zurich",
	"dar'a":                          "Asia/Damascus",
	"alladhiqiyah":                   "Asia/Damascus",
	"madinataththawrah":              "Asia/Damascus",
	"izaz":                           "Asia/Damascus",
	"manbij":                         "Asia/Damascus",
	"idlib":                          "Asia/Damascus",
	"alqamishli":                     "Asia/Damascus",
	"alhasakah":                      "Asia/Damascus",
	"duma":                           "Asia/Damascus",
	"tartus":                         "Asia/Damascus",
	"arraqqah":                       "Asia/Damascus",
	"hamah":                          "Asia/Damascus",
	"tadmur":                         "Asia/Damascus",
	"abukamal":                       "Asia/Damascus",
	"dayrazzawr":                     "Asia/Damascus",
	"assuwayda":                      "Asia/Damascus",
	"adnabk":                         "Asia/Damascus",
	"alqunaytirah":                   "Asia/Damascus",
	"hims":                           "Asia/Damascus",
	"aleppo":                         "Asia/Damascus",
	"damascus":                       "Asia/Damascus",
	"bade":                           "Asia/Taipei",
	"pingzhen":                       "Asia/Taipei",
	"taibao":                         "Asia/Taipei",
	"taoyuan":                        "Asia/Taipei",
	"yangmei":                        "Asia/Taipei",
	"yilan":                          "Asia/Taipei",
	"zhubei":                         "Asia/Taipei",
	"douliou":                        "Asia/Taipei",
	"zhongli":                        "Asia/Taipei",
	"keelung":                        "Asia/Taipei",
	"nantou":                         "Asia/Taipei",
	"puzi":                           "Asia/Taipei",
	"changhua":                       "Asia/Taipei",
	"chiayi":                         "Asia/Taipei",
	"hsinchu":                        "Asia/Taipei",
	"miaoli":                         "Asia/Taipei",
	"pingtung":                       "Asia/Taipei",
	"hualien":                        "Asia/Taipei",
	"newtaipei":                      "Asia/Taipei",
	"tainan":                         "Asia/Taipei",
	"taitung":                        "Asia/Taipei",
	"magong":                         "Asia/Taipei",
	"taichung":                       "Asia/Taipei",
	"kaohsiung":                      "Asia/Taipei",
	"taipei":                         "Asia/Taipei",
	"leninobod":                      "Asia/Dushanbe",
	"qurghonteppa":                   "Asia/Dushanbe",
	"konibodom":                      "Asia/Dushanbe",
	"kuybyshevskiy":                  "Asia/Dushanbe",
	"kulob":                          "Asia/Dushanbe",
	"uroteppa":                       "Asia/Dushanbe",
	"khorugh":                        "Asia/Dushanbe",
	"khujand":                        "Asia/Dushanbe",
	"dushanbe":                       "Asia/Dushanbe",
	"wete":                           "Africa/Dar_es_Salaam",
	"kibaha":                         "Africa/Dar_es_Salaam",
	"mkokotoni":                      "Africa/Dar_es_Salaam",
	"tunduma":                        "Africa/Dar_es_Salaam",
	"tukuyu":                         "Africa/Dar_es_Salaam",
	"sumbawanga":                     "Africa/Dar_es_Salaam",
	"mpanda":                         "Africa/Dar_es_Salaam",
	"kipili":                         "Africa/Dar_es_Salaam",
	"karema":                         "Africa/Dar_es_Salaam",
	"geita":                          "Africa/Dar_es_Salaam",
	"nyahanga":                       "Africa/Dar_es_Salaam",
	"kahama":                         "Africa/Dar_es_Salaam",
	"shinyanga":                      "Africa/Dar_es_Salaam",
	"nzega":                          "Africa/Dar_es_Salaam",
	"sikonge":                        "Africa/Dar_es_Salaam",
	"biharamulo":                     "Africa/Dar_es_Salaam",
	"bukoba":                         "Africa/Dar_es_Salaam",
	"ngara":                          "Africa/Dar_es_Salaam",
	"kakonko":                        "Africa/Dar_es_Salaam",
	"kasulu":                         "Africa/Dar_es_Salaam",
	"kanyato":                        "Africa/Dar_es_Salaam",
	"uvinza":                         "Africa/Dar_es_Salaam",
	"kigoma":                         "Africa/Dar_es_Salaam",
	"mikumi":                         "Africa/Dar_es_Salaam",
	"ifakara":                        "Africa/Dar_es_Salaam",
	"kilosa":                         "Africa/Dar_es_Salaam",
	"chakechake":                     "Africa/Dar_es_Salaam",
	"kibiti":                         "Africa/Dar_es_Salaam",
	"bagamoyo":                       "Africa/Dar_es_Salaam",
	"kilindoni":                      "Africa/Dar_es_Salaam",
	"mpwapwa":                        "Africa/Dar_es_Salaam",
	"njombe":                         "Africa/Dar_es_Salaam",
	"iringa":                         "Africa/Dar_es_Salaam",
	"masasi":                         "Africa/Dar_es_Salaam",
	"mtwara":                         "Africa/Dar_es_Salaam",
	"tunduru":                        "Africa/Dar_es_Salaam",
	"mbambabay":                      "Africa/Blantyre",
	"manyoni":                        "Africa/Dar_es_Salaam",
	"itigi":                          "Africa/Dar_es_Salaam",
	"singida":                        "Africa/Dar_es_Salaam",
	"ngorongoro":                     "Africa/Dar_es_Salaam",
	"oldeani":                        "Africa/Dar_es_Salaam",
	"mbulu":                          "Africa/Dar_es_Salaam",
	"babati":                         "Africa/Dar_es_Salaam",
	"same":                           "Africa/Dar_es_Salaam",
	"moshi":                          "Africa/Dar_es_Salaam",
	"musoma":                         "Africa/Dar_es_Salaam",
	"korogwe":                        "Africa/Dar_es_Salaam",
	"tabora":                         "Africa/Dar_es_Salaam",
	"lindi":                          "Africa/Dar_es_Salaam",
	"songea":                         "Africa/Dar_es_Salaam",
	"tanga":                          "Africa/Dar_es_Salaam",
	"morogoro":                       "Africa/Dar_es_Salaam",
	"dodoma":                         "Africa/Dar_es_Salaam",
	"arusha":                         "Africa/Dar_es_Salaam",
	"mbeya":                          "Africa/Dar_es_Salaam",
	"zanzibar":                       "Africa/Dar_es_Salaam",
	"daressalaam":                    "Africa/Dar_es_Salaam",
	"maehongson":                     "Asia/Bangkok",
	"phangnga":                       "Asia/Bangkok",
	"ranong":                         "Asia/Bangkok",
	"krabi":                          "Asia/Bangkok",
	"phatthalung":                    "Asia/Bangkok",
	"satun":                          "Asia/Bangkok",
	"lamphun":                        "Asia/Bangkok",
	"kamphaengphet":                  "Asia/Bangkok",
	"phichit":                        "Asia/Bangkok",
	"phetchabun":                     "Asia/Bangkok",
	"suphamburi":                     "Asia/Bangkok",
	"angthong":                       "Asia/Bangkok",
	"chainat":                        "Asia/Bangkok",
	"nakhonnayok":                    "Asia/Bangkok",
	"singburi":                       "Asia/Bangkok",
	"nakhonpathom":                   "Asia/Bangkok",
	"prachuapkhirikhan":              "Asia/Bangkok",
	"samutsakhon":                    "Asia/Bangkok",
	"samutsongkhram":                 "Asia/Bangkok",
	"yasothon":                       "Asia/Bangkok",
	"chachoengsao":                   "Asia/Bangkok",
	"trat":                           "Asia/Bangkok",
	"kalasin":                        "Asia/Bangkok",
	"mahasarakham":                   "Asia/Bangkok",
	"roiet":                          "Asia/Bangkok",
	"pattani":                        "Asia/Bangkok",
	"chumphon":                       "Asia/Bangkok",
	"thungsong":                      "Asia/Bangkok",
	"trang":                          "Asia/Bangkok",
	"yala":                           "Asia/Bangkok",
	"chiangrai":                      "Asia/Bangkok",
	"lampang":                        "Asia/Bangkok",
	"nan":                            "Asia/Bangkok",
	"phayao":                         "Asia/Bangkok",
	"phrae":                          "Asia/Bangkok",
	"phitsanulok":                    "Asia/Bangkok",
	"sukhothai":                      "Asia/Bangkok",
	"uttaradit":                      "Asia/Bangkok",
	"kanchanaburi":                   "Asia/Bangkok",
	"maesot":                         "Asia/Bangkok",
	"tak":                            "Asia/Bangkok",
	"uthaithani":                     "Asia/Bangkok",
	"lopburi":                        "Asia/Bangkok",
	"prachinburi":                    "Asia/Bangkok",
	"ayutthaya":                      "Asia/Bangkok",
	"pathumthani":                    "Asia/Bangkok",
	"saraburi":                       "Asia/Bangkok",
	"nonthaburi":                     "Asia/Bangkok",
	"phetchaburi":                    "Asia/Bangkok",
	"huahin":                         "Asia/Bangkok",
	"ratchaburi":                     "Asia/Bangkok",
	"samutprakan":                    "Asia/Bangkok",
	"sisaket":                        "Asia/Bangkok",
	"siracha":                        "Asia/Bangkok",
	"chonburi":                       "Asia/Bangkok",
	"chanthaburi":                    "Asia/Bangkok",
	"aranyaprathet":                  "Asia/Bangkok",
	"rayong":                         "Asia/Bangkok",
	"buriram":                        "Asia/Bangkok",
	"chaiyaphum":                     "Asia/Bangkok",
	"buayai":                         "Asia/Bangkok",
	"surin":                          "Asia/Bangkok",
	"loei":                           "Asia/Bangkok",
	"nongkhai":                       "Asia/Bangkok",
	"sakhonnakhon":                   "Asia/Bangkok",
	"udonthani":                      "Asia/Bangkok",
	"nakhonphanom":                   "Asia/Bangkok",
	"narathiwat":                     "Asia/Bangkok",
	"khonkaen":                       "Asia/Bangkok",
	"phuket":                         "Asia/Bangkok",
	"nakhonsithammarat":              "Asia/Bangkok",
	"songkhla":                       "Asia/Bangkok",
	"hatyai":                         "Asia/Bangkok",
	"nakhonsawan":                    "Asia/Bangkok",
	"ubonratchathani":                "Asia/Bangkok",
	"suratthani":                     "Asia/Bangkok",
	"chiangmai":                      "Asia/Bangkok",
	"nakhonratchasima":               "Asia/Bangkok",
	"bangkok":                        "Asia/Bangkok",
	"freeport":                       "America/Chicago",
	"nassau":                         "America/Nassau",
	"bassesantasu":                   "Africa/Banjul",
	"kerewan":                        "Africa/Banjul",
	"mansakonko":                     "Africa/Banjul",
	"bansang":                        "Africa/Banjul",
	"brikama":                        "Africa/Banjul",
	"banjul":                         "Africa/Banjul",
	"bassar":                         "Africa/Lome",
	"sotouboua":                      "Africa/Lome",
	"kpalime":                        "Africa/Lome",
	"sokode":                         "Africa/Lome",
	"mango":                          "Africa/Lome",
	"atakpame":                       "Africa/Lome",
	"lome":                           "Africa/Lome",
	"neiafu":                         "Pacific/Tongatapu",
	"nukualofa":                      "Pacific/Tongatapu",
	"port-of-spain":                  "America/Port_of_Spain",
	"medemine":                       "Africa/Tunis",
	"kebili":                         "Africa/Tunis",
	"tataouine":                      "Africa/Tunis",
	"l'ariana":                       "Africa/Tunis",
	"jendouba":                       "Africa/Tunis",
	"kasserine":                      "Africa/Tunis",
	"sdidbouzid":                     "Africa/Tunis",
	"siliana":                        "Africa/Tunis",
	"mahdia":                         "Africa/Tunis",
	"monasir":                        "Africa/Tunis",
	"zaghouan":                       "Africa/Tunis",
	"bengardane":                     "Africa/Tunis",
	"zarzis":                         "Africa/Tunis",
	"dehibat":                        "Africa/Tunis",
	"tozeur":                         "Africa/Tunis",
	"bizerte":                        "Africa/Tunis",
	"nabeul":                         "Africa/Tunis",
	"elkef":                          "Africa/Tunis",
	"qasserine":                      "Africa/Tunis",
	"gabes":                          "Africa/Tunis",
	"gafsa":                          "Africa/Tunis",
	"qairouan":                       "Africa/Tunis",
	"sfax":                           "Africa/Tunis",
	"sousse":                         "Africa/Tunis",
	"tunis":                          "Africa/Tunis",
	"kirklareli":                     "Europe/Istanbul",
	"bilecik":                        "Europe/Istanbul",
	"sakarya":                        "Europe/Istanbul",
	"kastamonu":                      "Europe/Istanbul",
	"burdur":                         "Europe/Istanbul",
	"kirsehir":                       "Europe/Istanbul",
	"nevsehir":                       "Europe/Istanbul",
	"antioch":                        "Europe/Istanbul",
	"giresun":                        "Europe/Istanbul",
	"tokat":                          "Europe/Istanbul",
	"coruh":                          "Europe/Istanbul",
	"bingol":                         "Europe/Istanbul",
	"bitlis":                         "Europe/Istanbul",
	"cankiri":                        "Europe/Istanbul",
	"nigde":                          "Europe/Istanbul",
	"yozgat":                         "Europe/Istanbul",
	"gumushane":                      "Europe/Istanbul",
	"siirt":                          "Europe/Istanbul",
	"tunceli":                        "Europe/Istanbul",
	"aydin":                          "Europe/Istanbul",
	"luleburgaz":                     "Europe/Istanbul",
	"isparta":                        "Europe/Istanbul",
	"kutahya":                        "Europe/Istanbul",
	"mugla":                          "Europe/Istanbul",
	"elazig":                         "Europe/Istanbul",
	"kahramanmaras":                  "Europe/Istanbul",
	"icel":                           "Europe/Istanbul",
	"corum":                          "Europe/Istanbul",
	"rize":                           "Europe/Istanbul",
	"tatvan":                         "Europe/Istanbul",
	"polatli":                        "Europe/Istanbul",
	"karabuk":                        "Europe/Istanbul",
	"nusaybin":                       "Europe/Istanbul",
	"hakkari":                        "Europe/Istanbul",
	"soke":                           "Europe/Istanbul",
	"balikesir":                      "Europe/Istanbul",
	"canakkale":                      "Europe/Istanbul",
	"edirne":                         "Europe/Istanbul",
	"tekirdag":                       "Europe/Istanbul",
	"kocaeli":                        "Europe/Istanbul",
	"bolu":                           "Europe/Istanbul",
	"afyon":                          "Europe/Istanbul",
	"denizli":                        "Europe/Istanbul",
	"manisa":                         "Europe/Istanbul",
	"adiyaman":                       "Europe/Istanbul",
	"malatya":                        "Europe/Istanbul",
	"tarsus":                         "Europe/Istanbul",
	"samandagi":                      "Europe/Istanbul",
	"hatay":                          "Europe/Istanbul",
	"iskenderun":                     "Europe/Istanbul",
	"amasya":                         "Europe/Istanbul",
	"ordu":                           "Europe/Istanbul",
	"sivas":                          "Europe/Istanbul",
	"bafra":                          "Europe/Istanbul",
	"erzurum":                        "Europe/Istanbul",
	"erzincan":                       "Europe/Istanbul",
	"agri":                           "Europe/Istanbul",
	"diyarbakir":                     "Europe/Istanbul",
	"mus":                            "Europe/Istanbul",
	"zonguldak":                      "Europe/Istanbul",
	"eregli":                         "Europe/Istanbul",
	"karaman":                        "Europe/Istanbul",
	"usak":                           "Europe/Istanbul",
	"kilis":                          "Europe/Istanbul",
	"kirikkale":                      "Europe/Istanbul",
	"kars":                           "Europe/Istanbul",
	"mardin":                         "Europe/Istanbul",
	"batman":                         "Europe/Istanbul",
	"van":                            "Europe/Istanbul",
	"adapazari":                      "Europe/Istanbul",
	"trabzon":                        "Europe/Istanbul",
	"sanliurfa":                      "Europe/Istanbul",
	"eskisehir":                      "Europe/Istanbul",
	"antalya":                        "Europe/Istanbul",
	"kayseri":                        "Europe/Istanbul",
	"gaziantep":                      "Europe/Istanbul",
	"izmir":                          "Europe/Istanbul",
	"bursa":                          "Europe/Istanbul",
	"samsun":                         "Europe/Istanbul",
	"konya":                          "Europe/Istanbul",
	"adana":                          "Europe/Istanbul",
	"ankara":                         "Europe/Istanbul",
	"istanbul":                       "Europe/Istanbul",
	"gyzlarbat":                      "Asia/Ashgabat",
	"celeken":                        "Asia/Ashgabat",
	"tejen":                          "Asia/Ashgabat",
	"buzmeyin":                       "Asia/Ashgabat",
	"koneurgench":                    "Asia/Ashgabat",
	"balkanabat":                     "Asia/Ashgabat",
	"kaka":                           "Asia/Ashgabat",
	"atamyrat":                       "Asia/Ashgabat",
	"dasoguz":                        "Asia/Ashgabat",
	"turkmenbasy":                    "Asia/Ashgabat",
	"turkmenabat":                    "Asia/Ashgabat",
	"mary":                           "Asia/Ashgabat",
	"ashgabat":                       "Asia/Ashgabat",
	"grandturk":                      "America/Grand_Turk",
	"funafuti":                       "Pacific/Funafuti",
	"kalangala":                      "Africa/Kampala",
	"kumi":                           "Africa/Kampala",
	"kaberamaido":                    "Africa/Kampala",
	"kayunga":                        "Africa/Kampala",
	"iganga":                         "Africa/Kampala",
	"kamuli":                         "Africa/Kampala",
	"pallisa":                        "Africa/Kampala",
	"mpigi":                          "Africa/Kampala",
	"adjumani":                       "Africa/Kampala",
	"nebbi":                          "Africa/Kampala",
	"kiboga":                         "Africa/Kampala",
	"nakasongola":                    "Africa/Kampala",
	"bombo":                          "Africa/Kampala",
	"masindi":                        "Africa/Kampala",
	"fortportal":                     "Africa/Kampala",
	"kibale":                         "Africa/Kampala",
	"sironko":                        "Africa/Kampala",
	"busia":                          "Africa/Kampala",
	"katakwi":                        "Africa/Kampala",
	"ntungamo":                       "Africa/Kampala",
	"kisoro":                         "Africa/Kampala",
	"jinja":                          "Africa/Kampala",
	"soroti":                         "Africa/Kampala",
	"arua":                           "Africa/Kampala",
	"pakwach":                        "Africa/Kampala",
	"moyo":                           "Africa/Kampala",
	"entebbe":                        "Africa/Kampala",
	"mubende":                        "Africa/Kampala",
	"mityana":                        "Africa/Kampala",
	"kitgum":                         "Africa/Kampala",
	"lira":                           "Africa/Kampala",
	"masindi-port":                   "Africa/Kampala",
	"mbale":                          "Africa/Kampala",
	"tororo":                         "Africa/Kampala",
	"kaabong":                        "Africa/Kampala",
	"moroto":                         "Africa/Kampala",
	"masaka":                         "Africa/Kampala",
	"katwe":                          "Africa/Kampala",
	"mbarara":                        "Africa/Kampala",
	"kabale":                         "Africa/Kampala",
	"kasese":                         "Africa/Kampala",
	"gulu":                           "Africa/Kampala",
	"kampala":                        "Africa/Kampala",
	"mykolayiv":                      "Europe/Kyiv",
	"chernihiv":                      "Europe/Kyiv",
	"khmelnytskyy":                   "Europe/Kyiv",
	"kamyanets-podilskyy":            "Europe/Kyiv",
	"drohobych":                      "Europe/Kyiv",
	"uzhgorod":                       "Europe/Uzhgorod",
	"uman":                           "Europe/Kyiv",
	"brovary":                        "Europe/Kyiv",
	"bilatserkva":                    "Europe/Kyiv",
	"illichivsk":                     "Europe/Kyiv",
	"konotop":                        "Europe/Kyiv",
	"kryvyyrih":                      "Europe/Kyiv",
	"makiyivka":                      "Europe/Kyiv",
	"horlivka":                       "Europe/Kyiv",
	"kramatorsk":                     "Europe/Kyiv",
	"berdyansk":                      "Europe/Zaporozhye",
	"dzhankoy":                       "Europe/Simferopol",
	"yevpatoriya":                    "Europe/Simferopol",
	"kerch":                          "Europe/Simferopol",
	"simferopol":                     "Europe/Simferopol",
	"kherson":                        "Europe/Kyiv",
	"voznesensk":                     "Europe/Kyiv",
	"nizhyn":                         "Europe/Kyiv",
	"rivne":                          "Europe/Kyiv",
	"chernivtsi":                     "Europe/Kyiv",
	"ivano-frankivsk":                "Europe/Kyiv",
	"ternopil":                       "Europe/Kyiv",
	"lutsk":                          "Europe/Kyiv",
	"kovel":                          "Europe/Kyiv",
	"cherkasy":                       "Europe/Kyiv",
	"kirovohrad":                     "Europe/Kyiv",
	"izmayil":                        "Europe/Kyiv",
	"vinnytsya":                      "Europe/Kyiv",
	"korosten":                       "Europe/Kyiv",
	"shostka":                        "Europe/Kyiv",
	"nikopol":                        "Europe/Kyiv",
	"kupyansk":                       "Europe/Kyiv",
	"lysychansk":                     "Europe/Kyiv",
	"luhansk":                        "Europe/Kyiv",
	"poltava":                        "Europe/Kyiv",
	"kremenchuk":                     "Europe/Kyiv",
	"melitopol":                      "Europe/Zaporozhye",
	"zaporizhzhya":                   "Europe/Zaporozhye",
	"yalta":                          "Europe/Simferopol",
	"chernobyl":                      "Europe/Kyiv",
	"sumy":                           "Europe/Kyiv",
	"mariupol":                       "Europe/Kyiv",
	"lvov":                           "Europe/Kyiv",
	"odessa":                         "America/Chicago",
	"zhytomyr":                       "Europe/Kyiv",
	"dnipropetrovsk":                 "Europe/Kyiv",
	"donetsk":                        "Europe/Kyiv",
	"kharkiv":                        "Europe/Kyiv",
	"sevastapol":                     "Europe/Simferopol",
	"kyiv":                           "Europe/Kyiv",
	"ummalqaywayn":                   "Asia/Dubai",
	"sharjah":                        "Asia/Dubai",
	"jabalali":                       "Asia/Dubai",
	"rasalkhaymah":                   "Asia/Dubai",
	"alfujayrah":                     "Asia/Dubai",
	"alayn":                          "Asia/Dubai",
	"abudhabi":                       "Asia/Dubai",
	"dubai":                          "Asia/Dubai",
	"greenock":                       "Europe/London",
	"sunderland":                     "Europe/London",
	"southampton":                    "Europe/London",
	"bristol":                        "America/New_York",
	"bournemouth":                    "Europe/London",
	"omagh":                          "Europe/London",
	"chester":                        "Europe/London",
	"swansea":                        "Europe/London",
	"carlisle":                       "Europe/London",
	"southend":                       "Europe/London",
	"reading":                        "Europe/London",
	"leicester":                      "Europe/London",
	"bradford":                       "Europe/London",
	"sheffield":                      "Europe/London",
	"fortwilliam":                    "Europe/London",
	"aberdeen":                       "America/Chicago",
	"dundee":                         "Europe/London",
	"middlesbrough":                  "Europe/London",
	"coventry":                       "Europe/London",
	"bath":                           "Europe/London",
	"exeter":                         "Europe/London",
	"cambridge":                      "Europe/London",
	"kingstonuponhull":               "Europe/London",
	"londonderry":                    "Europe/London",
	"lisburn":                        "Europe/London",
	"penzance":                       "Europe/London",
	"york":                           "America/New_York",
	"blackpool":                      "Europe/London",
	"dumfries":                       "Europe/London",
	"scarborough":                    "Europe/London",
	"plymouth":                       "Europe/London",
	"ipswich":                        "Europe/London",
	"norwich":                        "Europe/London",
	"brighton":                       "Europe/London",
	"kirkwall":                       "Europe/London",
	"inverness":                      "Europe/London",
	"oxford":                         "Europe/London",
	"luton":                          "Europe/London",
	"portsmouth":                     "Europe/London",
	"nottingham":                     "Europe/London",
	"stoke":                          "Europe/London",
	"dover":                          "America/New_York",
	"edinburgh":                      "Europe/London",
	"cardiff":                        "Europe/London",
	"wick":                           "Europe/London",
	"leeds":                          "Europe/London",
	"lerwick":                        "Europe/London",
	"manchester":                     "America/New_York",
	"birmingham":                     "America/Chicago",
	"belfast":                        "Europe/London",
	"glasgow":                        "America/Denver",
	"faribault":                      "America/Chicago",
	"mankato":                        "America/Chicago",
	"albertlea":                      "America/Chicago",
	"willmar":                        "America/Chicago",
	"brainerd":                       "America/Chicago",
	"crookston":                      "America/Chicago",
	"hardin":                         "America/Denver",
	"glendive":                       "America/Denver",
	"dillon":                         "America/Denver",
	"polson":                         "America/Denver",
	"devilslake":                     "America/Chicago",
	"burley":                         "America/Boise",
	"wallace":                        "America/Los_Angeles",
	"kennewick":                      "America/Los_Angeles",
	"centralia":                      "America/Los_Angeles",
	"glendale":                       "America/Phoenix",
	"safford":                        "America/Phoenix",
	"casagrande":                     "America/Phoenix",
	"mesa":                           "America/Phoenix",
	"lakehavasucity":                 "America/Phoenix",
	"berkeley":                       "America/Los_Angeles",
	"nationalcity":                   "America/Los_Angeles",
	"mendocino":                      "America/Los_Angeles",
	"pasorobles":                     "America/Los_Angeles",
	"riverside":                      "America/Los_Angeles",
	"delano":                         "America/Los_Angeles",
	"sanmateo":                       "America/Los_Angeles",
	"vallejo":                        "America/Los_Angeles",
	"glenwoodsprings":                "America/Denver",
	"aurora":                         "America/Chicago",
	"greeley":                        "America/Denver",
	"tonopah":                        "America/Los_Angeles",
	"deming":                         "America/Denver",
	"truthorconsequences":            "America/Denver",
	"lasvegas":                       "America/Los_Angeles",
	"farmington":                     "America/Denver",
	"springfield":                    "America/Chicago",
	"tillamook":                      "America/Los_Angeles",
	"ontario":                        "America/Boise",
	"lagrande":                       "America/Los_Angeles",
	"richfield":                      "America/Denver",
	"nephi":                          "America/Denver",
	"lander":                         "America/Denver",
	"powell":                         "America/Denver",
	"paragould":                      "America/Chicago",
	"iowacity":                       "America/Chicago",
	"ottumwa":                        "America/Chicago",
	"spencer":                        "America/Chicago",
	"ft.dodge":                       "America/Chicago",
	"hutchinson":                     "America/Chicago",
	"kansascity":                     "America/Chicago",
	"lawrence":                       "America/Chicago",
	"gardencity":                     "America/Chicago",
	"manhattan":                      "America/Chicago",
	"hays":                           "America/Chicago",
	"goodland":                       "America/Denver",
	"independence":                   "America/Chicago",
	"kirksville":                     "America/Chicago",
	"kearney":                        "America/Chicago",
	"grandisland":                    "America/Chicago",
	"alliance":                       "America/Denver",
	"bartlesville":                   "America/Chicago",
	"enid":                           "America/Chicago",
	"ardmore":                        "America/Chicago",
	"mcalester":                      "America/Chicago",
	"stillwater":                     "America/Chicago",
	"lead":                           "America/Denver",
	"slidell":                        "America/Chicago",
	"lakecharles":                    "America/Chicago",
	"metairie":                       "America/Chicago",
	"newiberia":                      "America/Chicago",
	"bryan":                          "America/Chicago",
	"longview":                       "America/Los_Angeles",
	"mcallen":                        "America/Chicago",
	"harlingen":                      "America/Chicago",
	"alice":                          "America/Chicago",
	"newbraunfels":                   "America/Chicago",
	"cleburne":                       "America/Chicago",
	"brownwood":                      "America/Chicago",
	"alpine":                         "America/Chicago",
	"vanhorn":                        "America/Chicago",
	"bigspring":                      "America/Chicago",
	"vernon":                         "America/Chicago",
	"childress":                      "America/Chicago",
	"hereford":                       "America/Chicago",
	"dalhart":                        "America/Chicago",
	"texascity":                      "America/Chicago",
	"pasadena":                       "America/Los_Angeles",
	"baytown":                        "America/Chicago",
	"newlondon":                      "America/New_York",
	"stamford":                       "America/New_York",
	"waterbury":                      "America/New_York",
	"newbedford":                     "America/New_York",
	"pittsfield":                     "America/New_York",
	"montpelier":                     "America/Boise",
	"auburn":                         "America/Chicago",
	"winterhaven":                    "America/New_York",
	"homestead":                      "America/New_York",
	"sanford":                        "America/New_York",
	"miamibeach":                     "America/New_York",
	"coralsprings":                   "America/New_York",
	"portcharlotte":                  "America/New_York",
	"springhill":                     "America/New_York",
	"palmcoast":                      "America/New_York",
	"leesburg":                       "America/New_York",
	"lakecity":                       "America/New_York",
	"crestview":                      "America/Chicago",
	"dalton":                         "America/New_York",
	"marietta":                       "America/New_York",
	"waycross":                       "America/New_York",
	"lagrange":                       "America/New_York",
	"southaven":                      "America/Chicago",
	"meridian":                       "America/Chicago",
	"laurel":                         "America/Chicago",
	"spartanburg":                    "America/New_York",
	"orangeburg":                     "America/New_York",
	"galesburg":                      "America/Chicago",
	"joliet":                         "America/Chicago",
	"capegirardeau":                  "America/Chicago",
	"rockford":                       "America/Chicago",
	"evanston":                       "America/Chicago",
	"rockisland":                     "America/Chicago",
	"elgin":                          "America/Chicago",
	"terrehaute":                     "America/Indiana/Indianapolis",
	"lafayette":                      "America/Chicago",
	"marion":                         "America/Indiana/Indianapolis",
	"southbend":                      "America/Indiana/Indianapolis",
	"newalbany":                      "America/Kentucky/Louisville",
	"elkhart":                        "America/Indiana/Indianapolis",
	"hopkinsville":                   "America/Chicago",
	"madisonville":                   "America/Chicago",
	"rockymount":                     "America/New_York",
	"salisbury":                      "America/New_York",
	"durham":                         "America/New_York",
	"lumberton":                      "America/New_York",
	"zanesville":                     "America/New_York",
	"mansfield":                      "America/New_York",
	"bowlinggreen":                   "America/Chicago",
	"lancaster":                      "America/Los_Angeles",
	"johnsoncity":                    "America/New_York",
	"kingsport":                      "America/New_York",
	"columbia":                       "America/New_York",
	"barlett":                        "America/Chicago",
	"blacksburg":                     "America/New_York",
	"harrisonburg":                   "America/New_York",
	"petersburg":                     "America/New_York",
	"hampton":                        "America/New_York",
	"sheboygan":                      "America/Chicago",
	"waukesha":                       "America/Chicago",
	"lacrosse":                       "America/Chicago",
	"eauclaire":                      "America/Chicago",
	"tomah":                          "America/Chicago",
	"janesville":                     "America/Chicago",
	"appleton":                       "America/Chicago",
	"parkersburg":                    "America/New_York",
	"whitesulphursprings":            "America/New_York",
	"clarksburg":                     "America/New_York",
	"st.charles":                     "America/Chicago",
	"annapolis":                      "America/New_York",
	"hagerstown":                     "America/New_York",
	"paterson":                       "America/New_York",
	"saratogasprings":                "America/New_York",
	"poughkeepsie":                   "America/New_York",
	"plattsburg":                     "America/New_York",
	"beaverfalls":                    "America/New_York",
	"altoona":                        "America/New_York",
	"williamsport":                   "America/New_York",
	"allentown":                      "America/New_York",
	"waterville":                     "America/New_York",
	"houlton":                        "America/New_York",
	"bentonharbor":                   "America/Detroit",
	"battlecreek":                    "America/Detroit",
	"baycity":                        "America/Chicago",
	"alpena":                         "America/Detroit",
	"ironmountain":                   "America/Menominee",
	"ironwood":                       "America/Menominee",
	"sandpoint":                      "America/Anchorage",
	"hydaburg":                       "America/Sitka",
	"mekoryuk":                       "America/Nome",
	"atqasuk":                        "America/Anchorage",
	"portheiden":                     "America/Anchorage",
	"perryville":                     "America/Anchorage",
	"dillingham":                     "America/Anchorage",
	"goodnewsbay":                    "America/Anchorage",
	"nyac":                           "America/Anchorage",
	"tununak":                        "America/Nome",
	"mountainvillage":                "America/Nome",
	"emmonak":                        "America/Nome",
	"kaltag":                         "America/Anchorage",
	"teller":                         "America/Nome",
	"koyukuk":                        "America/Anchorage",
	"kobuk":                          "America/Anchorage",
	"selawik":                        "America/Anchorage",
	"talkeetna":                      "America/Anchorage",
	"whittier":                       "America/Anchorage",
	"lakeminchumina":                 "America/Anchorage",
	"cantwell":                       "America/Anchorage",
	"gulkana":                        "America/Anchorage",
	"eagle":                          "America/Anchorage",
	"nenana":                         "America/Anchorage",
	"bigdelta":                       "America/Anchorage",
	"allakaket":                      "America/Anchorage",
	"tanana":                         "America/Anchorage",
	"virginia":                       "America/Chicago",
	"winona":                         "America/Chicago",
	"rochester":                      "America/New_York",
	"lakeville":                      "America/Chicago",
	"ely":                            "America/Los_Angeles",
	"moorhead":                       "America/Chicago",
	"st.cloud":                       "America/Chicago",
	"milescity":                      "America/Denver",
	"bozeman":                        "America/Denver",
	"dickinson":                      "America/Denver",
	"jamestown":                      "America/New_York",
	"williston":                      "America/Chicago",
	"lihue":                          "Pacific/Honolulu",
	"wahiawa":                        "Pacific/Honolulu",
	"wailuku":                        "Pacific/Honolulu",
	"twinfalls":                      "America/Boise",
	"caldwell":                       "America/Boise",
	"salmon":                         "America/Boise",
	"coeurd'alene":                   "America/Los_Angeles",
	"richland":                       "America/Los_Angeles",
	"bellingham":                     "America/Los_Angeles",
	"wallawalla":                     "America/Los_Angeles",
	"bremerton":                      "America/Los_Angeles",
	"everett":                        "America/Los_Angeles",
	"bullheadcity":                   "America/Phoenix",
	"winslow":                        "America/Denver",
	"gilabend":                       "America/Phoenix",
	"tombstone":                      "America/Phoenix",
	"willcox":                        "America/Phoenix",
	"kingman":                        "America/Phoenix",
	"grandcanyon":                    "America/Phoenix",
	"arcata":                         "America/Los_Angeles",
	"stockton":                       "America/Los_Angeles",
	"barstow":                        "America/Los_Angeles",
	"visalia":                        "America/Los_Angeles",
	"elcentro":                       "America/Los_Angeles",
	"sanluisobispo":                  "America/Los_Angeles",
	"merced":                         "America/Los_Angeles",
	"yubacity":                       "America/Los_Angeles",
	"redding":                        "America/Los_Angeles",
	"oceanside":                      "America/Los_Angeles",
	"modesto":                        "America/Los_Angeles",
	"irvine":                         "America/Los_Angeles",
	"ukiah":                          "America/Los_Angeles",
	"needles":                        "America/Los_Angeles",
	"bishop":                         "America/Los_Angeles",
	"palmsprings":                    "America/Los_Angeles",
	"tulare":                         "America/Los_Angeles",
	"mt.shasta":                      "America/Los_Angeles",
	"crescentcity":                   "America/Los_Angeles",
	"fortcollins":                    "America/Denver",
	"pueblo":                         "America/Denver",
	"lamar":                          "America/Denver",
	"gunnison":                       "America/Denver",
	"montrose":                       "America/Denver",
	"craig":                          "America/Denver",
	"boulder":                        "America/Denver",
	"bouldercity":                    "America/Los_Angeles",
	"winnemucca":                     "America/Los_Angeles",
	"roswell":                        "America/Denver",
	"clovis":                         "America/Denver",
	"lascruces":                      "America/Denver",
	"hobbs":                          "America/Denver",
	"gallup":                         "America/Denver",
	"raton":                          "America/Denver",
	"tucumcari":                      "America/Denver",
	"roseburg":                       "America/Los_Angeles",
	"pendleton":                      "America/Los_Angeles",
	"johnday":                        "America/Los_Angeles",
	"grantspass":                     "America/Los_Angeles",
	"corvallis":                      "America/Los_Angeles",
	"astoria":                        "America/Los_Angeles",
	"logan":                          "America/Denver",
	"parowan":                        "America/Denver",
	"kanab":                          "America/Denver",
	"monticello":                     "America/Denver",
	"moab":                           "America/Denver",
	"price":                          "America/Denver",
	"cedarcity":                      "America/Denver",
	"vernal":                         "America/Denver",
	"ogden":                          "America/Denver",
	"greenriver":                     "America/Denver",
	"rawlins":                        "America/Denver",
	"riverton":                       "America/Denver",
	"thermopolis":                    "America/Denver",
	"gillette":                       "America/Denver",
	"jonesboro":                      "America/Chicago",
	"texarkana":                      "America/Chicago",
	"pinebluff":                      "America/Chicago",
	"hotsprings":                     "America/Chicago",
	"fayetteville":                   "America/New_York",
	"conway":                         "America/Chicago",
	"davenport":                      "America/Chicago",
	"burlington":                     "America/New_York",
	"dubuque":                        "America/Chicago",
	"waterloo":                       "America/Chicago",
	"siouxcity":                      "America/Chicago",
	"councilbluffs":                  "America/Chicago",
	"ames":                           "America/Chicago",
	"masoncity":                      "America/Chicago",
	"emporia":                        "America/Chicago",
	"salina":                         "America/Chicago",
	"dodgecity":                      "America/Chicago",
	"coffeyville":                    "America/Chicago",
	"poplarbluff":                    "America/Chicago",
	"joplin":                         "America/Chicago",
	"st.joseph":                      "America/Chicago",
	"mccook":                         "America/Chicago",
	"norfolk":                        "America/New_York",
	"northplatte":                    "America/Chicago",
	"sidney":                         "America/Denver",
	"scottsbluff":                    "America/Denver",
	"chadron":                        "America/Denver",
	"lawton":                         "America/Chicago",
	"norman":                         "America/Chicago",
	"muskogee":                       "America/Chicago",
	"poncacity":                      "America/Chicago",
	"shawnee":                        "America/Chicago",
	"woodward":                       "America/Chicago",
	"guymon":                         "America/Chicago",
	"yankton":                        "America/Chicago",
	"brookings":                      "America/Chicago",
	"mitchell":                       "America/Chicago",
	"mobridge":                       "America/Chicago",
	"monroe":                         "America/Chicago",
	"conroe":                         "America/Chicago",
	"nacogdoches":                    "America/Chicago",
	"eaglepass":                      "America/Chicago",
	"edinburg":                       "America/Chicago",
	"kingsville":                     "America/Chicago",
	"portarthur":                     "America/Chicago",
	"huntsville":                     "America/Chicago",
	"killeen":                        "America/Chicago",
	"lufkin":                         "America/Chicago",
	"delrio":                         "America/Chicago",
	"sanangelo":                      "America/Chicago",
	"sherman":                        "America/Chicago",
	"beaumont":                       "America/Chicago",
	"portlavaca":                     "America/Chicago",
	"falfurrias":                     "America/Chicago",
	"beeville":                       "America/Chicago",
	"fortstockton":                   "America/Chicago",
	"pecos":                          "America/Chicago",
	"dumas":                          "America/Chicago",
	"denton":                         "America/Chicago",
	"midland":                        "America/Chicago",
	"temple":                         "America/Chicago",
	"newhaven":                       "America/New_York",
	"lowell":                         "America/New_York",
	"newport":                        "America/New_York",
	"dothan":                         "America/Chicago",
	"tuscaloosa":                     "America/Chicago",
	"gadsden":                        "America/Chicago",
	"enterprise":                     "America/Chicago",
	"selma":                          "America/Chicago",
	"coralgables":                    "America/New_York",
	"capecoral":                      "America/New_York",
	"fortpierce":                     "America/New_York",
	"kissimmee":                      "America/New_York",
	"titusville":                     "America/New_York",
	"st.augustine":                   "America/New_York",
	"ocala":                          "America/New_York",
	"fortlauderdale":                 "America/New_York",
	"apalachicola":                   "America/New_York",
	"verobeach":                      "America/New_York",
	"valdosta":                       "America/New_York",
	"macon":                          "America/New_York",
	"columbus":                       "America/New_York",
	"gulfport":                       "America/Chicago",
	"hattiesburg":                    "America/Chicago",
	"tupelo":                         "America/Chicago",
	"natchez":                        "America/Chicago",
	"sumter":                         "America/New_York",
	"anderson":                       "America/New_York",
	"aiken":                          "America/New_York",
	"beaufort":                       "America/New_York",
	"rockhill":                       "America/New_York",
	"decatur":                        "America/Chicago",
	"alton":                          "America/Chicago",
	"quincy":                         "America/Chicago",
	"urbana":                         "America/Chicago",
	"bloomington":                    "America/Indiana/Indianapolis",
	"kankakee":                       "America/Chicago",
	"waukegan":                       "America/Chicago",
	"carbondale":                     "America/Chicago",
	"muncie":                         "America/Indiana/Indianapolis",
	"kokomo":                         "America/Indiana/Indianapolis",
	"gary":                           "America/Chicago",
	"fortwayne":                      "America/Indiana/Indianapolis",
	"covington":                      "America/New_York",
	"paducah":                        "America/Chicago",
	"owensboro":                      "America/Chicago",
	"jacksonville":                   "America/New_York",
	"goldsboro":                      "America/New_York",
	"hickory":                        "America/New_York",
	"asheville":                      "America/New_York",
	"winston-salem":                  "America/New_York",
	"kittyhawk":                      "America/New_York",
	"akron":                          "America/New_York",
	"oakridge":                       "America/New_York",
	"murfreesboro":                   "America/Chicago",
	"clarksville":                    "America/Chicago",
	"jackson":                        "America/Chicago",
	"fredericksburg":                 "America/New_York",
	"roanoke":                        "America/New_York",
	"danville":                       "America/New_York",
	"winchester":                     "America/New_York",
	"superior":                       "America/Chicago",
	"westbend":                       "America/Chicago",
	"fonddulac":                      "America/Chicago",
	"oshkosh":                        "America/Chicago",
	"rhinelander":                    "America/Chicago",
	"racine":                         "America/Chicago",
	"marinette":                      "America/Chicago",
	"wheeling":                       "America/New_York",
	"morgantown":                     "America/New_York",
	"huntington":                     "America/New_York",
	"beckley":                        "America/New_York",
	"wilmington":                     "America/New_York",
	"cumberland":                     "America/New_York",
	"atlanticcity":                   "America/New_York",
	"newark":                         "America/New_York",
	"schenectady":                    "America/New_York",
	"binghamton":                     "America/New_York",
	"utica":                          "America/New_York",
	"watertown":                      "America/New_York",
	"niagarafalls":                   "America/New_York",
	"elmira":                         "America/New_York",
	"johnstown":                      "America/New_York",
	"scranton":                       "America/New_York",
	"statecollege":                   "America/New_York",
	"erie":                           "America/New_York",
	"wilkesbarre":                    "America/New_York",
	"barharbor":                      "America/New_York",
	"lewiston":                       "America/Los_Angeles",
	"presqueisle":                    "America/New_York",
	"annarbor":                       "America/Detroit",
	"kalamazoo":                      "America/Detroit",
	"muskegon":                       "America/Detroit",
	"flint":                          "America/Detroit",
	"grandrapids":                    "America/Detroit",
	"pontiac":                        "America/Detroit",
	"cadillac":                       "America/Detroit",
	"traversecity":                   "America/Detroit",
	"petoskey":                       "America/Detroit",
	"escanaba":                       "America/Detroit",
	"marquette":                      "America/Detroit",
	"hancock":                        "America/Detroit",
	"wrangell":                       "America/Sitka",
	"shishmaref":                     "America/Nome",
	"hoonah":                         "America/Juneau",
	"atka":                           "America/Adak",
	"nikolski":                       "America/Nome",
	"karluk":                         "America/Anchorage",
	"falsepass":                      "America/Nome",
	"kivalina":                       "America/Nome",
	"newhalen":                       "America/Anchorage",
	"pilotpoint":                     "America/Anchorage",
	"chignik":                        "America/Anchorage",
	"kingsalmon":                     "America/Anchorage",
	"quinhagak":                      "America/Anchorage",
	"aniak":                          "America/Anchorage",
	"kotlit":                         "America/Nome",
	"unalakleet":                     "America/Anchorage",
	"koyuk":                          "America/Anchorage",
	"mcgrath":                        "America/Anchorage",
	"hughes":                         "America/Anchorage",
	"ambler":                         "America/Anchorage",
	"wales":                          "America/Nome",
	"kotzebue":                       "America/Nome",
	"wasilla":                        "America/Anchorage",
	"circle":                         "America/Anchorage",
	"denalipark":                     "America/Anchorage",
	"yakutat":                        "America/Yakutat",
	"homer":                          "America/Anchorage",
	"tanacross":                      "America/Anchorage",
	"wiseman":                        "America/Anchorage",
	"kailua-kona":                    "Pacific/Honolulu",
	"butte":                          "America/Denver",
	"grandforks":                     "America/Chicago",
	"pocatello":                      "America/Boise",
	"tacoma":                         "America/Los_Angeles",
	"yuma":                           "America/Phoenix",
	"prescott":                       "America/Phoenix",
	"longbeach":                      "America/Los_Angeles",
	"grandjunction":                  "America/Denver",
	"carsoncity":                     "America/Los_Angeles",
	"carlsbad":                       "America/Denver",
	"alamogordo":                     "America/Denver",
	"medford":                        "America/Los_Angeles",
	"klamathfalls":                   "America/Los_Angeles",
	"st.george":                      "America/Denver",
	"provo":                          "America/Denver",
	"laramie":                        "America/Denver",
	"littlerock":                     "America/Chicago",
	"wichita":                        "America/Chicago",
	"jeffersoncity":                  "America/Chicago",
	"rapidcity":                      "America/Denver",
	"galveston":                      "America/Chicago",
	"wichitafalls":                   "America/Chicago",
	"waco":                           "America/Chicago",
	"lubbock":                        "America/Chicago",
	"hartford":                       "America/New_York",
	"providence":                     "America/New_York",
	"mobile":                         "America/Chicago",
	"pensacola":                      "America/Chicago",
	"biloxi":                         "America/Chicago",
	"frankfort":                      "America/New_York",
	"greensboro":                     "America/New_York",
	"dayton":                         "America/New_York",
	"virginiabeach":                  "America/New_York",
	"madison":                        "America/Chicago",
	"greenbay":                       "America/Chicago",
	"trenton":                        "America/New_York",
	"lansing":                        "America/Detroit",
	"gambell":                        "America/Nome",
	"palmer":                         "America/Anchorage",
	"seward":                         "America/Anchorage",
	"duluth":                         "America/Chicago",
	"bemidji":                        "America/Chicago",
	"havre":                          "America/Denver",
	"kalispell":                      "America/Denver",
	"idahofalls":                     "America/Boise",
	"yakima":                         "America/Los_Angeles",
	"wenatchee":                      "America/Los_Angeles",
	"bakersfield":                    "America/Los_Angeles",
	"oakland":                        "America/Los_Angeles",
	"chico":                          "America/Los_Angeles",
	"monterey":                       "America/Los_Angeles",
	"losalamos":                      "America/Denver",
	"eugene":                         "America/Los_Angeles",
	"coosbay":                        "America/Los_Angeles",
	"bend":                           "America/Los_Angeles",
	"cody":                           "America/Denver",
	"cedarrapids":                    "America/Chicago",
	"abilene":                        "America/Chicago",
	"brownsville":                    "America/Chicago",
	"tyler":                          "America/Chicago",
	"concord":                        "America/New_York",
	"keywest":                        "America/New_York",
	"westpalmbeach":                  "America/New_York",
	"sarasota":                       "America/New_York",
	"daytonabeach":                   "America/New_York",
	"gainesville":                    "America/New_York",
	"ft.myers":                       "America/New_York",
	"brunswick":                      "America/New_York",
	"augusta":                        "America/New_York",
	"vicksburg":                      "America/Chicago",
	"myrtlebeach":                    "America/New_York",
	"charleston":                     "America/New_York",
	"peoria":                         "America/Chicago",
	"evansville":                     "America/Chicago",
	"louisville":                     "America/Kentucky/Louisville",
	"lexington":                      "America/New_York",
	"charlotte":                      "America/New_York",
	"youngstown":                     "America/New_York",
	"canton":                         "America/New_York",
	"chattanooga":                    "America/New_York",
	"charlottesville":                "America/New_York",
	"lynchburg":                      "America/New_York",
	"wausau":                         "America/Chicago",
	"ithaca":                         "America/New_York",
	"harrisburg":                     "America/New_York",
	"bangor":                         "America/New_York",
	"saginaw":                        "America/Detroit",
	"ketchikan":                      "America/Sitka",
	"unalaska":                       "America/Nome",
	"togiak":                         "America/Anchorage",
	"reddevil":                       "America/Anchorage",
	"hooperbay":                      "America/Nome",
	"wainwright":                     "America/Anchorage",
	"galena":                         "America/Anchorage",
	"kaktovik":                       "America/Anchorage",
	"skagway":                        "America/Juneau",
	"cordova":                        "America/Anchorage",
	"kenai":                          "America/Anchorage",
	"fortyukon":                      "America/Anchorage",
	"sanbernardino":                  "America/Los_Angeles",
	"bridgeport":                     "America/New_York",
	"internationalfalls":             "America/Chicago",
	"st.paul":                        "America/Chicago",
	"billings":                       "America/Denver",
	"greatfalls":                     "America/Denver",
	"missoula":                       "America/Denver",
	"minot":                          "America/Chicago",
	"fargo":                          "America/Chicago",
	"hilo":                           "Pacific/Honolulu",
	"olympia":                        "America/Los_Angeles",
	"spokane":                        "America/Los_Angeles",
	"flagstaff":                      "America/Phoenix",
	"tucson":                         "America/Phoenix",
	"fresno":                         "America/Los_Angeles",
	"eureka":                         "America/Los_Angeles",
	"coloradosprings":                "America/Denver",
	"reno":                           "America/Los_Angeles",
	"elko":                           "America/Los_Angeles",
	"albuquerque":                    "America/Denver",
	"casper":                         "America/Denver",
	"topeka":                         "America/Chicago",
	"tulsa":                          "America/Chicago",
	"siouxfalls":                     "America/Chicago",
	"shreveport":                     "America/Chicago",
	"batonrouge":                     "America/Chicago",
	"ft.worth":                       "America/Chicago",
	"corpuschristi":                  "America/Chicago",
	"austin":                         "America/Chicago",
	"amarillo":                       "America/Chicago",
	"elpaso":                         "America/Denver",
	"laredo":                         "America/Chicago",
	"montgomery":                     "America/Chicago",
	"tallahassee":                    "America/New_York",
	"orlando":                        "America/New_York",
	"savannah":                       "America/New_York",
	"indianapolis":                   "America/Indiana/Indianapolis",
	"knoxville":                      "America/New_York",
	"baltimore":                      "America/New_York",
	"syracuse":                       "America/New_York",
	"saultste.marie":                 "America/Detroit",
	"sitka":                          "America/Sitka",
	"helena":                         "America/Denver",
	"bismarck":                       "America/Chicago",
	"boise":                          "America/Boise",
	"sacramento":                     "America/Los_Angeles",
	"saltlakecity":                   "America/Denver",
	"cheyenne":                       "America/Denver",
	"desmoines":                      "America/Chicago",
	"omaha":                          "America/Chicago",
	"oklahomacity":                   "America/Chicago",
	"pierre":                         "America/Chicago",
	"raleigh":                        "America/New_York",
	"cleveland":                      "America/New_York",
	"cincinnati":                     "America/New_York",
	"nashville":                      "America/Chicago",
	"memphis":                        "America/Chicago",
	"milwaukee":                      "America/Chicago",
	"buffalo":                        "America/New_York",
	"pittsburgh":                     "America/New_York",
	"kodiak":                         "America/Anchorage",
	"coldbay":                        "America/Nome",
	"bethel":                         "America/Anchorage",
	"pointhope":                      "America/Nome",
	"barrow":                         "America/Anchorage",
	"nome":                           "America/Nome",
	"juneau":                         "America/Juneau",
	"fairbanks":                      "America/Anchorage",
	"prudhoebay":                     "America/Anchorage",
	"minneapolis":                    "America/Chicago",
	"honolulu":                       "Pacific/Honolulu",
	"seattle":                        "America/Los_Angeles",
	"phoenix":                        "America/Phoenix",
	"sandiego":                       "America/Los_Angeles",
	"st.louis":                       "America/Chicago",
	"neworleans":                     "America/Chicago",
	"dallas":                         "America/Chicago",
	"boston":                         "America/New_York",
	"tampa":                          "America/New_York",
	"philadelphia":                   "America/New_York",
	"detroit":                        "America/Detroit",
	"anchorage":                      "America/Anchorage",
	"denver":                         "America/Denver",
	"houston":                        "America/Chicago",
	"miami":                          "America/New_York",
	"atlanta":                        "America/New_York",
	"chicago":                        "America/Chicago",
	"washington,d.c.":                "America/New_York",
	"newyork":                        "America/New_York",
	"christiansted":                  "America/St_Thomas",
	"coloniadelsacramento":           "America/Montevideo",
	"fraybentos":                     "America/Montevideo",
	"canelones":                      "America/Montevideo",
	"florida":                        "America/Montevideo",
	"artigas":                        "America/Montevideo",
	"baltasarbrum":                   "America/Montevideo",
	"tranqueras":                     "America/Montevideo",
	"tacuarembo":                     "America/Montevideo",
	"pasodelostoros":                 "America/Montevideo",
	"vergara":                        "America/Montevideo",
	"treintaytres":                   "America/Montevideo",
	"santalucia":                     "America/Montevideo",
	"josebatlleyordonez":             "America/Montevideo",
	"minas":                          "America/Montevideo",
	"maldonado":                      "America/Montevideo",
	"puntadeleste":                   "America/Montevideo",
	"aigua":                          "America/Montevideo",
	"lapaloma":                       "America/Montevideo",
	"carmelo":                        "America/Montevideo",
	"bellaunion":                     "America/Montevideo",
	"melo":                           "America/Montevideo",
	"rivera":                         "America/Montevideo",
	"lascano":                        "America/Montevideo",
	"castillos":                      "America/Montevideo",
	"sanjosedemayo":                  "America/Montevideo",
	"rocha":                          "America/Montevideo",
	"paysandu":                       "America/Montevideo",
	"salto":                          "America/Montevideo",
	"durazno":                        "America/Montevideo",
	"montevideo":                     "America/Montevideo",
	"khujayli":                       "Asia/Samarkand",
	"urgut":                          "Asia/Samarkand",
	"kattaqorgon":                    "Asia/Samarkand",
	"denow":                          "Asia/Samarkand",
	"guliston":                       "Asia/Tashkent",
	"iskandar":                       "Asia/Tashkent",
	"chirchiq":                       "Asia/Tashkent",
	"kogon":                          "Asia/Samarkand",
	"khiwa":                          "Asia/Samarkand",
	"chimboy":                        "Asia/Samarkand",
	"qunghirot":                      "Asia/Samarkand",
	"zarafshon":                      "Asia/Samarkand",
	"navoi":                          "Asia/Samarkand",
	"shahrisabz":                     "Asia/Samarkand",
	"qarshi":                         "Asia/Samarkand",
	"qoqon":                          "Asia/Tashkent",
	"jizzax":                         "Asia/Tashkent",
	"angren":                         "Asia/Tashkent",
	"olmaliq":                        "Asia/Tashkent",
	"muynoq":                         "Asia/Samarkand",
	"termiz":                         "Asia/Samarkand",
	"fargona":                        "Asia/Tashkent",
	"namangan":                       "Asia/Tashkent",
	"urgentch":                       "Asia/Samarkand",
	"bukhara":                        "Asia/Samarkand",
	"nukus":                          "Asia/Samarkand",
	"andijon":                        "Asia/Tashkent",
	"samarqand":                      "Asia/Samarkand",
	"tashkent":                       "Asia/Tashkent",
	"luganville":                     "Pacific/Efate",
	"port-vila":                      "Pacific/Efate",
	"vaticancity":                    "Europe/Rome",
	"sanjuandelosmorros":             "America/Caracas",
	"laasuncion":                     "America/Caracas",
	"guasdualito":                    "America/Caracas",
	"barinas":                        "America/Caracas",
	"valera":                         "America/Caracas",
	"cabimas":                        "America/Caracas",
	"carora":                         "America/Caracas",
	"guanare":                        "America/Caracas",
	"puertolacruz":                   "America/Caracas",
	"anaco":                          "America/Caracas",
	"losteques":                      "America/Caracas",
	"valledelapascua":                "America/Caracas",
	"ocumaredeltuy":                  "America/Caracas",
	"carupano":                       "America/Caracas",
	"santarita":                      "America/Caracas",
	"machiques":                      "America/Caracas",
	"sancarlosdelzulia":              "America/Caracas",
	"puertocabello":                  "America/Caracas",
	"acarigua":                       "America/Caracas",
	"upata":                          "America/Caracas",
	"elmanteco":                      "America/Caracas",
	"chaguaramas":                    "America/Caracas",
	"eltigre":                        "America/Caracas",
	"maiquetia":                      "America/Caracas",
	"calabozo":                       "America/Caracas",
	"zaraza":                         "America/Caracas",
	"altagraciadeorituco":            "America/Caracas",
	"tucupita":                       "America/Caracas",
	"porlamar":                       "America/Caracas",
	"sanfernandodeapure":             "America/Caracas",
	"barquisimeto":                   "America/Caracas",
	"maturin":                        "America/Caracas",
	"cumana":                         "America/Caracas",
	"coro":                           "America/Caracas",
	"puntofijo":                      "America/Caracas",
	"laesmeralda":                    "America/Caracas",
	"ciudadbolivar":                  "America/Caracas",
	"maracay":                        "America/Caracas",
	"puertoayacucho":                 "America/Caracas",
	"ciudadguayana":                  "America/Caracas",
	"maracaibo":                      "America/Caracas",
	"caracas":                        "America/Caracas",
	"tayninh":                        "Asia/Ho_Chi_Minh",
	"luanchau":                       "Asia/Ho_Chi_Minh",
	"backan":                         "Asia/Ho_Chi_Minh",
	"langson":                        "Asia/Ho_Chi_Minh",
	"sonla":                          "Asia/Ho_Chi_Minh",
	"tuyenquang":                     "Asia/Ho_Chi_Minh",
	"yenbai":                         "Asia/Ho_Chi_Minh",
	"haiduong":                       "Asia/Ho_Chi_Minh",
	"thaibinh":                       "Asia/Ho_Chi_Minh",
	"tuyhoa":                         "Asia/Ho_Chi_Minh",
	"thudaumot":                      "Asia/Ho_Chi_Minh",
	"dongha":                         "Asia/Ho_Chi_Minh",
	"caolanh":                        "Asia/Ho_Chi_Minh",
	"trucgiang":                      "Asia/Ho_Chi_Minh",
	"travinh":                        "Asia/Ho_Chi_Minh",
	"vinhlong":                       "Asia/Ho_Chi_Minh",
	"caobang":                        "Asia/Ho_Chi_Minh",
	"honggai":                        "Asia/Ho_Chi_Minh",
	"campha":                         "Asia/Ho_Chi_Minh",
	"laochi":                         "Asia/Ho_Chi_Minh",
	"hoabinh":                        "Asia/Ho_Chi_Minh",
	"sontay":                         "Asia/Ho_Chi_Minh",
	"ninhbinh":                       "Asia/Ho_Chi_Minh",
	"viettri":                        "Asia/Ho_Chi_Minh",
	"bacgiang":                       "Asia/Ho_Chi_Minh",
	"hatinh":                         "Asia/Ho_Chi_Minh",
	"buonmethuot":                    "Asia/Ho_Chi_Minh",
	"dalat":                          "Asia/Ho_Chi_Minh",
	"phanrang":                       "Asia/Ho_Chi_Minh",
	"honquan":                        "Asia/Ho_Chi_Minh",
	"kontum":                         "Asia/Ho_Chi_Minh",
	"quangngai":                      "Asia/Ho_Chi_Minh",
	"quangtri":                       "Asia/Ho_Chi_Minh",
	"vungtau":                        "Asia/Ho_Chi_Minh",
	"phanthiet":                      "Asia/Ho_Chi_Minh",
	"longxuyen":                      "Asia/Ho_Chi_Minh",
	"chaudoc":                        "Asia/Ho_Chi_Minh",
	"rachgia":                        "Asia/Ho_Chi_Minh",
	"tanan":                          "Asia/Ho_Chi_Minh",
	"mytho":                          "Asia/Ho_Chi_Minh",
	"baclieu":                        "Asia/Ho_Chi_Minh",
	"camau":                          "Asia/Ho_Chi_Minh",
	"soctrang":                       "Asia/Ho_Chi_Minh",
	"hagiang":                        "Asia/Ho_Chi_Minh",
	"thainguyen":                     "Asia/Ho_Chi_Minh",
	"thanhhoa":                       "Asia/Ho_Chi_Minh",
	"namdinh":                        "Asia/Ho_Chi_Minh",
	"vinh":                           "Asia/Ho_Chi_Minh",
	"donghoi":                        "Asia/Ho_Chi_Minh",
	"playku":                         "Asia/Ho_Chi_Minh",
	"nhatrang":                       "Asia/Ho_Chi_Minh",
	"camranh":                        "Asia/Ho_Chi_Minh",
	"quinhon":                        "Asia/Ho_Chi_Minh",
	"hue":                            "Asia/Ho_Chi_Minh",
	"bienhoa":                        "Asia/Ho_Chi_Minh",
	"cantho":                         "Asia/Ho_Chi_Minh",
	"haiphong":                       "Asia/Ho_Chi_Minh",
	"danang":                         "Asia/Ho_Chi_Minh",
	"hanoi":                          "Asia/Ho_Chi_Minh",
	"hochiminhcity":                  "Asia/Ho_Chi_Minh",
	"birlehlou":                      "Africa/El_Aaiun",
	"'ataq":                          "Asia/Aden",
	"marib":                          "Asia/Aden",
	"dhamar":                         "Asia/Aden",
	"ibb":                            "Asia/Aden",
	"ashshihr":                       "Asia/Aden",
	"zabid":                          "Asia/Aden",
	"hajjah":                         "Asia/Aden",
	"lahij":                          "Asia/Aden",
	"alghaydah":                      "Asia/Aden",
	"rida":                           "Asia/Aden",
	"hadiboh":                        "Asia/Aden",
	"saywun":                         "Asia/Aden",
	"sadah":                          "Asia/Aden",
	"alhudaydah":                     "Asia/Aden",
	"sayhut":                         "Asia/Aden",
	"almukalla":                      "Asia/Aden",
	"taizz":                          "Asia/Aden",
	"aden":                           "Asia/Aden",
	"sanaa":                          "Asia/Aden",
	"kawambwa":                       "Africa/Lusaka",
	"nchelenge":                      "Africa/Lusaka",
	"chinsali":                       "Africa/Lusaka",
	"kasama":                         "Africa/Lusaka",
	"kapirimposhi":                   "Africa/Lusaka",
	"mumbwa":                         "Africa/Lusaka",
	"chingola":                       "Africa/Lusaka",
	"chililabombwe":                  "Africa/Lusaka",
	"nyimba":                         "Africa/Lusaka",
	"lundazi":                        "Africa/Lusaka",
	"chipata":                        "Africa/Lusaka",
	"mwinilunga":                     "Africa/Lusaka",
	"kasempa":                        "Africa/Lusaka",
	"solwezi":                        "Africa/Lusaka",
	"choma":                          "Africa/Lusaka",
	"mongu":                          "Africa/Lusaka",
	"kaoma":                          "Africa/Lusaka",
	"sesheke":                        "Africa/Lusaka",
	"lukulu":                         "Africa/Lusaka",
	"kalabo":                         "Africa/Lusaka",
	"senanga":                        "Africa/Lusaka",
	"mansa":                          "Africa/Lusaka",
	"mpika":                          "Africa/Lusaka",
	"mbala":                          "Africa/Lusaka",
	"luanshya":                       "Africa/Lusaka",
	"ndola":                          "Africa/Lusaka",
	"zambezi":                        "Africa/Lusaka",
	"kafue":                          "Africa/Lusaka",
	"mazabuka":                       "Africa/Lusaka",
	"kabwe":                          "Africa/Lusaka",
	"mufulira":                       "Africa/Lusaka",
	"kitwe":                          "Africa/Lusaka",
	"livingstone":                    "Africa/Lusaka",
	"lusaka":                         "Africa/Lusaka",
	"mazowe":                         "Africa/Harare",
	"shamva":                         "Africa/Harare",
	"victoriafalls":                  "Africa/Harare",
	"zvishavane":                     "Africa/Harare",
	"kwekwe":                         "Africa/Harare",
	"plumtree":                       "Africa/Harare",
	"beitbridge":                     "Africa/Harare",
	"gwanda":                         "Africa/Harare",
	"chiredzi":                       "Africa/Harare",
	"masvingo":                       "Africa/Harare",
	"karoi":                          "Africa/Harare",
	"chinhoyi":                       "Africa/Harare",
	"kariba":                         "Africa/Harare",
	"hwange":                         "Africa/Harare",
	"gweru":                          "Africa/Harare",
	"mutare":                         "Africa/Harare",
	"kadoma":                         "Africa/Harare",
	"chitungwiza":                    "Africa/Harare",
	"harare":                         "Africa/Harare",
	"bulawayo":                       "Africa/Harare",
	"copenhagen":                     "Europe/Copenhagen",
	"oakleigh":                       "Australia/Melbourne",
	"oakpark":                        "America/Chicago",
}

var iso2ToTimezone = map[string]string{
	"af":  "Asia/Kabul",
	"ax":  "Europe/Mariehamn",
	"al":  "Europe/Tirane",
	"dz":  "Africa/Algiers",
	"as":  "Pacific/Pago_Pago",
	"ad":  "Europe/Andorra",
	"ao":  "Africa/Luanda",
	"ag":  "America/Antigua",
	"ar":  "America/Argentina/Buenos_Aires",
	"am":  "Asia/Yerevan",
	"aw":  "America/Aruba",
	"au":  "Australia/Melbourne",
	"at":  "Europe/Vienna",
	"az":  "Asia/Baku",
	"bh":  "Asia/Bahrain",
	"bd":  "Asia/Dhaka",
	"bb":  "America/Barbados",
	"by":  "Europe/Minsk",
	"be":  "Europe/Brussels",
	"bz":  "America/Belize",
	"bj":  "Africa/Porto-Novo",
	"bm":  "Atlantic/Bermuda",
	"bt":  "Asia/Thimphu",
	"bo":  "America/La_Paz",
	"ba":  "Europe/Sarajevo",
	"bw":  "Africa/Gaborone",
	"br":  "America/Sao_Paulo",
	"bn":  "Asia/Brunei",
	"bg":  "Europe/Sofia",
	"bf":  "Africa/Ouagadougou",
	"bi":  "Africa/Bujumbura",
	"kh":  "Asia/Phnom_Penh",
	"cm":  "Africa/Douala",
	"ca":  "America/Toronto",
	"cv":  "Atlantic/Cape_Verde",
	"ky":  "America/Cayman",
	"cf":  "Africa/Bangui",
	"td":  "Africa/Ndjamena",
	"cl":  "America/Santiago",
	"cn":  "Asia/Shanghai",
	"co":  "America/Bogota",
	"km":  "Indian/Comoro",
	"cg":  "Africa/Brazzaville",
	"cd":  "Africa/Kinshasa",
	"ck":  "Pacific/Rarotonga",
	"cr":  "America/Costa_Rica",
	"hr":  "Europe/Zagreb",
	"cu":  "America/Havana",
	"cw":  "America/Curacao",
	"cy":  "Asia/Nicosia",
	"cz":  "Europe/Prague",
	"dk":  "Europe/Copenhagen",
	"dj":  "Africa/Djibouti",
	"dm":  "America/Dominica",
	"do":  "America/Santo_Domingo",
	"tl":  "Asia/Dili",
	"ec":  "America/Guayaquil",
	"eg":  "Africa/Cairo",
	"sv":  "America/El_Salvador",
	"gq":  "Africa/Malabo",
	"er":  "Africa/Asmara",
	"ee":  "Europe/Tallinn",
	"et":  "Africa/Addis_Ababa",
	"fk":  "Atlantic/Stanley",
	"fo":  "Atlantic/Faroe",
	"fm":  "Pacific/Pohnpei",
	"fj":  "Pacific/Fiji",
	"fi":  "Europe/Helsinki",
	"fr":  "Europe/Paris",
	"gf":  "America/Cayenne",
	"mq":  "America/Martinique",
	"gp":  "America/Guadeloupe",
	"re":  "Indian/Reunion",
	"yt":  "Indian/Mayotte",
	"pf":  "Pacific/Tahiti",
	"ga":  "Africa/Libreville",
	"ge":  "Asia/Tbilisi",
	"de":  "Europe/Berlin",
	"gh":  "Africa/Accra",
	"gi":  "Europe/Gibraltar",
	"gr":  "Europe/Athens",
	"gl":  "America/Godthab",
	"gd":  "America/Grenada",
	"gu":  "Pacific/Guam",
	"gt":  "America/Guatemala",
	"gn":  "Africa/Conakry",
	"gw":  "Africa/Bissau",
	"gy":  "America/Guyana",
	"ht":  "America/Port-au-Prince",
	"hn":  "America/Tegucigalpa",
	"hk":  "Asia/Hong_Kong",
	"hu":  "Europe/Budapest",
	"is":  "Atlantic/Reykjavik",
	"in":  "Asia/Kolkata",
	"id":  "Asia/Jakarta",
	"ir":  "Asia/Tehran",
	"iq":  "Asia/Baghdad",
	"ie":  "Europe/Dublin",
	"im":  "Europe/Isle_of_Man",
	"il":  "Asia/Jerusalem",
	"it":  "Europe/Rome",
	"ci":  "Africa/Abidjan",
	"jm":  "America/Jamaica",
	"jp":  "Asia/Tokyo",
	"jo":  "Asia/Amman",
	"kz":  "Asia/Almaty",
	"ke":  "Africa/Nairobi",
	"ki":  "Pacific/Tarawa",
	"-99": "Africa/Mogadishu",
	"kw":  "Asia/Kuwait",
	"kg":  "Asia/Bishkek",
	"la":  "Asia/Vientiane",
	"lv":  "Europe/Riga",
	"lb":  "Asia/Beirut",
	"ls":  "Africa/Maseru",
	"lr":  "Africa/Monrovia",
	"ly":  "Africa/Tripoli",
	"li":  "Europe/Vaduz",
	"lt":  "Europe/Vilnius",
	"lu":  "Europe/Luxembourg",
	"mo":  "Asia/Macau",
	"mk":  "Europe/Skopje",
	"mg":  "Indian/Antananarivo",
	"mw":  "Africa/Blantyre",
	"my":  "Asia/Kuala_Lumpur",
	"mv":  "Indian/Maldives",
	"ml":  "Africa/Bamako",
	"mt":  "Europe/Malta",
	"mh":  "Pacific/Majuro",
	"mr":  "Africa/Nouakchott",
	"mu":  "Indian/Mauritius",
	"mx":  "America/Mexico_City",
	"md":  "Europe/Chisinau",
	"mc":  "Europe/Paris",
	"mn":  "Asia/Ulaanbaatar",
	"me":  "Europe/Podgorica",
	"ma":  "Africa/Casablanca",
	"mz":  "Africa/Maputo",
	"mm":  "Asia/Rangoon",
	"na":  "Africa/Windhoek",
	"np":  "Asia/Kathmandu",
	"nl":  "Europe/Amsterdam",
	"nc":  "Pacific/Noumea",
	"nz":  "Pacific/Auckland",
	"ni":  "America/Managua",
	"ne":  "Africa/Niamey",
	"ng":  "Africa/Lagos",
	"kp":  "Asia/Pyongyang",
	"mp":  "Pacific/Saipan",
	"no":  "Europe/Oslo",
	"om":  "Asia/Muscat",
	"pk":  "Asia/Karachi",
	"pw":  "Pacific/Palau",
	"ps":  "Asia/Gaza",
	"pa":  "America/Panama",
	"pg":  "Pacific/Port_Moresby",
	"py":  "America/Asuncion",
	"pe":  "America/Lima",
	"ph":  "Asia/Manila",
	"pl":  "Europe/Warsaw",
	"pt":  "Europe/Lisbon",
	"pr":  "America/Puerto_Rico",
	"qa":  "Asia/Qatar",
	"ro":  "Europe/Bucharest",
	"ru":  "Europe/Moscow",
	"rw":  "Africa/Kigali",
	"kn":  "America/St_Kitts",
	"lc":  "America/St_Lucia",
	"vc":  "America/St_Vincent",
	"ws":  "Pacific/Apia",
	"sm":  "Africa/Dakar",
	"st":  "Africa/Sao_Tome",
	"sa":  "Asia/Riyadh",
	"rs":  "Europe/Belgrade",
	"sc":  "Indian/Mahe",
	"sl":  "Africa/Freetown",
	"sg":  "Asia/Singapore",
	"sk":  "Europe/Bratislava",
	"si":  "Europe/Ljubljana",
	"sb":  "Pacific/Guadalcanal",
	"so":  "Africa/Mogadishu",
	"za":  "Africa/Johannesburg",
	"gs":  "Atlantic/South_Georgia",
	"kr":  "Asia/Seoul",
	"ss":  "Africa/Juba",
	"es":  "Europe/Madrid",
	"lk":  "Asia/Colombo",
	"sd":  "Africa/Khartoum",
	"sr":  "America/Paramaribo",
	"sj":  "Arctic/Longyearbyen",
	"sz":  "Africa/Mbabane",
	"se":  "Europe/Stockholm",
	"ch":  "Europe/Zurich",
	"sy":  "Asia/Damascus",
	"tw":  "Asia/Taipei",
	"tj":  "Asia/Dushanbe",
	"tz":  "Africa/Dar_es_Salaam",
	"th":  "Asia/Bangkok",
	"bs":  "America/Nassau",
	"gm":  "Africa/Banjul",
	"tg":  "Africa/Lome",
	"to":  "Pacific/Tongatapu",
	"tt":  "America/Port_of_Spain",
	"tn":  "Africa/Tunis",
	"tr":  "Europe/Istanbul",
	"tm":  "Asia/Ashgabat",
	"tc":  "America/Grand_Turk",
	"tv":  "Pacific/Funafuti",
	"ug":  "Africa/Kampala",
	"ua":  "Europe/Kyiv",
	"ae":  "Asia/Dubai",
	"gb":  "Europe/London",
	"us":  "America/Chicago",
	"vi":  "America/St_Thomas",
	"uy":  "America/Montevideo",
	"uz":  "Asia/Tashkent",
	"vu":  "Pacific/Efate",
	"va":  "Europe/Rome",
	"ve":  "America/Caracas",
	"vn":  "Asia/Ho_Chi_Minh",
	"eh":  "Africa/El_Aaiun",
	"ye":  "Asia/Aden",
	"zm":  "Africa/Lusaka",
	"zw":  "Africa/Harare",
}
