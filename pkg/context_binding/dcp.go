package contextbinding

import (
	"context"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

type ContextDCPsKey struct{}

func BindDCPs(target context.Context, dcpInfo *domain.PartnerDCPs) context.Context {
	return context.WithValue(target, ContextDCPsKey{}, dcpInfo)
}

func CopyDCPs(target context.Context, ctxVal context.Context) context.Context {
	return context.WithValue(target, ContextDCPsKey{}, ctxVal.Value(ContextDCPsKey{}))
}
