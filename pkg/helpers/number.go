package helpers

import (
	"math"
	"regexp"
	"strconv"
)

func NormalizeZero(f float64) float64 {
	if f == 0 && (1/f) < 0 {
		return 0 // <PERSON><PERSON><PERSON>n đổi -0 thành 0
	}
	return f
}

func ParseFloat(str string, useDefault bool) (float64, error) {
	if str == "" && useDefault {
		str = "0"
	}
	return strconv.ParseFloat(str, 64)
}

func RoundToPrecision(value float64, precision int) float64 {
	pow := math.Pow(10, float64(precision))
	return NormalizeZero(math.Round(value*pow) / pow)
}

func CeilToPrecision(value float64, precision int) float64 {
	pow := math.Pow(10, float64(precision))
	return NormalizeZero(math.Ceil(value*pow) / pow)
}

func RemoveNonDigitCharacters(input string) string {
	re := regexp.MustCompile(`\D`)
	return re.ReplaceAllString(input, "")
}
