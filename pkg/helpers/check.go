package helpers

import (
	"fmt"
	"reflect"
)

func CheckFieldsInitialized(value interface{}) []string {
	v := reflect.ValueOf(value)

	undefinedField := []string{}
	// Nếu giá trị không phải là struct, không cần kiểm tra
	if v.Kind() != reflect.Struct {
		fmt.Println("Value is not a struct")
		return undefinedField
	}

	// Duyệt qua các trường của struct
	for i := 0; i < v.NumField(); i++ {
		field := v.Field(i)
		fieldType := v.Type().Field(i)

		if field.IsZero() {
			undefinedField = append(undefinedField, fieldType.Name)
		}
	}

	return undefinedField
}
