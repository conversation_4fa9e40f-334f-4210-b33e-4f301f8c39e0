package helpers

import (
	"fmt"

	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
)

func GetPaxKey(firstName, lastName string, gender commonEnum.GenderType) string {
	return fmt.Sprintf("%s/%s/%d", firstName, lastName, gender)
}

func GetPaxTypeFromAge(age int) enum.PaxType {
	if age > 11 {
		return enum.PaxTypeAdult
	}

	if age > 1 && age < 12 {
		return enum.PaxTypeChildren
	}

	if age < 2 && age > -1 {
		return enum.PaxTypeInfant
	}

	return enum.PaxTypeNone
}
