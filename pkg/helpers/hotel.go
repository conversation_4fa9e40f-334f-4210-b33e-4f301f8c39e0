package helpers

import (
	"fmt"
	"math/rand"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
)

// Change this func might affect RetrieveHotelByID func
func GenerateHotelID(p enum.HotelProvider) string {
	const retry = 2

	for i := 0; i < retry; i++ {
		id := uuid.New().String()

		if id != "" {
			return strconv.Itoa(int(p)) + id
		}
	}
	return ""
}

func GenerateBookingCode() string {
	source := rand.NewSource(time.Now().UnixNano())
	randomNumber := rand.New(source).Intn(900000000) + 100000000
	randomNumberString := fmt.Sprintf("%09d", randomNumber)

	for randomNumberString[0] == '0' || hasConsecutiveDigits(randomNumberString) {
		randomNumber = rand.New(source).Intn(900000000) + 100000000
		randomNumberString = fmt.Sprintf("%09d", randomNumber)
	}

	return fmt.Sprintf("%d", randomNumber)
}

func hasConsecutiveDigits(numberString string) bool {
	for i := 0; i < len(numberString)-3; i++ {
		if numberString[i] == numberString[i+1] && numberString[i] == numberString[i+2] && numberString[i] == numberString[i+3] {
			return true
		}
	}
	return false
}

func EncodeSpecialChars(input string) string {
	replacements := map[string]string{
		"<": "&lt;",
		">": "&gt;",
		"(": "&#40;",
		")": "&#41;",
		"&": "&amp;",
	}

	// Use strings.Builder for efficient string concatenation
	var builder strings.Builder

	for _, char := range input {
		strChar := string(char)
		if replacement, exists := replacements[strChar]; exists {
			builder.WriteString(replacement)
		} else {
			builder.WriteString(strChar)
		}
	}

	return builder.String()
}

func ConvertToVietnameseDateFormat(dateStr string) string {
	parsedTime, err := time.Parse(time.RFC3339, dateStr)
	if err != nil {
		layout := "2006-01-02T15:04:05"
		parsedTime, err = time.Parse(layout, dateStr)
		if err != nil {
			layout := "2006-01-02T15:04"
			parsedTime, err = time.Parse(layout, dateStr)
			if err != nil {
				return ""
			}
		}
	}
	// location := parsedTime.Location()

	// localTime := parsedTime.In(location)

	// formattedDate := fmt.Sprintf("%02d tháng %02d năm %d", parsedTime.Day(), parsedTime.Month(), parsedTime.Year())

	return parsedTime.Format("02 tháng 01 năm 2006 15:04")
}
