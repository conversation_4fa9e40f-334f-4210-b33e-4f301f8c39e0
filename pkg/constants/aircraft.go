package constants

func GetAirCraftName(code string) string {
	t := aircraftMap[code]

	if t != "" {
		return t
	}

	return code
}

var aircraftMap = map[string]string{
	"A124": "Antonov A124",
	"A140": "Antonov A140",
	"A148": "Antonov A148",
	"A158": "Antonov A158",
	"A19N": "Airbus A319N",
	"A20N": "Airbus A320N",
	"A21N": "Airbus A321N",
	"A225": "Antonov 225",
	"A306": "Airbus A306",
	"A30B": "Airbus A300B2",
	"A310": "Airbus A310",
	"A318": "Airbus A318",
	"A319": "Airbus A319",
	"A320": "Airbus A320",
	"A321": "Airbus A321",
	"A332": "Airbus A332",
	"A333": "Airbus A333",
	"A337": "Airbus BelugaXL",
	"A338": "Airbus A330",
	"A339": "Airbus A330",
	"A342": "Airbus A340",
	"A343": "Airbus A340",
	"A345": "Airbus A340",
	"A346": "Airbus A340",
	"A359": "Airbus A359",
	"A35K": "Airbus A350",
	"A388": "Airbus A380",
	"A3ST": "Airbus Beluga",
	"AJ27": "COMAC AJ27",
	"AN24": "Antonov AN24",
	"AN28": "Antonov AN28",
	"AN30": "Antonov AN30",
	"AN32": "Antonov AN32",
	"AN72": "Antonov AN72",
	"AT43": "ATR 42",
	"AT45": "ATR 42",
	"AT46": "ATR 42",
	"AT72": "ATR 72",
	"AT73": "ATR 72",
	"AT75": "ATR 72",
	"AT76": "ATR 72",
	"ATP":  "British ATP",
	"B37M": "Boeing 737",
	"B38M": "Boeing 737",
	"B39M": "Boeing 737",
	"B3XM": "Boeing 737",
	"B462": "Avro RJ85",
	"B463": "BAe B463",
	"B703": "Boeing 707",
	"B712": "Boeing 717",
	"B720": "Boeing 720B",
	"B721": "Boeing 727",
	"B722": "Boeing 727",
	"B732": "Boeing 737",
	"B733": "Boeing 737",
	"B734": "Boeing 737",
	"B735": "Boeing 737",
	//
	"B736": "Boeing 737",
	"B738": "Boeing 737",
	"B737": "Boeing 737",
	"B739": "Boeing 737",
	"B741": "Boeing 747",
	"B742": "Boeing 747",
	"B743": "Boeing 747",
	"B744": "Boeing 747",
	"B748": "Boeing 747",
	"B74R": "Boeing 747",
	"B74S": "Boeing 747",
	"B752": "Boeing 757",
	"B753": "Boeing 757",
	"B762": "Boeing 767",
	"B763": "Boeing 767",
	"B764": "Boeing 767",
	"B772": "Boeing 777",
	"B773": "Boeing 777",
	"B778": "Boeing 777",
	"B779": "Boeing 777",
	"B77L": "Boeing 777",
	"B77W": "Boeing 777",
	"B788": "Boeing 787",
	"B789": "Boeing 787",
	"B78X": "Boeing 787",
	"BA11": "British BA11",
	"BCS1": "Airbus A220",
	"BCS3": "Airbus A220",
	"BLCF": "Boeing BLCF",
	"MD11": "McDonnell MD11",
	"MD81": "McDonnell MD81",
	"MD82": "McDonnell MD82",
	"MD83": "McDonnell MD83",
	"MD87": "McDonnell MD87",
	"MD88": "McDonnell MD88",
	"MD90": "McDonnell MD90",
	"MU2":  "Mitsubishi MU2",
	"N262": "Aerospatiale N262",
	"P8":   "Boeing P8",
	"RJ1H": "Avro RJ1H",
	"RJ70": "Avro RJ70",
	"RJ85": "Avro RJ85",
	"YK40": "Yakovlev YK40",
	"YK42": "Yakovlev YK42",
	//
	"A4F": "Antonov A124",
	"A40": "Antonov A140",
	"A81": "Antonov A148",
	"A58": "Antonov A158",
	"31N": "Airbus A319N",
	"32N": "Airbus A320N",
	"32Q": "Airbus A321N",
	"A5F": "Antonov 225",
	"AB6": "Airbus A306",
	"AB4": "Airbus A300B2",
	"312": "Airbus A310",
	"313": "Airbus A310",
	"318": "Airbus A318",
	"32C": "Airbus A318",
	"319": "Airbus A319",
	"32D": "Airbus A319",
	"320": "Airbus A320",
	"32A": "Airbus A320",
	"321": "Airbus A321",
	"32B": "Airbus A321",
	"332": "Airbus A332",
	"333": "Airbus A333",
	"33X": "Airbus A332",
	"33Y": "Airbus A333",
	"338": "Airbus A330",
	"339": "Airbus A330",
	"342": "Airbus A340",
	"343": "Airbus A340",
	"345": "Airbus A340",
	"346": "Airbus A340",
	"359": "Airbus A359",
	"351": "Airbus A350",
	"388": "Airbus A380",
	"ABB": "Airbus Beluga",
	"C27": "COMAC AJ27",
	"AN4": "Antonov AN24",
	"A28": "Antonov AN28",
	"A30": "Antonov AN30",
	"A32": "Antonov AN32",
	"AN7": "Antonov AN72",
	"AT4": "ATR 42",
	"AT5": "ATR 42",
	"ATR": "ATR 42",
	"AT7": "ATR 72",
	"7M7": "Boeing 737",
	"7M8": "Boeing 737",
	"7M9": "Boeing 737",
	"7MJ": "Boeing 737",
	"142": "Avro RJ85",
	"143": "BAe B463",
	"703": "Boeing 707",
	"717": "Boeing 717",
	"B72": "Boeing 720B",
	"721": "Boeing 727",
	"722": "Boeing 727",
	"732": "Boeing 737",
	"73F": "Boeing 737",
	"733": "Boeing 737",
	"73C": "Boeing 737",
	"73Y": "Boeing 737",
	"734": "Boeing 737",
	"73P": "Boeing 737",
	"735": "Boeing 737",
	"73E": "Boeing 737",
	//
	"736":  "Boeing 737",
	"738":  "Boeing 737",
	"73W":  "Boeing 737",
	"73H":  "Boeing 737",
	"73K":  "Boeing 737",
	"73U":  "Boeing 737",
	"73J":  "Boeing 737",
	"741":  "Boeing 747",
	"74T":  "Boeing 747",
	"742":  "Boeing 747",
	"74C":  "Boeing 747",
	"74X":  "Boeing 747",
	"743":  "Boeing 747",
	"74D":  "Boeing 747",
	"744":  "Boeing 747",
	"74E":  "Boeing 747",
	"74Y":  "Boeing 747",
	"74H":  "Boeing 747",
	"74N":  "Boeing 747",
	"74R":  "Boeing 747",
	"74V":  "Boeing 747",
	"74L":  "Boeing 747",
	"752":  "Boeing 757",
	"75F":  "Boeing 757F",
	"753":  "Boeing 757",
	"762":  "Boeing 767",
	"76X":  "Boeing 767",
	"763":  "Boeing 767",
	"76W":  "Boeing 767",
	"76Y":  "Boeing 767",
	"764":  "Boeing 767",
	"772":  "Boeing 777",
	"773":  "Boeing 777",
	"778":  "Boeing 777",
	"779":  "Boeing 777",
	"77X":  "Boeing 777",
	"77L":  "Boeing 777",
	"77W":  "Boeing 777",
	"788":  "Boeing 787",
	"789":  "Boeing 787",
	"781":  "Boeing 787",
	"B11":  "British BA11",
	"221":  "Airbus A220",
	"223":  "Airbus A220",
	"74B":  "Boeing BLCF",
	"M11":  "McDonnell MD11",
	"M1F":  "McDonnell MD11",
	"M1M":  "McDonnell MD11",
	"M81":  "McDonnell MD81",
	"M82":  "McDonnell MD82",
	"M83":  "McDonnell MD83",
	"M87":  "McDonnell MD87",
	"M88":  "McDonnell MD88",
	"M90":  "McDonnell MD90",
	"ND2":  "Aerospatiale N262",
	"AR1":  "Avro RJ1H",
	"AR7":  "Avro RJ70",
	"AR8":  "Avro RJ85",
	"YK4":  "Yakovlev YK40",
	"YK2":  "Yakovlev YK42",
	"330":  "Airbus A330",
	"340":  "Airbus A340",
	"350":  "Airbus A350",
	"380":  "Airbus A380",
	"737":  "Boeing 737",
	"747":  "Boeing 747",
	"757":  "Boeing 757",
	"767":  "Boeing 767",
	"777":  "Boeing 777",
	"787":  "Boeing 787",
	"145":  "Embraer E145",
	"190":  "Embraer E190",
	"72":   "ATR 72",
	"73G":  "Boeing 737-700",
	"739":  "Boeing 737-900",
	"ABY":  "Airbus A300-600 Freighter",
	"320B": "Airbus A320B",
}
