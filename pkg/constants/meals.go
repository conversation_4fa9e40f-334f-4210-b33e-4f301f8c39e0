package constants

var mealsVi = map[string]string{
	"all-inclusive":          "Trọn gói",
	"american-breakfast":     "Bữa sáng kiểu Mỹ",
	"asian-breakfast":        "Bữa sáng kiểu Á",
	"breakfast":              "Bao gồm bữa sáng",
	"breakfast-buffet":       "Bữa sáng tự chọn",
	"breakfast-for-1":        "Bữa sáng cho một khách",
	"breakfast-for-2":        "Bữa sáng cho 2 khách",
	"chinese-breakfast":      "Bữa sáng kiểu Trung Hoa",
	"continental-breakfast":  "Bữa sáng kiểu lục địa",
	"dinner":                 "Bữa tối",
	"english-breakfast":      "Bữa sáng kiểu Anh",
	"full-board":             "Có bao gồm bữa sáng, bữa trưa và bữa tối",
	"half-board":             "<PERSON><PERSON> bao gồm bữa sáng + bữa trưa hoặc bữa tối",
	"half-board-dinner":      "Bữa sáng và bữa tối",
	"half-board-lunch":       "Bữa sáng và bữa trưa",
	"irish-breakfast":        "Bữa sáng kiểu Ailen",
	"israeli-breakfast":      "Bữa sáng kiểu Israel",
	"japanese-breakfast":     "Bữa sáng kiểu Nhật",
	"lunch":                  "Bữa trưa",
	"nomeal":                 "Không bao gồm các bữa ăn",
	"scandinavian-breakfast": "Bữa sáng kiểu Scandinavi",
	"scottish-breakfast":     "Bữa sáng kiểu Scotland",
	"soft-all-inclusive":     "Trọn gói cơ bản",
	"some-meal":              "Bao gồm các bữa ăn",
	"super-all-inclusive":    "Trọn gói cao cấp",
	"ultra-all-inclusive":    "Trọn gói siêu cao cấp",
}

func GetMealName(mealCode string, lang string) string {
	temp := mealsVi[mealCode]
	if temp != "" {
		return temp
	}

	return mealCode
}
