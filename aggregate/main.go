package main

import (
	"bufio"
	"context"
	"fmt"
	"io"
	"log"
	"os"
	"time"

	commonMongoDB "gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"

	pb "gitlab.deepgate.io/apps/api/gen/go/skyhub/admin"
	"google.golang.org/grpc"
)

func streamFile(client pb.HotelAdminServiceClient, provider int32, filePath, language string, index int) error {
	// Mở file JSONL để đọc
	file, err := os.Open(filePath)
	if err != nil {
		return fmt.Errorf("error opening file: %v", err)
	}
	defer file.Close()

	// Tạo context với timeout và stream gRPC
	ctx, cancel := context.WithTimeout(context.Background(), 1000*time.Minute)
	defer cancel()

	stream, err := client.AggregateHotelContent(ctx)
	if err != nil {
		return fmt.Errorf("error creating stream: %v", err)
	}

	var countIndex int64
	reader := bufio.NewReader(file)

	for {
		line, err := reader.ReadBytes('\n')
		if err != nil {
			if err == io.EOF {
				break
			}
			return fmt.Errorf("error reading file: %v", err)
		}

		req := &pb.AggregateHotelContentReq{
			Content:      line,
			IndexContent: countIndex,
			Provider:     provider,
			Language:     language,
		}

		if err := stream.Send(req); err != nil {
			log.Printf("Error sending data: %v", err)
			continue
		}

		log.Printf("File %d: Sent JSON chunk: %d bytes, Index: %d", index, len(line), countIndex)
		countIndex++
	}

	// Đóng stream và nhận phản hồi từ server
	if _, err := stream.CloseAndRecv(); err != nil {
		log.Printf("Error closing stream: %v", err)
	}

	return nil
}

func streamRegionFile(client pb.HotelAdminServiceClient, filePath, language string) error {
	// Mở file JSONL để đọc
	file, err := os.Open(filePath)
	if err != nil {
		return fmt.Errorf("error opening file: %v", err)
	}
	defer file.Close()

	// Tạo context với timeout và stream gRPC
	ctx, cancel := context.WithTimeout(context.Background(), 1000*time.Minute)
	defer cancel()

	stream, err := client.AggregateRegionContent(ctx)
	if err != nil {
		return fmt.Errorf("error creating stream: %v", err)
	}

	var countIndex int64
	reader := bufio.NewReader(file)

	for {
		line, err := reader.ReadBytes('\n')
		if err != nil {
			if err == io.EOF {
				break
			}
			return fmt.Errorf("error reading file: %v", err)
		}

		req := &pb.AggregateRegionContentReq{
			Content:      line,
			IndexContent: countIndex,
			Provider:     14,
			Language:     language,
		}

		if err := stream.Send(req); err != nil {
			log.Printf("Error sending data: %v", err)
			continue
		}

		log.Printf("Sent JSON chunk: %d bytes, Index: %d", len(line), countIndex)
		countIndex++
	}

	// Đóng stream và nhận phản hồi từ server
	if _, err := stream.CloseAndRecv(); err != nil {
		log.Fatalf("Error closing stream: %v", err)
	}

	return nil
}

func MigrateDB() {
	cfg, err := config.New()
	if err != nil {
		panic(err)
	}

	err = commonMongoDB.Migrate(context.Background(), cfg.WriteURL, cfg.MongoDB, cfg.Env)
	if err != nil {
		panic(err)
	}
}

func migrateHotelRoomName(client pb.HotelAdminServiceClient, sourceLanguage, targetLanguage string) error {
	// Tạo context với timeout và stream gRPC
	ctx, cancel := context.WithTimeout(context.Background(), 1000*time.Minute)
	defer cancel()

	req := &pb.MigrateHotelNameContentReq{
		Provider:       14,
		SourceLanguage: sourceLanguage,
		TargetLanguage: targetLanguage,
	}

	_, err := client.MigrateHotelNameContent(ctx, req)
	if err != nil {
		return fmt.Errorf("error MigrateHotelNameContent: %v", err)
	}

	return nil
}

func streamFileHotelReview(client pb.HotelAdminServiceClient, filePath string, index int) error {
	// Mở file JSONL để đọc
	file, err := os.Open(filePath)
	if err != nil {
		return fmt.Errorf("error opening file: %v", err)
	}
	defer file.Close()

	// Tạo context với timeout và stream gRPC
	ctx, cancel := context.WithTimeout(context.Background(), 1000*time.Minute)
	defer cancel()

	stream, err := client.AggregateHotelReview(ctx)
	if err != nil {
		return fmt.Errorf("error creating stream: %v", err)
	}

	var countIndex int64
	reader := bufio.NewReader(file)

	for {
		line, err := reader.ReadBytes('\n')
		if err != nil {
			if err == io.EOF {
				break
			}
			return fmt.Errorf("error reading file: %v", err)
		}

		req := &pb.AggregateHotelReviewReq{
			Provider:          14,
			SkipUpdateOverall: false,
			Content:           line,
		}

		if err := stream.Send(req); err != nil {
			log.Printf("Error sending data: %v", err)
			continue
		}

		log.Printf("File %d: Sent JSON chunk: %d bytes, Index: %d", index, len(line), countIndex)
		countIndex++
	}

	// Đóng stream và nhận phản hồi từ server
	if _, err := stream.CloseAndRecv(); err != nil {
		log.Printf("Error closing stream: %v", err)
	}

	return nil
}

func main() {
	// ConvertRHReviewToJsonl()
	// return;

	// MigrateDB()
	// return;
	// SplitFileJsonl()

	// return

	// Kết nối tới gRPC server ở địa chỉ localhost:3016
	conn, err := grpc.Dial("localhost:3021", grpc.WithInsecure())
	if err != nil {
		log.Fatalf("Failed to connect to server: %v", err)
	}
	defer conn.Close()

	// Tạo client gRPC
	client := pb.NewHotelAdminServiceClient(conn)

	// err = migrateHotelRoomName(client, "en-US", "vi-VN")
	// if err != nil {
	// 	log.Fatalf("Failed to migrateHotelRoomName: %v", err)
	// }
	// return
	// regionFile := "ta_mapping_hotel_bonus_expedia_vi_cv.jsonl"
	// expandedPath, err := expandPath(regionFile)
	// if err != nil {
	// 	fmt.Println("Error:", err)
	// 	return
	// }

	// err = streamRegionFile(client, expandedPath, "vi-VN")
	// if err != nil {
	// 	fmt.Println("Error:", err)
	// 	return
	// }

	// vi-VN : 25
	// en-US : 7

	// for i := 1; i <= 1; i++ {
	// 	filePath := fmt.Sprintf("./output_rh_review/part_%d.jsonl", i)
	// 	err = streamFileHotelReview(client, filePath, i)
	// 	if err != nil {
	// 		// log.Fatalf("Error streaming file: %v", err)
	// 	}
	// 	log.Printf("Finished streaming file part %d, waiting 30 seconds...\n", i)
	// 	// time.Sleep(30 * time.Second)
	// }

	// for i := 17; i <= 37; i++ {
	i := 1
	filePath := "ta_mapping_hotel_bonus_expedia_vi_cv.jsonl"
	err = streamFile(client, 11, filePath, "vi-VN", i)
	if err != nil {
		log.Fatalf("Error streaming file: %v", err)
	}
	log.Printf("Finished streaming file part %d, waiting 30 seconds...\n", i)
	// time.Sleep(30 * time.Second)
	// }
}
