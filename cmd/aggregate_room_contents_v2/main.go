package main

import (
	"context"
	"fmt"
	"os"
	"strconv"
	"sync"
	"time"

	"github.com/samber/lo"
	"gitlab.deepgate.io/apps/common/adapter/mongodb"
	commonMongoDB "gitlab.deepgate.io/apps/common/adapter/mongodb"
	commonDomain "gitlab.deepgate.io/apps/common/domain"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/cmd/aggregate_hotel_contents_v2/helpers"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/models"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

var db commonMongoDB.DB

func main() {
	ctx := context.TODO()
	var err error

	helpers.LoadEnv()

	// Init CommonDB connection.
	db, err = commonMongoDB.New(&commonMongoDB.Config{
		WriteURL: os.Getenv("COMMON_WRITE_URL"),
		ReadURL:  os.Getenv("COMMON_WRITE_URL"),
		Database: os.Getenv("COMMON_MONGO_DB"),
	})
	if err != nil {
		panic(err)
	}

	page := int64(1)

	pagi := commonDomain.Pagination{
		PageLimit:      100,
		PageCurrent:    page,
		SkipCountTotal: true,
	}

	otps := []mongodb.Option{
		mongodb.WithHint("_id_"),
		mongodb.WithSorter(bson.M{"_id": 1}),
		mongodb.WithPaging(&pagi),
	}

	for {
		startT := time.Now()
		cur, err := db.FindWithCursor(ctx, "hotels_v2", otps...)
		if err != nil {
			panic(err)
		}

		var wg sync.WaitGroup

		totalRooms := int(0)
		totalHotel := int64(0)

		for cur.Next(ctx) {
			hotel := &models.Hotel{}
			if err := cur.Decode(hotel); err != nil {
				panic(err)
			}

			ids := lo.Map(hotel.RoomReferences, func(item *models.RoomRefInfo, _ int) primitive.ObjectID {
				return item.ID
			})

			wg.Add(1)
			go func(_ids []primitive.ObjectID) {
				bgCtx, cc := context.WithTimeout(ctx, time.Second*30)
				defer cc()

				defer wg.Done()
				updateFilter := bson.M{
					"_id": bson.M{
						"$in": ids,
					},
				}

				updateSet := bson.M{
					"hotel_id": hotel.HotelID,
				}

				options := &options.UpdateOptions{
					Hint: "_id_",
				}

				err := db.UpdateMany(bgCtx, "hotel_rooms_v2", updateFilter, updateSet, options)
				if err != nil {
					log.Error("db.UpdateMany error", log.Any("error", err), log.Any("updateFilter", updateFilter), log.Any("updateSet", updateSet))
					WriteErrorHotelID(hotel.HotelID, err)
				}
			}(ids)

			totalHotel++
			totalRooms += len(ids)
		}

		wg.Wait()
		cur.Close(ctx)

		WriteDonePage(int(pagi.PageCurrent))

		log.Info("processed page", log.Int64("page", pagi.PageCurrent), log.Duration("duration", time.Since(startT)), log.Int("total_hotels", int(totalHotel)), log.Int("total_rooms", totalRooms))

		pagi.PageCurrent++
	}
}

func WriteDonePage(currentPage int) {
	file, err := os.OpenFile("done_page.txt", os.O_APPEND|os.O_WRONLY|os.O_CREATE, 0644)
	if err != nil {
		fmt.Println(err)
		return
	}
	defer file.Close()

	_, err = fmt.Fprintln(file, strconv.Itoa(currentPage)+"\n"+time.Now().Format("2006-01-02 15:04:05")+"\n")
	if err != nil {
		fmt.Println(err)
		return
	}
}

func WriteErrorHotelID(hotelID string, err error) {
	file, err := os.OpenFile("done_page.txt", os.O_APPEND|os.O_WRONLY|os.O_CREATE, 0644)
	if err != nil {
		fmt.Println(err)
		return
	}
	defer file.Close()

	_, err = fmt.Fprintln(file, hotelID+" | "+err.Error()+"\n")
	if err != nil {
		fmt.Println(err)
		return
	}
}
