package adapter

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/pkg/errors"
	tracingHttp "gitlab.deepgate.io/apps/common/tracing/http"
)

func bindParams(url string, data interface{}) string {

	jsonBytes, err := json.Marshal(data)
	if err != nil {
		fmt.Println("Error marshaling struct:", err)
		return url
	}

	var m map[string]string
	err = json.Unmarshal(jsonBytes, &m)
	if err != nil {
		fmt.Println("Error unmarshaling JSON:", err)
		return url
	}

	paramsStr := ""

	for key, val := range m {
		paramsStr += fmt.Sprintf("%s=%s&", key, val)
	}

	return url + "?" + paramsStr
}

func (c *verotechAdapter) getAuthHeader() map[string]string {
	header := map[string]string{
		"Content-Type": "application/json",
		"accountId":    c.Account<PERSON>,
		"apiKey":       c.<PERSON>,
	}
	return header
}

func (c *verotechAdapter) do(
	ctx context.Context,
	fullPath string,
	method string,
	data interface{},
) (_ []byte, _ int, duration int64, _ error) {
	var body io.Reader

	beginAt := time.Now().UnixMilli()

	defer func() {
		duration = time.Now().UnixMilli() - beginAt
	}()

	var response *http.Response

	if method == http.MethodGet {
		fullPath = bindParams(fullPath, data)
	}

	response, err := tracingHttp.RawRequest(ctx, fullPath, method, body, c.getAuthHeader())
	if err != nil {
		return nil, 0, duration, errors.Wrap(err, "tracingHttp.RawRequest")
	}

	defer response.Body.Close()

	resBody, err := io.ReadAll(response.Body)
	if err != nil {
		return resBody, response.StatusCode, duration, errors.Wrap(err, "io.ReadAll")
	}

	if response.StatusCode != http.StatusOK {
		err := errors.New(response.Status)
		return resBody, response.StatusCode, duration, errors.Wrap(err, "response status code not success")
	}

	return resBody, response.StatusCode, duration, nil
}
