package adapter

import (
	"bufio"
	"context"
	"encoding/json"
	"net/http"
	"os"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/cmd/verotech/entities"
)

const (
	apiGetNewMappings = "/api/3.0/Mappings/GetNewMappings"
)

type VerotechAdapter interface {
	GetNewMappings(ctx context.Context, resumeKey string, limit string) (*entities.GetNewMappingsRes, error)
	GetNewMappingsStatic(ctx context.Context) (*json.Decoder, func() error, error)
}

type verotechAdapter struct {
	baseURL   string
	APIKey    string
	AccountID string
}

func NewVerotechAdapter(baseURL, APIKey, AccountID string) VerotechAdapter {
	return &verotechAdapter{
		baseURL:   baseURL,
		APIKey:    APIKey,
		AccountID: AccountID,
	}
}

func (v *verotechAdapter) GetNewMappingsStatic(ctx context.Context) (*json.Decoder, func() error, error) {
	file, err := os.Open("vervotech_mapping_merge.json") // Open the JSON file
	if err != nil {
		return nil, nil, err
	}

	decoder := json.NewDecoder(bufio.NewReader(file)) // Use buffered reader for efficiency

	// Read the opening JSON bracket `[` if it's an array
	if _, err := decoder.Token(); err != nil {
		return nil, nil, err
	}

	return decoder, file.Close, nil
}

func (v *verotechAdapter) GetNewMappings(ctx context.Context, resumeKey string, limit string) (*entities.GetNewMappingsRes, error) {

	clientReq := &entities.GetNewMappingsReq{
		Limit:              limit,
		LastUpdateDateTime: "2025-02-01T00:00:01Z",
		ResumeKey:          resumeKey,
	}

	fullPath := v.baseURL + apiGetNewMappings

	rawRes, _, _, err := v.do(ctx, fullPath, http.MethodGet, clientReq)

	if err != nil {
		return nil, err
	}

	res := &entities.GetNewMappingsRes{}
	err = json.Unmarshal(rawRes, &res)
	if err != nil {
		return nil, err
	}

	return res, nil
}
