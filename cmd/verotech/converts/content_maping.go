package converts

import (
	"strconv"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/cmd/verotech/entities"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/cmd/verotech/repo/models"
)

func FromVeroTechMappingsStatic(data []*entities.Mapping) []*models.ContentMappingItem {
	if data == nil {
		return nil
	}

	out := []*models.ContentMappingItem{}

	for _, mapping := range data {
		temp := FromVerotechContentItem(mapping)

		if temp != nil {
			out = append(out, temp)
		}
	}

	return out
}

func FromVeroTechMappings(data *entities.GetNewMappingsRes) []*models.ContentMappingItem {
	if data == nil {
		return nil
	}

	out := []*models.ContentMappingItem{}

	for _, mapping := range data.Mappings {
		temp := FromVerotechContentItem(&mapping)

		if temp != nil {
			out = append(out, temp)
		}
	}

	return out
}

func FromVerotechContentItem(mapping *entities.Mapping) *models.ContentMappingItem {
	if mapping == nil {
		return nil
	}

	verotechID := strconv.Itoa(mapping.VervotechId)

	out := &models.ContentMappingItem{
		ThridPartyID:      verotechID,
		ThridPartyName:    "verotech",
		HotelProviderName: mapping.ProviderName,
	}

	if mapping.ProviderName == "EAN" {
		out.ExpediaHotelID = mapping.ProviderHotelId
	}

	if mapping.ProviderName == "RateHawk" {
		out.RatehawkHotelID = mapping.ProviderHotelId
	}

	return out
}
