package models

import "go.mongodb.org/mongo-driver/bson/primitive"

type ContentMappingItem struct {
	ID                 primitive.ObjectID `bson:"_id,omitempty"`
	ThridPartyID       string             `bson:"third_party_id"`
	ThridPartyName     string             `bson:"third_party_name"`
	ExpediaHotelID     string             `bson:"-"`
	RatehawkHotelID    string             `bson:"-"`
	UpdatedAt          int64              `bson:"updated_at"`
	HotelProviderNames []string           `bson:"hotel_provider_names,omitempty"`
	HotelProviderName  string             `bson:"-"`
	ExpediaHotelIds    []string           `bson:"expedia_hotel_ids,omitempty"`
	RatehawkHotelIds   []string           `bson:"ratehawk_hotel_ids,omitempty"`
}
