package repo

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/cmd/verotech/repo/models"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

type ContentMappingRepository interface {
	UpsertMany(context.Context, []*models.ContentMappingItem) error
	// FindValidMappings(ctx context.Context, pagi *commonDomain.Pagination) ([]*models.ContentMappingItem, *commonDomain.Pagination, error)
	FindByThirdPartyIds(ctx context.Context, ids []string) ([]*models.ContentMappingItem, error)
	FindValidMappings(ctx context.Context) (*mongo.Cursor, error)
}

type contentMappingRepositoryImpl struct {
	db mongodb.DB
	contentMappingIndexes
	colName string
}

type contentMappingIndexes struct {
	indexDefault      string
	indexThirdPartyID string
	indexProviderID   string
}

func NewContentMappingIndexes() ([]mongo.IndexModel, contentMappingIndexes) {
	indexesName := contentMappingIndexes{
		indexDefault:      "_id",
		indexThirdPartyID: "__third_party_id__",
		indexProviderID:   "__provider_ids__",
	}

	// indexesModel := []mongo.IndexModel{
	// 	{
	// 		Keys: bson.D{
	// 			{Key: "_id", Value: 1},
	// 		},
	// 		Options: options.Index().SetName(indexesName.indexDefault),
	// 	},
	// 	{
	// 		Keys: bson.D{
	// 			{Key: "third_party_id", Value: 1},
	// 		},
	// 		Options: options.Index().SetName(indexesName.indexThirdPartyID),
	// 	},
	// 	{
	// 		Keys: bson.D{
	// 			{Key: "expedia_hotel_ids", Value: 1},
	// 			{Key: "ratehawk_hotel_ids", Value: 1},
	// 		},
	// 		Options: options.Index().SetName(indexesName.indexProviderID).Ba,
	// 	},
	// }

	return nil, indexesName
}

func NewContentMappingRepository(db mongodb.DB) ContentMappingRepository {
	colName := "content_mappings_v2"

	_, indexNames := NewContentMappingIndexes()

	// err := db.EnsureIndexes(context.Background(), colName, indexModels)
	// if err != nil {
	// 	panic(err)
	// }

	return &contentMappingRepositoryImpl{db, indexNames, colName}
}

func (r *contentMappingRepositoryImpl) FindByThirdPartyIds(ctx context.Context, ids []string) ([]*models.ContentMappingItem, error) {
	result := []*models.ContentMappingItem{}

	filter := bson.M{
		"third_party_id": bson.M{
			"$in": ids,
		},
	}

	otps := []mongodb.Option{
		mongodb.WithFilter(filter),
		mongodb.WithHint(r.indexThirdPartyID),
	}

	if err := r.db.Find(ctx, r.colName, &result, otps...); err != nil {
		return nil, errors.Wrap(err, "db.Find")
	}

	return result, nil
}

func (r *contentMappingRepositoryImpl) UpsertMany(ctx context.Context, items []*models.ContentMappingItem) error {
	request := make([]mongo.WriteModel, 0, len(items))
	for _, m := range items {

		upsertCond := bson.M{
			"third_party_id":   m.ThridPartyID,
			"third_party_name": m.ThridPartyName,
		}

		setCond := bson.M{
			"third_party_id":   m.ThridPartyID,
			"third_party_name": m.ThridPartyName,
			"updated_at":       time.Now().UnixMilli(),
		}

		addToSet := bson.M{
			"hotel_provider_names": m.HotelProviderName,
		}

		if m.ExpediaHotelID != "" {
			addToSet["expedia_hotel_ids"] = m.ExpediaHotelID
		}

		if m.RatehawkHotelID != "" {
			addToSet["ratehawk_hotel_ids"] = m.RatehawkHotelID
		}

		update := bson.M{
			"$set":      setCond,
			"$addToSet": addToSet,
		}

		request = append(request, mongo.NewUpdateOneModel().SetUpdate(update).SetFilter(upsertCond).SetUpsert(true).SetHint(r.indexThirdPartyID))
	}

	if len(request) == 0 {
		return nil
	}

	err := r.db.BulkWriteRaw(ctx, r.colName, request)
	if err != nil {
		return errors.Wrap(err, "db.BulkWriteRaw")
	}

	return nil
}

func (r *contentMappingRepositoryImpl) FindValidMappings(ctx context.Context) (*mongo.Cursor, error) {

	filter := bson.M{
		"expedia_hotel_ids":  bson.M{"$size": 1},
		"ratehawk_hotel_ids": bson.M{"$size": 1},
	}

	opts := []mongodb.Option{
		mongodb.WithHint(r.indexProviderID),
		mongodb.WithFilter(filter),
	}

	cur, err := r.db.FindWithCursor(ctx, r.colName, opts...)
	if err != nil {
		return nil, errors.Wrap(err, "db.Find")
	}

	return cur, nil
}
