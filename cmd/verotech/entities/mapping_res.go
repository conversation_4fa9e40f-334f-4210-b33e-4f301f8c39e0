package entities

type GetNewMappingsReq struct {
	Limit              string `json:"limit"`
	LastUpdateDateTime string `json:"lastUpdateDateTime"`
	ResumeKey          string `json:"resumeKey"`
}

type GetNewMappingsRes struct {
	ResumeKey  string    `json:"ResumeKey"`
	Mappings   []Mapping `json:"Mappings"`
	Status     bool      `json:"Status"`
	Message    string    `json:"Message"`
	StatusCode int       `json:"StatusCode"`
}

type Mapping struct {
	VervotechId          int    `json:"VervotechId"`
	ProviderHotelId      string `json:"ProviderHotelId"`
	ProviderName         string `json:"ProviderName"`
	ChannelIds           []int  `json:"ChannelIds"`
	ProviderLocationCode string `json:"ProviderLocationCode"`
	Type                 string `json:"Type"`
}
