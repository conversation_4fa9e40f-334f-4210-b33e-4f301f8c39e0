package repo

import (
	"context"

	"gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/cmd/aggregate_hotel_contents_v2/repo/models"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type ContentMappingJobRepository interface {
	InsertOne(ctx context.Context, req *models.ContentMappingJobItem) error
	FindActiveJobs(ctx context.Context) (*mongo.Cursor, error)
	UpdateOne(ctx context.Context, req *models.ContentMappingJobItem) error
}

type ContentMappingJobRepositoryImpl struct {
	db mongodb.DB
	ContentMappingJobIndexes
	colName string
}

type ContentMappingJobIndexes struct {
	indexDefault string
}

func NewContentMappingJobIndexes() ([]mongo.IndexModel, ContentMappingJobIndexes) {
	indexesName := ContentMappingJobIndexes{
		indexDefault: "_id_",
	}

	// indexesModel := []mongo.IndexModel{
	// 	{
	// 		Keys: bson.D{
	// 			{Key: "_id", Value: 1},
	// 		},
	// 		Options: options.Index().SetName(indexesName.indexDefault),
	// 	},
	// }

	return nil, indexesName
}

func NewContentMappingJobRepository(db mongodb.DB) ContentMappingJobRepository {
	colName := "content_mappings_jobs"

	_, indexNames := NewContentMappingJobIndexes()

	// err := db.EnsureIndexes(context.Background(), colName, indexModels)
	// if err != nil {
	// 	panic(err)
	// }

	return &ContentMappingJobRepositoryImpl{db, indexNames, colName}
}

func (r *ContentMappingJobRepositoryImpl) InsertOne(ctx context.Context, req *models.ContentMappingJobItem) error {
	return r.db.InsertOne(ctx, r.colName, req, &options.InsertOneOptions{})
}

func (r *ContentMappingJobRepositoryImpl) FindActiveJobs(ctx context.Context) (*mongo.Cursor, error) {
	filter := bson.M{
		"completed_at": bson.M{"$exists": false},
	}

	otps := []mongodb.Option{
		mongodb.WithFilter(filter),
		mongodb.WithHint(r.indexDefault),
		mongodb.WithSorter(bson.M{"_id": 1}),
	}

	return r.db.FindWithCursor(ctx, r.colName, otps...)
}

func (r *ContentMappingJobRepositoryImpl) UpdateOne(ctx context.Context, req *models.ContentMappingJobItem) error {
	return r.db.UpdateOne(ctx, r.colName, bson.M{
		"_id": req.ID,
	}, req, &options.UpdateOptions{
		Hint: r.indexDefault,
	})
}
