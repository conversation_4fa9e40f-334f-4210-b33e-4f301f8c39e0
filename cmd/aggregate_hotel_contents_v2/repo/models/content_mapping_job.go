package models

import "go.mongodb.org/mongo-driver/bson/primitive"

type ContentMappingJobItem struct {
	ID              primitive.ObjectID `bson:"_id,omitempty"`
	Index           int                `bson:"index,omitempty"`
	CompletedAt     int64              `bson:"completed_at,omitempty"`
	QueueIds        []string           `bson:"queue_ids,omitempty"`
	ProcessedIds    []string           `bson:"processed_ids,omitempty"`
	FailedIds       []string           `bson:"failed_ids,omitempty"`
	TotalDurationMs int64              `bson:"total_duration_ms,omitempty"`
	CreatedAt       int64              `bson:"created_at,omitempty"`
}
