package command

import (
	"context"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/cmd/aggregate_hotel_contents_v2/repo"
	veroRepo "gitlab.deepgate.io/skyhub/skyhub-hotels/cmd/verotech/repo"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
)

const MAX_CONCURRENCY = 50

type JobHandler interface {
	ProcessJobsQueue(ctx context.Context) error
	InitJobsQueue(ctx context.Context) error
}

type JobHandlerImpl struct {
	jobQueueRepo      repo.ContentMappingJobRepository
	mappingRepo       veroRepo.ContentMappingRepository
	hotelV2Repo       repositories.HotelRepository
	ratehawkHotelRepo repositories.HotelRepository
	ratehawkRoomRepo  repositories.RoomRepository
	roomV2Repo        repositories.RoomRepository
}

func NewJobHandler(
	jobQueueRepo repo.ContentMappingJobRepository,
	mappingRepo veroRepo.ContentMappingRepository,
	hotelV2Repo repositories.HotelRepository,
	ratehawkHotelRepo repositories.HotelRepository,
	ratehawkRoomRepo repositories.RoomRepository,
	roomV2Repo repositories.RoomRepository,
) JobHandler {
	return &JobHandlerImpl{jobQueueRepo, mappingRepo, hotelV2Repo, ratehawkHotelRepo, ratehawkRoomRepo, roomV2Repo}
}
