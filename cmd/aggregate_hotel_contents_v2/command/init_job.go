package command

import (
	"context"
	"fmt"
	"sync/atomic"
	"time"

	"github.com/samber/lo"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/cmd/aggregate_hotel_contents_v2/repo/models"
	veroModels "gitlab.deepgate.io/skyhub/skyhub-hotels/cmd/verotech/repo/models"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (h *JobHandlerImpl) InitJobsQueue(ctx context.Context) error {
	const jobItemLimit = 100
	var totalJob atomic.Int32
	var totalIds = 0

	mappingCur, err := h.mappingRepo.FindValidMappings(ctx)
	if err != nil {
		return err
	}
	i := 1

	var mappings []*veroModels.ContentMappingItem

	// counter := 0
	missingIds := []string{}
	for mappingCur.Next(ctx) {
		// counter++
		mapping := &veroModels.ContentMappingItem{}
		if err := mappingCur.Decode(&mapping); err != nil {
			return err
		}

		if len(mappings) < jobItemLimit {
			mappings = append(mappings, mapping)
			continue
		}
		missingIds = append(missingIds, mapping.ThridPartyID)

		ids := lo.Map(mappings, func(item *veroModels.ContentMappingItem, _ int) string {
			return item.ThridPartyID
		})

		go func(index int, _ids []string) {
			bgCtx, cc := context.WithTimeout(ctx, time.Second*30)
			defer cc()

			jobItem := &models.ContentMappingJobItem{
				Index:     index,
				ID:        primitive.NewObjectID(),
				QueueIds:  _ids,
				CreatedAt: time.Now().UnixMilli(),
			}

			if err := h.jobQueueRepo.InsertOne(bgCtx, jobItem); err != nil {
				log.Error("jobQueueRepo.InsertOne error", log.Any("error", err), log.Any("jobItem", jobItem), log.Int("index", index))
			}

			totalJob.Add(1)
		}(i, ids)

		totalIds += len(ids)

		mappings = []*veroModels.ContentMappingItem{}
		i++
	}

	time.Sleep(1 * time.Second)

	ids := lo.Map(mappings, func(item *veroModels.ContentMappingItem, _ int) string {
		return item.ThridPartyID
	})

	lastCountIndex := i

	missingIds = append(missingIds, ids...)

	batchSize := 100
	for i := 0; i < len(missingIds); i += batchSize {
		end := i + batchSize
		if end > len(missingIds) {
			end = len(missingIds)
		}
		_ids := missingIds[i:end]

		jobItem := &models.ContentMappingJobItem{
			Index:     lastCountIndex,
			ID:        primitive.NewObjectID(),
			QueueIds:  _ids,
			CreatedAt: time.Now().UnixMilli(),
		}

		if err := h.jobQueueRepo.InsertOne(ctx, jobItem); err != nil {
			log.Error("jobQueueRepo.InsertOne error", log.Any("error", err), log.Any("jobItem", jobItem), log.Int("index", i/batchSize))
		}

		totalJob.Add(1)

		lastCountIndex++
		totalIds += len(_ids)
	}

	log.Info(fmt.Sprintf("[initJobsQueue] totalJobs: %d, totalIds: %d", totalJob.Load(), totalIds))

	return nil
}
