package utils

// import (
// 	"fmt"
// 	"strconv"

// 	"gitlab.deepgate.io/apps/common/errors"
// 	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
// )

// func GetHotelProvider(hotelID string) (enum.HotelProvider, error) {
// 	if hotelID == "" {
// 		return enum.HotelProviderNone, errors.ErrInvalidInput
// 	}

// 	iProvider, err := strconv.ParseInt(string(hotelID[0]), 10, 0)
// 	if err != nil {
// 		return enum.HotelProviderNone, err
// 	}

// 	return enum.HotelProvider(iProvider), nil
// }

// func GetHotelGroupID(first enum.HotelProvider, sec int) string {
// 	return fmt.Sprintf("%d%d", first, sec)
// }
