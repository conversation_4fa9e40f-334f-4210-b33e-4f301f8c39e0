package utils

import (
	"testing"
	"time"
)

// TestCheckTimeInRange tests the CheckTimeInRange function
func TestCheckTimeInRange(t *testing.T) {
	// Mock the time zone for <PERSON><PERSON> (Asia/Ho_Chi_Minh)
	location, err := time.LoadLocation("Asia/Ho_Chi_Minh")
	if err != nil {
		t.Fatalf("failed to load location: %v", err)
	}

	tests := []struct {
		name      string
		timeRange string
		mockTime  time.Time
		want      bool
		wantErr   bool
	}{
		{
			name:      "Within range",
			timeRange: "0730:2000",
			mockTime:  time.Date(2024, time.August, 28, 10, 0, 0, 0, location),
			want:      true,
			wantErr:   false,
		},
		{
			name:      "Outside range before",
			timeRange: "0730:2000",
			mockTime:  time.Date(2024, time.August, 28, 7, 0, 0, 0, location),
			want:      false,
			wantErr:   false,
		},
		{
			name:      "Outside range after",
			timeRange: "0730:2000",
			mockTime:  time.Date(2024, time.August, 28, 21, 0, 0, 0, location),
			want:      false,
			wantErr:   false,
		},
		{
			name:      "Invalid time range format",
			timeRange: "07:2000",
			mockTime:  time.Date(2024, time.August, 28, 10, 0, 0, 0, location),
			want:      false,
			wantErr:   true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := CheckTimeInRange(tt.timeRange, tt.mockTime)
			if (err != nil) != tt.wantErr {
				t.Errorf("CheckTimeInRange() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("CheckTimeInRange() = %v, want %v", got, tt.want)
			}
		})
	}
}
