package utils

import (
	"fmt"
	"strconv"
	"strings"
	"time"
)

func CalcTimeRemainToEndOfDay(date time.Time, loc *time.Location) time.Duration {
	now := time.Now().In(loc)

	endOfDay := time.Date(date.Year(), date.Month(), date.Day(), 23, 59, 59, 0, loc)
	timeRemaining := endOfDay.Sub(now)

	return timeRemaining
}

func GetStartOfDay(in int64) int64 {
	t := time.UnixMilli(in).UTC()
	return time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, t.Location()).UnixMilli()
}

// CheckTimeInRange checks if the current time in tz is within the provided range
// Arg timeRange format : 0700:2100
func CheckTimeInRange(timeRange string, timeWithTz time.Time) (bool, error) {
	if len(timeRange) != 9 {
		return false, fmt.Errorf("invalid time range format")
	}

	location := timeWithTz.Location()
	now := timeWithTz

	// Parse the input time range string
	times := strings.Split(timeRange, ":")
	if len(times) != 2 {
		return false, fmt.Errorf("invalid time range format")
	}

	// Convert the start and end times to hours and minutes
	startHour, err := strconv.Atoi(times[0][:2])
	if err != nil {
		return false, fmt.Errorf("invalid start hour: %v", err)
	}
	startMinute, err := strconv.Atoi(times[0][2:])
	if err != nil {
		return false, fmt.Errorf("invalid start minute: %v", err)
	}

	endHour, err := strconv.Atoi(times[1][:2])
	if err != nil {
		return false, fmt.Errorf("invalid end hour: %v", err)
	}
	endMinute, err := strconv.Atoi(times[1][2:])
	if err != nil {
		return false, fmt.Errorf("invalid end minute: %v", err)
	}

	// Get the current time in tz

	// Define the start and end times for the range
	startTime := time.Date(now.Year(), now.Month(), now.Day(), startHour, startMinute, 0, 0, location)
	endTime := time.Date(now.Year(), now.Month(), now.Day(), endHour, endMinute, 0, 0, location)

	// Check if the current time is within the range
	if now.After(startTime) && now.Before(endTime) {
		return true, nil
	}
	return false, nil
}
