package utils

import (
	"encoding/json"
	"encoding/xml"
	"io"
	"os"

	"github.com/pkg/errors"
)

func WriteStructToXMLFile(data interface{}, filename string) error {
	file, err := os.Create(filename)
	if err != nil {
		return err
	}
	defer file.Close()

	encoder := xml.NewEncoder(file)

	return encoder.Encode(data)
}


func WriteStructToJSONFile(data interface{}, filename string) error {
	file, err := os.Create(filename)
	if err != nil {
		return err
	}
	defer file.Close()

	encoder := json.NewEncoder(file)

	return encoder.Encode(data)
}

func WriteXMLToFile(xmlData []byte, filename string) error {
	file, err := os.Create(filename)
	if err != nil {
		return err
	}
	defer file.Close()

	_, err = file.Write(xmlData)
	if err != nil {
		return err
	}

	return nil
}

func ReadXMLFile(filename string) ([]byte, error) {
	xmlFile, err := os.Open(filename)
	if err != nil {
		return nil, errors.Wrap(err, "os.Open")
	}

	defer xmlFile.Close()

	byteValue, err := io.ReadAll(xmlFile)
	if err != nil {
		return nil, errors.Wrap(err, "io.ReadAll")
	}

	return byteValue, nil
}
