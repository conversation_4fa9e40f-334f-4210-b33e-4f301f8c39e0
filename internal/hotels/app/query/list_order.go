package query

import (
	"context"

	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

type ListBookingHandler interface {
	Handle(ctx context.Context, req *domain.ListBookingRequest) ([]*domain.HubHotelOrder, error)
}

type listBookingHandler struct {
	orderRepo repositories.OrderRepository
}

func NewListBookingHandler(orderRepo repositories.OrderRepository) ListBookingHandler {
	return &listBookingHandler{orderRepo}
}

func (h *listBookingHandler) Handle(ctx context.Context, req *domain.ListBookingRequest) ([]*domain.HubHotelOrder, error) {
	res, err := h.orderRepo.ListOrder(ctx, req.Pagination, req)
	if err != nil {
		log.Error("bookingRepo.ListOrder error", log.Any("error", err), log.Any("req", req))
		return nil, err
	}

	return res, nil
}
