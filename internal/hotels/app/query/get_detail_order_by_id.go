package query

import (
	"context"
	"strconv"
	"strings"

	"gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
)

type GetDetailOrderByIDHandler interface {
	Handle(ctx context.Context, orderID string) (*domain.HubHotelOrder, *domain.Hotel, error)
}

type getDetailOrderByIDHandler struct {
	cfg            *config.Schema
	hotelOrderRepo repositories.OrderRepository
	hotelRepo      repositories.HotelRepository
}

func NewGetDetailOrderByIDHandler(
	cfg *config.Schema,
	hotelOrderRepo repositories.OrderRepository,
	hotelRepo repositories.HotelRepository,
) GetDetailOrderByIDHandler {
	return &getDetailOrderByIDHandler{cfg, hotelOrderRepo, hotelRepo}
}

func (h *getDetailOrderByIDHandler) Handle(ctx context.Context, orderID string) (*domain.HubHotelOrder, *domain.Hotel, error) {
	if orderID == "" {
		return nil, nil, errors.ErrInvalidInput
	}

	order, err := h.hotelOrderRepo.FindOrderByID(ctx, orderID)
	if err != nil {
		log.Error("bookingRepo.FindOneByBookingCode error", log.Any("error", err),
			log.String("officeID", orderID))
		return nil, nil, errors.ErrSomethingOccurred
	}
	var hotelContent *domain.Hotel
	if order.Hotel != nil {
		hotelContent, err = h.hotelRepo.FindByHotelID(ctx, order.Hotel.HotelID, constants.DefaultLanguage)
		if err != nil {
			log.Error("hotelRepo.FindByHotelID error", log.Any("error", err),
				log.String("hotelID", order.Hotel.HotelID))
			return nil, nil, errors.ErrSomethingOccurred
		}

		if hotelContent == nil {
			log.Error("hotelRepo.FindByHotelID error", log.Any("error", err),
				log.String("hotelID", order.Hotel.HotelID))
			return nil, nil, errors.ErrSomethingOccurred
		}
		order.Hotel.Address = hotelContent.Address
		if hotelContent.Ratings != nil &&
			hotelContent.Ratings.Property != nil &&
			strings.EqualFold(hotelContent.Ratings.Property.Type, "star") {
			rating, err := strconv.ParseFloat(hotelContent.Ratings.Property.Rating, 32)
			if err != nil {
				log.Error("strconv.ParseFloat err", log.Any("err", err), log.String("value", hotelContent.Ratings.Property.Rating))
				rating = 0
			}
			order.Hotel.Rating = rating
		}
	}

	return order, hotelContent, nil
}
