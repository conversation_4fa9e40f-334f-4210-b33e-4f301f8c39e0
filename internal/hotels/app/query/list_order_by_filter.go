package query

import (
	"context"
	"time"

	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/partner"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

type ListOrderByFilterHandler interface {
	Handle(ctx context.Context, req *domain.ListOrderFilter) ([]*domain.HubHotelOrder, error)
}

type listOrderHandler struct {
	orderRepo     repositories.OrderRepository
	partnerClient partner.PartnerClient
}

func NewListOrderByFilterHandler(orderRepo repositories.OrderRepository, partnerClient partner.PartnerClient) ListOrderByFilterHandler {
	return &listOrderHandler{orderRepo, partnerClient}
}

func (h *listOrderHandler) Handle(ctx context.Context, req *domain.ListOrderFilter) ([]*domain.HubHotelOrder, error) {
	res, err := h.orderRepo.ListOrderByFilter(ctx, req.Pagination, req)
	if err != nil {
		log.Error("bookingRepo.ListOrder error", log.Any("error", err), log.Any("req", req))
		return nil, err
	}

	for _, order := range res {
		if order.AgentCode == "" {
			partner, err := h.partnerClient.GetOfficeInfo(ctx, order.OfficeID, "")
			if err != nil {
				log.Error("partnerClient.GetOfficeInfo error", log.Any("error", err), log.Any("req", req))
				return nil, err
			}
			if partner == nil {
				log.Error("partnerClient.GetOfficeInfo error", log.Any("error", err), log.Any("req", req))
				continue
			}
			order.AgentCode = partner.Code
			go func(id, agentCode string) {
				newCtx, ctxCancel := context.WithTimeout(context.Background(), 5*time.Second)
				defer ctxCancel()
				err := h.orderRepo.UpdateOneV2(newCtx, id, &domain.HubOrderUpdate{
					AgentCode: agentCode,
				})
				if err != nil {
					log.Error("bookingRepo.UpdateAgentCode error", log.Any("error", err), log.Any("req", req))
				}
			}(order.ID, partner.Code)
		}
	}
	return res, nil
}
