package query

import (
	"context"

	"gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

type ListConfigHiddenFeeHandler interface {
	Handle(ctx context.Context, req *domain.ListHiddenServiceFeeReq) ([]*domain.HiddenServiceFee, error)
}

type listConfigHiddenFeeHandler struct {
	hiddenFeeRepo repositories.HiddenServiceFeeRepository
}

func NewListConfigHiddenFeeHandler(hiddenFeeRepo repositories.HiddenServiceFeeRepository) ListConfigHiddenFeeHandler {
	return &listConfigHiddenFeeHandler{hiddenFeeRepo}
}

func (h *listConfigHiddenFeeHandler) Handle(ctx context.Context, req *domain.ListHiddenServiceFeeReq) ([]*domain.HiddenServiceFee, error) {
	res, err := h.hiddenFeeRepo.FindAllByConditionAndFilter(ctx, req)
	if err != nil {
		log.Error("ConfigHiddenFeeRepo.FindAllByCondition error", log.Any("error", err), log.Any("req", req))
		return nil, errors.ErrSomethingOccurred
	}

	return res, nil
}
