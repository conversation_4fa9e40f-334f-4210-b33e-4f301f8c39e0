package query

import (
	"context"

	"gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
)

type RetrieveBookingHandler interface {
	Handle(ctx context.Context, req *domain.HubRetrieveBookingReq) (*domain.HubRetrieveBookingRes, error)
}

type retrieveBookingHandler struct {
	cfg            *config.Schema
	hotelOrderRepo repositories.OrderRepository
}

func NewRetrieveBookingHandler(
	cfg *config.Schema,
	hotelOrderRepo repositories.OrderRepository,
) RetrieveBookingHandler {
	return &retrieveBookingHandler{cfg, hotelOrderRepo}
}

func getHotelRes(data *domain.HubOrderHotelItem) *domain.HubOrderHotelItem {
	if data == nil {
		log.Error("getHotelPriceCheckRes input nil")
		return nil
	}

	for _, room := range data.ListRooms {
		//MOCK SuccessWithoutConfirmationId
		// if data.HotelID == constants.SuccessWithoutConfirmationId {
		// 	room.ConfirmationID = ""
		// }
		room.RateData = nil
	}

	return data
}

func (h *retrieveBookingHandler) Handle(ctx context.Context, req *domain.HubRetrieveBookingReq) (*domain.HubRetrieveBookingRes, error) {
	if req.OfficeID == "" && req.SessionID == "" {
		return nil, errors.ErrInvalidInput
	}

	var orderHotel *domain.HubHotelOrder
	var err error
	if req.OrderCode != "" {
		orderHotel, err = h.hotelOrderRepo.FindOneByOrderCode(ctx, req.OfficeID, req.OrderCode)
		if err != nil {
			log.Error("bookingRepo.FindOneByBookingCode error", log.Any("error", err),
				log.String("officeID", req.OfficeID), log.String("OrderCode", req.OrderCode))
			return nil, errors.ErrSomethingOccurred
		}
	}

	if orderHotel == nil && req.SessionID != "" {
		orderHotel, err = h.hotelOrderRepo.FindOrderBySessionID(ctx, req.OfficeID, req.SessionID)
		if err != nil {
			log.Error("bookingRepo.FindOneByBookingCode error", log.Any("error", err),
				log.String("officeID", req.OfficeID), log.String("SessionID", req.SessionID))
			return nil, errors.ErrSomethingOccurred
		}
	}

	if orderHotel == nil {
		return &domain.HubRetrieveBookingRes{
			ErrorRes: domain.ErrorRes{
				IsSuccess: false,
				ErrorCode: errors.ErrNotFound.Error(),
			},
		}, nil
	}

	refundedAmount := float64(0)
	if orderHotel.ExchangedRefundData != nil {
		refundedAmount = orderHotel.ExchangedRefundData.RefundAmount
	}

	return &domain.HubRetrieveBookingRes{
		ErrorRes:      domain.ErrorRes{IsSuccess: true},
		OrderCode:     orderHotel.OrderCode,
		Hotel:         getHotelRes(orderHotel.Hotel),
		RateDataCf:    orderHotel.ExchangedRateDataCf,
		BookingStatus: orderHotel.BookingStatus,
		RefundAmount:  float64(refundedAmount),
		Refunded:      orderHotel.Refunded,
		Cancelable:    orderHotel.CanCancel() == nil,
	}, nil
}
