package app

import (
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/command"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/query"
)

type Application struct {
	Commands Commands
	Queries  Queries
}

type Commands struct {
	AggregateHotelContentHandler       command.AggregateHotelContentHandler
	AggregateRegionsContentHandler     command.AggregateRegionsContentHandler
	AggregateHotelTransactionHandler   command.AggregateHotelTransactionHandler
	PriceCheckHandler                  command.PriceCheckHandler
	BookHotelHandler                   command.BookHotelHandler
	ProcessPendingBookingHandler       command.ProcessPendingBookingHandler
	AggregateTourmindContentHandler    command.AggregateTourmindContentHandler
	CheckAvailabilityHandler           command.CheckAvailabilityHandler
	SearchHotelsHandler                command.SearchHotelsHandler
	SearchHotelListHandler             command.SearchHotelListHandler
	UpdateOrderStatusHandler           command.UpdateOrderStatusHandler
	AggregatePlaceHandler              command.AggregatePlaceHandler
	SearchDestinationHandler           command.SearchDestinationHandler
	AggregateRegionContentHandler      command.AggregateRegionContentHandler
	AggregateHotelContentReviewHandler command.AggregateHotelContentReviewHandler
	ExpediaWebhookNotificationHandler  command.ExpediaWebhookNotificationHandler
	GetHotelReview                     command.GetReviewHandler
	DeleteConfigHiddenFeeHandler       command.DeleteConfigHiddenFeeHandler
	CreateConfigHiddenFeeHandler       command.CreateConfigHiddenFeeHandler
	UpdateConfigHiddenFeeHandler       command.UpdateConfigHiddenFeeHandler
	CancelBookingHandler               command.CancelBookingHandler
	CheckBookingCancelStatusHandler    command.CheckBookingCancelStatusHandler
	ProcessCancelingBookingHandler     command.ProcessCancelingBookingHandler
	TAWebHookHandler                   command.TAWebhookHandler
	BookOldProviderHandler             command.BookOldProviderHandler
}

type Queries struct {
	GetHotelDetailHandler        query.GetHotelDetailHandler
	RetrieveBookingHandler       query.RetrieveBookingHandler
	ListBookingHandler           query.ListBookingHandler
	ListOrderByFilterHandler     query.ListOrderByFilterHandler
	ListConfigHiddenFeeHandler   query.ListConfigHiddenFeeHandler
	DetailConfigHiddenFeeHandler query.DetailConfigHiddenFeeHandler
	GetDetailOrderByIDHandler    query.GetDetailOrderByIDHandler
}
