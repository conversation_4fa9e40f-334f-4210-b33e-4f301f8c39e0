package service

import (
	"context"
	"fmt"
	"strings"
	"time"

	commonConstants "gitlab.deepgate.io/apps/common/constants"
	"gitlab.deepgate.io/apps/common/errors"
	commonErrors "gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/dida"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/expedia_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mq"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/notification"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/partner"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/rate_hawk_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/redis"
	taclient "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/ta_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/tourmind_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/helpers"
)

type BookingService interface {
	Book(ctx context.Context, provider enum.HotelProvider, req *domain.HubBookReq, session *domain.HotelSession, order *domain.HubHotelOrder) (string, []*domain.HubRetrieveConfirmationID, enum.BookingStatus, string, error)
	MarkBookingFail(ctx context.Context, hubOrder *domain.HubHotelOrder) error
	GenOrderCode(ctx context.Context) (code string, err error)
	ResolvePendingBook(ctx context.Context, hotelOrder *domain.HubHotelOrder, session *domain.HotelSession) error
	Refund(ctx context.Context, hotelOrder *domain.HubHotelOrder) error
}

type bookingService struct {
	cfg                  *config.Schema
	bookingRedis         redis.BookingRepository
	hubOrderRepo         repositories.OrderRepository
	expediaAdapter       expedia_client.ExpediaAdapter
	tourmindAdapter      tourmind_client.TourmindAdapter
	rateHawkAdapter      rate_hawk_client.RateHawkAdapter
	taAdapter            taclient.TAAdapter
	didaAdapter          dida.Adapter
	notificationClient   notification.NotificationServiceClient
	partnerClient        partner.PartnerClient
	mqClient             mq.Adapter
	cancelBookingService CancelBookingService
}

func NewBookingService(
	cfg *config.Schema,
	bookingRedis redis.BookingRepository,
	hubOrderRepo repositories.OrderRepository,
	expediaAdapter expedia_client.ExpediaAdapter,
	tourmindAdapter tourmind_client.TourmindAdapter,
	rateHawkAdapter rate_hawk_client.RateHawkAdapter,
	taAdapter taclient.TAAdapter,
	didaAdapter dida.Adapter,
	notificationClient notification.NotificationServiceClient,
	partnerClient partner.PartnerClient,
	mqClient mq.Adapter,
	cancelBookingService CancelBookingService,
) BookingService {
	return &bookingService{
		cfg:                  cfg,
		bookingRedis:         bookingRedis,
		hubOrderRepo:         hubOrderRepo,
		expediaAdapter:       expediaAdapter,
		tourmindAdapter:      tourmindAdapter,
		notificationClient:   notificationClient,
		rateHawkAdapter:      rateHawkAdapter,
		partnerClient:        partnerClient,
		taAdapter:            taAdapter,
		didaAdapter:          didaAdapter,
		mqClient:             mqClient,
		cancelBookingService: cancelBookingService,
	}
}

func (s *bookingService) normalizeRequestHolder(input *domain.HubHolderInfo) {
	for _, item := range input.HolderDetail {
		item.GivenName = strings.ToUpper(strings.TrimSpace(item.GivenName))
		item.Surname = strings.ToUpper(strings.TrimSpace(item.Surname))
	}
}

func (s *bookingService) assignHolderToHotelRoom(in *domain.HubBookReq, rooms []*domain.HubOrderRoomItem) error {
	if in == nil || in.Holder == nil || len(in.Holder.HolderDetail) == 0 || len(rooms) == 0 {
		log.Error("AssignHolderToHotelRoom input empty")
		return commonErrors.ErrInvalidInput
	}

	defaultHolder := in.Holder.HolderDetail[0]

	for _, room := range rooms {
		useDefault := true
		for _, holder := range in.Holder.HolderDetail {
			if room.OccupancyIndex != holder.OccupancyIndex {
				continue
			}
			useDefault = false
			room.Email = holder.Email
			room.GivenName = holder.GivenName
			room.Surname = holder.Surname
			room.SpecialRequest = holder.SpecialRequest
		}

		if useDefault {
			room.Email = defaultHolder.Email
			room.GivenName = defaultHolder.GivenName
			room.Surname = defaultHolder.Surname
			room.SpecialRequest = defaultHolder.SpecialRequest
		}
	}

	return nil
}

func (s *bookingService) Book(ctx context.Context, provider enum.HotelProvider, req *domain.HubBookReq, session *domain.HotelSession, order *domain.HubHotelOrder) (string, []*domain.HubRetrieveConfirmationID, enum.BookingStatus, string, error) {
	if session == nil || order == nil || order.Hotel == nil {
		return "", nil, enum.BookingStatusNone, "", domain.ErrInvalidValue
	}

	providerBookingStatus := ""
	reservationCode := ""
	confirmationIDs := []*domain.HubRetrieveConfirmationID{}
	bookingStatus := enum.BookingStatusNone
	var err error

	s.normalizeRequestHolder(req.Holder)
	s.assignHolderToHotelRoom(req, order.Hotel.ListRooms)

	switch provider {
	case enum.HotelProviderExpedia:
		reservationCode, confirmationIDs, bookingStatus, err = s.expediaBook(ctx, req, session.ExpediaSessionInfo, order)
	// case enum.HotelProviderTourMind:
	// 	{
	// 		reservationCode, confirmationID, bookingStatus, err = s.tourmindBook(ctx, req, session.TourmindSessionInfo, order)
	// 	}
	case enum.HotelProviderRateHawk:
		{
			reservationCode, confirmationIDs, bookingStatus, providerBookingStatus, err = s.rateHawkBook(ctx, req, session.BasicSessionInfo, order)
		}
	case enum.HotelProviderTA:
		{
			reservationCode, confirmationIDs, bookingStatus, providerBookingStatus, err = s.taBook(ctx, req.SessionID, order)
		}
	case enum.HotelProviderDida:
		{
			reservationCode, confirmationIDs, bookingStatus, providerBookingStatus, err = s.didaBook(ctx, req, session.DidaSessionInfo, order)
		}
	default:
		log.Error("Unsupported provider error", log.Int("type", int(provider)))
		return reservationCode, confirmationIDs, bookingStatus, providerBookingStatus, commonErrors.ErrInvalidInput
	}

	if err != nil {
		log.Error("Booking error", log.Any("error", err))
		return reservationCode, confirmationIDs, bookingStatus, providerBookingStatus, err
	}

	return reservationCode, confirmationIDs, bookingStatus, providerBookingStatus, err
}

func (s *bookingService) ResolvePendingBook(ctx context.Context, hotelOrder *domain.HubHotelOrder, session *domain.HotelSession) error {
	if hotelOrder == nil ||
		hotelOrder.BookingStatus != enum.BookingStatusPending {
		return nil
	}

	err := s.bookingRedis.AcquireResolvePendingLock(hotelOrder.OrderCode)
	if err != nil {
		return err
	}

	defer s.bookingRedis.ReleaseResolvePendingLock(hotelOrder.OrderCode)

	forceFail := false
	bookingStatus := enum.BookingStatusNone
	confirmationIDs := []*domain.HubRetrieveConfirmationID{}

	initialStatus := hotelOrder.ProviderBookingStatus
	if !forceFail && !hotelOrder.ManualIssuing {
		confirmationIDs, bookingStatus, err = s.fetchBookingStatus(ctx, hotelOrder, session)
		if err != nil {
			return err
		}

		if bookingStatus == enum.BookingStatusFailed {
			forceFail = true
		}
	}

	if bookingStatus == enum.BookingStatusSuccess || initialStatus != hotelOrder.ProviderBookingStatus || hotelOrder.ShouldUpdate {
		// hotelOrder.ConfirmationID = confirmationID

		if bookingStatus == enum.BookingStatusSuccess {
			converts.MapRoomConfirmationID(hotelOrder.Provider, hotelOrder.Hotel.ListRooms, confirmationIDs)
			hotelOrder.BookingStatus = bookingStatus
		}

		err := s.hubOrderRepo.UpdateOne(ctx, hotelOrder.ID, hotelOrder)
		if err != nil {
			log.Error("UpdateOne error", log.Any("error", err))
			return err
		}
	}

	if bookingStatus == enum.BookingStatusPending && time.Now().UnixMilli() > hotelOrder.PendingDeadline {
		// https://www.notion.so/deeptechjsc/HotelHUB-Structure-50f6cca07147490d924d49bcb6d10aea?pvs=4#7587d113425c4733ba2b7833532763f8
		forceFail = false
		go func() {
			teleCtx, cc := context.WithTimeout(context.Background(), time.Minute)
			defer cc()

			location, err := time.LoadLocation("Asia/Ho_Chi_Minh")
			if err != nil {
				log.Error("failed to load location:", log.Any("err", err))
				return
			}

			if hotelOrder.BookingRequest == nil ||
				hotelOrder.RequestHolder == nil ||
				len(hotelOrder.RequestHolder.HolderDetail) == 0 {
				log.Error("Invalid Booking info", log.Any("hotelOrder", hotelOrder))
				return
			}

			partnerInfo, err := s.partnerClient.GetOfficeInfo(teleCtx, hotelOrder.OfficeID, "")
			if err != nil {
				log.Error("GetOfficeInfo", log.Any("err", err))
				return
			}

			if partnerInfo == nil {
				log.Error("partnerInfo not found")
				return
			}

			err = s.notificationClient.SendMessage(teleCtx, &notification.SendMessageRequest{
				EntityType:  commonConstants.NotificationEntityPartnerShop,
				EntityID:    partnerInfo.ID,
				ServiceCode: commonConstants.NotificationServiceCodeHUB,
				Action:      commonConstants.TelegramNotificationActionTopUpManual,
				Data: map[string]string{
					"PaxName":       fmt.Sprintf("%s %s", hotelOrder.RequestHolder.HolderDetail[0].Surname, hotelOrder.RequestHolder.HolderDetail[0].GivenName),
					"CreatedAt":     time.UnixMilli(hotelOrder.CreatedAt).In(location).Format("02/01/2006 15:04:05"),
					"OrderCode":     hotelOrder.OrderCode,
					"BookingStatus": enum.BookingStatusName[hotelOrder.BookingStatus],
					"PlatForm":      partnerInfo.Name,
					"HotelName":     hotelOrder.Hotel.Name,
					"Provider":      enum.HotelProviderName[hotelOrder.Provider],
				},
			})
			if err != nil {
				log.Error("SendMessage error", log.Any("err", err))
			}
		}()
	}

	if forceFail {
		hotelOrder.BookingStatus = enum.BookingStatusFailed
		err = s.Refund(ctx, hotelOrder)
		if err != nil {
			log.Error("bookingService.Refund error", log.Any("error", err))
			return err
		}
	}

	return nil
}

func (s *bookingService) fetchBookingStatus(ctx context.Context, hotelOrder *domain.HubHotelOrder, session *domain.HotelSession) ([]*domain.HubRetrieveConfirmationID, enum.BookingStatus, error) {
	confirmationIDs := []*domain.HubRetrieveConfirmationID{}
	bookingStatus := enum.BookingStatusNone

	if hotelOrder == nil || session == nil {
		return confirmationIDs, bookingStatus, domain.ErrInvalidValue
	}

	var err error

	switch hotelOrder.Provider {
	case enum.HotelProviderExpedia:
		confirmationIDs, bookingStatus, err = s.expediaAdapter.Retrieve(ctx, hotelOrder, hotelOrder.OrderCode)
	case enum.HotelProviderRateHawk:
		confirmationIDs, bookingStatus, err = s.rateHawkAdapter.Retrieve(ctx, hotelOrder)
	case enum.HotelProviderTA:
		{
			confirmationIDs, bookingStatus, err = s.fetchBookingStatusTA(ctx, hotelOrder)
		}
	case enum.HotelProviderDida:
		{
			confirmationIDs, bookingStatus, err = s.didaAdapter.Retrieve(ctx, hotelOrder, hotelOrder.OrderCode)
		}
	default:
		log.Error("Unsupported provider error", log.Any("type", hotelOrder.Provider))
	}

	if err != nil {
		log.Error("Retrieve Booking error", log.Any("error", err))
		return confirmationIDs, bookingStatus, nil
	}

	return confirmationIDs, bookingStatus, nil
}

func (s *bookingService) fetchBookingStatusTA(ctx context.Context, hotelOrder *domain.HubHotelOrder) ([]*domain.HubRetrieveConfirmationID, enum.BookingStatus, error) {
	if hotelOrder.ProviderBookingStatus == domain.TABookingStatusCompleted {
		return nil, enum.BookingStatusSuccess, nil
	}

	// Rebook due to book fail
	if hotelOrder.ReservationCode == "" {
		if err := s.mqClient.BookOldProvider(ctx, hotelOrder.OrderCode); err != nil {
			log.Error("mqClient.BookOldProvider error", log.Any("error", err), log.String("orderCode", hotelOrder.OrderCode))
			return nil, hotelOrder.BookingStatus, nil
		}

		return nil, enum.BookingStatusPending, nil
	}

	// Webhook complete status timeout
	cfIds, status, err := s.taAdapter.Retrieve(ctx, hotelOrder, hotelOrder.OrderCode)
	if err != nil {
		log.Error("taAdapter.Retrieve error", log.Any("error", err), log.String("orderCode", hotelOrder.OrderCode))

		if time.Since(time.UnixMilli(hotelOrder.PendingStartAt)).Minutes() > 7 {
			status = enum.BookingStatusPending
		} else {
			return nil, enum.BookingStatusPending, nil
		}
	}

	if status == enum.BookingStatusSuccess {
		return cfIds, status, nil
	}

	if status == enum.BookingStatusPending {
		hotelOrder.CancelReason = "completed webhook timeout"
		hotelOrder.ShouldUpdate = true

		if err := s.taAdapter.Cancel(ctx, hotelOrder.ReservationCode, hotelOrder.OrderCode); err != nil {
			log.Error("taAdapter.Cancel error", log.Any("error", err), log.String("orderCode", hotelOrder.OrderCode), log.Any("status", status))
			return nil, hotelOrder.BookingStatus, nil
		}
	}

	if err := s.mqClient.BookOldProvider(ctx, hotelOrder.OrderCode); err != nil {
		log.Error("mqClient.BookOldProvider error", log.Any("error", err), log.String("orderCode", hotelOrder.OrderCode))
		return nil, hotelOrder.BookingStatus, nil
	}

	return nil, enum.BookingStatusPending, nil
}

func (s *bookingService) Refund(ctx context.Context, hotelOrder *domain.HubHotelOrder) error {
	err := s.hubOrderRepo.WithTransaction(ctx, func(tnxSession context.Context) (interface{}, error) {
		hotelOrder.Refunded = true
		hotelOrder.ExchangedRefundData = &domain.RefundData{
			RefundAmount: hotelOrder.ExchangedRateDataCf.PayNow,
			Currency:     hotelOrder.ExchangedRateDataCf.Currency,
		}

		err := s.hubOrderRepo.UpdateOne(tnxSession, hotelOrder.ID, hotelOrder)
		if err != nil {
			log.Error("Refund update refund orderRepo.UpdateOne", log.Any("err", err))
			return nil, domain.ErrBookingCancelFailed
		}

		_, err = s.cancelBookingService.RefundBooking(tnxSession, nil, hotelOrder.OfficeID, hotelOrder.LastTransactionID, hotelOrder.OrderCode, enum.HotelProviderName[hotelOrder.Provider], hotelOrder.ExchangedRefundData.RefundAmount)
		if err != nil {
			log.Error("RefundBooking err", log.Any("err", err))
			return nil, errors.ErrSomethingOccurred
		}

		return nil, nil
	})
	if err != nil {
		return err
	}
	return nil
}

func (h *bookingService) GenOrderCode(ctx context.Context) (code string, err error) {
	const (
		bookingCodeLength = 12
		retry             = 3
	)

	for i := 0; i < retry; i++ {
		code = helpers.GenerateBookingCode()

		check, err := h.hubOrderRepo.FindOneByOrderCode(ctx, "", code)
		if err != nil {
			log.Error("bookingRepo.FindOneByBookingCode error", log.Any("error", err), log.String("code", code))
			return "", err
		}

		if check != nil || code == "" {
			continue
		}

		break
	}

	return
}

func (s *bookingService) MarkBookingFail(ctx context.Context, hubOrder *domain.HubHotelOrder) error {
	if hubOrder == nil {
		return domain.ErrInvalidValue
	}

	if hubOrder.BookingStatus == enum.BookingStatusSuccess {
		log.Error("[MarkBookingFail] booking confirmed,cannot mark fail")
		return domain.ErrInvalidValue
	}

	if hubOrder.BookingStatus == enum.BookingStatusCancel || hubOrder.HubOrderStatus == enum.HubOrderStatusCancelled {
		return domain.ErrBookingCanceled
	}

	hubOrder.BookingStatus = enum.BookingStatusFailed

	err := s.hubOrderRepo.UpdateOne(ctx, hubOrder.ID, hubOrder)
	if err != nil {
		log.Error("[MarkBookingFail] hubOrderRepo.UpdateOne err", log.Any("err", err))
		return err
	}

	return nil
}

// func (s *bookingService) tourmindBook(ctx context.Context, req *domain.HubBookReq, session *domain.TourmindSessionInfo, order *domain.HubHotelOrder) (string, string, enum.BookingStatus, error) {
// 	if req == nil || req.Holder == nil {
// 		return "", "", enum.BookingStatusNone, domain.ErrInvalidValue
// 	}

// 	reservationCode, confirmationID, bookingStatus, err := s.tourmindAdapter.Book(ctx, req, session, order, req.SessionID)
// 	if err != nil {
// 		log.Error("tourmindBook error", log.Any("error", err), log.Any("req", req), log.String("sessionID", req.SessionID))
// 	}

// 	return reservationCode, confirmationID, bookingStatus, err
// }

func (s *bookingService) expediaBook(ctx context.Context, req *domain.HubBookReq, session *domain.ExpediaSessionInfo, order *domain.HubHotelOrder) (string, []*domain.HubRetrieveConfirmationID, enum.BookingStatus, error) {
	if req == nil || req.Holder == nil || order == nil || order.Hotel == nil || order.HotelSearchRequest == nil {
		return "", nil, enum.BookingStatusNone, domain.ErrInvalidValue
	}

	req.Holder.PhoneCode = helpers.RemoveNonDigitCharacters(req.Holder.PhoneCode)
	reservationCode, confirmationIDs, bookingStatus, err := s.expediaAdapter.Book(ctx, req, session, order.Hotel.ListRooms, req.SessionID)
	if err != nil {
		log.Error("expediaBook error", log.Any("error", err))
	}

	return reservationCode, confirmationIDs, bookingStatus, err
}

func (s *bookingService) rateHawkBook(ctx context.Context, req *domain.HubBookReq, session *domain.BasicSessionInfo, order *domain.HubHotelOrder) (string, []*domain.HubRetrieveConfirmationID, enum.BookingStatus, string, error) {
	if req == nil || req.Holder == nil || order == nil || order.Hotel == nil || order.HotelSearchRequest == nil {
		return "", nil, enum.BookingStatusNone, "", domain.ErrInvalidValue
	}

	req.Holder.PhoneCode = helpers.RemoveNonDigitCharacters(req.Holder.PhoneCode)

	reservationCode, confirmationIDs, bookingStatus, providerBookingStatus, err := s.rateHawkAdapter.Book(ctx, req, session, order, req.SessionID)
	if err != nil {
		log.Error("rateHawkBook error", log.Any("error", err), log.String("sessionID", req.SessionID), log.Any("req", req))
		return "", nil, enum.BookingStatusNone, "", err
	}

	return reservationCode, confirmationIDs, bookingStatus, providerBookingStatus, err
}

func (s *bookingService) taBook(ctx context.Context, sessionID string, order *domain.HubHotelOrder) (string, []*domain.HubRetrieveConfirmationID, enum.BookingStatus, string, error) {
	if order == nil || order.Hotel == nil || order.HotelSearchRequest == nil {
		return "", nil, enum.BookingStatusNone, "", domain.ErrInvalidValue
	}

	reservationCode, confirmationIDs, bookingStatus, err := s.taAdapter.Book(ctx, order, sessionID)
	if err != nil {
		log.Error("rateHawkBook error", log.Any("error", err), log.String("sessionID", sessionID))
		return "", nil, enum.BookingStatusNone, "", err
	}

	return reservationCode, confirmationIDs, bookingStatus, "", err
}

func (s *bookingService) didaBook(ctx context.Context, req *domain.HubBookReq, session *domain.DidaSessionInfo, order *domain.HubHotelOrder) (string, []*domain.HubRetrieveConfirmationID, enum.BookingStatus, string, error) {
	if req == nil || req.Holder == nil || order == nil || order.Hotel == nil || order.HotelSearchRequest == nil {
		return "", nil, enum.BookingStatusNone, "", domain.ErrInvalidValue
	}

	reservationCode, confirmationIDs, bookingStatus, providerBookingStatus, err := s.didaAdapter.Book(ctx, req, session, order, req.SessionID)
	if err != nil {
		log.Error("didaBook error", log.Any("error", err), log.String("sessionID", req.SessionID), log.Any("req", req))
		return "", nil, enum.BookingStatusNone, "", err
	}

	return reservationCode, confirmationIDs, bookingStatus, providerBookingStatus, err
}
