package service

import (
	"context"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/samber/lo"
	commonErrs "gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/apps/common/tracing"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.opentelemetry.io/otel/codes"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	redisLock "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/redis"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/helpers"
)

type CheckAvailabilityService interface {
	Search(
		ctx context.Context,
		req *domain.HubCheckAvailabilityReq,
		reqProviders []enum.HotelProvider,
	) (
		_ *domain.HubCheckAvailabilityRes,
		_ []enum.HotelProvider,
		err error,
	)
}

type checkAvailabilityService struct {
	cfg              *config.Schema
	searchHotelsRepo repositories.ProviderSearchHotelsRepository
	searchHandler    *ProviderSearchHandlerImp
	redisLock        redisLock.CheckAvailabilityRepository
	hotelRepo        repositories.HotelRepository
	roomRepo         repositories.RoomRepository
}

func NewCheckAvailabilityService(
	cfg *config.Schema,
	searchHotelsRepo repositories.ProviderSearchHotelsRepository,
	searchHandler *ProviderSearchHandlerImp,
	redisRepo redisLock.SearchHotelsRepository,
	hotelRepo repositories.HotelRepository,
	roomRepo repositories.RoomRepository,
) CheckAvailabilityService {
	return &checkAvailabilityService{cfg, searchHotelsRepo, searchHandler, redisRepo, hotelRepo, roomRepo}
}

func (s *checkAvailabilityService) validateCheckAvailabilityRequest(req *domain.HubCheckAvailabilityReq) error {
	if req == nil {
		return commonErrs.ErrInvalidInput
	}

	roomCount := 0
	for _, room := range req.Occupancies {
		roomCount += int(room.Rooms)
	}

	if roomCount > constants.MaxRoomSearchAllowed {
		return domain.ErrInvalidRequestRoomAmount
	}

	checkIn, err := time.Parse(constants.HubSearchDateFormat, req.Stay.CheckIn)
	if err != nil {
		log.Error("parse time err", log.Any("err", err), log.String("check-in", req.Stay.CheckIn))
		return domain.ErrCheckInDateInvalid
	}

	checkOut, err := time.Parse(constants.HubSearchDateFormat, req.Stay.CheckOut)
	if err != nil {
		log.Error("parse time err", log.Any("err", err), log.String("check-out", req.Stay.CheckOut))
		return domain.ErrCheckOutDateInvalid
	}

	limitDate := time.Now().Add(365 * 24 * time.Hour) // 1 year

	if checkIn.Before(time.Now().Add(-24*time.Hour)) || checkIn.After(limitDate) {
		return domain.ErrCheckInDateInvalid
	}

	if checkOut.Before(checkIn) || checkOut.Before(time.Now()) || checkOut.After(limitDate) {
		return domain.ErrCheckOutDateInvalid
	}

	return nil
}

func (s *checkAvailabilityService) getEnableProviders(req []enum.HotelProvider) map[enum.HotelProvider]ProviderSearchHandler {
	providerSearchHandlersMap := map[enum.HotelProvider]ProviderSearchHandler{}

	for _, p := range req {
		providerSearchHandlersMap[p] = s.searchHandler.getHandler(p)
	}

	return providerSearchHandlersMap
}

func (s *checkAvailabilityService) cacheSearchResults(ctx context.Context, searchKey string, data map[enum.HotelProvider]*domain.HotelSearchResult) {
	cacheData := []*domain.HotelSearchResult{}
	for key, val := range data {
		val.ID = primitive.NewObjectID().Hex()
		val.Provider = key
		val.SearchKey = searchKey
		cacheData = append(cacheData, val)
	}

	if err := s.searchHotelsRepo.InsertMany(ctx, cacheData); err != nil {
		log.Error("searchHotelsRepo.InsertMany error", log.Any("error", err))
		return
	}
}

func (s *checkAvailabilityService) ToHubHotelResponse(in map[enum.HotelProvider]*domain.HotelSearchResult) (*domain.HubCheckAvailabilityRes, []enum.HotelProvider, error) {
	hotelRes := []*domain.HubHotel{}
	providers := []enum.HotelProvider{}

	for provider, result := range in {
		if result != nil && len(result.Hotels) > 0 {
			providers = append(providers, provider)
			hotelRes = append(hotelRes, result.Hotels...)
			// for _, hotel := range result.Hotels {
			// if provider == enum.HotelProviderTourMind {
			// 	seenTourmindHotel[hotel.HotelID] = true
			// } else if seenTourmindHotel[hotel.HotelID] {
			// 	continue
			// }

			// TODO merge hotel + room for comparing price
			// }

		}
	}

	return &domain.HubCheckAvailabilityRes{
		IsSuccess:            true,
		Hotels:               hotelRes,
		AvailableHotelReturn: uint(len(hotelRes)),
	}, providers, nil
}

func (s *checkAvailabilityService) bindingContentDataToHotel(in *domain.HotelSearchResult, content []*domain.Hotel, provider enum.HotelProvider, rooms map[string][]*domain.Room, engRooms map[string][]*domain.Room) {
	if in == nil {
		return
	}

	for _, hotelContent := range content {
		contentRooms := rooms[hotelContent.HotelID]
		contentEngRooms := engRooms[hotelContent.HotelID]

		for _, hotel := range in.Hotels {
			var hotelID string

			switch in.Provider {
			case enum.HotelProviderExpedia, enum.HotelProviderExpediaManual:
				hotelID = hotelContent.ProviderIds[enum.HotelProviderExpedia]
			default:
				hotelID = hotelContent.ProviderIds[provider]
			}

			if hotel.ProviderHotelID == hotelID {
				hotel.HotelID = hotelContent.HotelID
				hotel.Name = hotelContent.Name
				hotel.Address = hotelContent.Address
				if hotelContent.Ratings != nil &&
					hotelContent.Ratings.Property != nil &&
					strings.EqualFold(hotelContent.Ratings.Property.Type, "star") {
					rating, err := strconv.ParseFloat(hotelContent.Ratings.Property.Rating, 32)
					if err != nil {
						log.Error("strconv.ParseFloat err", log.Any("err", err), log.String("value", hotelContent.Ratings.Property.Rating))
						rating = 0
					}
					hotel.Rating = rating
				}

				if hotelContent.CheckIn != nil {
					hotel.CheckInTime = hotelContent.CheckIn.BeginTime
				}

				if hotelContent.Checkout != nil {
					hotel.CheckOutTime = hotelContent.Checkout.Time
				}

				if hotelContent.Category != nil {
					hotel.HotelType = hotelContent.Category.Name
				}

				roomProviderIDMap := map[string]*domain.Room{}
				for _, contentRoom := range contentRooms {
					providerRoomID := contentRoom.ProviderIDs[provider]
					roomProviderIDMap[providerRoomID] = contentRoom
				}

				roomProviderIDMapEng := map[string]*domain.Room{}
				for _, contentEngRoom := range contentEngRooms {
					providerRoomID := contentEngRoom.ProviderIDs[provider]
					roomProviderIDMapEng[providerRoomID] = contentEngRoom
				}

				var (
					isFlagMappingHasExtraBedFromContent, hasExtraBed, isNonSmoking bool
				)

				for _, room := range hotel.ListRooms {
					contentRoom := roomProviderIDMap[room.ProviderRoomID]
					if contentRoom == nil {
						room.RoomID = "" // skip room not found in content.
						continue
					}

					contentEngRoom := roomProviderIDMapEng[room.ProviderRoomID]

					//Default name_en is empty
					engRoomName := ""
					if contentEngRoom != nil {
						engRoomName = contentEngRoom.Name
					}
					room.NameEn = engRoomName

					if provider == enum.HotelProviderExpedia {
						isFlagMappingHasExtraBedFromContent = true
						hasExtraBed, isNonSmoking = ExpediaCheckExtraBedAndNonSmoking(contentRoom)
					}

					room.RoomID = contentRoom.RoomID
					room.Views = contentRoom.Views
					room.Area = contentRoom.Area
					room.Amenities = contentRoom.Amenities
					room.Images = contentRoom.Images
					// room.BedGroups = contentRoom.BedGroups
					room.Occupancy = contentRoom.Occupancy
					room.Descriptions = contentRoom.Descriptions

					for _, rate := range room.RateData {
						if rate.ProviderRateID == "" {
							rate.ProviderRateID = rate.RateID
						}

						rate.RateID = fmt.Sprintf("%d%s", in.Provider, rate.RateID)
						if isFlagMappingHasExtraBedFromContent {
							rate.HasExtraBed = hasExtraBed
							rate.NonSmoking = isNonSmoking
						}
					}
				}

				hotel.ListRooms = lo.Filter(hotel.ListRooms, func(item *domain.HubRoom, _ int) bool {
					return item.RoomID != ""
				})

				break
			}

		}
	}
}

func ExpediaCheckExtraBedAndNonSmoking(room *domain.Room) (hasExtraBed bool, isNonSmoking bool) {
	if room == nil {
		return false, false
	}
	isNonSmoking = true
	for _, amenity := range room.Amenities {
		if amenity.ID == "**********" {
			hasExtraBed = true
		}
		if amenity.ID == "6212" {
			isNonSmoking = false
		}
		if hasExtraBed && isNonSmoking {
			break
		}
	}

	return hasExtraBed, isNonSmoking
}

func (s *checkAvailabilityService) Search(
	ctx context.Context,
	req *domain.HubCheckAvailabilityReq,
	reqProviders []enum.HotelProvider,
) (
	_ *domain.HubCheckAvailabilityRes,
	_ []enum.HotelProvider,
	err error,
) {
	var (
		wg    sync.WaitGroup
		mutex sync.Mutex
		lock  bool
	)

	searchKey, err := req.GenSearchKey()
	if err != nil {
		log.Error("Search GenSearchKey err", log.Any("err", err))
		return nil, nil, err
	}

	defer func() {
		if err != nil && lock {
			s.redisLock.ReleaseLock(searchKey)
		}
	}()

	data := map[enum.HotelProvider]*domain.HotelSearchResult{}

	err = s.validateCheckAvailabilityRequest(req)
	if err != nil {
		return nil, nil, err
	}

	lock, err = s.redisLock.AcquireLock(searchKey)
	if err != nil {
		log.Error("AcquireLock error", log.Any("error", err), log.String("searchKey", searchKey))
		return nil, nil, commonErrs.ErrSomethingOccurred
	}

	if !lock {
		err = s.redisLock.WaitForLockToRelease(searchKey, time.Second, 70)
		if err != nil {
			log.Error("WaitForLockToRelease error", log.Any("error", err), log.String("searchKey", searchKey))
			return nil, nil, commonErrs.ErrSomethingOccurred
		}
	}

	hotels, cachedProviders, err := s.SearchCached(ctx, req, reqProviders)
	if err != nil {
		log.Error("s.SearchCached error", log.Any("error", err), log.Any("providers", reqProviders), log.Any("req", req))
		return nil, nil, err
	}

	for _, hotel := range hotels {
		data[hotel.Provider] = hotel
	}

	if len(cachedProviders) == len(reqProviders) {
		defer s.redisLock.ReleaseLock(searchKey)
		response, responseProviders, err := s.ToHubHotelResponse(data)
		if err != nil {
			log.Error("ToHubHotelResponse error", log.Any("error", err), log.String("searchKey", searchKey))
			return nil, nil, err
		}

		response.SearchKey = searchKey
		return response, responseProviders, nil
	}

	var missingProviders []enum.HotelProvider
	for _, provider := range reqProviders {
		if !lo.Contains(cachedProviders, provider) {
			missingProviders = append(missingProviders, provider)
		}
	}

	// Modify searchHandler
	providerSearchHandlersMap := s.getEnableProviders(missingProviders)
	if req.DefaultLanguage == "" {
		req.DefaultLanguage = constants.DefaultLanguage
	}

	hotelContents, defaultLangContents, err := s.hotelRepo.FindByHotelIDsWithDefault(ctx, req.ListHotels, req.DefaultLanguage)
	if err != nil {
		log.Error("Search FindByHotelIDsWithDefault err", log.Any("err", err))
		return nil, nil, err
	}

	if len(hotelContents) == 0 {
		hotelContents = defaultLangContents
		req.Language = constants.DefaultLanguage
	} else {
		req.Language = req.DefaultLanguage
	}

	if len(hotelContents) == 0 {
		log.Error("Search hotelContent empty", log.Any("lang", req.DefaultLanguage))
		return nil, nil, domain.ErrHotelPropertyNotFound
	}

	providersMapReqData, err := s.populateSearchRequestDataForProvider(ctx, req, hotelContents, defaultLangContents, reqProviders)
	if err != nil {
		return nil, nil, err
	}

	//Get english content
	req.Language = constants.LanguageEnglish
	providersMapEnglishReqData, err := s.populateSearchRequestDataForProvider(ctx, req, hotelContents, defaultLangContents, reqProviders)
	if err != nil {
		return nil, nil, err
	}

	for iProvider, iHandler := range providerSearchHandlersMap {
		if iProvider == enum.HotelProviderNone {
			continue
		}

		provider := iProvider
		handler := iHandler

		providerData := providersMapReqData[provider]

		providerEngData := providersMapEnglishReqData[provider]

		if providerData == nil || providerData.ProviderHotelIds == nil || providerData.SearchReq == nil {
			continue
		}

		wg.Add(1)
		go func() {
			defer wg.Done()

			ctxTimeout := time.Second * time.Duration(s.cfg.SearchHotelCtxTimeout)

			searchCtx, cc := context.WithTimeout(ctx, ctxTimeout)
			defer cc()

			searchCtx, span := tracing.StartSpanFromContext(searchCtx, fmt.Sprintf("searchHotelsService.Search.%s", enum.HotelProviderName[provider]))
			defer span.End()
			tracing.AddAttributes(span, searchCtx, providerData, searchKey)

			var providerRF *domain.HotelSearchResult
			var err error

			providerRF, err = handler.search(searchCtx, providerData, searchKey)
			if err != nil {
				err, ok := reflect.ValueOf(err).Interface().(error)
				if ok {
					span.SetStatus(codes.Error, fmt.Sprintf("s.SearchHotelsService.Search.%s failed", enum.HotelProviderName[provider]))
					span.RecordError(err)
				}

				log.Error("handler.search error", log.Any("error", err), log.Int("provider", int(provider)), log.String("searchKey", searchKey))
				return
			}

			providerRF.HotelSearchRequest = req.ToHotelSearchRequest()

			mutex.Lock()

			s.bindingContentDataToHotel(providerRF, hotelContents, provider, providerData.Rooms, providerEngData.Rooms)
			err = req.Stay.CountDays()

			if err != nil {
				log.Error("req.Stay.CountDays error", log.Any("error", err))
				return
			}

			converts.CalcPayNowForSearchResult(providerRF, req.Stay)
			s.UpdateRateRefundable(providerRF)
			data[provider] = providerRF
			mutex.Unlock()
		}()
	}

	wg.Wait()

	// Temporary lock and cache search results
	lock, err = s.redisLock.AcquireCachingLock(searchKey)
	if err != nil {
		log.Error("redisRepo.AcquireCachingLock error", log.Any("error", err), log.String("searchKey", searchKey))
		return nil, nil, err
	}

	if !lock {
		log.Error("redisRepo.AcquireCachingLock acquire lock error", log.Any("error", err), log.String("searchKey", searchKey))
		return nil, nil, err
	}

	go func(data map[enum.HotelProvider]*domain.HotelSearchResult) {
		defer s.redisLock.ReleaseLock(searchKey)
		defer s.redisLock.ReleaseCachingLock(searchKey)

		if len(data) == 0 {
			log.Error("Prepare cache search results empty data error")
			return
		}

		bgCtx, cancel := context.WithTimeout(context.Background(), constants.CachedSearchResultTimeout)
		defer cancel()

		s.cacheSearchResults(bgCtx, searchKey, data)
	}(data)

	response, responseProviders, err := s.ToHubHotelResponse(data)
	if err != nil {
		log.Error("ToHubHotelResponse error", log.Any("error", err), log.String("searchKey", searchKey))
		return nil, nil, err
	}

	response.SearchKey = searchKey

	return response, responseProviders, nil
}

func (s *checkAvailabilityService) SearchCached(
	ctx context.Context,
	req *domain.HubCheckAvailabilityReq,
	reqProviders []enum.HotelProvider,
) (
	_ []*domain.HotelSearchResult,
	_ []enum.HotelProvider,
	err error,
) {
	returnProviders := []enum.HotelProvider{}
	searchKey, err := req.GenSearchKey()
	if err != nil {
		log.Error("GenSearchKey error", log.Any("error", err), log.Any("req", req))
		return nil, nil, commonErrs.ErrSomethingOccurred
	}

	cachedHotels, err := s.searchHotelsRepo.FindByKey(ctx, searchKey, reqProviders)
	if err != nil {
		log.Error("searchHotelsRepo.Find error", log.Any("error", err), log.String("searchKey", searchKey))
		return nil, nil, commonErrs.ErrSomethingOccurred
	}

	for _, item := range cachedHotels {
		returnProviders = append(returnProviders, item.Provider)
	}

	returnProviders = lo.Uniq[enum.HotelProvider](returnProviders)
	return cachedHotels, returnProviders, nil
}

func (s *checkAvailabilityService) populateSearchRequestDataForProvider(
	ctx context.Context,
	req *domain.HubCheckAvailabilityReq,
	hotelContents []*domain.Hotel,
	_ []*domain.Hotel,
	enableProvider []enum.HotelProvider) (
	map[enum.HotelProvider]*domain.SearchHotelRequestData, error,
) {
	out := map[enum.HotelProvider]*domain.SearchHotelRequestData{}

	// mapHotelIDDefautHotel := map[string]*domain.Hotel{}

	// for _, hotel := range defaultHotelContents {
	// 	mapHotelIDDefautHotel[hotel.HotelID] = hotel
	// }

	for _, hotel := range hotelContents {
		roomsMap, err := s.getProviderHotelRooms(ctx, enableProvider, hotel, req.Language)
		if err != nil {
			return nil, err
		}

		// Common logic for all
		for _, provider := range enableProvider {
			providerKey := provider // use for expedia Fake

			if provider == enum.HotelProviderExpediaManual {
				providerKey = enum.HotelProviderExpedia
			}

			if hotel.ProviderIds[providerKey] != "" {
				if out[provider] == nil {
					out[provider] = &domain.SearchHotelRequestData{
						Hotels:           []*domain.Hotel{hotel},
						ProviderHotelIds: []string{hotel.ProviderIds[provider]},
						Rooms:            map[string][]*domain.Room{hotel.HotelID: roomsMap[provider]},
					}
				} else {
					out[provider].Hotels = append(out[provider].Hotels, hotel)
					out[provider].ProviderHotelIds = append(out[provider].ProviderHotelIds, hotel.ProviderIds[provider])
					out[provider].Rooms[hotel.HotelID] = roomsMap[provider]
				}
			}
		}
	}

	// if out[enum.HotelProviderTourMind] != nil {
	// 	if err := s.fetchHotelsRooms(ctx, out[enum.HotelProviderTourMind].Hotels); err != nil {
	// 		log.Error("fetchHotelsRooms error", log.Any("error", err))
	// 		return nil, err
	// 	}
	// }

	// Bind search request
	for key := range out {
		out[key].SearchReq = helpers.Copy(req).(*domain.HubCheckAvailabilityReq)
		out[key].SearchReq.ListHotels = out[key].ProviderHotelIds
	}

	return out, nil
}

func (s *checkAvailabilityService) getProviderHotelRooms(ctx context.Context, providers []enum.HotelProvider, hotel *domain.Hotel, language string) (map[enum.HotelProvider][]*domain.Room, error) {

	rooms, err := s.roomRepo.FindByRoomIDsV2(ctx, enum.HotelProviderNone, hotel.HotelID, language)
	if err != nil {
		log.Error("roomRepo.FindByRoomIDs error", log.Any("error", err), log.Any("hotel", hotel))
		return nil, err
	}

	out := map[enum.HotelProvider][]*domain.Room{}

	for _, provider := range providers {
		for _, room := range rooms {
			if room.ProviderIDs[provider] != "" {
				out[provider] = append(out[provider], room)
			}
		}
	}

	return out, nil
}

func (s *checkAvailabilityService) UpdateRateRefundable(items *domain.HotelSearchResult) {
	for _, hotel := range items.Hotels {
		for _, room := range hotel.ListRooms {
			allRefundable := true
			for _, rate := range room.RateData {
				if !rate.Refundable {
					allRefundable = false
					break
				}
			}

			if allRefundable {
				var minRate *domain.HubRateData
				for _, rate := range room.RateData {
					if minRate == nil || rate.PayNow < minRate.PayNow {
						minRate = rate
					}
				}

				if minRate != nil {
					minRate.Refundable = false
					minRate.FakeNonRefund = true
				}
			}
		}
	}
}
