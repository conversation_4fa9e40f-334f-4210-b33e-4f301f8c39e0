package service

import (
	"context"
	"errors"
	"time"

	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/dida"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/expedia_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/rate_hawk_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/tourmind_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
)

type expediaSearchHandler struct {
	adapter expedia_client.ExpediaAdapter
}

type tourmindSearchHandler struct {
	adapter tourmind_client.TourmindAdapter
}

type rateHawkSearchHandler struct {
	adapter rate_hawk_client.RateHawkAdapter
}

type didaSearchHandler struct {
	adapter dida.Adapter
}

type ProviderSearchHandler interface {
	search(ctx context.Context, data *domain.SearchHotelRequestData, searchKey string) (*domain.HotelSearchResult, error)
}

type ProviderSearchHandlerImp struct {
	expediaSearchHandler
	tourmindSearchHandler
	rateHawkSearchHandler
	didaSearchHandler
}

func NewProviderSearchHandler(
	expediaAdapter expedia_client.ExpediaAdapter,
	tourmindAdapter tourmind_client.TourmindAdapter,
	rateHawkAdapter rate_hawk_client.RateHawkAdapter,
	didaAdapter dida.Adapter,
) *ProviderSearchHandlerImp {
	return &ProviderSearchHandlerImp{
		expediaSearchHandler: expediaSearchHandler{
			adapter: expediaAdapter,
		},
		tourmindSearchHandler: tourmindSearchHandler{
			adapter: tourmindAdapter,
		},
		rateHawkSearchHandler: rateHawkSearchHandler{
			adapter: rateHawkAdapter,
		},
		didaSearchHandler: didaSearchHandler{
			adapter: didaAdapter,
		},
	}
}

func (h *ProviderSearchHandlerImp) search(ctx context.Context, data *domain.SearchHotelRequestData, searchKey string) (*domain.HotelSearchResult, error) {
	return nil, errors.New("not implemented handler for provider")
}

func (h *ProviderSearchHandlerImp) getHandler(p enum.HotelProvider) ProviderSearchHandler {
	switch p {
	case enum.HotelProviderExpedia:
		return &h.expediaSearchHandler
	case enum.HotelProviderTourMind:
		return &h.tourmindSearchHandler
	case enum.HotelProviderRateHawk:
		return &h.rateHawkSearchHandler
	case enum.HotelProviderDida:
		return &h.didaSearchHandler
	}

	return h
}

func (h *rateHawkSearchHandler) search(ctx context.Context, data *domain.SearchHotelRequestData, searchKey string) (*domain.HotelSearchResult, error) {
	if data.Hotels == nil || data.ProviderHotelIds == nil {
		return nil, errors.ErrUnsupported
	}

	hotel := data.Hotels[0]

	adapterReq := &domain.CheckAvaiAdapterReq{
		HubRequest:      data.SearchReq,
		ProviderHotelID: data.ProviderHotelIds[0],
		ContentRooms:    hotel.Rooms,
		Address:         hotel.Address,
		TracingID:       searchKey,
	}

	rooms, currency, err := h.adapter.CheckAvailability(ctx, adapterReq)
	if err != nil {
		log.Error("tourmindSearchHandler error", log.Any("error", err), log.Any("data", data), log.Any("searchKey", searchKey))
		return nil, err
	}

	out := &domain.HubHotel{
		Address:         hotel.Address,
		HotelID:         hotel.HotelID,
		ProviderHotelID: data.ProviderHotelIds[0],
		Name:            hotel.Name,
		VAT:             true,
		ListRooms:       rooms,
		Currency:        currency,
	}

	return &domain.HotelSearchResult{
		SearchKey: searchKey,
		Provider:  enum.HotelProviderRateHawk,
		Hotels:    []*domain.HubHotel{out},
		ExpireAt:  time.Now().Add(time.Minute * 5).UnixMilli(),
	}, nil
}

func (h *tourmindSearchHandler) search(ctx context.Context, data *domain.SearchHotelRequestData, searchKey string) (*domain.HotelSearchResult, error) {
	hotels, err := h.adapter.SearchHotel(ctx, data, searchKey)
	if err != nil {
		log.Error("tourmindSearchHandler error", log.Any("error", err), log.Any("data", data), log.Any("searchKey", searchKey))
		return nil, err
	}

	return hotels, nil
}

// ----------------------

func (h *expediaSearchHandler) search(ctx context.Context, data *domain.SearchHotelRequestData, searchKey string) (*domain.HotelSearchResult, error) {
	hotels, err := h.adapter.SearchHotel(ctx, data, searchKey)
	if err != nil {
		log.Error("expediaSearchHandler error", log.Any("error", err), log.Any("data", data), log.Any("searchKey", searchKey))
		return nil, err
	}

	return hotels, nil
}

func (h *didaSearchHandler) search(ctx context.Context, data *domain.SearchHotelRequestData, searchKey string) (*domain.HotelSearchResult, error) {
	if data.Hotels == nil || data.ProviderHotelIds == nil {
		return nil, errors.ErrUnsupported
	}

	hotel := data.Hotels[0]

	adapterReq := &domain.CheckAvaiAdapterReq{
		HubRequest:      data.SearchReq,
		ProviderHotelID: data.ProviderHotelIds[0],
		ContentRooms:    hotel.Rooms,
		Address:         hotel.Address,
		TracingID:       searchKey,
	}

	rooms, currency, err := h.adapter.CheckAvailability(ctx, adapterReq)
	if err != nil {
		log.Error("tourmindSearchHandler error", log.Any("error", err), log.Any("data", data), log.Any("searchKey", searchKey))
		return nil, err
	}

	out := &domain.HubHotel{
		Address:         hotel.Address,
		HotelID:         hotel.HotelID,
		ProviderHotelID: data.ProviderHotelIds[0],
		Name:            hotel.Name,
		VAT:             true,
		ListRooms:       rooms,
		Currency:        currency,
	}

	return &domain.HotelSearchResult{
		SearchKey: searchKey,
		Provider:  enum.HotelProviderDida,
		Hotels:    []*domain.HubHotel{out},
		ExpireAt:  time.Now().Add(time.Minute * 5).UnixMilli(),
	}, nil
}
