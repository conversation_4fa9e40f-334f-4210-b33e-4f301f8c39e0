package service

import (
	"context"
	"errors"
	"strings"
	"time"

	"github.com/samber/lo"
	commonErrors "gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/dida"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/expedia_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/rate_hawk_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/redis"
	taclient "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/ta_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/tourmind_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/utils"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/helpers"
)

type PriceCheckService interface {
	PriceCheck(ctx context.Context, req *domain.HubPriceCheckReq) (*domain.HubPriceCheckRes, error)
}

const (
	maxConcurrentUpdates = 50
)

type priceCheckService struct {
	cfg                     *config.Schema
	searchHotelsRepo        repositories.ProviderSearchHotelsRepository
	sessionService          SessionService
	hubHotelOrderRepo       repositories.OrderRepository
	currencyExchangeService CurrencyExchangeService
	hiddenFeeSvc            HiddenFeeService
	expediaAdapter          expedia_client.ExpediaAdapter
	tourmindAdapter         tourmind_client.TourmindAdapter
	rateHawkAdapter         rate_hawk_client.RateHawkAdapter
	taMappingRepo           repositories.TAMappingsRepository
	taAdapter               taclient.TAAdapter
	didaAdapter             dida.Adapter
	priceCheckRedis         redis.PriceCheckRepository
	updateWorkerPool        chan struct{}
}

func NewPriceCheckService(
	cfg *config.Schema,
	searchHotelsRepo repositories.ProviderSearchHotelsRepository,
	sessionService SessionService,
	hubHotelOrderRepo repositories.OrderRepository,
	currencyExchangeService CurrencyExchangeService,
	hiddenFeeSvc HiddenFeeService,
	expediaAdapter expedia_client.ExpediaAdapter,
	tourmindAdapter tourmind_client.TourmindAdapter,
	rateHawkAdapter rate_hawk_client.RateHawkAdapter,
	taMappingRepo repositories.TAMappingsRepository,
	taAdapter taclient.TAAdapter,
	didaAdapter dida.Adapter,
	priceCheckRedis redis.PriceCheckRepository,
) PriceCheckService {
	return &priceCheckService{
		cfg:                     cfg,
		searchHotelsRepo:        searchHotelsRepo,
		sessionService:          sessionService,
		hubHotelOrderRepo:       hubHotelOrderRepo,
		currencyExchangeService: currencyExchangeService,
		expediaAdapter:          expediaAdapter,
		tourmindAdapter:         tourmindAdapter,
		rateHawkAdapter:         rateHawkAdapter,
		hiddenFeeSvc:            hiddenFeeSvc,
		taMappingRepo:           taMappingRepo,
		taAdapter:               taAdapter,
		didaAdapter:             didaAdapter,
		priceCheckRedis:         priceCheckRedis,
		updateWorkerPool:        make(chan struct{}, maxConcurrentUpdates),
	}
}

func (f *priceCheckService) getHotelPriceCheckRes(req *domain.HubPriceCheckReq, data *domain.HotelSearchResult) (*domain.HubHotel, *domain.HubRateData, error) {
	if req == nil || data == nil || len(data.Hotels) == 0 {
		log.Error("getHotelPriceCheckRes input nil", log.Any("req", req))
		return nil, nil, commonErrors.ErrInvalidInput
	}

	var hotel *domain.HubHotel
	var rate *domain.HubRateData

	for _, item := range data.Hotels {
		if item.HotelID != req.HotelID {
			continue
		}
		roomList := []*domain.HubRoom{}
		for _, room := range item.ListRooms {
			if room.RoomID != req.RoomID {
				continue
			}

			for _, rateItem := range room.RateData {
				if rateItem.RateID != req.RateID {
					continue
				}

				rate = rateItem
			}

			room.RateData = nil
			roomList = append(roomList, room)
		}

		if len(roomList) == 0 {
			return nil, nil, domain.ErrSearchKeyNotFound
		}

		item.ListRooms = converts.FlattenHubRoomByOccupancies(roomList[0], data.HotelSearchRequest.Occupancies)
		hotel = item
	}

	if hotel == nil || rate == nil {
		return nil, nil, domain.ErrSearchKeyNotFound
	}

	return hotel, rate, nil
}

func (f *priceCheckService) bindDataToRateDataCf(in *domain.HubRateData, rateData *domain.HubRateData, provider enum.HotelProvider) {
	if provider != enum.HotelProviderDida {
		in.CancelPolicies = rateData.CancelPolicies
	}
	in.Amenities = rateData.Amenities
	in.Promotions = rateData.Promotions
	in.BedOptions = rateData.BedOptions
	in.SaleScenario = rateData.SaleScenario
	in.Refundable = rateData.Refundable
	in.HasBreakfast = rateData.HasBreakfast
	in.NonSmoking = rateData.NonSmoking
	in.HasExtraBed = rateData.HasExtraBed
	in.FakeNonRefund = rateData.FakeNonRefund
}

func (f *priceCheckService) isInTAActiveTime() (bool, error) {
	manualTimeRange := f.cfg.TAIssuingActiveTime

	saigonLoc, err := time.LoadLocation(constants.HCMTimezone)
	if err != nil {
		return false, err
	}

	nowInSaigon := time.Now().In(saigonLoc)
	ok, err := utils.CheckTimeInRange(manualTimeRange, nowInSaigon)
	if err != nil {
		return false, err
	}

	return ok, nil
}

// Return TA hotel id
func (f *priceCheckService) verifyTAHotel(ctx context.Context, req *domain.CacheCheckAvailabilityRequest, result *domain.HubHotel, roomID string, rateData *domain.HubRateData, tracing string, bedOptionID string) (string, string, error) {
	isInTA, err := f.isInTAActiveTime()
	if err != nil {
		log.Error("isInTAActiveTime error", log.Any("error", err), log.String("tracing", tracing), log.String("format", f.cfg.TAIssuingActiveTime))
		return "", "", nil
	}

	if !isInTA {
		return "", "", nil
	}

	hotelID := result.HotelID

	res, err := f.taMappingRepo.FindOneByExternalID(ctx, hotelID)
	if err != nil {
		log.Error("taMappingRepo.FindOneByExternalID error", log.Any("error", err), log.String("external id", hotelID))
		return "", "", err
	}
	rRoomID := roomID
	sRoomID := strings.Split(roomID, "-")
	if len(sRoomID) > 1 {
		roomID = sRoomID[1]
	}

	taRoomID := ""
	roomName := ""

	if res != nil {
		for _, item := range res.TARoomMappings {
			if item.ExternalID == roomID {
				taRoomID = item.TAID
				break
			}
		}
	}

	if taRoomID == "" {
		for _, room := range result.ListRooms {
			if room.RoomID == rRoomID {
				roomName = room.Name
				break
			}
		}
	}

	taNotSupported := func() (string, string, error) { return "", "", nil }

	if res == nil || res.TAID == "" || taRoomID == "" && roomName == "" {
		return taNotSupported()
	}

	ok, taPrice, err := f.taAdapter.HasPrice(ctx, req, res.TAID, taRoomID, roomName, rateData, bedOptionID, tracing)
	if err != nil {
		log.Error("taAdapter.HasPrice error", log.Any("error", err), log.Any("hotel id", res.TAID), log.Any("room id", taRoomID))
		return "", "", err
	}

	if !ok || taPrice > rateData.PayNow {
		return taNotSupported()
	}

	return res.TAID, taRoomID, nil
}

func (f *priceCheckService) PriceCheck(ctx context.Context, req *domain.HubPriceCheckReq) (*domain.HubPriceCheckRes, error) {
	searchData, err := f.searchHotelsRepo.FindByIDWithoutExpire(ctx, req.SearchKey, req.HotelID, req.RoomID, req.RateID, req.BedOptionID)
	if err != nil {
		log.Error("PriceCheck searchHotelsRepo.FindByID err", log.Any("err", err), log.Any("req", req))
		return nil, commonErrors.ErrSomethingOccurred
	}

	if searchData == nil {
		return nil, domain.ErrSearchKeyNotFound
	}

	if searchData.HotelSearchRequest == nil {
		return nil, commonErrors.ErrSomethingOccurred
	}

	result, rate, err := f.getHotelPriceCheckRes(req, searchData)
	if err != nil {
		log.Error("getHotelPriceCheckRes", log.Any("err", err))
		return nil, domain.ErrSearchKeyNotFound
	}

	if result == nil {
		return nil, commonErrors.ErrNotFound
	}

	if len(rate.BedOptions) == 0 {
		return nil, domain.ErrBedOptionNotFound
	}

	if rate.IsSoldOut {
		log.Info("Rate is marked as sold out",
			log.String("searchKey", req.SearchKey),
			log.String("hotelID", req.HotelID),
			log.String("roomID", req.RoomID),
			log.String("rateID", req.RateID))

		return nil, domain.ErrRoomSoldOut
	}

	provider := searchData.Provider
	oldProvider := enum.HotelProviderNone

	if !lo.Contains(req.EnableProviders, provider) {
		return nil, domain.ErrProviderNotAllowed
	}

	exchangedRateRaw, _, err := f.currencyExchangeService.ConvertHubRateDataCurrency(ctx, rate, provider, rate.Currency, req.Currency)
	if err != nil {
		log.Error("currencyExchangeService.ConvertHubRateDataCurrency err", log.Any("err", err))
		return nil, err
	}

	exchangedRate := helpers.Copy(exchangedRateRaw).(*domain.HubRateData)

	// var totalChargeAmount float64
	stay := searchData.HotelSearchRequest.Stay
	multiplier := stay.DayCount * stay.RoomCount

	if !req.Stateful {
		return &domain.HubPriceCheckRes{
			Hotel:    result,
			RateData: exchangedRate,
			MatchKey: enum.HotelProviderToHash(provider),
		}, nil
	}

	if lo.Contains(req.EnableProviders, enum.HotelProviderTA) && !exchangedRateRaw.Refundable {
		taID, taRoomID, err := f.verifyTAHotel(ctx, searchData.HotelSearchRequest, result, req.RoomID, exchangedRateRaw, req.SearchKey, req.BedOptionID)
		if err != nil {
			return nil, err
		}

		if taID != "" {
			oldProvider = provider
			provider = enum.HotelProviderTA
			result.OldProviderID = result.ProviderHotelID
			result.ProviderHotelID = taID

			for _, room := range result.ListRooms {
				if room.RoomID == req.RoomID {
					room.OldProviderRoomID = room.ProviderRoomID
					room.ProviderRoomID = taRoomID
					break
				}
			}
		}
	}

	rateDataCf := &domain.HubRateData{}
	sessionData := &domain.HotelSessionData{
		ExpediaSessionInfo: &domain.ExpediaSessionInfo{},
	}

	var selectedBed *domain.BedOption

	for _, option := range rate.BedOptions {
		if option.OptionID == req.BedOptionID {
			selectedBed = option
			break
		}
	}

	if selectedBed == nil {
		return nil, domain.ErrBedOptionNotFound
	}

	rateDataCf, err = f.callProviderPriceCheck(ctx, provider, req, searchData, selectedBed, rate, result, sessionData)
	if err != nil {
		log.Error("Provider price check error", log.Any("error", err), log.Any("req", req))

		if errors.Is(err, domain.ErrRoomSoldOut) {
			//nolint:contextcheck
			f.handleRoomSoldOutError(req)
		}

		return nil, err
	}

	if rateDataCf != nil {
		f.bindDataToRateDataCf(rateDataCf, rate, provider)
	} else {
		log.Error("rateDataCf is nil", log.Any("req", req))
		return nil, commonErrors.ErrSomethingOccurred
	}

	converts.CalcRatePayNow(rateDataCf)

	newSession, err := f.sessionService.New(ctx, req.OfficeID, provider, sessionData)
	if err != nil {
		log.Error("sessionService.New err", log.Any("err", err))
		return nil, commonErrors.ErrSomethingOccurred
	}

	exchangedRateCfRaw, exchangeRate, err := f.currencyExchangeService.ConvertHubRateDataCurrency(ctx, rateDataCf, provider, rate.Currency, req.Currency)
	if err != nil {
		log.Error("currencyExchangeService.ConvertHubRateDataCurrency err", log.Any("err", err))
		return nil, err
	}

	// Apply Hidden Fee
	exchangedRateCf := helpers.Copy(exchangedRateCfRaw).(*domain.HubRateData)

	f.hiddenFeeSvc.CalculateHotelRateDataPrices(ctx, &domain.CalculateHotelRateDataPricesReq{
		Multiplier:    uint32(multiplier),
		PartnershipID: req.PartnershipID,
		OfficeID:      req.OfficeID,
		Rating:        result.Rating,
		CountryCode:   result.Address.CountryCode,
		CategoryType:  result.HotelType,
		HubRateData:   exchangedRateCf,
		HotelID:       result.HotelID,
	})

	f.hiddenFeeSvc.CalculateHotelRateDataPrices(ctx, &domain.CalculateHotelRateDataPricesReq{
		Multiplier:    uint32(multiplier),
		PartnershipID: req.PartnershipID,
		OfficeID:      req.OfficeID,
		Rating:        result.Rating,
		CountryCode:   result.Address.CountryCode,
		CategoryType:  result.HotelType,
		HubRateData:   exchangedRate,
		HotelID:       result.HotelID,
	})

	err = f.hubHotelOrderRepo.InsertOne(ctx, &domain.HubHotelOrder{
		SessionID:              newSession,
		OfficeID:               req.OfficeID,
		Hotel:                  converts.FromHubHotelToHubOrderHotelItem(result, searchData.HotelSearchRequest.Occupancies, selectedBed),
		RateData:               rate,
		OriginalRateDataCf:     rateDataCf,
		ExchangedRateDataCfRaw: exchangedRateCfRaw,
		ExchangedRateDataCf:    exchangedRateCf,
		// Fee: &domain.Fee{
		// 	HiddenFee: &domain.OrderHiddenFee{
		// 		HiddenFeeConfig:   appliedHiddenFee,
		// 		TotalChargeAmount: totalChargeAmount,
		// 	},
		// },
		ExchangeRateApply:  exchangeRate,
		Provider:           provider,
		BookingRequest:     req,
		HubOrderStatus:     enum.HubOrderStatusDraft,
		BookingStatus:      enum.BookingStatusNone,
		HotelSearchRequest: searchData.HotelSearchRequest,
		SearchKey:          req.SearchKey,
		OldProvider:        oldProvider,
	})

	if err != nil {
		log.Error("hubHotelOrderRepo.InsertOne err", log.Any("err", err))
		return nil, commonErrors.ErrSomethingOccurred
	}

	res := &domain.HubPriceCheckRes{
		SessionID:       newSession,
		Hotel:           result,
		RateData:        exchangedRate,
		RateDataCf:      exchangedRateCf,
		MatchKey:        enum.HotelProviderToHash(provider),
		RawExRateDataCf: exchangedRateCfRaw,
		ProviderCfRate:  rateDataCf,
		SessionInfo:     sessionData,
		ErrorRes: domain.ErrorRes{
			IsSuccess: true,
		},
	}

	warningCode, warningMsg := CheckRateChanges(exchangedRateCfRaw, exchangedRateRaw)
	if warningCode != "" {
		//nolint:contextcheck
		go func() {
			f.updateWorkerPool <- struct{}{}
			defer func() { <-f.updateWorkerPool }()

			copiedData := helpers.Copy(rateDataCf)
			rateCopy, ok := copiedData.(*domain.HubRateData)

			if !ok {
				log.Error("Failed to convert copied data to HubRateData", log.Any("data", copiedData))
				return
			}
			rateCopy.IsSoldOut = false
			f.UpdateSearchCacheWithRateData(context.Background(), req.SearchKey, req.HotelID, req.RoomID, req.RateID, req.BedOptionID, rateCopy)
		}()

		res.WarningCode = warningCode
		res.WarningMsg = warningMsg
	}

	return res, nil
}

func CheckRateChanges(exchangedRateCfRaw, exchangedRateRaw *domain.HubRateData) (string, string) {
	if exchangedRateCfRaw == nil || exchangedRateRaw == nil {
		return "", ""
	}

	isPriceChanged := exchangedRateCfRaw.PayNow != exchangedRateRaw.PayNow ||
		exchangedRateCfRaw.TotalPayAtHotel != exchangedRateRaw.TotalPayAtHotel

	isPolicyChanged := !areCancelPoliciesEqual(exchangedRateRaw.CancelPolicies, exchangedRateCfRaw.CancelPolicies)

	switch {
	case isPriceChanged && isPolicyChanged:
		return constants.WarnCodeRateInfoChanged, constants.WarnMsgRateInfoChanged
	case isPriceChanged:
		return constants.WarnCodePriceChanged, constants.WarnMsgPriceChanged
	case isPolicyChanged:
		return constants.WarnCodeCancelPolicyChanged, constants.WarnMsgCancelPolicyChanged
	}

	return "", ""
}

func areCancelPoliciesEqual(searchPolicies, priceConfirmPolicies []*domain.HubCancelPolicy) bool {
	if len(searchPolicies) != len(priceConfirmPolicies) {
		return false
	}

	for i := range searchPolicies {
		if searchPolicies[i] == nil && priceConfirmPolicies[i] == nil {
			continue
		}

		if (searchPolicies[i] == nil && priceConfirmPolicies[i] != nil) || (searchPolicies[i] != nil && priceConfirmPolicies[i] == nil) {
			return false
		}

		if searchPolicies[i].StartDate != priceConfirmPolicies[i].StartDate || searchPolicies[i].EndDate != priceConfirmPolicies[i].EndDate {
			return false
		}

		if priceConfirmPolicies[i].PenaltyAmount > searchPolicies[i].PenaltyAmount {
			return false
		}

		// Check other fields remain the same
		if searchPolicies[i].Currency != priceConfirmPolicies[i].Currency ||
			searchPolicies[i].EndDate != priceConfirmPolicies[i].EndDate ||
			searchPolicies[i].StartDate != priceConfirmPolicies[i].StartDate ||
			searchPolicies[i].PenaltyInfo.Percent != priceConfirmPolicies[i].PenaltyInfo.Percent ||
			searchPolicies[i].PenaltyInfo.Amount != priceConfirmPolicies[i].PenaltyInfo.Amount {
			return false
		}
	}

	return true
}

// Xử lý trường hợp phòng đã bán hết.
// Đánh dấu rate là sold out trong cache.
func (f *priceCheckService) handleRoomSoldOutError(req *domain.HubPriceCheckReq) {
	go func() {
		f.updateWorkerPool <- struct{}{}
		defer func() { <-f.updateWorkerPool }()

		// Tạo một rate mới với flag IsSoldOut = true
		soldOutRate := &domain.HubRateData{
			RateID:    req.RateID,
			IsSoldOut: true,
		}
		f.UpdateSearchCacheWithRateData(context.Background(), req.SearchKey, req.HotelID, req.RoomID, req.RateID, req.BedOptionID, soldOutRate)
	}()
}

func (f *priceCheckService) callProviderPriceCheck(
	ctx context.Context,
	provider enum.HotelProvider,
	req *domain.HubPriceCheckReq,
	searchData *domain.HotelSearchResult,
	selectedBed *domain.BedOption,
	rate *domain.HubRateData,
	result *domain.HubHotel,
	sessionData *domain.HotelSessionData,
) (*domain.HubRateData, error) {
	var rateDataCf *domain.HubRateData
	var err error

	//nolint:exhaustive
	switch provider {
	case enum.HotelProviderExpedia:
		rateDataCf, sessionData.ExpediaSessionInfo, err = f.expediaAdapter.PriceCheck(ctx, selectedBed.PriceCheckToken, req.SearchKey, searchData.HotelSearchRequest.Occupancies)
		if err != nil {
			log.Error("f.expediaAdapter error", log.Any("error", err), log.Any("req", req))
			return nil, err
		}
	case enum.HotelProviderTourMind:
		rateDataCf, sessionData.TourmindSessionInfo, err = f.tourmindAdapter.PriceCheck(ctx, searchData.HotelSearchRequest, result, rate, req.SearchKey)
		if err != nil {
			log.Error("f.tourmindAdapter error", log.Any("error", err), log.Any("req", req))
			return nil, err
		}
	case enum.HotelProviderExpediaManual, enum.HotelProviderTA:
		rateDataCf = rate
	case enum.HotelProviderRateHawk:
		rateDataCf, sessionData.BasicSessionInfo, err = f.rateHawkAdapter.PriceCheck(ctx, searchData.HotelSearchRequest, selectedBed, rate, req.SearchKey, result.Address)
		if err != nil {
			log.Error("f.rateHawkAdapter error", log.Any("error", err), log.Any("req", req))
			return nil, err
		}
	case enum.HotelProviderDida:
		var hotel *domain.HubHotel

		for _, item := range searchData.Hotels {
			if item.HotelID == req.HotelID {
				hotel = item
			}
		}

		rateDataCf, sessionData.DidaSessionInfo, err = f.didaAdapter.PriceCheck(ctx, searchData.HotelSearchRequest, hotel.ProviderHotelID, result.Address, rate, req.SearchKey)
		if err != nil {
			log.Error("f.didaAdapter error", log.Any("error", err), log.Any("req", req))
			return nil, err
		}
	default:
		log.Error("Unsupported provider error", log.String("provider", enum.HotelProviderName[provider]))
		return nil, commonErrors.ErrSomethingOccurred
	}

	return rateDataCf, nil
}

func (f *priceCheckService) UpdateSearchCacheWithRateData(ctx context.Context, searchKey, hotelID, roomID, rateID, bedOptionID string, exchangedRateCfRaw *domain.HubRateData) {
	defer func() {
		if r := recover(); r != nil {
			log.Error("UpdateSearchCacheWithRateData panic recovered", log.Any("panic", r))
		}
	}()

	startTime := time.Now()
	defer func() {
		duration := time.Since(startTime)
		log.Info("UpdateSearchCacheWithRateData duration", log.Duration("duration", duration))
	}()

	const contextTimeout = 30 * time.Second

	ctx, cancel := context.WithTimeout(ctx, contextTimeout)
	defer cancel()

	var rateCopy *domain.HubRateData

	if exchangedRateCfRaw != nil {
		copiedData := helpers.Copy(exchangedRateCfRaw)
		var ok bool
		rateCopy, ok = copiedData.(*domain.HubRateData)

		if !ok {
			log.Error("Failed to convert copied data to HubRateData", log.Any("data", copiedData))
			return
		}
	}

	exchangedRateCfRaw = rateCopy

	lock, err := f.priceCheckRedis.AcquirePriceCheckLock(ctx, searchKey, hotelID, roomID, rateID, bedOptionID)
	if err != nil {
		log.Error("UpdateSearchCacheWithRateData AcquirePriceCheckLock error",
			log.Any("error", err),
			log.String("searchKey", searchKey),
			log.String("hotelID", hotelID),
			log.String("roomID", roomID),
			log.String("rateID", rateID))

		return
	}

	if !lock {
		log.Info("UpdateSearchCacheWithRateData skipped, another process is already updating",
			log.String("searchKey", searchKey),
			log.String("hotelID", hotelID),
			log.String("roomID", roomID),
			log.String("rateID", rateID))

		return
	}

	// Ensure the lock is released when the function completes.
	defer func() {
		if err := f.priceCheckRedis.ReleasePriceCheckLock(ctx, searchKey, hotelID, roomID, rateID, bedOptionID); err != nil {
			log.Error("UpdateSearchCacheWithRateData ReleasePriceCheckLock error",
				log.Any("error", err),
				log.String("searchKey", searchKey),
				log.String("hotelID", hotelID),
				log.String("roomID", roomID),
				log.String("rateID", rateID))
		}
	}()

	// Tìm document ID từ searchKey
	searchResult, err := f.searchHotelsRepo.FindByIDWithoutExpire(ctx, searchKey, hotelID, roomID, rateID, bedOptionID)
	if err != nil {
		log.Error("updateSearchCacheWithRateData searchHotelsRepo.FindByIDWithoutExpire error",
			log.Any("error", err),
			log.String("searchKey", searchKey),
			log.String("hotelID", hotelID),
			log.String("roomID", roomID),
			log.String("rateID", rateID))

		return
	}

	if searchResult == nil {
		log.Error("updateSearchCacheWithRateData searchResult is nil",
			log.String("searchKey", searchKey),
			log.String("hotelID", hotelID),
			log.String("roomID", roomID),
			log.String("rateID", rateID))

		return
	}

	switch {
	case exchangedRateCfRaw == nil:
		log.Info("updateSearchCacheWithRateData removing rate",
			log.String("searchResultID", searchResult.ID),
			log.String("hotelID", hotelID),
			log.String("roomID", roomID),
			log.String("rateID", rateID))
	case exchangedRateCfRaw.IsSoldOut:
		log.Info("updateSearchCacheWithRateData marking rate as sold out",
			log.String("searchResultID", searchResult.ID),
			log.String("hotelID", hotelID),
			log.String("roomID", roomID),
			log.String("rateID", rateID))
	default:
		log.Info("updateSearchCacheWithRateData updating rate",
			log.String("searchResultID", searchResult.ID),
			log.String("hotelID", hotelID),
			log.String("roomID", roomID),
			log.String("rateID", rateID))
	}

	err = f.searchHotelsRepo.UpdateRateInDocument(ctx, searchResult.ID, hotelID, roomID, rateID, exchangedRateCfRaw)
	if err != nil {
		log.Error("updateSearchCacheWithRateData UpdateRateInDocument error",
			log.Any("error", err),
			log.String("searchResultID", searchResult.ID),
			log.String("hotelID", hotelID),
			log.String("roomID", roomID),
			log.String("rateID", rateID))

		return
	}
}
