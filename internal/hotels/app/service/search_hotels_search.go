package service

import (
	"context"
	"sort"
	"time"

	"github.com/samber/lo"
	commonErrs "gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
)

func (s *searchHotelService) Search(
	ctx context.Context,
	req *domain.HubSearchHotelRequest,
	reqProviders []enum.HotelProvider,
) (
	_ []*domain.HotelSummary,
	_ bool,
	err error,
) {
	// Validate & side logic
	if len(reqProviders) == 0 {
		return []*domain.HotelSummary{}, false, nil
	}

	if req.Language == "" {
		req.Language = constants.DefaultLanguage
	}

	if req.CountryCode == "" {
		req.CountryCode = "VN"
	}

	err = s.validateSearchHotelRequest(req)
	if err != nil {
		return nil, false, err
	}
	// Begin
	firstBatchSize := int64(req.Pagination.PageLimit)

	searchKey, err := req.GenSearchKey()
	if err != nil {
		log.Error("GenSearchKey error", log.Any("error", err), log.Any("req", req))
		return nil, false, commonErrs.ErrSomethingOccurred
	}

	// t := time.Now()

	dataProviderMap, foundProviders, err := s.SearchCached(ctx, req, reqProviders, searchKey)
	if err != nil {
		log.Error("s.SearchCached error", log.Any("error", err), log.Any("providers", reqProviders), log.Any("req", req))
		return nil, false, err
	}

	// Retun first batch size
	shouldWaitFullData := true
	loadMore := false
	totalCount := 0

	missingProviders, _ := lo.Difference(reqProviders, foundProviders)
	if len(missingProviders) > 0 {
		lock, err := s.redisLock.AcquireLock(searchKey)
		if err != nil {
			log.Error("AcquireLock error", log.Any("error", err), log.String("searchKey", searchKey))
			return nil, false, err
		}

		if !lock {
			err = s.redisLock.WaitForLockToRelease(searchKey, time.Second, constants.CacheSearchResultTimeoutSecs)
			if err != nil {
				log.Error("WaitForLockToRelease error", log.Any("error", err), log.String("searchKey", searchKey))
				return nil, false, err
			}

			return s.Search(ctx, req, reqProviders)
		}

		// Find data from provider
		areaCache, err := s.hotelAreaCacheRepo.FindByArea(ctx, req.Place.PlaceID, req.Place.Type)
		if err != nil {
			log.Error("s.hotelAreaCacheRepo.FindByArea error", log.Any("error", err), log.Any("req", req))
			return nil, false, err
		}

		if firstBatchSize == 20 && req.Pagination.PageCurrent == 1 {
			shouldWaitFullData = false

			getOutputSearchReq := &getOutputSearchReq{
				batchSize:        int(firstBatchSize),
				shouldGetFull:    shouldWaitFullData,
				providers:        missingProviders,
				areaCacheItem:    areaCache,
				searchKey:        searchKey,
				isFirstBatchOnly: true,
				timeout:          3,
			}

			var dataProviderMapTemp map[enum.HotelProvider][]*domain.HotelSummary

			dataProviderMapTemp, loadMore, _, err = s.getOutputSearchHotels(ctx, req, getOutputSearchReq, false)
			if err != nil {
				log.Error("s.getOutputSearchForBatchHotel error", log.Any("error", err), log.Any("providers", missingProviders), log.Any("req", req))
				return nil, false, err
			}

			for provider, data := range dataProviderMapTemp {
				dataProviderMap[provider] = data
			}
		}

		if loadMore || shouldWaitFullData {
			waitChanFullData := make(chan map[enum.HotelProvider][]*domain.HotelSummary)
			// var err error

			// Get full & cache
			go func() {
				bgCtx, cc := context.WithTimeout(context.Background(), constants.CachedSearchResultTimeout)
				defer cc()

				var fullDataMap map[enum.HotelProvider][]*domain.HotelSummary

				defer func() {
					if shouldWaitFullData {
						waitChanFullData <- fullDataMap
					}
				}()

				if areaCache == nil {
					areaCache, err = s.hotelAreaCacheRepo.FindByArea(bgCtx, req.Place.PlaceID, req.Place.Type)
					if err != nil {
						log.Error("s.hotelAreaCacheRepo.FindByArea error", log.Any("error", err), log.Any("req", req))
						return
					}
				}

				getOutputSearchReq := &getOutputSearchReq{
					batchSize:        int(firstBatchSize),
					shouldGetFull:    shouldWaitFullData,
					providers:        missingProviders,
					areaCacheItem:    areaCache,
					searchKey:        searchKey,
					isFirstBatchOnly: false,
					timeout:          5,
				}

				fullDataMap, _, _, err = s.getOutputSearchHotels(bgCtx, req, getOutputSearchReq, false)
				if err != nil {
					log.Error("s.getOutputSearchFull error", log.Any("error", err), log.Any("providers", missingProviders), log.Any("req", req))
					s.redisLock.ReleaseLock(searchKey)
					return
				}

				if len(fullDataMap) > 0 {
					for key, val := range fullDataMap {
						fullDataMap[key] = simpleFilterHotels(val, req)
					}

					go func(data map[enum.HotelProvider][]*domain.HotelSummary) {
						defer s.redisLock.ReleaseLock(searchKey)

						if len(data) == 0 {
							log.Error("Prepare cache search results empty data error")
							return
						}

						cacheCtx, cancel := context.WithTimeout(context.Background(), constants.CachedSearchResultTimeout)
						defer cancel()

						s.cacheSearchResults(cacheCtx, searchKey, data)
					}(fullDataMap)
				} else {
					s.redisLock.ReleaseLock(searchKey)
				}
			}()

			if shouldWaitFullData {
				dataProviderMapTemp := <-waitChanFullData

				for provider, data := range dataProviderMapTemp {
					dataProviderMap[provider] = data
				}

				// if err != nil {
				// 	return nil, false, err
				// }
			}
		} else {
			s.redisLock.ReleaseLock(searchKey)
		}
	}

	data := s.processProviderDataMap(ctx, dataProviderMap, req.PriceConditionConfig)

	data = simpleFilterHotels(data, req)

	if loadMore && totalCount != 0 {
		req.Pagination.TotalRecord = int64(totalCount)
	}

	return data, loadMore, nil
}

func (s *searchHotelService) processProviderDataMap(ctx context.Context, dataMap map[enum.HotelProvider][]*domain.HotelSummary, config *domain.HotelPriceConditionConfig) []*domain.HotelSummary {
	var defaultHotel *domain.HotelSummary

	if len(dataMap[enum.HotelProviderNone]) > 0 {
		defaultHotel = dataMap[enum.HotelProviderNone][0]
		delete(dataMap, enum.HotelProviderNone)
	}

	outMap := map[string][]*domain.HotelSummary{}

	for p, val := range dataMap {
		var exRateItem *domain.CurrencyExchange

		for _, hotelSum := range val {
			if hotelSum != nil && hotelSum.Price != nil {
				var err error
				var rate float64

				if exRateItem != nil && exRateItem.Rate != 0 && exRateItem.From == hotelSum.Price.Currency {
					rate = exRateItem.Rate
				}

				hotelSum.Price, exRateItem, err = s.currencyExSvc.ConvertHotelSummaryPrice(ctx, hotelSum.Price, constants.VNDCurrency, rate)
				if err != nil {
					log.Error("currencyExSvc.ConvertHotelSummaryPrice err", log.Any("err", err))
					return nil
				}
			}

			hotelSum.Provider = p
			outMap[hotelSum.ID] = append(outMap[hotelSum.ID], hotelSum)
		}
	}

	out := make([]*domain.HotelSummary, 0, len(outMap)+1)

	if defaultHotel != nil && len(outMap[defaultHotel.ID]) == 0 {
		out = append(out, defaultHotel)
	}

	temp := s.filterByPriceConditionConfig(outMap, config)
	out = append(out, temp...)

	return out
}

func (s *searchHotelService) filterByPriceConditionConfig(dataMap map[string][]*domain.HotelSummary, config *domain.HotelPriceConditionConfig) []*domain.HotelSummary {
	out := make([]*domain.HotelSummary, 0, len(dataMap))

	for _, iHotels := range dataMap {
		hotels := lo.Filter(iHotels, func(item *domain.HotelSummary, _ int) bool { return item != nil && item.Price != nil })

		if len(hotels) == 0 {
			continue
		}

		if len(hotels) == 1 {
			out = append(out, hotels[0])
			continue
		}

		sort.Slice(hotels, func(i, j int) bool {
			return hotels[i].Price.PricePerNight.DiscountPrice < hotels[j].Price.PricePerNight.DiscountPrice
		})

		firstHotel := hotels[0]
		secHotel := hotels[1]

		if !config.Active || !config.IsInPriceGap(firstHotel.Price.PricePerNight.DiscountPrice, secHotel.Price.PricePerNight.DiscountPrice) {
			out = append(out, firstHotel)
			continue
		}

		temp := config.SelectHotelProviderByOrder([]*domain.HotelSummary{firstHotel, secHotel})
		out = append(out, temp)
	}

	return out
}

func simpleFilterHotels(hotels []*domain.HotelSummary, req *domain.HubSearchHotelRequest) []*domain.HotelSummary {
	listHotelWithPrices := []*domain.HotelSummary{}
	for index, hotel := range hotels {
		if req.Place.Type == enum.PlaceTypeHotel && index == 0 {
			listHotelWithPrices = append(listHotelWithPrices, hotel)
			continue
		}

		if hotel != nil && hotel.Price != nil {
			listHotelWithPrices = append(listHotelWithPrices, hotel)
		}
	}

	return listHotelWithPrices
}

type getOutputSearchReq struct {
	batchSize        int
	shouldGetFull    bool
	providers        []enum.HotelProvider
	areaCacheItem    *domain.HotelAreaCacheItem
	searchKey        string
	isFirstBatchOnly bool
	timeout          int
}

func (s *searchHotelService) getOutputSearchHotels(ctx context.Context, req *domain.HubSearchHotelRequest, getReq *getOutputSearchReq, isSearchList bool) (map[enum.HotelProvider][]*domain.HotelSummary, bool, int, error) {
	areaCacheItem := getReq.areaCacheItem
	batchSize := getReq.batchSize
	providers := getReq.providers
	firstBatchOnly := getReq.isFirstBatchOnly
	loadMore := false

	var hotels []*domain.Hotel
	var err error
	providerPricesMap := map[enum.HotelProvider][]*domain.HotelSummary{}
	var totalCount int

	if !isSearchList && areaCacheItem != nil {
		totalCount = len(areaCacheItem.Hotels)
		hIds := make([]string, 0, totalCount)
		providerIdsMap := map[enum.HotelProvider][]string{}
		hotelDistanceMap := map[string]float64{}

		for i, hotel := range areaCacheItem.Hotels {
			found := false
			for _, provider := range providers {
				if providerID, ok := hotel.ProviderIds[provider]; ok && providerID != "" {
					providerIdsMap[provider] = append(providerIdsMap[provider], providerID)
					found = true
				}
			}

			if found {
				hIds = append(hIds, hotel.HotelID)
				hotelDistanceMap[hotel.HotelID] = hotel.HotelDistance
			}

			if len(providers) == 1 && len(providerIdsMap[providers[0]]) < batchSize {
				continue
			}

			if firstBatchOnly && i+1 >= batchSize {
				break
			}
		}

		if firstBatchOnly && len(areaCacheItem.Hotels) > len(hIds) {
			loadMore = true
		}

		wait := make(chan struct{})
		go func() {
			defer close(wait)

			//Find provider hotelIDs
			if len(providerIdsMap) > 0 {
				providerPricesMap = s.prepareSearchHotelPrice(ctx, providerIdsMap, req, getReq.timeout, getReq.searchKey)
			}
		}()

		hotels, err = s.hotelRepo.FindByHotelIDs(ctx, hIds, req.Language)
		if err != nil {
			log.Error("s.hotelRepo.FindByHotelIDs error", log.Any("error", err), log.Any("req", req))
			return nil, false, 0, err
		}
		// Sort to keep order
		hotelIndexMap := map[string]int{}
		for i, val := range hIds {
			hotelIndexMap[val] = i
		}

		for _, hotel := range hotels {
			hotel.Distance = hotelDistanceMap[hotel.HotelID]
		}

		sort.Slice(hotels, func(i, j int) bool {
			return hotelIndexMap[hotels[i].HotelID] < hotelIndexMap[hotels[j].HotelID]
		})

		<-wait
	} else {
		maxHotelLimit := 1000
		if firstBatchOnly {
			maxHotelLimit = batchSize + 1
		}

		var fullHotels []*domain.Hotel

		if isSearchList {
			maxHotelLimit = 250
			fullHotels, err = s.SearchHotelByHotelIds(ctx, req, maxHotelLimit, getReq.searchKey)
			if err != nil {
				log.Error("s.SearchHotelByHotelId error", log.Any("error", err), log.Any("req", req))
				return nil, false, 0, err
			}
		} else {
			fullHotels, err = s.SearchHotelByPlace(ctx, req, maxHotelLimit, getReq.searchKey)
			if err != nil {
				log.Error("s.SearchHotelByPlace error", log.Any("error", err), log.Any("req", req))
				return nil, false, 0, err
			}

			if !firstBatchOnly {
				go s.cachingAreaHotels(req.Place.PlaceID, req.Place.Type, fullHotels)
			}
		}

		if len(fullHotels) > batchSize {
			loadMore = true
			fullHotels = fullHotels[:batchSize]
		}

		hotels = fullHotels
	}

	hotelsWithContent, err := converts.FromHubHotelToHotelSummaries(hotels, req.Place, providers)
	if err != nil {
		log.Error("converts.FromHubHotelToHotelSummaries error", log.Any("error", err), log.Any("provider", providers), log.Any("req", req))
		return nil, false, 0, err
	}

	if len(providerPricesMap) == 0 {
		providerIdsMap := map[enum.HotelProvider][]string{}

		//Find provider hotelIDs
		for _, hotel := range hotels {
			for _, p := range providers {
				if providerID, ok := hotel.ProviderIds[p]; ok && providerID != "" {
					providerIdsMap[p] = append(providerIdsMap[p], providerID)
				}
			}
		}

		providerPricesMap = s.prepareSearchHotelPrice(ctx, providerIdsMap, req, getReq.timeout, getReq.searchKey)
	}

	if !isSearchList && req.Place.Type == enum.PlaceTypeHotel && len(hotelsWithContent) > 0 {
		providerPricesMap[enum.HotelProviderNone] = []*domain.HotelSummary{hotelsWithContent[0]}
	}

	s.mappingContentToPricesMap(hotelsWithContent, providerPricesMap)

	return providerPricesMap, loadMore, totalCount, nil
}

func (s *searchHotelService) expediaSearchHotel(ctx context.Context, req *domain.HubSearchHotelRequest, propertyIds []string, searchKey string) ([]*domain.HotelSummary, error) {
	searchReq := &domain.SearchHotelRequestData{
		ProviderHotelIds: propertyIds,
		SearchReq: &domain.HubCheckAvailabilityReq{
			Stay:          req.Stay,
			Occupancies:   req.Occupancies,
			CountryCode:   "VN",
			Language:      req.Language,
			SaleChannel:   req.SaleChannel,
			TravelPurpose: req.TravelPurpose,
			RatePlanCount: 1,
			SalesEnv:      req.SalesEnv,
		},
	}

	searchResult, err := s.expediaAdapter.SearchHotel(ctx, searchReq, searchKey)
	if err != nil {
		log.Error("s.expediaSearchHandler.SearchHotel error", log.Any("error", err), log.Any("req", searchReq))
		return nil, err
	}

	if searchResult == nil {
		log.Error("s.expediaSearchHandler.SearchHotel search empty", log.Any("error", err), log.Any("req", searchReq))
		return nil, domain.ErrRoomSoldOut
	}

	out := []*domain.HotelSummary{}

	for _, item := range searchResult.Hotels {
		searchHotelData := converts.FromHubHotelToHotelSummary(item, req.Stay.DayCount, enum.HotelProviderExpedia)
		out = append(out, searchHotelData)
	}

	return out, nil
}

func (s *searchHotelService) cachingAreaHotels(placeID string, placeType enum.PlaceType, hotels []*domain.Hotel) {

	bgCtx, cc := context.WithTimeout(context.Background(), constants.DefaultCtxTimeout)
	defer cc()

	cacheItem := &domain.HotelAreaCacheItem{
		Hotels:   []domain.AreaCacheHotelInfo{},
		AreaType: placeType,
		AreaID:   placeID,
	}

	for _, hotel := range hotels {
		cacheItem.Hotels = append(cacheItem.Hotels, domain.AreaCacheHotelInfo{
			HotelID:       hotel.HotelID,
			ProviderIds:   hotel.ProviderIds,
			HotelDistance: hotel.Distance,
		})
	}

	if placeType == enum.PlaceTypeHotel && len(cacheItem.Hotels) == 1 {
		return
	}

	if err := s.hotelAreaCacheRepo.InsertOne(bgCtx, cacheItem); err != nil {
		log.Error("s.hotelAreaCacheRepo.InsertOne error", log.Any("error", err), log.Any("cacheItem", cacheItem))
	}
}
