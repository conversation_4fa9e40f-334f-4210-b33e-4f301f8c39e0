package service

// import (
// 	"context"
// 	"time"

// 	commonErrs "gitlab.deepgate.io/apps/common/errors"
// 	"gitlab.deepgate.io/apps/common/log"
// 	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service/converts"
// 	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
// 	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
// 	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
// 	"golang.org/x/sync/errgroup"
// )

// func (s *searchHotelService) deprecated_Search(
// 	ctx context.Context,
// 	req *domain.HubSearchHotelRequest,
// 	reqProviders []enum.HotelProvider) (_ []*domain.HotelSummary, err error) {
// 	var (
// 		lock bool
// 	)

// 	if len(reqProviders) == 0 {
// 		return []*domain.HotelSummary{}, nil
// 	}

// 	searchKey, err := req.GenSearchKey()
// 	if err != nil {
// 		log.Error("Search GenSearchKey err", log.Any("err", err))
// 		return nil, err
// 	}

// 	if req.Language == "" {
// 		req.Language = constants.DefaultLanguage
// 	}

// 	if req.CountryCode == "" {
// 		req.CountryCode = "VN"
// 	}

// 	defer func() {
// 		if err != nil && lock {
// 			s.redisLock.ReleaseLock(searchKey)
// 		}
// 	}()

// 	err = s.validateSearchHotelRequest(req)
// 	if err != nil {
// 		return nil, err
// 	}

// 	lock, err = s.redisLock.AcquireLock(searchKey)
// 	if err != nil {
// 		log.Error("AcquireLock error", log.Any("error", err), log.String("searchKey", searchKey))
// 		return nil, commonErrs.ErrSomethingOccurred
// 	}

// 	if !lock {
// 		err = s.redisLock.WaitForLockToRelease(searchKey, time.Second, 70)
// 		if err != nil {
// 			log.Error("WaitForLockToRelease error", log.Any("error", err), log.String("searchKey", searchKey))
// 			return nil, commonErrs.ErrSomethingOccurred
// 		}
// 	}

// 	data, cachedProviders, err := s.SearchCached(ctx, req, reqProviders, searchKey)
// 	if err != nil {
// 		log.Error("s.SearchCached error", log.Any("error", err), log.Any("providers", reqProviders), log.Any("req", req))
// 		return nil, err
// 	}

// 	if len(cachedProviders) == len(reqProviders) {
// 		defer s.redisLock.ReleaseLock(searchKey)
// 		return data, nil
// 	}

// 	// var missingProviders []enum.HotelProvider
// 	// for _, provider := range reqProviders {
// 	// 	if !lo.Contains(cachedProviders, provider) {
// 	// 		missingProviders = append(missingProviders, provider)
// 	// 	}
// 	// }

// 	listHotelWithPrices := []*domain.HotelSummary{}

// 	// Temp for call expedia fake
// 	if s.cfg.UseExpediaFake {
// 		listHotelWithPrices, err = s.expediaFakeAdapter.SearchHotel(ctx, req, searchKey)
// 		if err != nil {
// 			log.Error("s.expediaFakeAdapter.SearchHotel error", log.Any("error", err), log.Any("providers", reqProviders), log.Any("req", req))
// 			return nil, err
// 		}

// 		listIDs := make([]string, 0, len(listHotelWithPrices))
// 		for _, item := range listHotelWithPrices {
// 			listIDs = append(listIDs, item.ID)
// 		}

// 		hotelContents, defaultLangContents, err := s.hotelRepo.FindByHotelByExpediaIDsWithDefault(ctx, listIDs, req.DefaultLanguage)
// 		if err != nil {
// 			log.Error("Search FindByHotelIDsWithDefault err", log.Any("err", err))
// 			return nil, err
// 		}

// 		if len(hotelContents) == 0 {
// 			hotelContents = defaultLangContents
// 			req.Language = constants.DefaultLanguage
// 		}

// 		if len(hotelContents) == 0 {
// 			log.Error("Search hotelContent empty", log.Any("lang", req.DefaultLanguage))
// 			return nil, domain.ErrHotelPropertyNotFound
// 		}

// 		// Map hub content to expedia fake content
// 		s.bindingContentDataToHotel(listHotelWithPrices, hotelContents, enum.HotelProviderExpediaManual)

// 		// Temporary lock and cache search results
// 		lock, err = s.redisLock.AcquireCachingLock(searchKey)
// 		if err != nil {
// 			log.Error("redisRepo.AcquireCachingLock error", log.Any("error", err), log.String("searchKey", searchKey))
// 			return nil, err
// 		}

// 		if !lock {
// 			log.Error("redisRepo.AcquireCachingLock acquire lock error", log.Any("error", err), log.String("searchKey", searchKey))
// 			return nil, err
// 		}

// 		data := map[enum.HotelProvider][]*domain.HotelSummary{
// 			enum.HotelProviderExpediaManual: listHotelWithPrices,
// 		}

// 		go func(data map[enum.HotelProvider][]*domain.HotelSummary) {
// 			defer s.redisLock.ReleaseLock(searchKey)
// 			defer s.redisLock.ReleaseCachingLock(searchKey)

// 			if len(data) == 0 {
// 				log.Error("Prepare cache search results empty data error")
// 				return
// 			}

// 			bgCtx, cancel := context.WithTimeout(context.Background(), constants.CachedSearchResultTimeout)
// 			defer cancel()

// 			s.cacheSearchResults(bgCtx, searchKey, data)
// 		}(data)
// 	} else {
// 		provider := reqProviders[0]
// 		hotels, err := s.SearchHotelByPlace(ctx, req, provider, 1000, searchKey)
// 		if err != nil {
// 			log.Error("s.hotelID error", log.Any("error", err), log.Any("provider", provider), log.Any("req", req))
// 			return nil, err
// 		}

// 		hotelsWithContent, err := converts.FromHubHotelToHotelSummaries(hotels, req.Place, provider)
// 		if err != nil {
// 			log.Error("converts.FromHubHotelToHotelSummaries error", log.Any("error", err), log.Any("provider", provider), log.Any("req", req))
// 			return nil, err
// 		}

// 		hotelIDs := []string{}

// 		//Find provider hotelIDs
// 		for _, hotel := range hotels {
// 			if value, ok := hotel.ProviderIds[provider]; ok {
// 				hotelIDs = append(hotelIDs, value)
// 			}
// 		}

// 		chunkHotelIDs := ChunkStringArray(hotelIDs, 200)
// 		g, gCtx := errgroup.WithContext(ctx)

// 		for _, providerHotelIDs := range chunkHotelIDs {
// 			if len(providerHotelIDs) == 0 {
// 				continue
// 			}

// 			g.Go(func() error {
// 				ctxTimeout := time.Second * time.Duration(s.cfg.SearchHotelCtxTimeout)

// 				searchCtx, cc := context.WithTimeout(gCtx, ctxTimeout)
// 				defer cc()

// 				err = s.SearchHotelPrice(searchCtx, hotelsWithContent, providerHotelIDs, req, provider, searchKey)
// 				if err != nil {
// 					log.Error("s.SearchHotelPrice error", log.Any("error", err), log.Any("provider", provider), log.Any("req", req))
// 					return err
// 				}

// 				return nil
// 			})
// 		}

// 		if err := g.Wait(); err != nil {
// 			// return nil, err
// 		}

// 		for index, hotel := range hotelsWithContent {
// 			if req.Place.Type == enum.PlaceTypeHotel && index == 0 {
// 				listHotelWithPrices = append(listHotelWithPrices, hotel)
// 				continue
// 			}

// 			if hotel != nil && hotel.Price != nil {
// 				listHotelWithPrices = append(listHotelWithPrices, hotel)
// 			}
// 		}

// 		go func(data map[enum.HotelProvider][]*domain.HotelSummary) {
// 			defer s.redisLock.ReleaseLock(searchKey)
// 			defer s.redisLock.ReleaseCachingLock(searchKey)

// 			if len(data) == 0 {
// 				log.Error("Prepare cache search results empty data error")
// 				return
// 			}

// 			bgCtx, cancel := context.WithTimeout(context.Background(), constants.CachedSearchResultTimeout)
// 			defer cancel()

// 			s.cacheSearchResults(bgCtx, searchKey, data)
// 		}(map[enum.HotelProvider][]*domain.HotelSummary{
// 			provider: listHotelWithPrices,
// 		})
// 	}

// 	if len(listHotelWithPrices) == 0 {
// 		return listHotelWithPrices, nil
// 	}

// 	return listHotelWithPrices, nil
// }
