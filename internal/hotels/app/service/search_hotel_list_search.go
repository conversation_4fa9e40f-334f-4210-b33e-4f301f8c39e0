package service

import (
	"context"
	"time"

	"github.com/samber/lo"
	commonErrs "gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
)

func (s *searchHotelService) SearchList(
	ctx context.Context,
	req *domain.HubSearchHotelRequest,
	reqProviders []enum.HotelProvider,
) (
	_ []*domain.HotelSummary,
	_ bool,
	err error,
) {
	// Validate & side logic
	if len(reqProviders) == 0 {
		return []*domain.HotelSummary{}, false, nil
	}

	if req.Language == "" {
		req.Language = constants.DefaultLanguage
	}

	if req.CountryCode == "" {
		req.CountryCode = "VN"
	}

	err = s.validateSearchHotelRequest(req)
	if err != nil {
		return nil, false, err
	}

	// General occupancy logic
	occupancies := req.ConvertGeneralToOccupancies(req.GeneralOccupancy)

	if occupancies != nil {
		req.Occupancies = occupancies
	}

	// Begin
	firstBatchSize := int64(req.Pagination.PageLimit)

	searchKey, err := req.GenSearchKey()
	if err != nil {
		log.Error("GenSearchKey error", log.Any("error", err), log.Any("req", req))
		return nil, false, commonErrs.ErrSomethingOccurred
	}

	// t := time.Now()

	dataProviderMap, foundProviders, err := s.SearchCached(ctx, req, reqProviders, searchKey)
	if err != nil {
		log.Error("s.SearchCached error", log.Any("error", err), log.Any("providers", reqProviders), log.Any("req", req))
		return nil, false, err
	}

	// Retun first batch size
	shouldWaitFullData := true
	loadMore := false
	totalCount := 0

	missingProviders, _ := lo.Difference(reqProviders, foundProviders)
	if len(missingProviders) > 0 {
		lock, err := s.redisLock.AcquireLock(searchKey)
		if err != nil {
			log.Error("AcquireLock error", log.Any("error", err), log.String("searchKey", searchKey))
			return nil, false, err
		}

		if !lock {
			err = s.redisLock.WaitForLockToRelease(searchKey, time.Second, constants.CacheSearchResultTimeoutSecs)
			if err != nil {
				log.Error("WaitForLockToRelease error", log.Any("error", err), log.String("searchKey", searchKey))
				return nil, false, err
			}

			return s.SearchList(ctx, req, reqProviders)
		}

		if firstBatchSize == 20 && req.Pagination.PageCurrent == 1 {
			shouldWaitFullData = false

			getOutputSearchReq := &getOutputSearchReq{
				batchSize:        int(firstBatchSize),
				shouldGetFull:    shouldWaitFullData,
				providers:        missingProviders,
				searchKey:        searchKey,
				isFirstBatchOnly: true,
				timeout:          3,
			}

			var dataProviderMapTemp map[enum.HotelProvider][]*domain.HotelSummary

			dataProviderMapTemp, loadMore, _, err = s.getOutputSearchHotels(ctx, req, getOutputSearchReq, true)
			if err != nil {
				log.Error("s.getOutputSearchForBatchHotel error", log.Any("error", err), log.Any("providers", missingProviders), log.Any("req", req))
				return nil, false, err
			}

			for provider, data := range dataProviderMapTemp {
				dataProviderMap[provider] = data
			}
		}

		if loadMore || shouldWaitFullData {
			waitChanFullData := make(chan map[enum.HotelProvider][]*domain.HotelSummary)
			// var err error

			// Get full & cache
			go func() {
				bgCtx, cc := context.WithTimeout(context.Background(), constants.CachedSearchResultTimeout)
				defer cc()

				var fullDataMap map[enum.HotelProvider][]*domain.HotelSummary

				defer func() {
					if shouldWaitFullData {
						waitChanFullData <- fullDataMap
					}
				}()

				getOutputSearchReq := &getOutputSearchReq{
					batchSize:        int(firstBatchSize),
					shouldGetFull:    shouldWaitFullData,
					providers:        missingProviders,
					searchKey:        searchKey,
					isFirstBatchOnly: false,
					timeout:          5,
				}

				fullDataMap, _, _, err = s.getOutputSearchHotels(bgCtx, req, getOutputSearchReq, true)
				if err != nil {
					log.Error("s.getOutputSearchFull error", log.Any("error", err), log.Any("providers", missingProviders), log.Any("req", req))
					s.redisLock.ReleaseLock(searchKey)
					return
				}

				if len(fullDataMap) > 0 {
					for key, val := range fullDataMap {
						fullDataMap[key] = simpleFilterHotelList(val)
					}

					go func(data map[enum.HotelProvider][]*domain.HotelSummary) {
						defer s.redisLock.ReleaseLock(searchKey)

						if len(data) == 0 {
							log.Error("Prepare cache search results empty data error")
							return
						}

						cacheCtx, cancel := context.WithTimeout(context.Background(), constants.CachedSearchResultTimeout)
						defer cancel()

						s.cacheSearchResults(cacheCtx, searchKey, data)
					}(fullDataMap)
				} else {
					s.redisLock.ReleaseLock(searchKey)
				}
			}()

			if shouldWaitFullData {
				dataProviderMapTemp := <-waitChanFullData

				for provider, data := range dataProviderMapTemp {
					dataProviderMap[provider] = data
				}

				// if err != nil {
				// 	return nil, false, err
				// }
			}
		} else {
			s.redisLock.ReleaseLock(searchKey)
		}
	}

	data := s.processProviderDataMap(ctx, dataProviderMap, req.PriceConditionConfig)

	data = simpleFilterHotelList(data)

	if loadMore && totalCount != 0 {
		req.Pagination.TotalRecord = int64(totalCount)
	}

	return data, loadMore, nil
}

// Return hotels with content by hotel_ids
func (s *searchHotelService) SearchHotelByHotelIds(ctx context.Context, req *domain.HubSearchHotelRequest, limit int, searchKey string) ([]*domain.Hotel, error) {
	if req == nil {
		return nil, commonErrs.ErrInvalidInput
	}

	hotelIds := req.HotelIds

	hotels, err := s.hotelRepo.FindByHotelIDs(ctx, hotelIds, req.Language)

	if err != nil {
		log.Error("[SearchHotelByHotelIds] hotelContentRepo.FindByHotelIDs err", log.Any("err", err), log.Any("hotelIds", hotelIds))
		return nil, domain.ErrPlaceNotFound
	}

	return hotels, nil
}

func simpleFilterHotelList(hotels []*domain.HotelSummary) []*domain.HotelSummary {
	listHotelWithPrices := []*domain.HotelSummary{}
	for _, hotel := range hotels {

		if hotel != nil && hotel.Price != nil {
			listHotelWithPrices = append(listHotelWithPrices, hotel)
		}
	}

	return listHotelWithPrices
}
