package converts

import (
	"strings"

	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/helpers"
)

func processName(name string) string {
	name = strings.ToUpper(name)
	return helpers.RemoveAccents(name)
}

func mapRoomConfirmationIDExpedia(rooms []*domain.HubOrderRoomItem, confirmationIDs []*domain.HubRetrieveConfirmationID) {
	for _, room := range rooms {

		for _, confirmID := range confirmationIDs {
			if room.ProviderRoomID == confirmID.ProviderRoomID &&
				room.OccupancyType == confirmID.OccupancyType &&
				checkNames(room, confirmID) && room.ConfirmationID == "" && !confirmID.Used {
				confirmID.Used = true
				room.ConfirmationID = confirmID.ConfirmationID
				room.BookingStatus = confirmID.BookStatus
			}
		}
	}
}

func MapRoomConfirmationIDRateHawk(rooms []*domain.HubOrderRoomItem, confirmationIDs []*domain.HubRetrieveConfirmationID) {
	for _, room := range rooms {
		for _, confirmID := range confirmationIDs {
			if room.ProviderRoomID == confirmID.ProviderRoomID &&
				room.OccupancyType == confirmID.OccupancyType &&
				// room.GivenName == confirmID.GivenName &&
				// room.Surname == confirmID.Surname &&
				room.ConfirmationID == "" {
				room.ConfirmationID = confirmID.ConfirmationID
				room.BookingStatus = confirmID.BookStatus
			}
		}
	}
}

func MapRoomConfirmationIDDida(rooms []*domain.HubOrderRoomItem, confirmationIDs []*domain.HubRetrieveConfirmationID) {
	for _, room := range rooms {
		for _, confirmID := range confirmationIDs {
			if room.ProviderRoomID == confirmID.ProviderRoomID &&
				room.ConfirmationID == "" {
				room.ConfirmationID = confirmID.ConfirmationID
				room.BookingStatus = confirmID.BookStatus
			}
		}
	}
}

func MapRoomConfirmationID(provider enum.HotelProvider, rooms []*domain.HubOrderRoomItem, confirmationIDs []*domain.HubRetrieveConfirmationID) {
	switch provider {
	case enum.HotelProviderExpedia:
		mapRoomConfirmationIDExpedia(rooms, confirmationIDs)
	case enum.HotelProviderRateHawk:
		MapRoomConfirmationIDRateHawk(rooms, confirmationIDs)
	case enum.HotelProviderDida:
		MapRoomConfirmationIDDida(rooms, confirmationIDs)
	default:
		log.Error("Unsupported provider error", log.Int("type", int(provider)))
	}
}

func checkNames(room *domain.HubOrderRoomItem, confirmID *domain.HubRetrieveConfirmationID) bool {
	if confirmID.GivenName == "" && confirmID.Surname == "" {
		return true
	}

	return processName(room.GivenName) == processName(confirmID.GivenName) && processName(room.Surname) == processName(confirmID.Surname)
}
