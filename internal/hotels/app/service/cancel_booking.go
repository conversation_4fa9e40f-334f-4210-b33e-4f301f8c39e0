package service

import (
	"context"
	"time"

	env "gitlab.deepgate.io/apps/common/constants"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
	commonErrors "gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/dida"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/expedia_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/partner"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/payment"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/rate_hawk_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/redis"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/wallet"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/webhook"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/helpers"
)

type CancelBookingService interface {
	CancelBooking(ctx context.Context, req *domain.HubCancelBookingReq) error
	CheckCancelStatus(ctx context.Context, hubOrder *domain.HubHotelOrder, webHookConfig *domain.WebhookCfg) error
	RefundBooking(refundCtx context.Context, cf *domain.WebhookCfg, officeID, transID, bkCode string, provider string, refundAmount float64) (*domain.TransactionInfo, error)
}

type cancelBookingService struct {
	cfg             *config.Schema
	expediaAdapter  expedia_client.ExpediaAdapter
	rateHawkAdapter rate_hawk_client.RateHawkAdapter
	redisLock       redis.CancelRepository
	orderRepo       repositories.OrderRepository
	walletClient    wallet.WalletClient
	paymentClient   payment.PaymentClient
	partnerClient   partner.PartnerClient
	webhookClient   webhook.WebhookAdapter
	dida            dida.Adapter
}

func NewCancelBookingService(
	cfg *config.Schema,
	expediaAdapter expedia_client.ExpediaAdapter,
	rateHawkAdapter rate_hawk_client.RateHawkAdapter,
	redisLock redis.CancelRepository,
	orderRepo repositories.OrderRepository,
	walletClient wallet.WalletClient,
	paymentClient payment.PaymentClient,
	partnerClient partner.PartnerClient,
	webhookClient webhook.WebhookAdapter,
	dida dida.Adapter,
) CancelBookingService {
	return &cancelBookingService{
		cfg:             cfg,
		expediaAdapter:  expediaAdapter,
		rateHawkAdapter: rateHawkAdapter,
		redisLock:       redisLock,
		orderRepo:       orderRepo,
		walletClient:    walletClient,
		paymentClient:   paymentClient,
		partnerClient:   partnerClient,
		webhookClient:   webhookClient,
		dida:            dida,
	}
}

func convertRefundAmount(ctx context.Context, target *domain.RefundData, rate *domain.CurrencyExchange) (*domain.RefundData, error) {
	if target == nil {
		return target, nil
	}

	currentCurrency := target.Currency

	if currentCurrency == "" {
		return target, nil
	}

	if currentCurrency == rate.To {
		return target, nil
	}

	result := helpers.Copy(target).(*domain.RefundData)

	result.Currency = rate.To

	precision := 3
	if result.Currency == constants.VNDCurrency {
		precision = -3
	}

	result.ProviderRefundAmount = helpers.CeilToPrecision(helpers.RoundToPrecision(rate.Rate*result.ProviderRefundAmount, 6), precision)
	result.RefundAmount = helpers.CeilToPrecision(helpers.RoundToPrecision(rate.Rate*result.RefundAmount, 6), precision)
	result.PenaltyAmount = helpers.CeilToPrecision(helpers.RoundToPrecision(rate.Rate*result.PenaltyAmount, 6), precision)

	return result, nil
}

func (f *cancelBookingService) RefundBooking(refundCtx context.Context, cf *domain.WebhookCfg, officeID, transID, bkCode string, provider string, refundAmount float64) (*domain.TransactionInfo, error) {
	if officeID == "" || transID == "" || bkCode == "" {
		return nil, commonErrors.ErrInvalidInput
	}

	const defaultPM = commonEnum.PaymentMethod_Wallet
	var err error

	pm, err := f.paymentClient.GetPaymentMethod(refundCtx, defaultPM)
	if err != nil {
		log.Error("paymentClient.GetPaymentMethod error", log.Any("error", err))
		return nil, err
	}

	shopInfo, err := f.partnerClient.GetOfficeInfo(refundCtx, officeID, "")
	if err != nil {
		log.Error("GetOfficeInfo error", log.Any("error", err), log.String("officeID", officeID))
		return nil, err
	}

	ownerID := shopInfo.OwnerID
	if cf == nil {
		cf = shopInfo.WebhookCfg
	}

	refundTransInfo, refundErr := f.walletClient.RequestRefund(refundCtx, ownerID, transID, shopInfo, provider, refundAmount)
	if refundErr != nil {
		log.Error(" h.walletClient.RequestRefund error", log.Any("error", refundErr), log.String("userID", ownerID), log.String("transID", transID))
		return nil, refundErr
	}

	go func() {
		webhookCfg := cf

		if webhookCfg == nil || webhookCfg.WebhookKey == "" || webhookCfg.WebhookURLCfg.Transaction == "" {
			return
		}

		// hard code webhook URL
		webhookCfg.WebhookURLCfg.Transaction = f.cfg.WebhookTransaction

		webhookCtx, cc := context.WithTimeout(context.Background(), constants.ClientRequestTimeout)
		defer cc()

		reqTrans := &domain.Transaction{
			OrderCode:     bkCode,
			TransactionID: refundTransInfo.ID,
			Type:          refundTransInfo.Type,
			Amount:        refundAmount,
			PaymentMethod: pm,
			OfficeID:      officeID,
			CreatedAt:     refundTransInfo.CreatedAt,
			Currency:      constants.VNDCurrency,
		}

		if err := f.webhookClient.SendTransaction(webhookCtx, reqTrans, webhookCfg.WebhookKey, webhookCfg.WebhookURLCfg.Transaction); err != nil {
			log.Error("webhookClient.SendTransaction error", log.Any("error", err), log.Any("reqTrans", reqTrans))
		}
	}()

	return nil, nil
}

func (f *cancelBookingService) HandleRetrieveBookingCancel(ctx context.Context, cf *domain.WebhookCfg, hubOrder *domain.HubHotelOrder, isCanceled, isRefundAll bool, refundData *domain.RefundData) error {
	if !isCanceled {
		log.Info("booking not canceled", log.Any("order", hubOrder.OrderCode))
		return nil
	}

	var err error

	for _, room := range hubOrder.Hotel.ListRooms {
		room.BookingStatus = enum.BookingStatusCancel
	}

	hubOrder.BookingStatus = enum.BookingStatusCancel
	hubOrder.RefundData = refundData
	hubOrder.ExchangedRefundData, err = convertRefundAmount(ctx, hubOrder.RefundData, hubOrder.ExchangeRateApply)
	if err != nil {
		log.Error("CancelBooking orderRepo.UpdateOne", log.Any("err", err))
		return domain.ErrBookingCancelFailed
	}

	if isRefundAll {
		hubOrder.ExchangedRefundData.RefundAmount = hubOrder.ExchangedRateDataCf.PayNow
	} else {
		hubOrder.ExchangedRefundData.RefundAmount = hubOrder.ExchangedRateDataCf.PayNow - (hubOrder.ExchangedRefundData.PenaltyAmount + hubOrder.ExchangedRateDataCf.HiddenFeeAmount + hubOrder.ExchangedRateDataCf.DiscountAmount)
	}

	err = f.orderRepo.UpdateOne(ctx, hubOrder.ID, hubOrder)
	if err != nil {
		log.Error("CancelBooking update Refund orderRepo.UpdateOne", log.Any("err", err))
		return domain.ErrBookingCancelFailed
	}

	err = f.orderRepo.WithTransaction(ctx, func(tnxSession context.Context) (interface{}, error) {
		hubOrder.Refunded = true
		err := f.orderRepo.UpdateOne(tnxSession, hubOrder.ID, hubOrder)
		if err != nil {
			log.Error("CancelBooking update refund orderRepo.UpdateOne", log.Any("err", err))
			return nil, domain.ErrBookingCancelFailed
		}

		_, err = f.RefundBooking(tnxSession, nil, hubOrder.OfficeID, hubOrder.LastTransactionID, hubOrder.OrderCode, enum.HotelProviderName[hubOrder.Provider], hubOrder.ExchangedRefundData.RefundAmount)
		if err != nil {
			log.Error("CancelBooking RefundBooking", log.Any("err", err))
			return nil, commonErrors.ErrSomethingOccurred
		}

		return nil, nil
	})
	if err != nil {
		log.Error("CancelBooking orderRepo.WithTransaction", log.Any("err", err))
	}

	return nil
}

func (f *cancelBookingService) CancelBooking(ctx context.Context, req *domain.HubCancelBookingReq) error {
	if req == nil {
		log.Error("CancelBooking input nil", log.Any("req", req))
		return commonErrors.ErrInvalidInput
	}

	hubOrder, err := f.orderRepo.FindOneByOrderCode(ctx, req.OfficeID, req.OrderCode)
	if err != nil {
		log.Error("CancelBooking FindOneByOrderCode", log.Any("err", err))
		return err
	}

	isSandbox := f.cfg.Env == env.EnvSandbox

	//MOCK TIMEOUT
	if hubOrder.Hotel != nil && isSandbox && hubOrder.Hotel.HotelID == constants.TimeOutCancelBooking {
		ctx, cancel := context.WithTimeout(ctx, 9*time.Minute)
		defer cancel()

		<-ctx.Done()
		return ctx.Err()
	}

	err = hubOrder.CanCancel()
	if err != nil {
		log.Error("CancelBooking req.CanCancel", log.Any("err", err))
		return err
	}

	err = f.redisLock.AcquireCancelBookingLock(hubOrder.OrderCode)
	if err != nil {
		log.Error("AcquireLock error", log.Any("error", err), log.String("req.OrderCode", hubOrder.OrderCode))
		return domain.ErrAnotherRequestInProgress
	}

	defer f.redisLock.ReleaseCancelBookingLock(hubOrder.OrderCode)

	bgCtx, cc := context.WithTimeout(context.Background(), 10*time.Minute)
	defer cc()

	for _, room := range hubOrder.Hotel.ListRooms {
		room.BookingStatus = enum.BookingStatusCanceling
	}
	hubOrder.CancelingStartAt = time.Now().UTC().UnixMilli()
	hubOrder.BookingStatus = enum.BookingStatusCanceling

	err = f.orderRepo.UpdateOne(bgCtx, hubOrder.ID, hubOrder)
	if err != nil {
		log.Error("CancelBooking orderRepo.UpdateOne", log.Any("err", err))
		return domain.ErrBookingCancelFailed
	}

	var isCanceled bool
	var isRefundAll bool
	var refundData *domain.RefundData

	switch hubOrder.Provider {
	case enum.HotelProviderExpedia:
		err = f.expediaAdapter.Cancel(bgCtx, hubOrder, hubOrder.CustomerIP, hubOrder.OrderCode)
		if err != nil {
			log.Error("CancelBooking expediaAdapter.Cancel", log.Any("err", err))
			return err
		}

		isCanceled, isRefundAll, refundData = f.expediaAdapter.RetrieveForRefund(bgCtx, hubOrder, hubOrder.OrderCode)
	case enum.HotelProviderRateHawk:
		err = f.rateHawkAdapter.Cancel(bgCtx, hubOrder, hubOrder.CustomerIP, hubOrder.OrderCode)
		if err != nil {
			log.Error("CancelBooking expediaAdapter.Cancel", log.Any("err", err))
			return err
		}

		isCanceled, isRefundAll, refundData = f.rateHawkAdapter.RetrieveCancelStatus(bgCtx, hubOrder)
	case enum.HotelProviderDida:
		confirmationID, penaltyAmount, err := f.dida.PreCancel(ctx, hubOrder.ReservationCode, hubOrder.OrderCode)
		if err != nil {
			log.Error("CancelBooking didaAdapter.PreCancel", log.Any("err", err))
			return err
		}

		if penaltyAmount >= hubOrder.OriginalRateDataCf.PayNow {
			return domain.ErrBookingNotAllowedToCancel
		}

		err = f.dida.Cancel(ctx, hubOrder.ReservationCode, confirmationID, hubOrder.OrderCode)
		if err != nil {
			log.Error("CancelBooking didaAdapter.Cancel", log.Any("err", err))
		}

		isCanceled, isRefundAll, refundData, err = f.dida.RetrieveCancelStatus(ctx, hubOrder.ReservationCode, penaltyAmount, hubOrder.OrderCode)
		if err != nil {
			log.Error("CancelBooking didaAdapter.RetrieveCancelStatus", log.Any("err", err))
			return err
		}

	}

	return f.HandleRetrieveBookingCancel(bgCtx, req.WebhookCfg, hubOrder, isCanceled, isRefundAll, refundData)
}

func (f *cancelBookingService) CheckCancelStatus(ctx context.Context, hubOrder *domain.HubHotelOrder, webHookConfig *domain.WebhookCfg) error {
	if hubOrder == nil {
		return domain.ErrBookingNotFound
	}

	if hubOrder.BookingStatus != enum.BookingStatusCanceling {
		return domain.ErrInvalidBookingStatus
	}

	var isCanceled bool
	var isRefundAll bool
	var refundData *domain.RefundData

	switch hubOrder.Provider {
	case enum.HotelProviderExpedia:
		isCanceled, isRefundAll, refundData = f.expediaAdapter.RetrieveForRefund(ctx, hubOrder, hubOrder.OrderCode)
	case enum.HotelProviderDida:
		var err error

		if hubOrder.RefundData == nil {
			log.Error("CancelBooking hubOrder.RefundData is nil", log.Any("hubOrder", hubOrder))
		} else {
			isCanceled, isRefundAll, refundData, err = f.dida.RetrieveCancelStatus(ctx, hubOrder.ReservationCode, hubOrder.RefundData.RefundAmount, hubOrder.OrderCode)
			if err != nil {
				log.Error("CancelBooking didaAdapter.RetrieveCancelStatus", log.Any("err", err))
				return err
			}
		}
	}

	return f.HandleRetrieveBookingCancel(ctx, webHookConfig, hubOrder, isCanceled, isRefundAll, refundData)
}
