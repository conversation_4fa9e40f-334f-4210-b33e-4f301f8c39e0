package command

import (
	"context"

	"fmt"
	"math"
	"strconv"
	"strings"
	"sync/atomic"
	"time"

	"github.com/gammazero/workerpool"
	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/tourmind_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/tourmind_client/client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/utils"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/strcmp"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type AggregateTourmindContentHandler interface {
	Handle(ctx context.Context, req *AggregateTourmindContentReq) (*AggregateTourmindContentRes, error)
}

type aggregateTourmindContentHandler struct {
	tourmindJobsQueueRepo repositories.TourmindJobsQueueRepository
	tourmindAdapter       tourmind_client.TourmindAdapter
	hotelRepo             repositories.HotelRepository
	tourmindRegionsRepo   repositories.TourmindRegionsRepository
}

func NewAggregateTourmindContentHandler(
	tourmindJobsQueueRepo repositories.TourmindJobsQueueRepository,
	tourmindAdapter tourmind_client.TourmindAdapter,
	hotelRepo repositories.HotelRepository,
	tourmindRegionsRepo repositories.TourmindRegionsRepository,
) AggregateTourmindContentHandler {
	return &aggregateTourmindContentHandler{
		tourmindJobsQueueRepo,
		tourmindAdapter,
		hotelRepo,
		tourmindRegionsRepo,
	}
}

type AggregateTourmindContentReq struct {
	CountryCode string `json:"country_code"`
	ForceInit   bool   `json:"force_init"`
	BatchSize   int    `json:"batch_size"`
}

type AggregateTourmindContentRes struct {
	Success      bool   `json:"success"`
	MappedHotels int    `json:"mapped_hotels"`
	TotalHotels  int    `json:"total_hotels"`
	Message      string `json:"message"`
}

func (h aggregateTourmindContentHandler) handleCountry(ctx context.Context, req *AggregateTourmindContentReq) (*AggregateTourmindContentRes, error) {
	var jobs []*domain.TourmindJobsQueue
	var err error

	jobs, err = h.tourmindJobsQueueRepo.RetrieveActiveJobs(ctx, req.CountryCode)
	if err != nil {
		return nil, errors.Wrap(err, "tourmindJobsQueueRepo.RetrieveActiveJobs")
	}

	if req.ForceInit || len(jobs) == 0 {
		jobs, err = h.initJobsQueue(ctx, req)
		if err != nil {
			return nil, errors.Wrap(err, "initJobsQueue")
		}
	}
	if len(jobs) == 0 {
		return &AggregateTourmindContentRes{
			Success: true,
			Message: "no job found, consider to force init jobs queue",
		}, nil
	}

	firstJob := jobs[0]
	totalItems := firstJob.TotalItems
	beginFrom := (firstJob.PageIndex * firstJob.BatchSize) - firstJob.BatchSize + 1
	log.Info(fmt.Sprintf("[AggregateTourmindContentHandler] %s Begin from %d of %d, totalJobs: %d", req.CountryCode, beginFrom, totalItems, len(jobs)))

	const poolSize = 5
	var mappedHotelsCount atomic.Int32
	var loadedHotelsCount atomic.Int32

	wp := workerpool.New(poolSize)

	for _, job := range jobs {
		job := job

		wp.Submit(func() {
			beginAt := time.Now()

			jobItems, err := h.getJobsItems(ctx, job)
			if err != nil {
				log.Error("getJobsItems error", log.Any("error", err), log.Any("job", job))
				return
			}

			if jobItems == nil || jobItems.HotelStaticListResult.Pagination.TotalCount == 0 {
				return
			}

			for _, jobItem := range jobItems.HotelStaticListResult.Hotels {
				canMap, err := h.doJob(ctx, &jobItem)
				if err != nil {
					log.Error("doJob error", log.Any("error", err), log.Any("jobItem", jobItem))
					return
				}

				loadedHotelsCount.Add(1)

				if canMap {
					job.SuccessIds = append(job.SuccessIds, jobItem.HotelID)
					mappedHotelsCount.Add(1)
				} else {
					job.CannotMapIds = append(job.CannotMapIds, jobItem.HotelID)
				}

				job.DurationMs = int(time.Since(beginAt).Milliseconds())

				if err := h.tourmindJobsQueueRepo.MarkJobAsCompleted(ctx, job); err != nil {
					log.Error("tourmindJobsQueueRepo.MarkJobAsCompleted error", log.Any("error", err), log.Any("job", job))
				}
			}

			log.Info(fmt.Sprintf("[AggregateTourmindContentHandler] %s Processed %d of %d (%d), success: %d", req.CountryCode, loadedHotelsCount.Load(), totalItems-beginFrom, totalItems, mappedHotelsCount.Load()))
		})
	}

	wp.StopWait()

	return &AggregateTourmindContentRes{
		Success:      true,
		MappedHotels: int(mappedHotelsCount.Load()),
		TotalHotels:  totalItems,
	}, nil
}

func (h aggregateTourmindContentHandler) Handle(ctx context.Context, req *AggregateTourmindContentReq) (*AggregateTourmindContentRes, error) {
	if req.CountryCode != "" {
		res, err := h.handleCountry(ctx, req)
		if err != nil {
			return nil, errors.Wrap(err, "handleCountry")
		}

		if err := h.tourmindRegionsRepo.MarkAsCompleted(ctx, req.CountryCode); err != nil {
			log.Error("tourmindRegionsRepo.MarkAsCompleted error", log.Any("err", err), log.String("countryCode", req.CountryCode))
			return nil, errors.Wrap(err, "tourmindRegionsRepo.MarkAsCompleted")
		}

		return res, nil
	}

	countriesList, err := h.tourmindRegionsRepo.FindAll(ctx, !req.ForceInit)
	if err != nil {
		return nil, errors.Wrap(err, "tourmindRegionsRepo.FindAll")
	}

	countResMapped := 0
	countResTotal := 0

	for _, country := range countriesList {
		req.CountryCode = country.CountryCode
		res, err := h.handleCountry(ctx, req)
		if err != nil {
			return nil, errors.Wrap(err, "handleCountry")
		}

		countResMapped += res.MappedHotels
		countResTotal += res.TotalHotels

		if err := h.tourmindRegionsRepo.MarkAsCompleted(ctx, country.CountryCode); err != nil {
			log.Error("tourmindRegionsRepo.MarkAsCompleted error", log.Any("err", err), log.String("countryCode", req.CountryCode))
			return nil, errors.Wrap(err, "tourmindRegionsRepo.MarkAsCompleted")
		}
	}

	return &AggregateTourmindContentRes{
		Success:      true,
		MappedHotels: countResMapped,
		TotalHotels:  countResTotal,
	}, nil
}

func (h aggregateTourmindContentHandler) doJob(ctx context.Context, item *entities.Hotel) (bool, error) {
	var hotels []*domain.Hotel
	var err error

	stage1Key := utils.GenHotelKey(item.Name, item.EscapedCityName(), item.CountryCode)

	hotels, err = h.hotelRepo.FindWithFilter(ctx, &domain.FindHotelsWithFilterReq{
		HotelKeyStage1: stage1Key,
	})
	if err != nil {
		return false, errors.Wrap(err, "FindWithFilter")
	}

	if len(hotels) == 0 {
		stage2Key := utils.GenHotelKey("", item.EscapedCityName(), item.CountryCode)

		hotels, err = h.hotelRepo.FindWithFilter(ctx, &domain.FindHotelsWithFilterReq{
			HotelKeyStage2: stage2Key,
			Lat:            item.GetLat(),
			Long:           item.GetLong(),
			Radius:         0.000898, // 100m
		})
		if err != nil {
			return false, errors.Wrap(err, "FindWithFilter stage2")
		}
	}

	if len(hotels) == 0 {
		return false, nil
	}

	var hotel *domain.Hotel

	if len(hotels) > 1 {
		hotel, err = h.getTrueHotel(hotels, item.Address)
		if err != nil {
			return false, errors.Wrap(err, "getTrueHotel")
		}
	} else {
		hotel = hotels[0]
	}

	if hotel == nil {
		return false, nil
	}

	if err := h.hotelRepo.UpdateProviderIds(ctx, hotel.HotelID, enum.HotelProviderTourMind, item.HotelID); err != nil {
		return false, errors.Wrap(err, "UpdateProviderIds")
	}

	return true, nil
}

func (h aggregateTourmindContentHandler) getTrueHotel(hotels []*domain.Hotel, trueAddress string) (*domain.Hotel, error) {

	var outHotel *domain.Hotel
	var outHotelRatio float64

	for _, hotel := range hotels {
		str1 := strings.ToLower(hotel.Address.Line1)
		str2 := strings.ToLower(trueAddress)

		_, ratio := strcmp.Compare(str1, str2)
		if ratio < 0.55 {
			continue
		}

		if outHotel == nil || outHotelRatio < ratio {
			outHotel = hotel
			outHotelRatio = ratio
		}
	}
	return outHotel, nil
}

func (h aggregateTourmindContentHandler) getJobsItems(ctx context.Context, job *domain.TourmindJobsQueue) (*entities.HotelStaicListRes, error) {
	sampleReq := &entities.HotelStaicListReq{
		CountryCode: job.CountryCode,
		Pagination: entities.Pagination{
			PageIndex: job.PageIndex,
			PageSize:  job.BatchSize,
		},
	}

	tracingID := "agg_tourmind_" + job.CountryCode + "_" + job.ID

	items, err := h.tourmindAdapter.HotelStaticList(ctx, sampleReq, tracingID)
	if err != nil {
		return nil, err
	}

	if items.Error.ErrorCode != "" {
		return nil, errors.New(items.Error.ErrorCode)
	}

	if items.HotelStaticListResult.Pagination.TotalCount == 0 {
		return nil, nil
	}

	return items, nil
}

func (h aggregateTourmindContentHandler) getSampleItem(ctx context.Context, req *AggregateTourmindContentReq) (*entities.HotelStaicListRes, error) {
	sampleReq := &entities.HotelStaicListReq{
		CountryCode: req.CountryCode,
		Pagination: entities.Pagination{
			PageIndex: 1,
			PageSize:  1,
		},
	}

	tracingID := "tracing-id-" + req.CountryCode + "-" + time.Now().Format("2006-01-02")

	sampleItem, err := h.tourmindAdapter.HotelStaticList(ctx, sampleReq, tracingID)
	if err != nil {
		return nil, err
	}

	if sampleItem.Error.ErrorCode != "" {
		return nil, errors.New(sampleItem.Error.ErrorCode)
	}

	return sampleItem, nil
}

func (h aggregateTourmindContentHandler) initJobsQueue(ctx context.Context, req *AggregateTourmindContentReq) ([]*domain.TourmindJobsQueue, error) {
	// Clear the job queue
	if err := h.tourmindJobsQueueRepo.SoftDeleteMany(ctx, req.CountryCode); err != nil {
		return nil, err
	}

	sampleItem, err := h.getSampleItem(ctx, req)
	if err != nil {
		return nil, err
	}

	if sampleItem == nil || sampleItem.HotelStaticListResult.Pagination.TotalCount == 0 {
		return nil, nil
	}

	totalCount := sampleItem.HotelStaticListResult.Pagination.TotalCount
	var batchSize = 100

	if req.BatchSize > 0 {
		if req.BatchSize > totalCount {
			return nil, errors.New("batch size is greater than total count " + strconv.Itoa(totalCount))
		}

		batchSize = req.BatchSize
	}

	jobsNums := float64(totalCount) / float64(batchSize)
	jobsNums = math.Ceil(jobsNums)

	jobs := make([]*domain.TourmindJobsQueue, int(jobsNums))
	for i := 0; i < int(jobsNums); i++ {
		jobs[i] = &domain.TourmindJobsQueue{
			ID:          primitive.NewObjectID().Hex(),
			CreatedAt:   time.Now().UnixMilli(),
			CountryCode: req.CountryCode,
			PageIndex:   i + 1,
			BatchSize:   batchSize,
			LastItem:    i+1 == int(jobsNums),
			TotalItems:  totalCount,
		}
	}

	if err := h.tourmindJobsQueueRepo.CreateMany(ctx, jobs); err != nil {
		return nil, err
	}

	return jobs, nil
}
