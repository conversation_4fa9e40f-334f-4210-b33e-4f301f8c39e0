package command

import (
	"context"

	"gitlab.deepgate.io/apps/common/log"
	redisRepo "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/redis"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

type PriceCheckHandler interface {
	Handle(ctx context.Context, req *domain.HubPriceCheckReq) (*domain.HubPriceCheckRes, error)
}

type priceCheckHandler struct {
	priceCheckService  service.PriceCheckService
	sessionService     service.SessionService
	searchHotelService service.CheckAvailabilityService
	searchRedisRepo    redisRepo.SearchHotelsRepository
}

func NewPriceCheckHandler(
	priceCheckService service.PriceCheckService,
	sessionService service.SessionService,
	searchHotelService service.CheckAvailabilityService,
	searchRedisRepo redisRepo.SearchHotelsRepository,
) PriceCheckHandler {
	return &priceCheckHandler{
		priceCheckService,
		sessionService,
		searchHotelService,
		searchRedisRepo,
	}
}

func (h *priceCheckHandler) Handle(ctx context.Context, req *domain.HubPriceCheckReq) (*domain.HubPriceCheckRes, error) {
	if err := h.searchRedisRepo.WaitCachingLockRelease(req.SearchKey); err != nil {
		log.Error("h.searchRedisRepo.WaitCachingLockRelease error", log.Any("error", err), log.String("searchKey", req.SearchKey))
		return nil, err
	}

	response, err := h.priceCheckService.PriceCheck(ctx, req)
	if err != nil {
		log.Error("h.priceCheckService.PriceCheck error", log.Any("error", err), log.Any("req", req))
		return nil, err
	}

	return response, nil
}
