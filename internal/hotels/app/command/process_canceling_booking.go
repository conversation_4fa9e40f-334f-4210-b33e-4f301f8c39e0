package command

import (
	"context"
	"time"

	"github.com/gammazero/workerpool"
	constantsCommon "gitlab.deepgate.io/apps/common/constants"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/notification"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
)

type ProcessCancelingBookingHandler interface {
	Handle(ctx context.Context) error
}

type processCancelingBookingHandler struct {
	cfg                  *config.Schema
	orderRepo            repositories.OrderRepository
	cancelBookingService service.CancelBookingService
	notificationClient   notification.NotificationServiceClient
}

func NewProcessCancelingBookingHandler(
	cfg *config.Schema,
	cancelBookingService service.CancelBookingService,
	orderRepo repositories.OrderRepository,
	notificationClient notification.NotificationServiceClient,
) ProcessCancelingBookingHandler {
	return &processCancelingBookingHandler{
		cfg,
		orderRepo,
		cancelBookingService,
		notificationClient,
	}
}

func (h *processCancelingBookingHandler) Handle(ctx context.Context) error {
	bookings, err := h.orderRepo.ListCancelingOrder(ctx)
	if err != nil {
		log.Error("h.processCancelingBookingService.ListCancelingOrder error", log.Any("error", err))
		return err
	}

	wp := workerpool.New(5)
	for _, booking := range bookings {
		wp.Submit(func() {
			bgCtx, cc := context.WithTimeout(ctx, constants.ThirdPartyRequestTimeout)
			defer cc()
			cancelingDeadline := time.UnixMilli(booking.CancelingStartAt)
			now := time.Now()

			if cancelingDeadline.Before(now.Add(-2 * time.Hour)) {
				booking.SkipScanCheckCancel = true
				err := h.orderRepo.UpdateOne(bgCtx, booking.ID, booking)
				if err != nil {
					log.Error("h.orderRepo.UpdateOne error", log.Any("error", err))
					return
				}

				go func(booking *domain.HubHotelOrder) {
					teleCtx, cc := context.WithTimeout(context.Background(), 30*time.Second)
					defer cc()

					err := h.notificationClient.SendMessage(teleCtx, &notification.SendMessageRequest{
						EntityType:  constantsCommon.NotificationEntityPartnership,
						EntityID:    h.cfg.HubPartnershipID,
						ServiceCode: constantsCommon.NotificationServiceCodeHUB,
						Action:      constantsCommon.TelegramNotificationActionHotelBookingCancelling,
						Data: map[string]string{
							"OrderCode":       booking.OrderCode,
							"ReservationCode": booking.ReservationCode,
							"Provider":        enum.HotelProviderName[booking.Provider],
							"Flatform":        h.cfg.HubAppName,
						},
					})

					if err != nil {
						log.Error("[h.notificationClient.SendMessage] error", log.Any("error", err))
					}

				}(booking)

				return
			}

			err := h.cancelBookingService.CheckCancelStatus(ctx, booking, nil)
			if err != nil {
				log.Error("h.processCancelingBookingService.ProcessCancelingBooking error", log.Any("error", err), log.Any("booking", booking))
				return
			}
		})
	}

	wp.StopWait()
	return nil
}
