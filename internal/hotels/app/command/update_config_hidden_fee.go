package command

import (
	"context"

	"gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

type UpdateConfigHiddenFeeHandler interface {
	Handle(ctx context.Context, req *domain.UpsertConfigHiddenFeeRequest) (*domain.HiddenServiceFee, error)
}

type updateConfigHiddenFeeHandler struct {
	hiddenFeeRepo repositories.HiddenServiceFeeRepository
}

func NewUpdateConfigHiddenFeeHandler(
	hiddenFeeRepo repositories.HiddenServiceFeeRepository,
) UpdateConfigHiddenFeeHandler {
	return &updateConfigHiddenFeeHandler{
		hiddenFeeRepo: hiddenFeeRepo,
	}
}

func (h *updateConfigHiddenFeeHandler) Handle(ctx context.Context, req *domain.UpsertConfigHiddenFeeRequest) (*domain.HiddenServiceFee, error) {
	configHiddenFee := &domain.HiddenServiceFee{
		OfficeID: req.OfficeID,
		Type:     req.Type,
		Config: &domain.HiddenServiceFeeConfig{
			LocationType: req.Location,
			CountryCode:  req.Countries,
			Rating:       req.Ratings,
			HotelType:    req.HotelType,
		},
		Provider:  req.Provider,
		HotelID:   req.HotelID,
		HotelName: req.HotelName,
		Amount:    req.Amount,
		Percent:   req.Percent,
	}

	configHiddenRecord, err := h.hiddenFeeRepo.FindOne(ctx, configHiddenFee)
	if err != nil {
		log.Error("hotelOrderRepo.FindOneByOrderCode error", log.Any("error", err))
		return nil, errors.ErrSomethingOccurred
	}

	if configHiddenRecord != nil && configHiddenRecord.ID != req.ID {
		return nil, domain.ErrDuplicateHiddenServiceFee
	}

	updateRecord, err := h.hiddenFeeRepo.FindByID(ctx, req.ID)
	if err != nil {
		log.Error("hotelOrderRepo.FindOneByOrderCode error", log.Any("error", err))
		return nil, errors.ErrSomethingOccurred
	}

	if updateRecord == nil {
		return nil, errors.ErrNotFound
	}

	// map base config
	configHiddenFee.Base = updateRecord.Base
	configHiddenFee.Base.UpdatedBy = req.UserID
	config, err := h.hiddenFeeRepo.Update(ctx, configHiddenFee)
	if err != nil {
		log.Error("hotelOrderRepo.FindOneByOrderCode error", log.Any("error", err))
		return nil, err
	}

	return config, nil
}
