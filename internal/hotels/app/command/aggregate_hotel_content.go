package command

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"sync"
	"time"

	"gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
)

var (
	mu sync.Mutex
)

const (
	DefaultLanguage = "vi-VN"
)
const maxRetries = 3
const retryDelay = 500 * time.Millisecond

type AggregateHotelContentHandler interface {
	Handle(ctx context.Context, req *AggregateHotelContentReq) error
}

type aggregateHotelContentHandler struct {
	hotelRepo repositories.HotelRepository
	roomRepo  repositories.RoomRepository
}

type MigrateData struct {
	Page           int
	From           int
	To             int
	TargetLanguage string
	SourceLanguage string
}

type AggregateHotelContentReq struct {
	Migrate             *MigrateData
	ContentVersion      string
	Provider            enum.HotelProvider
	PrefixNumber        int
	Language            string
	ExpediaHotelContent *domain.ExpediaHotelContent
	RateHawkContent     *domain.RateHawkHotelContent
}

func NewAggregateHotelContentHandler(
	hotelRepo repositories.HotelRepository,
	roomRepo repositories.RoomRepository,
) AggregateHotelContentHandler {
	return &aggregateHotelContentHandler{hotelRepo, roomRepo}
}

func WriteErrorRecordForRetry(content any, filepath string) {
	file, err := os.Create(filepath)
	if err != nil {
		fmt.Println("Error creating file:", err)
		return
	}
	defer file.Close()

	encoder := json.NewEncoder(file)
	encoder.SetIndent("", "  ") // Tùy chọn để định dạng đẹp
	if err := encoder.Encode(content); err != nil {
		fmt.Println("Error writing to file:", err)
		return
	}
}

func (h *aggregateHotelContentHandler) Handle(ctx context.Context, req *AggregateHotelContentReq) error {
	switch req.Provider {
	case enum.HotelProviderExpedia, enum.HotelProviderRateHawk:
		if req.Migrate != nil {
			return h.MigrateHotelRoomName(ctx, req.Migrate.Page, req.Migrate.From, req.Migrate.To, req.Migrate.TargetLanguage, req.Migrate.SourceLanguage)
		}
		return h.HandleInitContent(ctx, req)
	}

	return nil
}

func (h *aggregateHotelContentHandler) HandleInitContent(ctx context.Context, req *AggregateHotelContentReq) error {
	if req == nil {
		log.Error("HandleInitContent req nil")
		return errors.ErrInvalidInput
	}

	var rooms []*domain.Room
	var createRooms []*domain.Room
	var updateRooms []*domain.Room

	var hotel *domain.Hotel

	switch req.Provider {
	case enum.HotelProviderExpedia:
		if req.ExpediaHotelContent == nil {
			log.Error("HandleInitContent req.ExpediaHotelContent nil")
			return errors.ErrInvalidInput
		}
		rooms, hotel = domain.MapExpediaHotelContentToRoomsAndHotel(req.ExpediaHotelContent, req.Language)
	case enum.HotelProviderRateHawk:
		if req.RateHawkContent == nil {
			log.Error("HandleInitContent req.RateHawkContent nil")
			return errors.ErrInvalidInput
		}
		rooms, hotel = domain.MapRateHawkHotelContentToRoomsAndHotel(req.RateHawkContent, req.Language)
	default:
		log.Error("HandleInitContent un-support provider")
		return domain.ErrNoProviderForOfficeID
	}

	if hotel == nil {
		return errors.ErrSomethingOccurred
	}

	hotelRecord, err := h.hotelRepo.FindByHotelID(ctx, hotel.HotelID, req.Language)
	if err != nil {
		log.Error("Find hotel error", log.Any("err", err))
		return err
	}

	isUpdate := false
	if hotelRecord != nil {
		isUpdate = true
		hotel.ID = hotelRecord.ID

		for _, room := range rooms {
			roomRecord, err := h.roomRepo.FindByRoomIDAndHotelRef(ctx, hotel.ID, room.RoomID, req.Language)
			if err != nil {
				log.Error("Find Room error", log.Any("err", err))
				return err
			}

			if roomRecord == nil {
				createRooms = append(createRooms, room)
			} else {
				room.ID = roomRecord.ID
				updateRooms = append(updateRooms, room)
			}
		}
	} else {
		createRooms = rooms
	}

	if len(rooms) == 0 {
		hotel.Active = false
	}

	if isUpdate {
		err = h.retryUpdate(ctx, hotel, createRooms, updateRooms)
		if err != nil {
			log.Error("retryCreateHotel error", log.Any("err", err))
			return err
		}
		fmt.Println("Updated ", hotel.Name, fmt.Sprintf(" Created %d and update %d rooms", len(createRooms), len(updateRooms)))
	} else {
		err = h.retryCreateHotel(ctx, hotel, createRooms)
		if err != nil {
			log.Error("retryCreateHotel error", log.Any("err", err))
			return err
		}
		fmt.Println("Created ", hotel.Name, fmt.Sprintf(" Created %d rooms", len(createRooms)))
	}

	defer func() {
		if err != nil {
			switch req.Provider {
			case enum.HotelProviderExpedia:
				if req.ExpediaHotelContent != nil {
					WriteErrorRecordForRetry(req.ExpediaHotelContent, fmt.Sprintf("./logs/expedia_content_err_%s", req.Language))
				}
			case enum.HotelProviderRateHawk:
				if req.RateHawkContent != nil {
					WriteErrorRecordForRetry(req.RateHawkContent, fmt.Sprintf("./logs/rate_hawk_content_err_%s", req.Language))
				}
			}
		}
	}()
	return nil
}

func (h *aggregateHotelContentHandler) MigrateHotelRoomName(ctx context.Context, page, from int, to int, targetLanguage string, sourceLanguage string) error {
	h.roomRepo.MigrateHotelRoomName(ctx, page, from, to, targetLanguage, sourceLanguage)
	return nil
}

func (h *aggregateHotelContentHandler) retryCreateHotel(ctx context.Context, hotel *domain.Hotel, rooms []*domain.Room) error {
	var err error
	for i := 0; i < maxRetries; i++ {
		// re-generate ID
		hotel.ID = ""
		hotel.InitID()

		roomReference := []*domain.RoomRefInfo{}

		for _, room := range rooms {
			room.HotelRef = hotel.ID
			// re-generate ID
			room.ID = ""
			room.Base.InitID()

			roomReference = append(roomReference, &domain.RoomRefInfo{
				ID:          room.ID,
				ProviderIds: room.GetProviderIDs(),
				RoomID:      room.RoomID,
			})
		}

		hotel.RoomReferences = roomReference

		err := h.hotelRepo.WithTransaction(ctx, func(tnxSession context.Context) (interface{}, error) {
			err = h.hotelRepo.Create(tnxSession, hotel)
			if err != nil {
				return nil, err
			}

			if len(rooms) > 0 {
				err = h.roomRepo.CreateMany(tnxSession, rooms, hotel.Language)
				if err != nil {
					return nil, err
				}
			}

			return nil, nil
		})
		if err == nil {
			return nil
		}

		log.Error("WithTransaction error", log.Any("err", err), log.String("hotelID", hotel.HotelID))
		time.Sleep(retryDelay)
	}
	return nil
}

func (h *aggregateHotelContentHandler) retryUpdate(ctx context.Context, hotel *domain.Hotel, rooms, updateRooms []*domain.Room) error {
	var err error
	for i := 0; i < maxRetries; i++ {
		roomReference := []*domain.RoomRefInfo{}

		for _, room := range updateRooms {
			roomReference = append(roomReference, &domain.RoomRefInfo{
				ID:          room.ID,
				ProviderIds: room.GetProviderIDs(),
				RoomID:      room.RoomID,
			})
		}

		for _, room := range rooms {
			room.HotelRef = hotel.ID
			// re-generate ID
			room.ID = ""
			room.Base.InitID()
			roomReference = append(roomReference, &domain.RoomRefInfo{
				ID:          room.ID,
				ProviderIds: room.GetProviderIDs(),
				RoomID:      room.RoomID,
			})
		}

		hotel.RoomReferences = roomReference

		err := h.hotelRepo.WithTransaction(ctx, func(tnxSession context.Context) (interface{}, error) {
			err = h.hotelRepo.Update(tnxSession, hotel)
			if err != nil {
				return nil, err
			}

			if len(rooms) > 0 {
				err = h.roomRepo.CreateMany(tnxSession, rooms, hotel.Language)
				if err != nil {
					return nil, err
				}
			}

			if len(updateRooms) > 0 {
				for _, updateRoom := range updateRooms {
					err = h.roomRepo.Update(tnxSession, updateRoom)
					if err != nil {
						return nil, err
					}
				}
			}

			return nil, nil
		})
		if err == nil {
			return nil
		}

		log.Error("WithTransaction error", log.Any("err", err), log.String("hotelID", hotel.HotelID))
		time.Sleep(retryDelay)
	}
	return nil
}
