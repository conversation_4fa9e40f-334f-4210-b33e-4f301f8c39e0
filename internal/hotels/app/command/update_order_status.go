package command

import (
	"context"

	"github.com/samber/lo"
	"gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
)

type UpdateOrderStatusHandler interface {
	Handle(ctx context.Context, req *domain.UpdateOrderStatusRequest) error
}

type updateOrderStatusHandler struct {
	hotelOrderRepo repositories.OrderRepository
	bookingService service.BookingService
}

func NewUpdateOrderStatusHandler(
	hotelOrderRepo repositories.OrderRepository,
	bookingService service.BookingService,
) UpdateOrderStatusHandler {
	return &updateOrderStatusHandler{
		hotelOrderRepo: hotelOrderRepo,
		bookingService: bookingService,
	}
}

func (h *updateOrderStatusHandler) validateRequest(req *domain.UpdateOrderStatusRequest, order *domain.HubHotelOrder) error {
	if order.BookingStatus != enum.BookingStatusPending {
		log.Error("updateOrderStatusHandler validateRequest invalid status", log.Any("order.BookingStatus", order.BookingStatus))
		return domain.ErrInvalidBookingStatus
	}

	allowedStatus := []enum.BookingStatus{
		enum.BookingStatusSuccess,
		enum.BookingStatusFailed,
		enum.BookingStatusCancel,
	}

	if !lo.Contains(allowedStatus, req.Status) {
		log.Error("updateOrderStatusHandler validateRequest invalid status")
		return errors.ErrInvalidInput
	}

	if len(req.OccupancyInfos) != len(order.Hotel.ListRooms) {
		return domain.ErrInvalidConfirmationIDAmount
	}

	return nil
}

func (h *updateOrderStatusHandler) Handle(ctx context.Context, req *domain.UpdateOrderStatusRequest) error {
	hotelOrder, err := h.hotelOrderRepo.FindOneByOrderCode(ctx, "", req.OrderCode)
	if err != nil {
		log.Error("hotelOrderRepo.FindOneByOrderCode error", log.Any("error", err), log.String("OrderCode", req.OrderCode))
		return err
	}

	if hotelOrder == nil || hotelOrder.Hotel == nil {
		return errors.ErrNotFound
	}

	if err := h.validateRequest(req, hotelOrder); err != nil {
		return err
	}

	if req.Status == enum.BookingStatusCancel || req.Status == enum.BookingStatusFailed {
		if err := h.bookingService.Refund(ctx, hotelOrder); err != nil {
			return err
		}
	}

	hotelOrder.BookingStatus = req.Status
	updatedCount := 0
	for _, occupancy := range req.OccupancyInfos {
		if req.Status == enum.BookingStatusSuccess && occupancy.ConfirmationID == "" {
			log.Error("updateOrderStatusHandler validateRequest invalid occupancy", log.Any("occupancy", occupancy))
			return errors.ErrInvalidInput
		}

		for _, room := range hotelOrder.Hotel.ListRooms {
			if room.ConfirmationID != "" ||
				room.OccupancyIndex != uint(occupancy.OccupancyIndex) {
				continue
			}

			room.ConfirmationID = occupancy.ConfirmationID
			room.BookingStatus = req.Status
			updatedCount++
		}
	}

	if updatedCount != len(req.OccupancyInfos) {
		return domain.ErrInvalidConfirmationIDAmount
	}

	if err := h.hotelOrderRepo.UpdateOne(ctx, hotelOrder.ID, hotelOrder); err != nil {
		log.Error("hotelOrderRepo.UpdateOne error", log.Any("error", err), log.String("hotelOrder.ID", hotelOrder.ID))
		return err
	}

	return nil
}
