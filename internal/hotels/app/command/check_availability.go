package command

import (
	"context"

	"github.com/samber/lo"
	"gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
)

type CheckAvailabilityHandler interface {
	Handle(ctx context.Context, req *domain.HubCheckAvailabilityReq) (*domain.HubCheckAvailabilityRes, error)
}

type checkAvailabilityHandler struct {
	cfg           *config.Schema
	searchService service.CheckAvailabilityService
	currencyExSvc service.CurrencyExchangeService
	hiddenFeeSvc  service.HiddenFeeService
}

func NewCheckAvailabilityHandler(
	cfg *config.Schema,
	searchService service.CheckAvailabilityService,
	currencyExSvc service.CurrencyExchangeService,
	hiddenFeeSvc service.HiddenFeeService,
) CheckAvailabilityHandler {
	return &checkAvailabilityHandler{cfg, searchService, currencyExSvc, hiddenFeeSvc}
}

func (h *checkAvailabilityHandler) Handle(ctx context.Context, req *domain.HubCheckAvailabilityReq) (*domain.HubCheckAvailabilityRes, error) {
	if err := req.Stay.CountDays(); err != nil {
		log.Error("SearchHotelPrice stay.DayCount err", log.Any("err", err), log.Any("stay.DayCount", req.Stay.CountDays))
		return nil, err
	}
	req.Stay.RoomCount = req.CountRooms()

	if req.HotelID == "" && len(req.ListHotels) > 0 {
		req.HotelID = req.ListHotels[0]
	}

	if len(req.ListHotels) == 0 {
		return nil, errors.New(errors.BadRequest, "not support multi hotel request")
	}

	provider := enum.HotelProviderFromHash(req.MatchKey)
	if provider == enum.HotelProviderNone {
		return nil, domain.ErrInvalidMatchKey
	}

	if !lo.Contains(req.EnableProviders, provider) {
		return nil, domain.ErrProviderNotAllowed
	}

	enableProvider := []enum.HotelProvider{provider}

	// Search
	res, _, err := h.searchService.Search(ctx, req, enableProvider)
	if err != nil {
		return nil, err
	}

	if res == nil {
		return nil, nil
	}

	res.Hotels, err = h.currencyExSvc.ConvertCurrency(ctx, req, enableProvider[0], res.Hotels, constants.VNDCurrency)
	if err != nil {
		log.Error("currencyExSvc.ConvertCurrency err", log.Any("err", err))
		return nil, err
	}

	for _, hotel := range res.Hotels {
		for _, room := range hotel.ListRooms {
			rateFinal := make([]*domain.HubRateData, 0)
			for _, rate := range room.RateData {
				if rate.IsSoldOut {
					continue
				} else {
					rateFinal = append(rateFinal, rate)
				}
			}

			room.RateData = rateFinal
		}

		h.hiddenFeeSvc.CalculateHotelDetailPrices(ctx, &domain.CalculateHotelDetailPricesReq{
			PartnershipID: req.PartnershipID,
			OfficeID:      req.OfficeID,
			Multiplier:    uint32(req.Stay.DayCount) * uint32(req.Stay.RoomCount),
			HubHotel:      hotel,
		})
	}

	// group rate data
	formatGroupBy := req.FormatRateDataGroups()
	converts.GroupRateData(res.Hotels, formatGroupBy)

	return res, nil
}
