package command

import (
	"context"

	commonDomain "gitlab.deepgate.io/apps/common/domain"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/helpers"
)

type SearchHotelsHandler interface {
	Handle(ctx context.Context, req *domain.HubSearchHotelRequest) (*domain.HubSearchHotelResponse, error)
}

type searchHotelsHandler struct {
	cfg           *config.Schema
	searchService service.SearchHotelService
	currencyExSvc service.CurrencyExchangeService
	hiddenFeeSvc  service.HiddenFeeService
}

func NewSearchHotelsHandler(
	cfg *config.Schema,
	searchService service.SearchHotelService,
	currencyExSvc service.CurrencyExchangeService,
	hiddenFeeSvc service.HiddenFeeService,
) SearchHotelsHandler {
	return &searchHotelsHandler{cfg, searchService, currencyExSvc, hiddenFeeSvc}
}

func (h *searchHotelsHandler) Handle(ctx context.Context, req *domain.HubSearchHotelRequest) (*domain.HubSearchHotelResponse, error) {
	if req.Place.Type == enum.PlaceTypeContinent || req.Place.Type == enum.PlaceTypeCountry {
		return &domain.HubSearchHotelResponse{
			SearchHotelFilterRequest: &domain.SearchHotelFilterResponse{},
			HotelSummary:             []*domain.HotelSummary{},
			Pagination:               &commonDomain.Pagination{},
		}, nil
	}

	if err := req.Stay.CountDays(); err != nil {
		log.Error("SearchHotelPrice stay.DayCount err", log.Any("err", err), log.Any("stay.DayCount", req.Stay.CountDays))
		return nil, err
	}

	req.Stay.RoomCount = req.CountRooms()

	enableProvider := []enum.HotelProvider{
		enum.HotelProviderNone,
	}

	enableProvider = append(enableProvider, req.EnableProviders...)
	// Search
	res, loadMore, err := h.searchService.Search(ctx, req, enableProvider)
	if err != nil {
		return nil, err
	}

	if res == nil {
		return &domain.HubSearchHotelResponse{
			ErrorRes: domain.ErrorRes{
				IsSuccess: true,
			},
			SearchHotelFilterRequest: &domain.SearchHotelFilterResponse{},
			Pagination:               req.Pagination,
			HotelSummary:             []*domain.HotelSummary{},
		}, nil
	}

	totalCounts := req.Pagination.TotalRecord

	filter := h.searchService.CalcFilterRequest(ctx, res)

	filterList := h.searchService.ApplyFilterRequest(ctx, res, req.SearchHotelFilterRequest)

	sortedList := h.searchService.ApplySortRequest(ctx, req, filterList)

	pagedData := h.searchService.ApplyPaging(ctx, sortedList, req.Pagination)

	pagedDataWithHiddenFee := helpers.Copy(pagedData).([]*domain.HotelSummary)

	h.hiddenFeeSvc.CalculateHotelSearchPrices(ctx, &domain.CalculateHotelSearchPricesReq{
		Multiplier:     uint32(req.Stay.RoomCount * req.Stay.DayCount),
		PartnershipID:  req.PartnershipID,
		OfficeID:       req.OfficeID,
		HotelSummaries: pagedDataWithHiddenFee,
	})
	// re-gen key no need to check error
	searchKey, _ := req.GenSearchKey()
	// Skip convert currency with fake provider

	result := &domain.HubSearchHotelResponse{
		ErrorRes: domain.ErrorRes{
			IsSuccess: true,
		},
		SearchHotelFilterRequest: filter,
		Pagination:               req.Pagination,
		HotelSummary:             pagedDataWithHiddenFee,
	}

	// if h.cfg.Env != commonConstants.EnvProduction {
	result.SearchKey = searchKey
	// }

	if loadMore && result.Pagination.PageCurrent == result.Pagination.TotalPage {
		if totalCounts == 0 || totalCounts <= req.Pagination.PageLimit {
			totalCounts = 1000
		}

		result.Pagination.TotalPage = totalCounts / req.Pagination.PageLimit
		result.Pagination.TotalRecord = totalCounts
	}

	return result, nil
}
