package command

import (
	"context"

	"gitlab.deepgate.io/apps/common/log"
	esRepo "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/es/es_repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
)

type SearchDestinationHandler interface {
	Handle(ctx context.Context, req *domain.SearchDestinationReq) (*domain.SearchDestinationRes, error)
}

type searchDestinationHandler struct {
	cfg         *config.Schema
	esPlaceRepo esRepo.PlaceElasticRepository
}

func NewSearchDestinationHandler(
	cfg *config.Schema,
	esPlaceRepo esRepo.PlaceElasticRepository,
) SearchDestinationHandler {
	return &searchDestinationHandler{cfg, esPlaceRepo}
}

func (h *searchDestinationHandler) Handle(ctx context.Context, req *domain.SearchDestinationReq) (*domain.SearchDestinationRes, error) {
	places, pagination, err := h.esPlaceRepo.Search(ctx, req, h.cfg.ContentVersion)
	if err != nil {
		log.Error("esPlaceRepo.Search err", log.Any("err", err))
		return nil, err
	}

	language := req.Language
	if language == "" {
		language = constants.DefaultLanguage
	}

	return &domain.SearchDestinationRes{
		ErrorRes: domain.ErrorRes{
			IsSuccess: true,
		},
		Pagination:   pagination,
		Destinations: places,
		Language:     language,
	}, nil
}
