package command

import (
	"context"
	"fmt"
	"time"

	env "gitlab.deepgate.io/apps/common/constants"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
	commonError "gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/order"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/partner"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/payment"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/redis"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/wallet"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/webhook"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
)

const (
	// Timeout constants
	issueTimeout     = 5 * time.Minute
	webhookTimeout   = 20 * time.Second
	pendingTimeout   = 90 * time.Second
	pendingTimeout25 = 25 * time.Minute
)

type BookHotelHandler interface {
	Handle(ctx context.Context, req *domain.HubBookReq, webhookCfg *domain.WebhookCfg) (*domain.HubBookRes, error)
}

type bookHotelHandler struct {
	cfg            *config.Schema
	hubOrderRepo   repositories.OrderRepository
	sessionRepo    repositories.SessionRepository
	bookingService service.BookingService
	bookingRedis   redis.BookingRepository
	orderClient    order.OrderServiceClient
	paymentClient  payment.PaymentClient
	walletClient   wallet.WalletClient
	interClient    partner.PartnerClient
	webhookClient  webhook.WebhookAdapter
}

func NewBookHotelHandler(
	cfg *config.Schema,
	hubOrderRepo repositories.OrderRepository,
	sessionRepo repositories.SessionRepository,
	bookingService service.BookingService,
	bookingRedis redis.BookingRepository,
	orderClient order.OrderServiceClient,
	paymentClient payment.PaymentClient,
	walletClient wallet.WalletClient,
	interClient partner.PartnerClient,
	webhookClient webhook.WebhookAdapter,
) BookHotelHandler {
	return &bookHotelHandler{
		cfg,
		hubOrderRepo,
		sessionRepo,
		bookingService,
		bookingRedis,
		orderClient,
		paymentClient,
		walletClient,
		interClient,
		webhookClient,
	}
}

func (h *bookHotelHandler) Handle(ctx context.Context, req *domain.HubBookReq, webhookCfg *domain.WebhookCfg) (*domain.HubBookRes, error) {
	if h.cfg.Env == env.ProductionEnvName {
		req.TestingHashKey = ""
	}

	if !ValidateTestingHashKey(req.TestingHashKey) {
		return nil, domain.ErrInvalidTestingKey
	}

	const defaultPM = commonEnum.PaymentMethod_Wallet

	err := h.bookingRedis.AcquireCreateBookingLock(req.SessionID)
	if err != nil {
		return nil, domain.ErrAnotherRequestInProgress
	}
	defer func() {
		if err := h.bookingRedis.ReleaseCreateBookingLock(req.SessionID); err != nil {
			log.Error("ReleaseCreateBookingLock error", log.Any("error", err), log.String("sessionID", req.SessionID))
		}
	}()

	session, err := h.sessionRepo.FindBySessionID(ctx, req.HubOfficeID, req.SessionID)
	if err != nil {
		log.Error("sessionRepo.FindBySessionID error", log.Any("error", err),
			log.String("officeID", req.HubOfficeID), log.String("SessionID", req.SessionID))
		return nil, commonError.ErrSomethingOccurred
	}

	if session == nil {
		return nil, domain.ErrSessionInvalidOrExpired
	}

	hotelOrder, err := h.hubOrderRepo.FindOrderBySessionID(ctx, req.HubOfficeID, req.SessionID)
	if err != nil {
		log.Error("bookingRepo.FindOneByBookingCode error", log.Any("error", err),
			log.String("officeID", req.HubOfficeID), log.String("SessionID", req.SessionID))
		return nil, commonError.ErrSomethingOccurred
	}

	if hotelOrder == nil {
		return nil, domain.ErrBookingNotFound
	}

	isSandbox := h.cfg.Env == env.EnvSandbox

	//MOCK TIMEOUT
	if hotelOrder.Hotel != nil && isSandbox && hotelOrder.Hotel.HotelID == constants.TimeOutHotelID {
		ctx, cancel := context.WithTimeout(ctx, 9*time.Minute)
		defer cancel()

		<-ctx.Done()
		return nil, ctx.Err()
	}

	//MOCK FAIL
	if hotelOrder.Hotel != nil && isSandbox && hotelOrder.Hotel.HotelID == constants.FailBookingHotelID {
		return nil, fmt.Errorf("fail to booking hotel")
	}

	if err := hotelOrder.CanBook(); err != nil {
		return nil, err
	}

	// DO NOT REMOVE!!!
	hotelOrder.CustomerIP = req.EndUserIPAddress

	hotelOrder.RequestHolder = req.Holder
	orderPayment := domain.ToDomainOrderPayment(hotelOrder)
	orderPayment.UserID = req.AgentID

	// Auto fill missing holder
	reqRoomsLen := len(hotelOrder.Hotel.ListRooms)

	if len(req.Holder.HolderDetail) < reqRoomsLen {
		baseHolder := req.Holder.HolderDetail[0]
		missingCount := reqRoomsLen - len(req.Holder.HolderDetail)

		for i := 0; i < missingCount; i++ {
			req.Holder.HolderDetail = append(req.Holder.HolderDetail, &domain.HolderDetail{
				Email:     baseHolder.Email,
				GivenName: baseHolder.GivenName,
				Surname:   baseHolder.Surname,
				// SpecialRequest: baseHolder.SpecialRequest, // https://www.notion.so/deeptechjsc/Hub-M-field-special_request-a7fe494ef3cf4d3db68ca6ba02085dd3
			})
		}

		for i, holder := range req.Holder.HolderDetail {
			holder.OccupancyIndex = uint(i + 1)
		}
	}

	orderPaymentID, err := h.orderClient.CreateHotelOrder(ctx, orderPayment)
	if err != nil {
		log.Error("h.orderClient.CreateHotelOrder error", log.Any("error", err), log.String("sessionID", req.SessionID), log.Any("hotelOrder", hotelOrder))
		return nil, commonError.ErrSomethingOccurred
	}

	pmInfo, err := h.paymentClient.GetPaymentMethod(ctx, defaultPM)
	if err != nil {
		log.Error("paymentClient.GetPaymentMethod error", log.Any("error", err))
	}

	shopInfo, err := h.interClient.GetOfficeInfo(ctx, req.HubOfficeID, "")
	if err != nil {
		log.Error("GetOfficeInfo error", log.Any("error", err), log.String("officeID", req.HubOfficeID))
		return nil, err
	}

	hotelOrder.OrderPaymentID = orderPaymentID

	if hotelOrder.OrderCode == "" {
		hotelOrder.OrderCode, err = h.bookingService.GenOrderCode(ctx)
		if err != nil {
			log.Error("h.bookingService.GenOrderCode error", log.Any("error", err),
				log.String("officeID", req.HubOfficeID), log.String("SessionID", req.SessionID))
			return nil, commonError.ErrSomethingOccurred
		}
	}

	//Pay order
	transInfo, err := h.orderClient.PlaceHotelOrder(ctx, hotelOrder, shopInfo)
	if err != nil {
		log.Error("PlaceHotelOrder error", log.Any("error", err), log.String("orderPaymentID", orderPaymentID), log.String("userID", req.AgentID))

		return nil, err
	}
	if transInfo == nil {
		log.Error("PlaceHotelOrder transInfo  nil")
		return nil, commonError.ErrSomethingOccurred
	}

	hotelOrder.LastTransactionID = transInfo.ID

	// New ctx for booking
	issueCtx, cc := context.WithTimeout(context.Background(), issueTimeout)
	defer cc()

	if err := h.sessionRepo.WithTransaction(issueCtx, func(txCtx context.Context) (interface{}, error) {
		// Close session
		err = h.sessionRepo.UpdateExpireTime(ctx, req.SessionID, 0)
		if err != nil {
			log.Error("sessionRepo.UpdateExpireTime error", log.Any("error", err), log.String("sessionID", req.SessionID))
			return nil, commonError.ErrSomethingOccurred
		}

		req.OrderCode = hotelOrder.OrderCode

		// set pending before confirm booking
		hotelOrder.BookingStatus = enum.BookingStatusPending
		hotelOrder.PendingDeadline = time.Now().Add(constants.TicketPendingDeadlineTime).UnixMilli()

		err = h.hubOrderRepo.UpdateOne(ctx, hotelOrder.ID, hotelOrder)
		if err != nil {
			log.Error("h.hotelOrder.UpdateOne error", log.Any("error", err), log.String("orderID", hotelOrder.ID), log.Any("req", req))
			return nil, commonError.ErrSomethingOccurred
		}

		return nil, nil
	}); err != nil {
		return nil, err
	}

	go func() {
		bCtx, cc := context.WithTimeout(context.Background(), webhookTimeout)
		defer cc()

		h.sendWebhook(bCtx, hotelOrder.OrderCode, hotelOrder.SessionID, req.HubOfficeID, &domain.TransactionInfo{
			ID:        transInfo.ID,
			Type:      transInfo.Type,
			Amount:    transInfo.Amount,
			CreatedAt: transInfo.CreatedAt,
		}, pmInfo, webhookCfg)
	}()

	// return nil, domain.ErrBookingFailed

	reservationCode, confirmationIDs, bookingStatus, providerBookingStatus, err := h.bookingService.Book(issueCtx, hotelOrder.Provider, req, session, hotelOrder)
	if err != nil {
		log.Error("bookingService.Book error", log.Any("error", err), log.String("SessionID", req.SessionID), log.String("officeID", req.HubOfficeID))

		if bookingStatus == enum.BookingStatusFailed || bookingStatus == enum.BookingStatusNone {
			rollbackCtx, cc := context.WithTimeout(context.Background(), constants.DefaultCtxTimeout)
			defer cc()
			// Cancel booking due to order is unusable
			err := h.bookingService.MarkBookingFail(rollbackCtx, hotelOrder)
			if err != nil {
				log.Error("bookingService.MarkBookingFail error", log.Any("error", err), log.String("SessionID", req.SessionID), log.String("officeID", req.HubOfficeID))
				return nil, commonError.ErrSomethingOccurred
			}

			err = h.bookingService.Refund(ctx, hotelOrder)
			if err != nil {
				log.Error("bookingService.Refund error", log.Any("error", err), log.String("SessionID", req.SessionID), log.String("officeID", req.HubOfficeID))
				return nil, commonError.ErrSomethingOccurred
			}
			return nil, domain.ErrBookingFailed
		}
	}

	hotelOrder.ProviderBookingStatus = providerBookingStatus
	hotelOrder.BookingStatus = bookingStatus
	hotelOrder.HubOrderStatus = enum.HubOrderStatusConfirmed
	hotelOrder.ReservationCode = reservationCode

	if bookingStatus == enum.BookingStatusSuccess {
		converts.MapRoomConfirmationID(hotelOrder.Provider, hotelOrder.Hotel.ListRooms, confirmationIDs)
	}

	// Renew PendingDeadline
	if bookingStatus == enum.BookingStatusPending && hotelOrder.Provider != enum.HotelProviderExpediaManual {
		hotelOrder.PendingDeadline = time.Now().Add(constants.TicketPendingDeadlineTime).UnixMilli()
		hotelOrder.PendingStartAt = time.Now().UnixMilli()

		switch hotelOrder.Provider {
		case enum.HotelProviderExpedia:
			hotelOrder.PendingStartAt = time.Now().Add(pendingTimeout).UnixMilli()
		case enum.HotelProviderTA:
			if reservationCode != "" {
				hotelOrder.PendingStartAt = time.Now().Add(pendingTimeout25).UnixMilli()
			}
		}
	} else {
		hotelOrder.PendingDeadline = 0
	}

	err = h.hubOrderRepo.UpdateOne(issueCtx, hotelOrder.ID, hotelOrder)
	if err != nil {
		log.Error("h.hotelOrder.UpdateOne error", log.Any("error", err), log.String("orderID", hotelOrder.ID), log.Any("req", req))
		return nil, commonError.ErrSomethingOccurred
	}

	return &domain.HubBookRes{
		OrderCode:       hotelOrder.OrderCode,
		BookingStatus:   bookingStatus,
		PendingDeadline: hotelOrder.PendingDeadline,
		ErrorRes:        domain.ErrorRes{IsSuccess: true},
	}, nil
}

func (h *bookHotelHandler) sendWebhook(ctx context.Context, orderCode, sessionID, officeID string, trans *domain.TransactionInfo, pm *domain.PaymentMethod, whCfg *domain.WebhookCfg) {
	if whCfg == nil || whCfg.WebhookKey == "" || orderCode == "" || trans == nil {
		return
	}

	// TODO remove when have config
	whCfg.WebhookURLCfg.Transaction = h.cfg.WebhookTransaction

	reqTrans := &domain.Transaction{
		OrderCode:     orderCode,
		SessionID:     sessionID,
		TransactionID: trans.ID,
		Type:          trans.Type,
		Amount:        trans.Amount,
		PaymentMethod: pm,
		OfficeID:      officeID,
		CreatedAt:     trans.CreatedAt,
	}

	if err := h.webhookClient.SendTransaction(ctx, reqTrans, whCfg.WebhookKey, whCfg.WebhookURLCfg.Transaction); err != nil {
		log.Error("webhookClient.SendTransaction error", log.Any("error", err), log.Any("reqTrans", reqTrans))
	}
}

func ValidateTestingHashKey(testingHashKey string) bool {
	if testingHashKey == "" {
		return true
	}

	for _, actions := range constants.TestCaseMap {
		for _, keys := range actions {
			if _, exists := keys[testingHashKey]; exists {
				return true
			}
		}
	}

	return false
}
