package command

import (
	"context"

	"gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

type CheckBookingCancelStatusHandler interface {
	Handle(ctx context.Context, req *domain.HubCancelBookingReq) error
}

type checkBookingCancelStatusHandler struct {
	orderRepo            repositories.OrderRepository
	cancelBookingService service.CancelBookingService
}

func NewCheckBookingCancelStatusHandler(
	cancelBookingService service.CancelBookingService,
	orderRepo repositories.OrderRepository,
) CheckBookingCancelStatusHandler {
	return &checkBookingCancelStatusHandler{
		orderRepo,
		cancelBookingService,
	}
}

func (h *checkBookingCancelStatusHandler) Handle(ctx context.Context, req *domain.HubCancelBookingReq) error {
	if req == nil {
		return errors.ErrInvalidInput
	}

	hubOrder, err := h.orderRepo.FindOneByOrderCode(ctx, req.OfficeID, req.OrderCode)
	if err != nil {
		log.Error("CancelBooking FindOneByOrderCode", log.Any("err", err))
		return err
	}

	err = h.cancelBookingService.CheckCancelStatus(ctx, hubOrder, req.WebhookCfg)
	if err != nil {
		log.Error("h.checkBookingCancelStatusService.CheckBookingCancelStatus error", log.Any("error", err), log.Any("req", req))
		return err
	}

	return nil
}
