package command

import (
	"context"

	"github.com/samber/lo"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/helpers"
)

type SearchHotelListHandler interface {
	Handle(ctx context.Context, req *domain.HubSearchHotelRequest) (*domain.HubSearchHotelResponse, error)
}

type searchHotelListHandler struct {
	cfg           *config.Schema
	searchService service.SearchHotelService
	currencyExSvc service.CurrencyExchangeService
	hiddenFeeSvc  service.HiddenFeeService
}

func NewSearchHotelListHandler(
	cfg *config.Schema,
	searchService service.SearchHotelService,
	currencyExSvc service.CurrencyExchangeService,
	hiddenFeeSvc service.HiddenFeeService,
) SearchHotelListHandler {
	return &searchHotelListHandler{cfg, searchService, currencyExSvc, hiddenFeeSvc}
}

func (h *searchHotelListHandler) Handle(ctx context.Context, req *domain.HubSearchHotelRequest) (*domain.HubSearchHotelResponse, error) {
	if len(req.HotelIds) == 0 {
		return &domain.HubSearchHotelResponse{
			SearchHotelFilterRequest: &domain.SearchHotelFilterResponse{},
			HotelSummary:             []*domain.HotelSummary{},
			Occupancies:              req.Occupancies,
			Pagination:               req.Pagination,
		}, nil
	}

	hotelIdSeen := make(map[string]bool)

	req.HotelIds = lo.Filter(req.HotelIds, func(item string, index int) bool {
		if item == "" || hotelIdSeen[item] {
			return false
		}
		hotelIdSeen[item] = true
		return true
	})

	if err := req.Stay.CountDays(); err != nil {
		log.Error("SearchHotelPrice stay.DayCount err", log.Any("err", err), log.Any("stay.DayCount", req.Stay.CountDays))
		return nil, err
	}

	req.Stay.RoomCount = req.CountRooms()

	enableProvider := []enum.HotelProvider{
		enum.HotelProviderNone,
	}

	enableProvider = append(enableProvider, req.EnableProviders...)

	// Search
	res, loadMore, err := h.searchService.SearchList(ctx, req, enableProvider)
	if err != nil {
		return nil, err
	}

	if res == nil {
		return &domain.HubSearchHotelResponse{
			ErrorRes: domain.ErrorRes{
				IsSuccess: true,
			},
			SearchHotelFilterRequest: &domain.SearchHotelFilterResponse{},
			Pagination:               req.Pagination,
			HotelSummary:             []*domain.HotelSummary{},
			Occupancies:              req.Occupancies,
		}, nil
	}

	totalCounts := req.Pagination.TotalRecord

	filter := h.searchService.CalcFilterRequest(ctx, res)

	filterList := h.searchService.ApplyFilterRequest(ctx, res, req.SearchHotelFilterRequest)

	sortedList := h.searchService.ApplySortRequest(ctx, req, filterList)

	pagedData := h.searchService.ApplyPaging(ctx, sortedList, req.Pagination)

	pagedDataWithHiddenFee := helpers.Copy(pagedData).([]*domain.HotelSummary)

	h.hiddenFeeSvc.CalculateHotelSearchPrices(ctx, &domain.CalculateHotelSearchPricesReq{
		Multiplier:     uint32(req.Stay.RoomCount * req.Stay.DayCount),
		PartnershipID:  req.PartnershipID,
		OfficeID:       req.OfficeID,
		HotelSummaries: pagedDataWithHiddenFee,
	})
	// re-gen key no need to check error
	searchKey, _ := req.GenSearchKey()
	// Skip convert currency with fake provider

	result := &domain.HubSearchHotelResponse{
		ErrorRes: domain.ErrorRes{
			IsSuccess: true,
		},
		SearchHotelFilterRequest: filter,
		Pagination:               req.Pagination,
		HotelSummary:             pagedDataWithHiddenFee,
		Occupancies:              req.Occupancies,
	}

	// if h.cfg.Env != commonConstants.EnvProduction {
	result.SearchKey = searchKey
	// }

	if loadMore && result.Pagination.PageCurrent == result.Pagination.TotalPage {
		if totalCounts == 0 || totalCounts <= req.Pagination.PageLimit {
			totalCounts = 250
		}

		result.Pagination.TotalPage = totalCounts / req.Pagination.PageLimit
		result.Pagination.TotalRecord = totalCounts
	}

	return result, nil
}
