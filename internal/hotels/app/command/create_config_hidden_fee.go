package command

import (
	"context"

	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

type CreateConfigHiddenFeeHandler interface {
	Handle(ctx context.Context, req *domain.UpsertConfigHiddenFeeRequest) (string, error)
}

type createConfigHiddenFeeHandler struct {
	hiddenFeeRepo repositories.HiddenServiceFeeRepository
}

func NewCreateConfigHiddenFeeHandler(
	hiddenFeeRepo repositories.HiddenServiceFeeRepository,
) CreateConfigHiddenFeeHandler {
	return &createConfigHiddenFeeHandler{
		hiddenFeeRepo: hiddenFeeRepo,
	}
}

func (h *createConfigHiddenFeeHandler) Handle(ctx context.Context, req *domain.UpsertConfigHiddenFeeRequest) (string, error) {
	configHiddenFee := &domain.HiddenServiceFee{
		OfficeID: req.OfficeID,
		Type:     req.Type,
		Config: &domain.HiddenServiceFeeConfig{
			LocationType: req.Location,
			CountryCode:  req.Countries,
			Rating:       req.Ratings,
			HotelType:    req.HotelType,
		},
		Provider:  req.Provider,
		HotelID:   req.HotelID,
		HotelName: req.HotelName,
		Amount:    req.Amount,
		Percent:   req.Percent,
	}

	configHiddenRecord, err := h.hiddenFeeRepo.FindOne(ctx, configHiddenFee)
	if err != nil {
		log.Error("hotelOrderRepo.FindOneByOrderCode error", log.Any("error", err))
		return "", err
	}

	if configHiddenRecord != nil {
		return "", domain.ErrDuplicateHiddenServiceFee
	}

	configID, err := h.hiddenFeeRepo.Create(ctx, configHiddenFee)
	if err != nil {
		log.Error("hotelOrderRepo.FindOneByOrderCode error", log.Any("error", err))
		return "", err
	}

	return configID, nil
}
