package command

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/expedia_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

type AggregateRegionsContentHandler interface {
	Handle(ctx context.Context, req *AggregateRegionsContentReq) error
}

type aggregateRegionsContentHandler struct {
	regionRepo    repositories.RegionRepository
	polygonRepo   repositories.PolygonRepository
	expediaClient expedia_client.ExpediaAdapter
}

type AggregateRegionsContentReq struct {
	Language    string
	Include     string
	CountryCode string
	Type        []string
}

func NewAggregateRegionsContentHandler(
	regionRepo repositories.RegionRepository,
	polygonRepo repositories.PolygonRepository,
	expediaClient expedia_client.ExpediaAdapter,
) AggregateRegionsContentHandler {
	return &aggregateRegionsContentHandler{regionRepo, polygonRepo, expediaClient}
}

func (h *aggregateRegionsContentHandler) Handle(ctx context.Context, req *AggregateRegionsContentReq) error {
	regions, err := h.expediaClient.GetRegions(ctx, &domain.RegionsRequest{
		Include:     []string{req.Include, "property_ids"},
		Language:    req.Language,
		CountryCode: []string{req.CountryCode},
		Type:        req.Type,
	}, uuid.New().String())
	if err != nil {
		log.Error("GetRegions error", log.Any("err", err))
		return err
	}

	for _, region := range regions {
		fmt.Println("receive region", region.Name, region.FullName, region.CountryCode)
		// if err := h.regionRepo.WithTransaction(ctx, func(tnxSession context.Context) (interface{}, error) {
		// if region.Coordinates != nil && region.Coordinates.BoundingPolygon != nil && region.Coordinates.BoundingPolygon.Coordinates != nil {
		// 	polygonID, err := h.polygonRepo.Create(ctx, region.Coordinates.BoundingPolygon)
		// 	if err != nil {
		// 		log.Error("h.polygonRepo.Create error", log.Any("err", err))
		// 		return nil, err
		// 	}
		// 	region.Coordinates.BoundingPolygonIDRef = polygonID
		// }

		err = h.regionRepo.Upsert(ctx, region, req.Language)
		if err != nil {
			log.Error("Upsert error", log.Any("err", err))
			return err
		}
		// return nil, nil
		// }); err != nil {
		// 	log.Error("WithTransaction error", log.Any("err", err))
		// 	return err
		// }

		fmt.Println("done region", region.Name, region.FullName, region.CountryCode)
	}

	return nil
}
