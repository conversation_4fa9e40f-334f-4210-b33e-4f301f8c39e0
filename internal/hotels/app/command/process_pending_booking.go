package command

import (
	"context"

	"github.com/gammazero/workerpool"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
)

type ProcessPendingBookingHandler interface {
	Handle(ctx context.Context, req *ProcessPendingBookingRequest) error
}

type processPendingBookingHandler struct {
	orderRepo      repositories.OrderRepository
	sessionRepo    repositories.SessionRepository
	bookingService service.BookingService
}

type ProcessPendingBookingRequest struct {
	Bookings []*domain.HubHotelOrder
}

func NewProcessPendingBookingHandler(
	orderRepo repositories.OrderRepository,
	sessionRepo repositories.SessionRepository,
	bookingService service.BookingService,
) ProcessPendingBookingHandler {
	return &processPendingBookingHandler{orderRepo, sessionRepo, bookingService}
}

func (h *processPendingBookingHandler) Handle(ctx context.Context, req *ProcessPendingBookingRequest) error {
	wp := workerpool.New(2)

	for _, booking := range req.Bookings {
		booking := booking

		wp.Submit(func() {
			bgCtx, cc := context.WithTimeout(ctx, constants.ThirdPartyRequestTimeout)
			defer cc()

			session, err := h.sessionRepo.FindBySessionIDWithoutExpire(ctx, booking.OfficeID, booking.SessionID)
			if err != nil {
				log.Error("sessionRepo.FindBySessionID error", log.Any("error", err),
					log.String("officeID", booking.OfficeID), log.String("SessionID", booking.SessionID))
				return
			}

			err = h.bookingService.ResolvePendingBook(bgCtx, booking, session)
			if err != nil {
				log.Error("ResolvePendingBooking error", log.Any("error", err), log.Any("booking", booking))
			}
		})
	}

	wp.StopWait()

	return nil
}
