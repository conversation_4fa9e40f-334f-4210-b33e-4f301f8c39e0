package command

import (
	"context"

	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

type CancelBookingHandler interface {
	Handle(ctx context.Context, req *domain.HubCancelBookingReq) error
}

type cancelBookingHandler struct {
	cancelBookingService service.CancelBookingService
}

func NewCancelBookingHandler(
	cancelBookingService service.CancelBookingService,
) CancelBookingHandler {
	return &cancelBookingHandler{
		cancelBookingService,
	}
}

func (h *cancelBookingHandler) Handle(ctx context.Context, req *domain.HubCancelBookingReq) error {
	err := h.cancelBookingService.CancelBooking(ctx, req)
	if err != nil {
		log.Error("h.cancelBookingService.CancelBooking error", log.Any("error", err), log.Any("req", req))
		return err
	}

	return nil
}
