package command

import (
	"context"

	"gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
)

type DeleteConfigHidden<PERSON><PERSON><PERSON>and<PERSON> interface {
	Handle(ctx context.Context, id []string, userID string) error
}

type deleteConfigHiddenFeeHandler struct {
	hiddenFeeRepo repositories.HiddenServiceFeeRepository
}

func NewDeleteConfigHiddenFeeHandler(hiddenFeeRepo repositories.HiddenServiceFeeRepository) DeleteConfigHiddenFeeHandler {
	return &deleteConfigHiddenFeeHandler{hiddenFeeRepo}
}

func (h *deleteConfigHiddenFeeHandler) Handle(ctx context.Context, id []string, userID string) error {
	err := h.hiddenFeeRepo.Deletes(ctx, id, userID)
	if err != nil {
		log.Error("ConfigHiddenFeeRepo.FindByID error", log.Any("error", err), log.Any("req", id))
		return errors.ErrSomethingOccurred
	}

	return nil
}
