package converts

// import (
// 	"fmt"
// 	"strconv"
// 	"strings"
// 	"time"

// 	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/tourmind_client/client/entities"
// 	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
// 	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
// 	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
// )

// const DefaultExpireTime = 25 * time.Minute

// func getTrueRoom(inputs []*domain.Room, bedDesc string) *domain.Room {
// 	for _, input := range inputs {
// 		for _, bed := range input.BedGroups {
// 			if standarizeString(bed.Description) == standarizeString(bedDesc) {
// 				return input
// 			}
// 		}
// 	}

// 	return nil
// }

// func getFilterRatesKey(rate *entities.RateInfo) string {
// 	return fmt.Sprintf("%s-%s-%s-%t", rate.Name, rate.BedTypeDesc, rate.MealInfo.MealType, rate.Refundable)
// }

// func filterRates(rates []entities.RateInfo) []entities.RateInfo {
// 	out := []entities.RateInfo{}

// 	listRateMap := map[string]entities.RateInfo{}

// 	for _, rate := range rates {
// 		if rate.BedTypeDesc == "" {
// 			continue
// 		}

// 		key := getFilterRatesKey(&rate)

// 		if listRateMap[key].RateCode == "" {
// 			listRateMap[key] = rate
// 			continue
// 		}

// 		if rate.TotalPrice < listRateMap[key].TotalPrice {
// 			listRateMap[key] = rate
// 		}
// 	}

// 	for _, value := range listRateMap {
// 		out = append(out, value)
// 	}

// 	return out
// }

// func ToDomainHubRateData(rate *entities.RateInfo) *domain.HubRateData {
// 	outputRate := &domain.HubRateData{
// 		RateID:          rate.RateCode,
// 		Available:       strconv.Itoa(rate.Allotment),
// 		CancelPolicies:  toDomainHubCancelPolicies(rate.CancelPolicyInfos),
// 		Currency:        rate.CurrencyCode,
// 		TotalRateAmount: rate.TotalPrice,
// 		TotalRateBasic:  rate.TotalPrice,
// 		TotalTaxAmount:  0, // undefined
// 		PayNow:          rate.TotalPrice,
// 		TotalPayAtHotel: 0, // undefined
// 		BedType:         []string{rate.BedTypeDesc},
// 		PriceCheckToken: rate.RateCode,
// 		Refundable:      rate.Refundable,
// 		OccupancyRate: []*domain.HubOccupancyRate{
// 			{
// 				OccupancyType:    "",
// 				TotalNightlyRate: toTotalNightlyRate(rate.DailyPriceInfo, rate.CurrencyCode),
// 			},
// 		},
// 	}

// 	if rate.MealInfo.MealType == "2" {
// 		outputRate.Amenities = append(outputRate.Amenities, &domain.HubAmenity{
// 			Name: "Free breakfast",
// 		})
// 	}

// 	return outputRate
// }

// func ToHotelSearchResult(reqData *domain.SearchHotelRequestData, resData *entities.HotelDetailRes, searchKey string) (*domain.HotelSearchResult, error) {
// 	hotelMap := getHotelMap(reqData.Hotels)
// 	outputHotels := []*domain.HubHotel{}

// 	currency := ""

// 	resHotels := resData.Hotels
// 	for _, resHotel := range resHotels {
// 		reqHotel := hotelMap[resHotel.HotelCode]
// 		outputHotel := &domain.HubHotel{
// 			HotelID:         reqHotel.HotelID,
// 			ProviderHotelID: resHotel.HotelCode,
// 			Name:            reqHotel.Name,
// 			VAT:             true,
// 			ListRooms:       []*domain.HubRoom{},
// 			Currency:        "", // fill later
// 		}

// 		reqRooms := reqHotel.Rooms
// 		for _, resRoom := range resHotel.RoomTypes {
// 			roomMap := getRoomsMap(reqRooms)
// 			matchedRooms := roomMap[getRoomKeyUnsafe(resRoom.Name)]

// 			if len(matchedRooms) == 0 || resRoom.BedTypeDesc == "" {
// 				continue
// 			}

// 			var room *domain.Room
// 			if len(matchedRooms) != 1 {
// 				room = getTrueRoom(matchedRooms, resRoom.BedTypeDesc)
// 			} else {
// 				room = matchedRooms[0]
// 			}

// 			if room == nil {
// 				continue
// 			}

// 			outputRoom := &domain.HubRoom{
// 				RoomID:         room.RoomID,
// 				Name:           resRoom.Name,
// 				Provider:       enum.HotelProviderTourMind,
// 				RateData:       []*domain.HubRateData{},
// 				ProviderRoomID: resRoom.RoomTypeCode,
// 			}

// 			resRates := filterRates(resRoom.RateInfos)

// 			for _, rate := range resRates {
// 				outputRate := ToDomainHubRateData(&rate)

// 				if currency == "" && rate.CurrencyCode != "" {
// 					currency = rate.CurrencyCode
// 				}

// 				outputRoom.RateData = append(outputRoom.RateData, outputRate)
// 			}

// 			outputHotel.ListRooms = append(outputHotel.ListRooms, outputRoom)
// 		}

// 		outputHotel.Currency = currency
// 		outputHotels = append(outputHotels, outputHotel)
// 	}

// 	return &domain.HotelSearchResult{
// 		SearchKey:    searchKey,
// 		CreatedAt:    time.Now().UnixMilli(),
// 		Provider:     enum.HotelProviderTourMind,
// 		Hotels:       outputHotels,
// 		SaleScenario: string(reqData.SearchReq.SaleChannel),
// 		ExpireAt:     time.Now().Add(DefaultExpireTime).UnixMilli(),
// 	}, nil
// }

// func parseDateToHubDate(input string) string {
// 	t, _ := time.Parse("2006-01-02", input)
// 	return t.Format(constants.HubDateFormat)
// }

// func toDomainHubCancelPolicy(input entities.CancelPolicyInfo) *domain.HubCancelPolicy {
// 	night := ""
// 	if input.NightCount != 0 {
// 		night = strconv.Itoa(input.NightCount)
// 	}

// 	return &domain.HubCancelPolicy{
// 		Amount:         &input.Amount,
// 		StartDate:      parseDateToHubDate(input.StartDateTime),
// 		EndDate:        parseDateToHubDate(input.EndDateTime),
// 		Currency:       input.CurrencyCode,
// 		NumberOfNights: night,
// 	}
// }

// func toTotalNightlyRate(inputs []entities.DailyPriceInfo, currency string) []*domain.HubRate {
// 	out := []*domain.HubRate{}

// 	for _, input := range inputs {
// 		out = append(out, &domain.HubRate{
// 			RateAmount: input.Price,
// 			RateBasic:  input.Price,
// 			TaxAmount:  0,
// 			Currency:   currency,
// 		})
// 	}

// 	return out
// }

// func toDomainHubCancelPolicies(inputs []entities.CancelPolicyInfo) []*domain.HubCancelPolicy {
// 	out := []*domain.HubCancelPolicy{}

// 	for _, input := range inputs {
// 		out = append(out, toDomainHubCancelPolicy(input))
// 	}

// 	return out
// }

// func standarizeString(input string) string {
// 	return strings.TrimSpace(strings.ToLower(input))
// }

// func getHotelMap(input []*domain.Hotel) map[string]*domain.Hotel {
// 	out := map[string]*domain.Hotel{}

// 	for _, hotel := range input {
// 		out[hotel.ProviderIds[enum.HotelProviderTourMind]] = hotel
// 	}

// 	return out
// }

// func getRoomsMap(input []*domain.Room) map[string][]*domain.Room {
// 	out := map[string][]*domain.Room{}

// 	for _, room := range input {
// 		key := getRoomKeyUnsafe(room.Name)
// 		out[key] = append(out[key], room)
// 	}

// 	return out
// }

// func getRoomKeyUnsafe(name string) string {
// 	return standarizeString(name)
// }
