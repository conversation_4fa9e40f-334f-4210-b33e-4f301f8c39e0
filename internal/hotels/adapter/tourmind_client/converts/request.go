package converts

// import (
// 	"strconv"

// 	"github.com/pkg/errors"
// 	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/tourmind_client/client/entities"
// 	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
// )

// func ToCheckRoomRateReq(input *domain.CacheCheckAvailabilityRequest, hubHotel *domain.HubHotel, rate *domain.HubRateData) (*entities.CheckRoomRateReq, error) {
// 	stay := input.Stay

// 	hotelCode, err := strconv.Atoi(hubHotel.ProviderHotelID)
// 	if err != nil {
// 		return nil, errors.Wrap(err, "convert string to int")
// 	}

// 	return &entities.CheckRoomRateReq{
// 		CheckIn:     stay.CheckIn,
// 		CheckOut:    stay.CheckOut,
// 		HotelCodes:  []int{hotelCode},
// 		Nationality: input.CountryCode,
// 		PaxRooms:    ToPaxRoomRequest(input.Occupancies),
// 		RateCode:    rate.PriceCheckToken,
// 	}, nil
// }

// func ToHotelDetailReq(input *domain.SearchHotelRequestData) (*entities.HotelDetailReq, error) {
// 	searchReq := input.SearchReq
// 	stay := searchReq.Stay

// 	hotelCodes := make([]int, len(input.ProviderHotelIds))
// 	for i, hotelCode := range input.ProviderHotelIds {
// 		code, err := strconv.Atoi(hotelCode)
// 		if err != nil {
// 			return nil, errors.New("invalid hotel code " + hotelCode)
// 		}
// 		hotelCodes[i] = code
// 	}

// 	return &entities.HotelDetailReq{
// 		CheckIn:      stay.CheckIn,
// 		CheckOut:     stay.CheckOut,
// 		HotelCodes:   hotelCodes,
// 		IsDailyPrice: true,
// 		Nationality:  searchReq.CountryCode,
// 		PaxRooms:     ToPaxRoomRequest(searchReq.Occupancies),
// 	}, nil
// }

// func ToPaxRoomRequest(ins []*domain.HubSearchOccupancy) []entities.PaxRoomRequest {
// 	out := []entities.PaxRoomRequest{}

// 	for _, occu := range ins {
// 		temp := entities.PaxRoomRequest{
// 			Adults:    int(occu.Adults),
// 			RoomCount: int(occu.Rooms),
// 		}

// 		if occu.Children != nil {
// 			temp.Children = int(occu.Children.Number)
// 			temp.ChildrenAges = occu.Children.Age
// 		}

// 		out = append(out, temp)
// 	}

// 	return out
// }

// func ToPaxRoomOrderData(ins []*domain.HubSearchOccupancy, pax *domain.HubHolderInfo) []entities.PaxRoomOrderData {
// 	out := []entities.PaxRoomOrderData{}

// 	for _, occu := range ins {
// 		if occu == nil {
// 			continue
// 		}

// 		temp := entities.PaxRoomOrderData{
// 			Adults:    int(occu.Adults),
// 			RoomCount: int(occu.Rooms),
// 			PaxNames: []entities.PaxName{{
// 				// FirstName: pax.GivenName,
// 				// LastName:  pax.Surname,
// 				Type:      "ADU",
// 			}},
// 		}

// 		if occu.Children != nil {
// 			temp.Children = int(occu.Children.Number)
// 		}

// 		out = append(out, temp)
// 	}

// 	return out
// }

// func ToCreateOrderRequest(bookReq *domain.HubBookReq, session *domain.TourmindSessionInfo, order *domain.HubHotelOrder, tracingID string) *entities.CreateOrderReq {
// 	searchReq := order.HotelSearchRequest
// 	holder := bookReq.Holder

// 	originCfRate := order.OriginalRateDataCf

// 	return &entities.CreateOrderReq{
// 		AgentRefID: order.OrderCode,
// 		CheckIn:    searchReq.Stay.CheckIn,
// 		CheckOut:   searchReq.Stay.CheckOut,
// 		ContactInfo: entities.ContactInfo{
// 			// Email:     holder.Email,
// 			// FirstName: holder.GivenName,
// 			// LastName:  holder.Surname,
// 			PhoneNo:   holder.PhoneCode + holder.PhoneNumber,
// 		},
// 		CurrencyCode:   originCfRate.Currency,
// 		HotelCode:      session.HotelCode,
// 		PaxRooms:       ToPaxRoomOrderData(searchReq.Occupancies, bookReq.Holder),
// 		RateCode:       session.RateCode,
// 		SpecialRequest: "",
// 		TotalPrice:     originCfRate.TotalRateAmount,
// 	}
// }
