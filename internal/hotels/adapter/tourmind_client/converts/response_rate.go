package converts

// import (
// 	"github.com/samber/lo"
// 	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/tourmind_client/client/entities"
// 	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
// )

// func GetItemsFromHotels(hotels []entities.HotelDetail, hotelID, rateCode string, roomIds []string) (*entities.HotelDetail, *entities.RoomType, *entities.RateInfo, error) {

// 	var resHotel *entities.HotelDetail

// 	for _, resHotelItem := range hotels {
// 		if resHotelItem.HotelCode == hotelID {
// 			resHotel = &resHotelItem
// 			break
// 		}
// 	}

// 	if resHotel == nil {
// 		return nil, nil, nil, domain.ErrRoomSoldOut
// 	}

// 	var resRate *entities.RateInfo
// 	var resRoom *entities.RoomType

// 	for _, resRoomItem := range resHotel.RoomTypes {
// 		if lo.Contains(roomIds, resRoomItem.RoomTypeCode) {
// 			for _, resRateItem := range resRoomItem.RateInfos {
// 				if resRateItem.RateCode == rateCode {
// 					resRate = &resRateItem
// 					resRoom = &resRoomItem
// 					break
// 				}
// 			}
// 		}

// 		if resRate != nil {
// 			break
// 		}
// 	}

// 	if resRate == nil {
// 		return nil, nil, nil, domain.ErrRoomSoldOut
// 	}

// 	return resHotel, resRoom, resRate, nil
// }
