package entities

type CreateOrderReq struct {
	AgentRefID     string             `json:"AgentRefID"`
	CheckIn        string             `json:"CheckIn"`
	CheckOut       string             `json:"CheckOut"`
	ContactInfo    ContactInfo        `json:"ContactInfo"`
	CurrencyCode   string             `json:"CurrencyCode"`
	HotelCode      int                `json:"HotelCode"`
	PaxRooms       []PaxRoomOrderData `json:"PaxRooms"`
	RateCode       string             `json:"RateCode"`
	RequestHeader  RequestHeader      `json:"RequestHeader"`
	SpecialRequest string             `json:"SpecialRequest"`
	TotalPrice     float64            `json:"TotalPrice"`
}

type CreateOrderRes struct {
	ResponseHeader ResponseHeader `json:"ResponseHeader"`
	ReservationID  string         `json:"ReservationID"`
	OrderInfo      OrderInfo      `json:"OrderInfo"`
	Error          ErrorDetails   `json:"Error"`
}

type SearchOrderReq struct {
	RequestHeader `json:"RequestHeader"`
	AgentRefID    string `json:"AgentRefID"`
}

type SearchOrderRes struct {
	ResponseHeader ResponseHeader `json:"ResponseHeader"`
	ReservationID  string         `json:"ReservationID"`
	OrderInfo      OrderInfo      `json:"OrderInfo"`
	Error          ErrorDetails   `json:"Error"`
}

type CancelOrderReq struct {
	RequestHeader RequestHeader `json:"RequestHeader"`
	AgentRefID    string        `json:"AgentRefID"`
}

type CancelOrderRes struct {
	ResponseHeader ResponseHeader `json:"ResponseHeader"`
	CancelResult   CancelResult   `json:"CancelResult"`
	Error          ErrorDetails   `json:"Error"`
}

//

type CancelResult struct {
	CancelFee    float64 `json:"CancelFee"`
	CurrencyCode string  `json:"CurrencyCode"`
	OrderStatus  string  `json:"OrderStatus"`
}

type PaxRoomOrderData struct {
	Adults    int       `json:"Adults"`
	Children  int       `json:"Children"`
	PaxNames  []PaxName `json:"PaxNames"`
	RoomCount int       `json:"RoomCount"`
}

type OrderInfo struct {
	ReservationID       string  `json:"ReservationID"`
	AgentRefID          string  `json:"AgentRefID"`
	HotelConfirmationNo string  `json:"HotelConfirmationNo"`
	OrderStatus         string  `json:"OrderStatus"`
	TotalPrice          float64 `json:"TotalPrice"`
	CurrencyCode        string  `json:"CurrencyCode"`
}
