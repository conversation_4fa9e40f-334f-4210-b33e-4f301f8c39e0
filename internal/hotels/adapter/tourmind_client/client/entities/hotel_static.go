package entities

import (
	"strconv"
	"strings"
)

type HotelStaicListReq struct {
	CountryCode   string        `json:"CountryCode"`
	Pagination    Pagination    `json:"Pagination"`
	RequestHeader RequestHeader `json:"RequestHeader"`
}

type HotelStaicListRes struct {
	ResponseHeader        ResponseHeader        `json:"ResponseHeader"`
	HotelStaticListResult HotelStaticListResult `json:"HotelStaticListResult"`
	Error                 ErrorDetails          `json:"Error"`
}

//

type Pagination struct {
	PageIndex int `json:"PageIndex"`
	PageSize  int `json:"PageSize"`
}

type HotelStaticListResult struct {
	Hotels     []Hotel       `json:"Hotels"`
	Pagination PaginationRes `json:"Pagination"`
}

type Hotel struct {
	HotelID     string `json:"HotelId"`
	Name        string `json:"Name"`
	NameCN      string `json:"Name_CN"`
	StarRating  string `json:"StarRating"`
	Address     string `json:"Address"`
	AddressCN   string `json:"Address_CN"`
	CountryCode string `json:"CountryCode"`
	CityCode    string `json:"CityCode"`
	CityName    string `json:"CityName"`
	Latitude    string `json:"Latitude"`
	Longitude   string `json:"Longitude"`
	Phone       string `json:"Phone"`
}

func (h *Hotel) GetLong() float64 {
	if h == nil {
		return 0
	}

	lng, err := strconv.ParseFloat(h.Longitude, 64)
	if err != nil {
		return 0
	}

	return lng
}

func (h *Hotel) GetLat() float64 {
	if h == nil {
		return 0
	}

	lat, err := strconv.ParseFloat(h.Latitude, 64)
	if err != nil {
		return 0
	}

	return lat
}

func (h *Hotel) EscapedCityName() string {
	if h == nil {
		return ""
	}

	return strings.TrimSpace(strings.Split(h.CityName, " (")[0])
}

type PaginationRes struct {
	PageCount  int `json:"PageCount"`
	TotalCount int `json:"TotalCount"`
}
