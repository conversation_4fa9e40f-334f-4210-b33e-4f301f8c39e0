package entities

type HotelDetailReq struct {
	CheckIn       string           `json:"CheckIn"`
	CheckOut      string           `json:"CheckOut"`
	HotelCodes    []int            `json:"HotelCodes"`
	IsDailyPrice  bool             `json:"IsDailyPrice"`
	Nationality   string           `json:"Nationality"`
	PaxRooms      []PaxRoomRequest `json:"PaxRooms"`
	RequestHeader RequestHeader    `json:"RequestHeader"`
}

type HotelDetailRes struct {
	ResponseHeader ResponseHeader `json:"ResponseHeader"`
	Hotels         []HotelDetail  `json:"Hotels"`
	Error          ErrorDetails   `json:"Error"`
}

type CheckRoomRateReq struct {
	CheckIn       string           `json:"CheckIn"`
	CheckOut      string           `json:"CheckOut"`
	HotelCodes    []int            `json:"HotelCodes"`
	Nationality   string           `json:"Nationality"`
	PaxRooms      []PaxRoomRequest `json:"PaxRooms"`
	RateCode      string           `json:"RateCode"`
	RequestHeader RequestHeader    `json:"RequestHeader"`
}

type CheckRoomRateRes struct {
	ResponseHeader ResponseHeader `json:"ResponseHeader"`
	Hotels         []HotelDetail  `json:"Hotels"`
	Error          ErrorDetails   `json:"Error"`
}

type PaxRoomRequest struct {
	Adults       int    `json:"Adults"`
	Children     int    `json:"Children"`
	ChildrenAges []uint `json:"ChildrenAges"`
	RoomCount    int    `json:"RoomCount"`
}

type HotelDetail struct {
	HotelCode string     `json:"HotelCode"`
	CheckIn   string     `json:"CheckIn"`
	CheckOut  string     `json:"CheckOut"`
	RoomTypes []RoomType `json:"RoomTypes"`
}

type RoomType struct {
	RoomTypeCode  string     `json:"RoomTypeCode"`
	Name          string     `json:"Name"`
	NameCN        string     `json:"NameCN"`
	BedTypeDesc   string     `json:"BedTypeDesc"`
	BedTypeDescCN string     `json:"BedTypeDescCN"`
	RateInfos     []RateInfo `json:"RateInfos"`
}

type MealInfo struct {
	MealType  string `json:"MealType"`
	MealCount int    `json:"MealCount"`
}
