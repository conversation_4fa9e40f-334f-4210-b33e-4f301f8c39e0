package entities

type RequestHeader struct {
	AgentCode     string `json:"AgentCode"`
	Password      string `json:"Password"`
	RequestTime   string `json:"RequestTime"`
	TransactionID string `json:"TransactionID"`
	UserName      string `json:"UserName"`
}

type ResponseHeader struct {
	ResponseTime  string `json:"ResponseTime"`
	TransactionID string `json:"TransactionID"`
}

type ErrorDetails struct {
	ErrorCode    string `json:"ErrorCode"`
	ErrorMessage string `json:"ErrorMessage"`
}
