package entities

type RateInfo struct {
	BedTypeDesc       string             `json:"bedTypeDesc"`
	BedTypeDescCN     string             `json:"bedTypeDescCN"`
	RateCode          string             `json:"RateCode"`
	Name              string             `json:"Name"`
	NameCN            string             `json:"NameCN"`
	Allotment         int                `json:"Allotment"`
	CurrencyCode      string             `json:"CurrencyCode"`
	TotalPrice        float64            `json:"TotalPrice"`
	MealInfo          MealInfo           `json:"MealInfo"`
	Refundable        bool               `json:"Refundable"`
	CancelPolicyInfos []CancelPolicyInfo `json:"CancelPolicyInfos"`
	DailyPriceInfo    []DailyPriceInfo   `json:"DailyPriceInfo"`
}

type DailyPriceInfo struct {
	Date  string  `json:"Date"`
	Price float64 `json:"Price"`
	Count int     `json:"Count"`
}

type CancelPolicyInfo struct {
	StartDateTime string  `json:"StartDateTime"`
	EndDateTime   string  `json:"EndDateTime"`
	Amount        float64 `json:"Amount"`
	CurrencyCode  string  `json:"CurrencyCode"`
	NightCount    int     `json:"NightCount"`
}
