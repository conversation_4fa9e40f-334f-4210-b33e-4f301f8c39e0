package client

import (
	"context"
	"testing"

	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/tourmind_client/client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
)

func getTestCfg() *config.Schema {
	return &config.Schema{
		TourmindBaseURL:   "http://39.108.114.224:7080",
		TourmindAgentCode: "tms_test",
		TourmindPassword:  "tms_test",
		TourmindUsername:  "tms_test",
	}
}

func TestTourmindClient_HotelDetail(t *testing.T) {
	// Setup
	ctx := context.Background()
	req := &entities.HotelDetailReq{
		CheckIn:      "2024-11-15",
		CheckOut:     "2024-11-18",
		HotelCodes:   []int{21108624},
		IsDailyPrice: true,
		Nationality:  "VN",
		PaxRooms: []entities.PaxRoomRequest{{
			Adults:       1,
			Children:     0,
			ChildrenAges: []uint{},
			RoomCount:    1,
		}},
	}
	tracingID := "test-tracing-id"

	// Create tourmindClient instance
	cfg := getTestCfg()
	tourmindClient := NewTourmindClient(cfg, nil)

	// Test cases
	tests := []struct {
		name      string
		req       *entities.HotelDetailReq
		tracingID string
		wantErr   bool
	}{
		{
			name:      "success",
			req:       req,
			tracingID: tracingID,
			wantErr:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Call HotelDetail function
			res, err := tourmindClient.HotelDetail(ctx, tt.req, tt.tracingID)
			if err != nil {
				t.Errorf("HotelDetail() err %v", err)
				return
			}

			if res.ResponseHeader.TransactionID == "" {
				t.Errorf("HotelDetail() empty")
			}

			log.Info("HotelDetail", log.Any("res", res))
		})
	}
}

func TestTourmindClient_CheckRoomRate(t *testing.T) {
	// Setup
	ctx := context.Background()
	req := &entities.CheckRoomRateReq{
		CheckIn:     "2024-11-15",
		CheckOut:    "2024-11-18",
		HotelCodes:  []int{17764671},
		Nationality: "VN",
		PaxRooms: []entities.PaxRoomRequest{{
			Adults:       1,
			Children:     0,
			ChildrenAges: []uint{},
			RoomCount:    1,
		}},
		RateCode: "401057866099",
	}
	tracingID := "test-tracing-id"

	// Mock dependencies

	// Create tourmindClient instance
	tourmindClient := NewTourmindClient(getTestCfg(), nil)

	// Test cases
	tests := []struct {
		name      string
		req       *entities.CheckRoomRateReq
		tracingID string
		wantErr   bool
	}{
		{
			name:      "success",
			req:       req,
			tracingID: tracingID,
			wantErr:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Call CheckRoomRate function
			res, err := tourmindClient.CheckRoomRate(ctx, tt.req, tt.tracingID)
			if err != nil {
				t.Errorf("CheckRoomRate() err %v", err)
				return
			}

			if res.ResponseHeader.TransactionID == "" {
				t.Errorf("CheckRoomRate() empty")
			}

			log.Info("CheckRoomRate", log.Any("res", res))
		})
	}
}

func TestTourmindClient_CreateOrder(t *testing.T) {
	// Setup
	ctx := context.Background()
	req := &entities.CreateOrderReq{
		AgentRefID: "123123",
		CheckIn:    "2024-11-15",
		CheckOut:   "2024-11-18",
		ContactInfo: entities.ContactInfo{
			Email:     "<EMAIL>",
			FirstName: "THANH",
			LastName:  "TRAN",
			PhoneNo:   "773991212",
		},
		CurrencyCode: "CNY",
		HotelCode:    17764671,
		PaxRooms: []entities.PaxRoomOrderData{{
			Adults:   1,
			Children: 0,
			PaxNames: []entities.PaxName{{
				FirstName: "NGUYEN",
				LastName:  "NGO",
				Type:      "ADU",
			}},
			RoomCount: 1,
		}},
		RateCode:       "401057866099",
		SpecialRequest: "",
		TotalPrice:     4724,
	}

	tracingID := "test-tracing-id"

	tourmindClient := NewTourmindClient(getTestCfg(), nil)

	// Test cases
	tests := []struct {
		name      string
		req       *entities.CreateOrderReq
		tracingID string
		wantErr   bool
		wantRes   *entities.CreateOrderRes
	}{
		{
			name:      "success",
			req:       req,
			tracingID: tracingID,
			wantErr:   false,
			wantRes:   &entities.CreateOrderRes{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Call CreateOrder function
			res, err := tourmindClient.CreateOrder(ctx, tt.req, tt.tracingID)
			if err != nil {
				t.Errorf("CreateOrder() err %v", err)
				return
			}

			if res.ResponseHeader.TransactionID == "" {
				t.Errorf("CreateOrder() empty")
			}

			log.Info("CreateOrder() =", log.Any("res", res))
		})
	}
}

func TestTourmindClient_SearchOrder(t *testing.T) {
	// Setup
	ctx := context.Background()
	req := &entities.SearchOrderReq{
		AgentRefID: "5965833",
	}

	tracingID := "test-tracing-id"

	// Create tourmindClient instance

	tourmindClient := NewTourmindClient(getTestCfg(), nil)

	// Test cases
	tests := []struct {
		name      string
		req       *entities.SearchOrderReq
		tracingID string
		wantErr   bool
	}{
		{
			name:      "success",
			req:       req,
			tracingID: tracingID,
			wantErr:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Call SearchOrder function
			res, err := tourmindClient.SearchOrder(ctx, tt.req, tt.tracingID)
			if err != nil {
				t.Errorf("SearchOrder() err %v", err)
				return
			}

			if res.ResponseHeader.TransactionID == "" {
				t.Errorf("SearchOrder() empty")
			}

			log.Info("SearchOrder", log.Any("res", res))
		})
	}
}

func TestTourmindClient_CancelOrder(t *testing.T) {
	// Setup
	ctx := context.Background()
	req := &entities.CancelOrderReq{
		AgentRefID: "5965833",
	}

	tracingID := "test-tracing-id"

	tourmindClient := NewTourmindClient(getTestCfg(), nil)

	// Test cases
	tests := []struct {
		name      string
		req       *entities.CancelOrderReq
		tracingID string
		wantErr   bool
	}{
		{
			name:      "success",
			req:       req,
			tracingID: tracingID,
			wantErr:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Call CancelOrder function
			res, err := tourmindClient.CancelOrder(ctx, tt.req, tt.tracingID)
			if err != nil {
				t.Errorf("CancelOrder() err %v", err)
				return
			}

			if res.ResponseHeader.TransactionID == "" {
				t.Errorf("CancelOrder() empty")
			}

			log.Info("CancelOrder", log.Any("resj", res))
		})
	}
}

func TestTourmindClient_HotelStaticList(t *testing.T) {
	// Setup
	ctx := context.Background()
	req := &entities.HotelStaicListReq{
		CountryCode: "VN",
		Pagination: entities.Pagination{
			PageIndex: 1,
			PageSize:  2,
		},
	}

	tracingID := "test-tracing-id"

	tourmindClient := NewTourmindClient(getTestCfg(), nil)

	// Test cases
	tests := []struct {
		name      string
		req       *entities.HotelStaicListReq
		tracingID string
		wantErr   bool
	}{
		{
			name:      "success",
			req:       req,
			tracingID: tracingID,
			wantErr:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Call HotelStaticList function
			res, err := tourmindClient.HotelStaticList(ctx, tt.req, tt.tracingID)

			if err != nil {
				t.Errorf("HotelStaticList() err %v", err)
				return
			}

			if res.ResponseHeader.TransactionID == "" {
				t.Errorf("HotelStaticList() empty")
			}

			log.Info("HotelStaticList", log.Any("res", res))
		})
	}
}
