package client

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"net/http"
	"net/url"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/log"
	tracingHttp "gitlab.deepgate.io/apps/common/tracing/http"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/tourmind_client/client/constants"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/tourmind_client/client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	pkgConstants "gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
)

const (
	successfulCode        = 200
	methodGet             = "GET"
	methodPost            = "POST"
	defaultRequestTimeout = 60
)

type TourmindClient interface {
	CreateOrder(ctx context.Context, req *entities.CreateOrderReq, tracingID string) (*entities.CreateOrderRes, error)
	CheckRoomRate(ctx context.Context, req *entities.CheckRoomRateReq, tracingID string) (*entities.CheckRoomRateRes, error)
	HotelDetail(ctx context.Context, req *entities.HotelDetailReq, tracingID string) (*entities.HotelDetailRes, error)
	SearchOrder(ctx context.Context, req *entities.SearchOrderReq, tracingID string) (*entities.SearchOrderRes, error)
	CancelOrder(ctx context.Context, req *entities.CancelOrderReq, tracingID string) (*entities.CancelOrderRes, error)
	HotelStaticList(ctx context.Context, req *entities.HotelStaicListReq, tracingID string) (*entities.HotelStaicListRes, error)
}

type tourmindClient struct {
	baseUrl     string
	agentCode   string
	password    string
	username    string
	requestRepo repositories.RequestRepository
}

func NewTourmindClient(cfg *config.Schema, requestRepo repositories.RequestRepository) TourmindClient {
	return &tourmindClient{
		baseUrl:     cfg.TourmindBaseURL,
		agentCode:   cfg.TourmindAgentCode,
		password:    cfg.TourmindPassword,
		username:    cfg.TourmindUsername,
		requestRepo: requestRepo,
	}
}

func (c *tourmindClient) getHeader() map[string]string {
	return map[string]string{
		"Content-Type": "application/json",
	}
}

func (c *tourmindClient) getRequestHeader() entities.RequestHeader {
	return entities.RequestHeader{
		AgentCode:   c.agentCode,
		Password:    c.password,
		RequestTime: time.Now().Format("2006-01-02 15:04:05"),
		// TransactionID: uuid.New().String(),
		UserName: c.username,
	}
}

func (c *tourmindClient) handleErr(errMsg string) error {
	return errors.New(errMsg)
}

func (c *tourmindClient) CheckRoomRate(ctx context.Context, req *entities.CheckRoomRateReq, tracingID string) (*entities.CheckRoomRateRes, error) {
	res := &entities.CheckRoomRateRes{}

	req.RequestHeader = c.getRequestHeader()

	if err := c.doRequest(ctx, methodPost, constants.APIRouteCheckRoomRate, c.getHeader(), req, tracingID, res); err != nil {
		return nil, errors.Wrap(err, "doRequest")
	}

	if res.Error.ErrorCode != "" {
		return nil, c.handleErr(res.Error.ErrorMessage)
	}

	return res, nil
}

func (c *tourmindClient) CancelOrder(ctx context.Context, req *entities.CancelOrderReq, tracingID string) (*entities.CancelOrderRes, error) {
	res := &entities.CancelOrderRes{}

	req.RequestHeader = c.getRequestHeader()

	if err := c.doRequest(ctx, methodPost, constants.APIRouteCancelOrder, c.getHeader(), req, tracingID, res); err != nil {
		return nil, errors.Wrap(err, "doRequest")
	}

	if res.Error.ErrorCode != "" {
		return nil, c.handleErr(res.Error.ErrorMessage)
	}

	return res, nil
}

func (c *tourmindClient) HotelStaticList(ctx context.Context, req *entities.HotelStaicListReq, tracingID string) (*entities.HotelStaicListRes, error) {
	res := &entities.HotelStaicListRes{}

	req.RequestHeader = c.getRequestHeader()

	if err := c.doRequest(ctx, methodPost, constants.APIRouteHotelStaticList, c.getHeader(), req, tracingID, res); err != nil {
		return nil, errors.Wrap(err, "doRequest")
	}

	if res.Error.ErrorCode != "" {
		return nil, c.handleErr(res.Error.ErrorMessage)
	}

	return res, nil
}

func (c *tourmindClient) SearchOrder(ctx context.Context, req *entities.SearchOrderReq, tracingID string) (*entities.SearchOrderRes, error) {
	res := &entities.SearchOrderRes{}

	req.RequestHeader = c.getRequestHeader()

	if err := c.doRequest(ctx, methodPost, constants.APIRouteSearchOrder, c.getHeader(), req, tracingID, res); err != nil {
		return nil, errors.Wrap(err, "doRequest")
	}

	if res.Error.ErrorCode != "" {
		return nil, c.handleErr(res.Error.ErrorMessage)
	}

	return res, nil
}

func (c *tourmindClient) CreateOrder(ctx context.Context, req *entities.CreateOrderReq, tracingID string) (*entities.CreateOrderRes, error) {
	res := &entities.CreateOrderRes{}

	req.RequestHeader = c.getRequestHeader()

	if err := c.doRequest(ctx, methodPost, constants.APIRouteCreateOrder, c.getHeader(), req, tracingID, res); err != nil {
		return nil, errors.Wrap(err, "doRequest")
	}

	if res.Error.ErrorCode != "" {
		return nil, c.handleErr(res.Error.ErrorMessage)
	}

	return res, nil
}

func (c *tourmindClient) HotelDetail(ctx context.Context, req *entities.HotelDetailReq, tracingID string) (*entities.HotelDetailRes, error) {
	res := &entities.HotelDetailRes{}

	req.RequestHeader = c.getRequestHeader()

	if err := c.doRequest(ctx, methodPost, constants.APIRouteHotelDetail, c.getHeader(), req, tracingID, res); err != nil {
		return nil, errors.Wrap(err, "doRequest")
	}

	if res.Error.ErrorCode != "" {
		return nil, c.handleErr(res.Error.ErrorMessage)
	}

	return res, nil
}

func (c *tourmindClient) do(
	ctx context.Context,
	header map[string]string,
	fullPath string,
	method string,
	action string,
	body interface{},
) (_ []byte, _ int, duration int64, _ error) {
	beginAt := time.Now().UnixMilli()

	defer func() {
		duration = time.Now().UnixMilli() - beginAt
	}()

	jsonData, err := json.Marshal(body)
	if err != nil {
		log.Error("tourmind Marshal error",
			log.Any("error", err),
			log.String("relativePath", fullPath),
			log.String("action", action),
			log.Any("req", body))
		return nil, 0, duration, err
	}

	var response *http.Response

	payload := bytes.NewBuffer(jsonData)
	response, err = tracingHttp.RawRequest(ctx, fullPath, method, payload, header)
	if err != nil {
		return nil, 0, duration, errors.Wrap(err, "tracingHttp.RawRequest")
	}

	defer response.Body.Close()

	resBody, err := io.ReadAll(response.Body)
	if err != nil {
		return resBody, response.StatusCode, duration, errors.Wrap(err, "io.ReadAll")
	}

	if response.StatusCode != successfulCode {
		err := errors.New(response.Status)
		return resBody, response.StatusCode, duration, errors.Wrap(err, "response status code not success")
	}

	return resBody, response.StatusCode, duration, nil
}

func (c *tourmindClient) doRequest(
	ctx context.Context,
	method string,
	apiPath string,
	header map[string]string,
	body interface{},
	tracingID string,
	responseData interface{},
) error {
	var err error

	fullPath, err := url.JoinPath(c.baseUrl, apiPath)
	if err != nil {
		return errors.Wrap(err, "Parse base URL")
	}

	response, statusCode, duration, err := c.do(ctx, header, fullPath, method, apiPath, body)

	headerClone := map[string]string{}

	for key, val := range header {
		headerClone[key] = val
	}

	go func(headerClone map[string]string) {
		path := apiPath
		bCtx, cancel := context.WithTimeout(context.Background(), pkgConstants.RequestRepoCtxTimeout)

		defer cancel()

		requestID := uuid.NewString()

		if c.requestRepo == nil {
			return
		}

		doErr := err

		if err := c.requestRepo.Create(bCtx, &repositories.Request{
			RequestID:  requestID,
			Path:       path,
			Method:     methodPost,
			Body:       body,
			Headers:    headerClone,
			Response:   response,
			StatusCode: statusCode,
			Duration:   duration,
			Action:     apiPath,
			Provider:   enum.HotelProviderTourMind,
			TracingID:  tracingID,
			IsJson:     true,
			ErrorMsg:   doErr,
		}); err != nil {
			log.Error("Tourmind requestRepo.CreateV2 error",
				log.Any("error", err),
				log.String("requestID", requestID),
				log.String("path", path),
				log.String("action", apiPath),
				log.Any("req", body),
			)
		}
	}(headerClone)

	if err != nil {
		return errors.Wrap(err, "c.do")
	}

	if err := json.Unmarshal(response, responseData); err != nil {
		return errors.Wrap(err, "Unmarshal")
	}

	return nil
}
