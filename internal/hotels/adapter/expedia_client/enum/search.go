package enum

type Currency string

const (
	CurrencyUSD Currency = "USD"
	CurrencyVND Currency = "VND"
)

type SalesChannel string

const (
	SalesChannelWebsite   SalesChannel = "website"
	SalesChannelAgentTool SalesChannel = "agent_tool"
	SalesChannelMobileApp SalesChannel = "mobile_app"
	SalesChannelMobileWeb SalesChannel = "mobile_web"
	SalesChannelMeta      SalesChannel = "meta"
	SalesChannelCache     SalesChannel = "cache"
)

type SalesEnvironment string

const (
	SalesEnvironmentHotelOnly    SalesEnvironment = "hotel_only"
	SalesEnvironmentHotelPackage SalesEnvironment = "hotel_package"
	SalesEnvironmentLoyalty      SalesEnvironment = "loyalty"
)

type TravelPurpose string

const (
	TravelPurposeLeisure            TravelPurpose = "leisure"
	TravelPurposeBusiness           TravelPurpose = "business"
	TravelPurposeLeisureAndBusiness TravelPurpose = "business_and_leisure"
)

type PropertyStatus string

const (
	PropertyStatusAvailable            PropertyStatus = "available"
	PropertyStatusPartiallyUnavailable PropertyStatus = "partially_unavailable"
)

type RateStatus string

const (
	RateStatusAvailable    RateStatus = "available"
	RateStatusPriceChanged RateStatus = "price_changed"
	RateStatusSoldOut      RateStatus = "sold_out"
)

type MerchantOfRecord string

const (
	MerchantOfRecordExpedia  MerchantOfRecord = "expedia"
	MerchantOfRecordProperty MerchantOfRecord = "property"
)

type OfferType string

const (
	OfferTypeBuyOneGetOneFree OfferType = "buy_one_get_one_free"
	OfferTypeCredit           OfferType = "credit"
	OfferTypeDiscount         OfferType = "discount"
	OfferTypeFree             OfferType = "free"
	OfferTypeVoucher          OfferType = "voucher"
)

type Frequency string

const (
	FrequencyUnknown   Frequency = "unknown"
	FrequencyPerNight  Frequency = "per_night"
	FrequencyPerDay    Frequency = "per_day"
	FrequencyPerStay   Frequency = "per_stay"
	FrequencyPerWeek   Frequency = "per_week"
	FrequencyRoundTrip Frequency = "round_trip"
	FrequencyOneWay    Frequency = "one_way"
)

type FeeType string

const (
	FeeTypeBaseRate               FeeType = "base_rate"
	FeeTypeTaxAndServiceFee       FeeType = "tax_and_service_fee"
	FeeTypeExtraPersonFee         FeeType = "extra_person_fee"
	FeeTypePropertyFee            FeeType = "property_fee"
	FeeTypeSalesTax               FeeType = "sales_tax"
	FeeTypeAdjustment             FeeType = "adjustment"
	FeeTypeRecoveryChargesAndFees FeeType = "recovery_charges_and_fees"
	FeeTypeTravelerServiceFee     FeeType = "traveler_service_fee"
)

type UnavailableReasonCode string

const (
	AdultsExceedThreshold     UnavailableReasonCode = "adults_exceed_threshold"
	ChildrenExceedThreshold   UnavailableReasonCode = "children_exceed_threshold"
	InfantsExceedThreshold    UnavailableReasonCode = "infants_exceed_threshold"
	MinimumChildAge           UnavailableReasonCode = "minimum_child_age"
	MaximumOccupancy          UnavailableReasonCode = "maximum_occupancy"
	CheckinNotAllowed         UnavailableReasonCode = "checkin_not_allowed"
	CheckoutNotAllowed        UnavailableReasonCode = "checkout_not_allowed"
	MinimumStay               UnavailableReasonCode = "minimum_stay"
	MaximumStay               UnavailableReasonCode = "maximum_stay"
	RestrictedStayLengths     UnavailableReasonCode = "restricted_stay_lengths"
	SameDayRestrictions       UnavailableReasonCode = "same_day_restrictions"
	MaximumRooms              UnavailableReasonCode = "maximum_rooms"
	ChildrenNotSupported      UnavailableReasonCode = "children_not_supported"
	MinimumAdvancePurchase    UnavailableReasonCode = "minimum_advance_purchase"
	MaximumAdvancePurchase    UnavailableReasonCode = "maximum_advance_purchase"
	PartialInventoryAvailable UnavailableReasonCode = "partial_inventory_available"
	NoInventoryAvailable      UnavailableReasonCode = "no_inventory_available"
)
