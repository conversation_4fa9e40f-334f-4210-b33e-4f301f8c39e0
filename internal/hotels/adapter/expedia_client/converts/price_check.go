package converts

import (
	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/log"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/expedia_client/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/expedia_client/models/price_check"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

func FromPriceCheckResponseToHubHotel(response *price_check.PriceCheckResponse, occupancyMap map[string]string, occupancyCount map[string]uint) (*domain.HubRateData, *domain.ExpediaSessionInfo, error) {
	if response == nil {
		log.Error("FromPriceCheckResponseToHubHotel response nil")
		return nil, nil, domain.ErrInvalidValue
	}

	if response.Status == enum.PriceCheckStatusSoldOut {
		return nil, nil, domain.ErrRoomSoldOut
	}

	result := &domain.HubRateData{}

	if _, ok := response.Links["book"]; !ok {
		return nil, nil, domain.ErrRoomSoldOut
	}

	nextStepInfo := &domain.ExpediaSessionInfo{
		HoldBookingSession: response.Links["book"].Href,
	}

	occupancyRates, _, err := CalculateOccupancyRatesPriceCheck(response.OccupancyPricing, occupancyMap, occupancyCount)
	if err != nil {
		return nil, nil, errors.Wrap(err, "calculateOccupancyRates")
	}

	result.OccupancyRate = occupancyRates

	return result, nextStepInfo, nil
}
