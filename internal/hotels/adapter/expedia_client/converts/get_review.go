package converts

import (
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/expedia_client/models/get_reviews"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

func ToGetReviewRequest(in *domain.HubHotelReviewReq) *get_reviews.ExpediaGetReviewRequest {
	if in == nil {
		return nil
	}

	return &get_reviews.ExpediaGetReviewRequest{
		PropertyID:  in.HotelID,
		Language:    in.Language,
		BillingTerm: "EAC",
		PartnerPoS:  "B2B_CORP_SA_PKG_MOD",
		PaymentTerm: "1",
	}
}

func ConvertReviewToHubHotelReviewItem(review get_reviews.Review) *domain.HubHotelReviewItem {
	managementResponses := make([]domain.HubManagerResponse, 0, len(review.ManagerResponses))

	for _, item := range review.ManagerResponses {
		managementResponses = append(managementResponses, domain.HubManagerResponse{
			Date: item.Date,
			Text: item.Text,
		})
	}

	return &domain.HubHotelReviewItem{
		VerificationSource:  review.VerificationSource,
		DateSubmitted:       review.DateSubmitted,
		Rating:              review.Rating,
		ReviewerName:        review.ReviewerName,
		TravelCompanion:     review.TravelCompanion,
		TripReason:          string(review.TripReason),
		Text:                review.Text,
		StayDate:            review.StayDate,
		HubManagerResponses: managementResponses,
	}
}
