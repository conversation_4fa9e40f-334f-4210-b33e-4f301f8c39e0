package expedia_client

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gitlab.deepgate.io/apps/common/adapter/redis"
	commonError "gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/expedia_client/client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/expedia_client/constants"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/expedia_client/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
)

type ExpediaAdapter interface {
	SearchHotel(ctx context.Context, req *domain.SearchHotelRequestData, tracingID string) (*domain.HotelSearchResult, error)
	GetRegions(ctx context.Context, req *domain.RegionsRequest, tracingID string) ([]*domain.Region, error)
	PriceCheck(ctx context.Context, priceCheckPath, searchKey string, occupancies []*domain.HubSearchOccupancy) (*domain.HubRateData, *domain.ExpediaSessionInfo, error)
	Book(ctx context.Context, req *domain.HubBookReq, session *domain.ExpediaSessionInfo, rooms []*domain.HubOrderRoomItem, tracingID string) (string, []*domain.HubRetrieveConfirmationID, enum.BookingStatus, error)
	Retrieve(ctx context.Context, order *domain.HubHotelOrder, tracingID string) ([]*domain.HubRetrieveConfirmationID, enum.BookingStatus, error)
	GetReviews(ctx context.Context, req *domain.HubHotelReviewReq, tracingID string) ([]*domain.HubHotelReviewItem, error)
	Cancel(ctx context.Context, order *domain.HubHotelOrder, endUserIP, tracingID string) error
	RetrieveForRefund(ctx context.Context, order *domain.HubHotelOrder, tracingID string) (bool, bool, *domain.RefundData)
}

type expediaAdapter struct {
	expediaClient client.ExpediaClient
	cfg           *config.Schema
}

func NewExpediaAdapter(cfg *config.Schema, redis redis.IRedis, requestRepo repositories.RequestRepository) ExpediaAdapter {
	return &expediaAdapter{
		expediaClient: client.NewExpediaClient(cfg, requestRepo),
		cfg:           cfg,
	}
}

func (a *expediaAdapter) SearchHotel(ctx context.Context, req *domain.SearchHotelRequestData, searchKey string) (*domain.HotelSearchResult, error) {
	if req == nil {
		return nil, commonError.ErrInvalidInput
	}

	searchReq := req.SearchReq

	clientSearchReq, err := converts.ToSearchHotelReq(req)
	if err != nil {
		return nil, errors.Wrap(err, "converts.ToSearchHotelReq error")
	}

	response, err := a.expediaClient.SearchHotel(ctx, clientSearchReq, searchKey)
	if err != nil {
		log.Error("expediaClient.SearchHotel error", log.Any("clientSearchReq", clientSearchReq), log.Any("err", err))
		return nil, errors.Wrap(err, "expediaClient.SearchHotel")
	}

	occupancyMap, occupancyMapCount := converts.GenOccupancyTypeMap(searchReq.Occupancies)

	if err := searchReq.Stay.CountDays(); err != nil {
		return nil, errors.Wrap(err, "countdays error")
	}

	parseResponse, err := converts.FromSearchResponseToHubHotels(response, occupancyMap, occupancyMapCount, int64(searchReq.Stay.DayCount))
	if err != nil {
		log.Error("FromSearchResponseToHubHotels error", log.Any("err", err))
		return nil, errors.Wrap(err, "converts.FromSearchResponseToHubHotels error")
	}

	return &domain.HotelSearchResult{
		SearchKey:          searchKey,
		Provider:           enum.HotelProviderExpedia,
		ExpireAt:           time.Now().Add(constants.DefaultExpireTime).UnixMilli(),
		Hotels:             parseResponse,
		HotelSearchRequest: searchReq.ToHotelSearchRequest(),
		SaleScenario:       string(searchReq.SaleChannel),
	}, nil
}

func (a *expediaAdapter) GetRegions(ctx context.Context, req *domain.RegionsRequest, tracingID string) ([]*domain.Region, error) {
	if req == nil {
		return nil, commonError.ErrInvalidInput
	}

	regionReq := converts.FromDomainRegionRequest(req)

	response, err := a.expediaClient.GetRegions(ctx, regionReq, tracingID)
	if err != nil {
		log.Error("a.expediaClient.GetRegions error", log.Any("regionReq", regionReq), log.Any("err", err))
		return nil, err
	}

	return converts.ToDomainRegions(response), nil
}

func (a *expediaAdapter) GetReviews(ctx context.Context, req *domain.HubHotelReviewReq, tracingID string) ([]*domain.HubHotelReviewItem, error) {
	if req == nil {
		return nil, commonError.ErrInvalidInput
	}

	regionReq := converts.ToGetReviewRequest(req)

	response, err := a.expediaClient.GuestReview(ctx, regionReq, tracingID)
	if err != nil {
		log.Error("a.expediaClient.GuestReview error", log.Any("regionReq", regionReq), log.Any("err", err))
		return nil, err
	}

	result := make([]*domain.HubHotelReviewItem, 0, len(response))

	for _, item := range response {
		result = append(result, converts.ConvertReviewToHubHotelReviewItem(item))
	}

	return result, nil
}

func (a *expediaAdapter) PriceCheck(ctx context.Context, priceCheckPath, searchKey string, occupancies []*domain.HubSearchOccupancy) (*domain.HubRateData, *domain.ExpediaSessionInfo, error) {
	response, err := a.expediaClient.PriceCheck(ctx, priceCheckPath, searchKey)
	if err != nil {
		log.Error("expediaClient.PriceCheck error", log.Any("searchKey", searchKey), log.Any("err", err))
		return nil, nil,
			errors.Wrap(err, "expediaClient.PriceCheck")
	}
	occupancyMap, occupancyMapCount := converts.GenOccupancyTypeMap(occupancies)
	parseResponse, nextStepInfo, err := converts.FromPriceCheckResponseToHubHotel(response, occupancyMap, occupancyMapCount)
	if err != nil {
		log.Error("FromPriceCheckResponseToHubHotel error", log.Any("err", err))
		return nil, nil, errors.Wrap(err, "converts.FromPriceCheckResponseToHubHotel error")
	}

	return parseResponse, nextStepInfo, nil
}

func (a *expediaAdapter) Book(ctx context.Context, req *domain.HubBookReq, session *domain.ExpediaSessionInfo, rooms []*domain.HubOrderRoomItem, searchKey string) (string, []*domain.HubRetrieveConfirmationID, enum.BookingStatus, error) {
	if session == nil || req == nil {
		log.Error("req or session nil error", log.Any("searchKey", searchKey))
		return "", nil, enum.BookingStatusNone, domain.ErrInvalidValue
	}

	response, statusCode, err := a.expediaClient.Book(ctx, session.HoldBookingSession, req, rooms, searchKey)
	if err != nil {
		log.Error("expediaClient.Book error", log.Any("searchKey", searchKey), log.Any("err", err))
		return "", nil, enum.BookingStatusNone,
			errors.Wrap(err, "expediaClient.Book")
	}

	// https://prnt.sc/QiEXFX359N4D
	failedCode := []int{400, 401, 403, 409, 410, 426}

	if lo.Contains(failedCode, statusCode) {
		return "", nil, enum.BookingStatusFailed, domain.ErrBookingFailed
	}

	itineraryID := response.ItineraryID

	return itineraryID, nil, enum.BookingStatusPending, nil
}

func (a *expediaAdapter) Retrieve(ctx context.Context, order *domain.HubHotelOrder, tracingID string) ([]*domain.HubRetrieveConfirmationID, enum.BookingStatus, error) {
	retrieveResponse, statusCode, err := a.expediaClient.RetrieveBooking(ctx, order.OrderCode, &domain.HubBookReq{
		EndUserIPAddress: order.CustomerIP,
	}, tracingID)

	if err != nil {
		log.Error("expediaClient.RetrieveBooking error", log.Any("tracingID", tracingID), log.Any("err", err))
		return nil, enum.BookingStatusPending,
			errors.Wrap(err, "expediaClient.Book")
	}

	// https: //prnt.sc/-iNd5lOaLUeD
	failedStatus := []int{400, 401, 403, 404, 409, 410, 426}

	if lo.Contains(failedStatus, statusCode) {
		return nil, enum.BookingStatusFailed, domain.ErrBookingFailed
	}

	confirmIDs, bookingStatus, err := converts.GetConfirmationID(retrieveResponse)
	if err != nil {
		log.Error("GetConfirmationID error", log.Any("err", err))
		return nil, converts.ToHubBookingStatus(bookingStatus), errors.Wrap(err, "converts.GetConfirmationID error")
	}

	return confirmIDs, converts.ToHubBookingStatus(bookingStatus), nil
}

func (a *expediaAdapter) Cancel(ctx context.Context, order *domain.HubHotelOrder, endUserIP, tracingID string) error {
	retrieveResponse, statusCode, err := a.expediaClient.RetrieveBooking(ctx, order.OrderCode, &domain.HubBookReq{
		EndUserIPAddress: order.CustomerIP,
	}, tracingID)

	if err != nil {
		log.Error("expediaClient.RetrieveBooking error", log.Any("tracingID", tracingID), log.Any("err", err))
		return errors.Wrap(err, "expediaClient.Book")
	}

	// https: //prnt.sc/-iNd5lOaLUeD
	failedStatus := []int{400, 401, 403, 404, 409, 410, 426}

	if lo.Contains(failedStatus, statusCode) {
		return domain.ErrBookingCancelFailed
	}

	cancelURLs, err := converts.GetCancelLink(retrieveResponse)
	if err != nil {
		log.Error("GetCancelLink error", log.Any("err", err))
		return errors.Wrap(err, "converts.GetCancelLink error")
	}

	maxRetries := 3
	delay := 2 * time.Second

	for _, cancelURL := range cancelURLs {
		var err error
		for i := 0; i < maxRetries; i++ {
			statusCode, err = a.expediaClient.CancelBooking(ctx, cancelURL, endUserIP, tracingID)

			if err != nil || lo.Contains(failedStatus, statusCode) {
				log.Error(
					"cancel error",
					log.String("url", cancelURL),
					log.Int("response_code", statusCode),
					log.Any("err", err),
				)

				time.Sleep(delay)

				continue
			}

			break
		}

		if err != nil {
			return domain.ErrBookingCancelFailed
		}
	}

	return nil
}

func (a *expediaAdapter) RetrieveForRefund(ctx context.Context, order *domain.HubHotelOrder, tracingID string) (bool, bool, *domain.RefundData) {
	retrieveResponse, statusCode, err := a.expediaClient.RetrieveBooking(ctx, order.OrderCode, &domain.HubBookReq{
		EndUserIPAddress: order.CustomerIP,
	}, tracingID)

	if err != nil {
		log.Error("expediaClient.RetrieveForRefund error", log.Any("tracingID", tracingID), log.Any("err", err))
		return false, false, nil
	}

	// https: //prnt.sc/-iNd5lOaLUeD
	failedStatus := []int{400, 401, 403, 404, 409, 410, 426}

	if lo.Contains(failedStatus, statusCode) {
		return false, false, nil
	}

	isCanceled, isRefundTotal, refundData := converts.GetRefundInfo(retrieveResponse)

	return isCanceled, isRefundTotal, refundData
}
