package search_hotel

import (
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/expedia_client/enum"
)

type SearchResponse struct {
	PropertyID string              `json:"property_id"`
	Status     enum.PropertyStatus `json:"status"`
	Score      float64             `json:"score"`
	Rooms      []Room              `json:"rooms"`
	Links      Links               `json:"links"`
}

type Room struct {
	ID       string `json:"id"`
	RoomName string `json:"room_name"`
	Rates    []Rate `json:"rates"`
}

type Rate struct {
	ID                  string                      `json:"id"`
	Status              string                      `json:"status"` // enum: available, price_changed, sold_out
	AvailableRooms      int                         `json:"available_rooms"`
	Refundable          bool                        `json:"refundable"`
	MemberDealAvailable bool                        `json:"member_deal_available"`
	SaleScenario        map[string]bool             `json:"sale_scenario"`
	MerchantOfRecord    string                      `json:"merchant_of_record"`
	Amenities           map[string]Amenity          `json:"amenities"`
	Links               map[string]Link             `json:"links"`
	BedGroups           map[string]BedGroup         `json:"bed_groups"`
	CancelPenalties     []CancelPenalty             `json:"cancel_penalties"`
	NonRefundableRanges []NonRefundableRange        `json:"nonrefundable_date_ranges"`
	MarketingIncentives []MarketingIncentive        `json:"marketing_fee_incentives"`
	OccupancyPricing    map[string]OccupancyPricing `json:"occupancy_pricing"`
	Promotions          Promotions                  `json:"promotions"`
	CardOnFileLimit     Amount                      `json:"card_on_file_limit"`
	RefundableDeposit   Amount                      `json:"refundable_damage_deposit"`
	Deposits            []Deposit                   `json:"deposits"`
}

// type SaleScenario struct {
// 	Package         bool `json:"package"`
// 	Member          bool `json:"member"`
// 	Corporate       bool `json:"corporate"`
// 	Distribution    bool `json:"distribution"`
// 	MobilePromotion bool `json:"mobile_promotion"`
// }

type Amenity struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

type Link struct {
	Method  string `json:"method"`
	Href    string `json:"href"`
	Expires string `json:"expires,omitempty"`
}

type BedGroup struct {
	ID            string             `json:"id"`
	Description   string             `json:"description"`
	Links         map[string]Link    `json:"links"`
	Configuration []BedConfiguration `json:"configuration"`
}

type BedConfiguration struct {
	Type     string `json:"type"`
	Size     string `json:"size"`
	Quantity int    `json:"quantity"`
}

type CancelPenalty struct {
	Currency string `json:"currency"`
	Start    string `json:"start"`
	End      string `json:"end"`
	Amount   string `json:"amount"`
	Nights   string `json:"nights"`
	Percent  string `json:"percent"`
}

type NonRefundableRange struct {
	Start string `json:"start"`
	End   string `json:"end"`
}

type MarketingIncentive struct {
	Source string `json:"source"`
	Start  string `json:"start"`
	End    string `json:"end"`
}

type OccupancyPricing struct {
	Nightly [][]Amount             `json:"nightly"`
	Stay    []Amount               `json:"stay"`
	Totals  Totals                 `json:"totals"`
	Fees    map[string]TotalAmount `json:"fees"`
}

type Amount struct {
	Type     enum.FeeType `json:"type"`
	Value    string       `json:"value"`
	Currency string       `json:"currency"`
}

type TotalAmount struct {
	BillableCurrency Amount `json:"billable_currency"`
	RequestCurrency  Amount `json:"request_currency"`
}

type Totals struct {
	Inclusive                      TotalAmount `json:"inclusive"`
	Exclusive                      TotalAmount `json:"exclusive"`
	PropertyInclusive              TotalAmount `json:"property_inclusive"`
	InclusiveStrikethrough         TotalAmount `json:"inclusive_strikethrough"`
	Strikethrough                  TotalAmount `json:"strikethrough"`
	PropertyInclusiveStrikethrough TotalAmount `json:"property_inclusive_strikethrough"`
	MarketingFee                   TotalAmount `json:"marketing_fee"`
	GrossProfit                    TotalAmount `json:"gross_profit"`
	MinimumSellingPrice            TotalAmount `json:"minimum_selling_price"`
	PropertyFees                   TotalAmount `json:"property_fees"`
}

type Promotions struct {
	ValueAdds map[string]ValueAdd `json:"value_adds"`
	Deal      Deal                `json:"deal"`
}

type ValueAdd struct {
	ID          string `json:"id"`
	Description string `json:"description"`
	Category    string `json:"category"`   // enum: food_and_beverage, entertainment, etc.
	OfferType   string `json:"offer_type"` // enum: buy_one_get_one_free, credit, etc.
	Frequency   string `json:"frequency"`  // enum: unknown, per_night, etc.
	PersonCount int    `json:"person_count"`
}

type Deal struct {
	ID          string `json:"id"`
	Description string `json:"description"`
}

type Deposit struct {
	Value    string `json:"value"`
	Due      string `json:"due"`
	Currency string `json:"currency"`
}

type Links struct {
	AdditionalRates Link `json:"additional_rates"`
}
