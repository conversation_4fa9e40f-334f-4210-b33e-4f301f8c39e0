package search_hotel

import (
	"net/url"
	"strconv"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/expedia_client/enum"
)

type SearchRequest struct {
	CheckIn          string                `json:"checkin"`
	CheckOut         string                `json:"checkout"`
	Currency         string                `json:"currency"`
	CountryCode      string                `json:"country_code"`
	Language         string                `json:"language"`
	Occupancy        []string              `json:"occupancy"`
	PropertyID       []string              `json:"property_id"`
	SalesChannel     enum.SalesChannel     `json:"sales_channel"`
	SalesEnvironment enum.SalesEnvironment `json:"sales_environment"`
	RatePlanCount    int                   `json:"rate_plan_count"`
	AmenityCategory  []string              `json:"amenity_category"`
	Exclusion        []string              `json:"exclusion"`
	Filter           []string              `json:"filter"`
	Include          []string              `json:"include"`
	RateOption       []string              `json:"rate_option"`
	TravelPurpose    enum.TravelPurpose    `json:"travel_purpose,omitempty"`
	PointOfSale      string                `json:"point_of_sale,omitempty"`
	PaymentTerms     string                `json:"payment_terms,omitempty"`
	BillingTerms     string                `json:"billing_terms,omitempty"`
}

func (s *SearchRequest) ToQueryParams() string {
	queryParams := url.Values{}

	queryParams.Set("checkin", s.CheckIn)
	queryParams.Set("checkout", s.CheckOut)
	queryParams.Set("currency", s.Currency)
	queryParams.Set("country_code", s.CountryCode)
	queryParams.Set("language", s.Language)
	queryParams["occupancy"] = s.Occupancy
	queryParams["property_id"] = s.PropertyID
	queryParams.Set("rate_plan_count", strconv.Itoa(s.RatePlanCount))
	queryParams.Set("sales_channel", string(s.SalesChannel))
	queryParams.Set("sales_environment", string(s.SalesEnvironment))
	// queryParams["amenity_category"] = s.AmenityCategory
	// queryParams["exclusion"] = s.Exclusion
	// queryParams["filter"] = s.Filter
	// queryParams["include"] = s.Include
	queryParams["rate_option"] = s.RateOption
	queryParams.Set("travel_purpose", string(s.TravelPurpose))
	queryParams.Set("billing_terms", s.BillingTerms)
	queryParams.Set("payment_terms", s.PaymentTerms)
	queryParams.Set("partner_point_of_sale", s.PointOfSale)

	return queryParams.Encode()
}
