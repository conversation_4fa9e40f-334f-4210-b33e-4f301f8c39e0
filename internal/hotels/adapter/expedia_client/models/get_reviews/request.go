package get_reviews

import (
	"net/url"
	"strings"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/expedia_client/enum"
)

type ExpediaGetReviewRequest struct {
	PropertyID   string `json:"property_id"`
	Language     string
	TripReason   []enum.TravelPurpose
	BillingTerm  string
	PartnerPoS   string
	PaymentTerm  string
	PlatformName string
}

func convertTravelPurposeToString(purposes []enum.TravelPurpose) []string {
	var result []string
	for _, purpose := range purposes {
		result = append(result, string(purpose))
	}
	return result
}

func (r *ExpediaGetReviewRequest) ToQueryParam() string {
	params := url.Values{}

	if r.Language != "" {
		params.Add("language", r.Language)
	}
	if len(r.TripReason) > 0 {
		params.Add("trip_reason", strings.Join(convertTravelPurposeToString(r.TripReason), ","))
	}
	if r.BillingTerm != "" {
		params.Add("billing_terms", r.BillingTerm)
	}
	if r.PartnerPoS != "" {
		params.Add("partner_point_of_sale", r.PartnerPoS)
	}
	if r.PaymentTerm != "" {
		params.Add("payment_terms", r.PaymentTerm)
	}
	if r.PlatformName != "" {
		params.Add("platform_name", r.PlatformName)
	}

	return params.Encode()
}
