package get_reviews

import "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/expedia_client/enum"

type ManagerResponse struct {
	Date string `json:"date"`
	Text string `json:"text"`
}
type Review struct {
	VerificationSource string             `json:"verification_source"`
	Title              string             `json:"title"`
	DateSubmitted      string             `json:"date_submitted"`
	Rating             string             `json:"rating"`
	ReviewerName       string             `json:"reviewer_name"`
	StayDate           string             `json:"stay_date"`
	TripReason         enum.TravelPurpose `json:"trip_reason"`
	Text               string             `json:"text"`
	TravelCompanion    string             `json:"travel_companion"`
	ManagerResponses   []ManagerResponse  `json:"management_responses"`
}

type ReviewRecent struct {
	Recent []Review `json:"recent"`
}

type Response struct {
	Verified ReviewRecent `json:"verified"`
}
