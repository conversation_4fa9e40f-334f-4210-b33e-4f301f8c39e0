package retrieve_booking

import "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/expedia_client/enum"

type Link struct {
	Method  string `json:"method"`
	Href    string `json:"href"`
	Expires string `json:"expires,omitempty"`
}

type ConfirmationInfo struct {
	Expedia  string `json:"expedia"`
	Property string `json:"property"`
}

type RefundAmount struct {
	Amount   string `json:"amount"`
	Currency string `json:"currency"`
}

type Amount struct {
	Value    string `json:"value"`
	Currency string `json:"currency"`
}

type TotalAmount struct {
	BillableCurrency Amount `json:"billable_currency"`
	RequestCurrency  Amount `json:"request_currency"`
}

type Totals struct {
	Inclusive TotalAmount `json:"inclusive"`
}

type Pricing struct {
	Totals *Totals `json:"totals"`
}

type Rate struct {
	ID           string        `json:"id"`
	CancelRefund *RefundAmount `json:"cancel_refund"`
	Pricing      *Pricing      `json:"pricing"`
}

type Room struct {
	ID             string                    `json:"id"`
	Links          map[string]Link           `json:"links"`
	Rate           *Rate                     `json:"rate"`
	ConfirmationID ConfirmationInfo          `json:"confirmation_id"`
	Status         enum.ExpediaBookingStatus `json:"status"`
	BedGroupID     string                    `json:"bed_group_id"`
	NumberOfAdults uint                      `json:"number_of_adults"`
	Chd            []uint                    `json:"child_ages"`
	GivenName      string                    `json:"given_name"`
	SurName        string                    `json:"family_name"`
}

type Response struct {
	ItineraryID string  `json:"itinerary_id"`
	PropertyID  string  `json:"property_id"`
	Rooms       []*Room `json:"rooms"`
}
