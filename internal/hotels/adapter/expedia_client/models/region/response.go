package region

type Coordinates struct {
	CenterLongitude float64          `json:"center_longitude"`
	CenterLatitude  float64          `json:"center_latitude"`
	BoundingPolygon *BoundingPolygon `json:"bounding_polygon"`
}

type BoundingPolygon struct {
	Type        string      `json:"type"`
	Coordinates interface{} `json:"coordinates"`
}

type Ancestor struct {
	ID   string `json:"id"`
	Type string `json:"type"`
}

type RegionsResponse struct {
	ID                     string              `json:"id"`
	Type                   string              `json:"type"`
	Name                   string              `json:"name"`
	FullName               string              `json:"name_full"`
	Descriptor             string              `json:"descriptor"`
	IATAAirportCode        string              `json:"iata_airport_code"`
	IATAAirportMetroCode   string              `json:"iata_airport_metro_code"`
	CountryCode            string              `json:"country_code"`
	CountrySubdivisionCode string              `json:"country_subdivision_code"`
	Coordinates            *Coordinates        `json:"coordinates"`
	Associations           map[string][]string `json:"associations"`
	Ancestors              []*<PERSON>ces<PERSON>         `json:"ancestors"`
	Descendants            map[string][]string `json:"descendants"`
	PropertyIDs            []string            `json:"property_ids"`
	PropertyIDsExpanded    []string            `json:"property_ids_expanded"`
	Categories             []string            `json:"categories"`
	Tags                   []string            `json:"tags"`
}
