package create_booking

type RequestRoom struct {
	GivenName  string `json:"given_name"`
	FamilyName string `json:"family_name"`
	// Smoking        bool   `json:"smoking"`
	SpecialRequest string `json:"special_request,omitempty"`
	// LoyaltyID      string `json:"loyalty_id,omitempty"`
	// Loyalty        struct {
	// 	MemberID  string `json:"member_id,omitempty"`
	// 	ProgramID string `json:"program_id,omitempty"`
	// } `json:"loyalty,omitempty"`
}

type RequestPhone struct {
	CountryCode string `json:"country_code"`
	Number      string `json:"number"`
}

type RequestAddress struct {
	CountryCode       string `json:"country_code"`
	Line1             string `json:"line_1"`
	StateProvinceCode string `json:"state_province_code"`
	PostalCode        string `json:"postal_code"`
}

type RequestBillingContact struct {
	GivenName  string          `json:"given_name"`
	FamilyName string          `json:"family_name"`
	Address    *RequestAddress `json:"address"`
}

type RequestPayment struct {
	Type           string                 `json:"type"`
	BillingContact *RequestBillingContact `json:"billing_contact"`
}
type Request struct {
	Hold                 bool              `json:"hold"`
	Email                string            `json:"email"`
	Phone                *RequestPhone     `json:"phone"`
	Rooms                []*RequestRoom    `json:"rooms"`
	Payments             []*RequestPayment `json:"payments"`
	AffiliateReferenceID string            `json:"affiliate_reference_id"`
}
