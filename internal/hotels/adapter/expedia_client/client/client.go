package client

import (
	"bytes"
	"context"
	"crypto/sha512"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"regexp"
	"strconv"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/log"
	tracingHttp "gitlab.deepgate.io/apps/common/tracing/http"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/expedia_client/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/expedia_client/models/create_booking"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/expedia_client/models/get_reviews"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/expedia_client/models/price_check"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/expedia_client/models/region"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/expedia_client/models/retrieve_booking"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/expedia_client/models/search_hotel"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
)

const (
	methodGet             = "GET"
	methodPost            = "POST"
	methodDelete          = "DELETE"
	defaultRequestTimeout = 60
	actionSearchHotel     = "availability"
	actionGetRegion       = "region"
	actionPriceCheck      = "price-check"
	actionBook            = "book"
	actionRetrieve        = "retrieve"
	actionGetReview       = "get_review"
	actionCancelBooking   = "cancel_booking"

	pathGetRegion   = "/v3/regions"
	pathSearchHotel = "/v3/properties/availability"
	pathGetReview   = "/v3/properties/%s/guest-reviews"
)

var successCodeRange = []int{200, 300}

type ExpediaClient interface {
	GetRegions(ctx context.Context, req *region.RegionsRequest, tracingID string) ([]*region.RegionsResponse, error)
	SearchHotel(ctx context.Context, req *search_hotel.SearchRequest, tracingID string) ([]*search_hotel.SearchResponse, error)
	PriceCheck(ctx context.Context, priceCheckPath, tracingID string) (*price_check.PriceCheckResponse, error)
	Book(ctx context.Context, bookPath string, req *domain.HubBookReq, rooms []*domain.HubOrderRoomItem, tracingID string) (*create_booking.Response, int, error)
	RetrieveBooking(ctx context.Context, orderCode string, req *domain.HubBookReq, tracingID string) (*retrieve_booking.Response, int, error)
	GuestReview(ctx context.Context, req *get_reviews.ExpediaGetReviewRequest, tracingID string) ([]get_reviews.Review, error)
	CancelBooking(ctx context.Context, cancelBookingPath, endUserIPAddress, tracingID string) (int, error)
}

type expediaClient struct {
	cfg          *config.Schema
	baseUrl      string
	apiKey       string
	sharedSecret string
	requestRepo  repositories.RequestRepository
}

func NewExpediaClient(cfg *config.Schema, requestRepo repositories.RequestRepository) ExpediaClient {
	return &expediaClient{
		cfg:          cfg,
		baseUrl:      cfg.ExpediaURL,
		apiKey:       cfg.ExpediaApiKey,
		sharedSecret: cfg.ExpediaSharedSecret,
		requestRepo:  requestRepo,
	}
}

func (c *expediaClient) SearchHotel(ctx context.Context, req *search_hotel.SearchRequest, tracingID string) ([]*search_hotel.SearchResponse, error) {
	response := []*search_hotel.SearchResponse{}

	requestPath := pathSearchHotel + "?" + req.ToQueryParams()
	_, _, err := c.doRequestWithRetry(ctx, methodGet, c.baseUrl+requestPath, c.getHeader(c.apiKey), nil, actionSearchHotel, tracingID, &response)
	if err != nil {
		return nil, errors.Wrap(err, "doRequest")
	}

	return response, nil
}

func (c *expediaClient) GuestReview(ctx context.Context, req *get_reviews.ExpediaGetReviewRequest, tracingID string) ([]get_reviews.Review, error) {
	response := &get_reviews.Response{}

	requestPath := fmt.Sprintf(pathGetReview, req.PropertyID) + "?" + req.ToQueryParam()
	_, _, err := c.doRequestWithRetry(ctx, methodGet, c.baseUrl+requestPath, c.getHeader(c.apiKey), nil, actionGetReview, tracingID, &response)
	if err != nil {
		return []get_reviews.Review{}, errors.Wrap(err, "GuestReview")
	}

	return response.Verified.Recent, nil
}

func (c *expediaClient) PriceCheck(ctx context.Context, priceCheckPath, tracingID string) (*price_check.PriceCheckResponse, error) {
	response := &price_check.PriceCheckResponse{}

	_, statusCode, err := c.doRequestWithRetry(ctx, methodGet, c.baseUrl+priceCheckPath, c.getHeader(c.apiKey), nil, actionPriceCheck, tracingID, &response)
	if err != nil {
		if statusCode == http.StatusServiceUnavailable {
			return nil, domain.ErrRoomSoldOut
		}

		return nil, errors.Wrap(err, "doRequest")
	}

	return response, nil
}

func (c *expediaClient) CancelBooking(ctx context.Context, cancelBookingPath, endUserIPAddress, tracingID string) (int, error) {
	headers := c.getHeader(c.apiKey)
	headers["Customer-Ip"] = endUserIPAddress

	_, statusCode, err := c.doRequestWithRetry(ctx, methodDelete, c.baseUrl+cancelBookingPath, headers, nil, actionCancelBooking, tracingID, nil)
	if err != nil {
		return statusCode, errors.Wrap(err, "doRequest")
	}

	return statusCode, nil
}

func (c *expediaClient) Book(ctx context.Context, bookPath string, req *domain.HubBookReq, rooms []*domain.HubOrderRoomItem, tracingID string) (*create_booking.Response, int, error) {
	if req.TestingHashKey != "" {
		res_mock := &create_booking.Response{
			ItineraryID: "test",
			Links:       map[string]create_booking.Link{},
		}

		if value := constants.GetTestCaseValue(enum.HotelProviderExpedia, constants.ActionCreateBooking, req.TestingHashKey); value != "" {
			res_mock.TestingHashKeyMatch = true

			if value != "NOT DEFINED" {
				intValue, _ := strconv.Atoi(value)
				return res_mock, intValue, nil
			}

			return res_mock, 0, nil
		}
	}

	response := &create_booking.Response{}

	request, err := converts.ToBookRequest(req, rooms, c.cfg.CompanyEmail)
	if err != nil {
		return nil, 0, errors.Wrap(err, "ToBookRequest")
	}

	headers := c.getHeader(c.apiKey)
	headers["Customer-Ip"] = req.EndUserIPAddress

	_, statusCode, err := c.doRequestWithRetry(ctx, methodPost, c.baseUrl+bookPath, headers, request, actionBook, tracingID, &response)
	if err != nil {
		if statusCode == http.StatusGone || statusCode == http.StatusServiceUnavailable {
			return nil, statusCode, domain.ErrRoomSoldOut
		}

		if statusCode == http.StatusConflict {
			return nil, statusCode, domain.ErrRoomRateHasBeenChanged
		}

		return nil, 0, errors.Wrap(err, "doRequestWithRetry")
	}

	return response, statusCode, nil
}

func (c *expediaClient) RetrieveBooking(ctx context.Context, orderCode string, req *domain.HubBookReq, tracingID string) (*retrieve_booking.Response, int, error) {
	response := []*retrieve_booking.Response{}
	email := c.cfg.CompanyEmail

	headers := c.getHeader(c.apiKey)
	headers["Customer-Ip"] = req.EndUserIPAddress

	baseURL := fmt.Sprintf("%s/v3/itineraries?affiliate_reference_id=%s&email=%s", c.baseUrl, orderCode, email)

	_, statusCode, err := c.doRequestWithRetry(ctx, methodGet, baseURL, headers, nil, actionRetrieve, tracingID, &response)
	if err != nil {
		return nil, 0, errors.Wrap(err, "doRequestWithRetry")
	}

	if len(response) == 0 {
		return nil, statusCode, errors.New("empty response")
	}

	return response[0], statusCode, nil
}

func (c *expediaClient) doRequest(ctx context.Context, method string, apiPath string, header map[string]string, body interface{}, action string, tracingID string, responseData interface{}) (string, int, error) {
	var err error
	headerClone := map[string]string{}

	response, statusCode, duration, nextLink, err := c.do(ctx, header, apiPath, method, action, body)

	for key, val := range header {
		headerClone[key] = val
	}

	if action != actionGetRegion {
		go func(_ context.Context, headerClone map[string]string) {
			bCtx, cancel := context.WithTimeout(context.Background(), constants.RequestRepoCtxTimeout)
			defer cancel()

			path := apiPath
			requestID := uuid.NewString()

			if c.requestRepo == nil {
				return
			}

			doErr := err

			if err := c.requestRepo.Create(bCtx, c.convertToRequest(body, headerClone, response, statusCode, duration, doErr, requestID, path, method, action, tracingID)); err != nil {
				log.Error("Expedia requestRepo.CreateV2 error",
					log.Any("error", err),
					log.String("requestID", requestID),
					log.String("path", path),
					log.String("action", action),
					log.Any("req", body),
				)
			}
		}(ctx, headerClone)
	}

	if err != nil {
		return nextLink, statusCode, errors.Wrap(err, "c.do")
	}

	if statusCode != http.StatusNoContent {
		if err := json.Unmarshal(response, responseData); err != nil {
			return nextLink, statusCode, errors.Wrap(err, "Unmarshal")
		}
	}

	return nextLink, statusCode, nil
}

func (c *expediaClient) convertToRequest(body interface{}, headerClone map[string]string, response []byte, statusCode int, duration int64, doErr error, requestID, path, method, action, tracingID string) *repositories.Request {
	return &repositories.Request{
		RequestID:  requestID,
		Path:       path,
		Method:     method,
		Body:       body,
		Headers:    headerClone,
		Response:   response,
		StatusCode: statusCode,
		Duration:   duration,
		Action:     action,
		Provider:   enum.HotelProviderExpedia,
		TracingID:  tracingID,
		IsJson:     true,
		ErrorMsg:   doErr,
	}
}

func (c *expediaClient) do(
	ctx context.Context,
	header map[string]string,
	fullPath string,
	method string,
	action string,
	body interface{},
) ([]byte, int, int64, string, error) {
	var duration int64
	var nextLink string
	beginAt := time.Now()
	jsonData, err := json.Marshal(body)
	if err != nil {
		log.Error("expedia Marshal error",
			log.Any("error", err),
			log.String("relativePath", fullPath),
			log.String("action", action),
			log.Any("req", body))
		return nil, 0, duration, nextLink, err
	}

	var response *http.Response

	payload := bytes.NewBuffer(jsonData)
	response, err = tracingHttp.RawRequest(ctx, fullPath, method, payload, header)
	if err != nil {
		duration = time.Since(beginAt).Milliseconds()
		return nil, 0, duration, nextLink, errors.Wrap(err, "tracingHttp.RawRequest")
	}

	if response.Header.Get("Link") != "" {
		nextLink = response.Header.Get("Link")
	}

	duration = time.Since(beginAt).Milliseconds()

	defer response.Body.Close()

	resBody, err := io.ReadAll(response.Body)
	if err != nil {
		return resBody, response.StatusCode, duration, nextLink, errors.Wrap(err, "io.ReadAll")
	}

	if response.StatusCode < successCodeRange[0] ||
		response.StatusCode >= successCodeRange[1] {
		err := errors.New(response.Status)
		return resBody, response.StatusCode, duration, nextLink, errors.Wrap(err, "response status code not success")
	}

	return resBody, response.StatusCode, duration, nextLink, nil
}

func (c *expediaClient) getHeader(_ string) map[string]string {
	return map[string]string{
		"Content-Type":  "application/json",
		"Authorization": c.getAuthorizationKey(c.apiKey),
	}
}

func (c *expediaClient) getSigSHA512(timestamp string) string {
	sig := c.apiKey + c.sharedSecret + timestamp
	h := sha512.New()
	h.Write([]byte(sig))
	return hex.EncodeToString(h.Sum(nil))
}

// getAuthorizationKey generates the authorization key.
func (c *expediaClient) getAuthorizationKey(apiKey string) string {
	timestamp := fmt.Sprintf("%d", time.Now().Unix())
	signature := c.getSigSHA512(timestamp)
	return fmt.Sprintf("EAN apikey=%s,signature=%s,timestamp=%s", apiKey, signature, timestamp)
}

func extractNextLink(header string) string {
	re := regexp.MustCompile(`<([^>]+)>`)
	matches := re.FindStringSubmatch(header)
	if len(matches) > 1 {
		return matches[1]
	}
	return ""
}

func (c *expediaClient) GetRegions(ctx context.Context, req *region.RegionsRequest, tracingID string) ([]*region.RegionsResponse, error) {
	response := []*region.RegionsResponse{}

	requestPath := pathGetRegion + "?" + req.ToQueryParams()
	nextLink, _, err := c.doRequestWithRetry(ctx, methodGet, c.baseUrl+requestPath, c.getHeader(c.apiKey), nil, actionGetRegion, tracingID, &response)
	if err != nil {
		return nil, errors.Wrap(err, "doRequest")
	}

	for nextLink != "" {
		responseNext := []*region.RegionsResponse{}
		nextLink, _, err = c.doRequestWithRetry(ctx, methodGet, extractNextLink(nextLink), c.getHeader(c.apiKey), nil, actionSearchHotel, tracingID, &responseNext)
		if err != nil {
			return nil, errors.Wrap(err, "doRequest")
		}
		response = append(response, responseNext...)
	}

	return response, nil
}

func (c *expediaClient) doRequestWithRetry(ctx context.Context, method, url string, headers map[string]string, body interface{}, action string, tracingID string, response interface{}) (string, int, error) {
	const maxRetries = 10
	var nextLink string
	var statusCode int

	for i := 0; i < maxRetries; i++ {
		var err error
		nextLink, statusCode, err = c.doRequest(ctx, method, url, headers, body, action, tracingID, response)
		if err == nil {
			return nextLink, statusCode, nil
		}
		if errors.Is(err, io.EOF) || errors.Is(err, io.ErrUnexpectedEOF) {
			continue
		}
		return "", statusCode, err
	}

	return "", statusCode, nil
}
