package partnership

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	partnershipBackendPb "gitlab.deepgate.io/apps/api/gen/go/partnership/backend"
	commonError "gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/server"
	tracingGRPC "gitlab.deepgate.io/apps/common/tracing/grpc"
	"google.golang.org/grpc/metadata"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
)

type partnershipClient struct {
	cfg *config.Schema
}

type PartnershipClient interface {
	RetrievePartnership(ctx context.Context, pId string) (*domain.Partnership, error)
}

func NewPartnershipClient(cfg *config.Schema) PartnershipClient {
	return &partnershipClient{cfg: cfg}
}

func (c *partnershipClient) RetrievePartnership(ctx context.Context, pId string) (*domain.Partnership, error) {
	conn, err := tracingGRPC.DialWithTrace(ctx, c.cfg.PartnershipServiceEndpoint)
	if err != nil {
		return nil, errors.Wrap(err, "Cannot connect partner service")
	}
	defer conn.Close()

	md := server.WithMetadataInternalPartnership(c.cfg.InternalSecretToken, pId)
	newCtx := metadata.NewOutgoingContext(ctx, md)

	req := &partnershipBackendPb.RetrievePartnershipReq{Id: pId}

	client := partnershipBackendPb.NewPartnershipServiceClient(conn)

	res, err := client.RetrievePartnership(newCtx, req)
	if err != nil {
		return nil, commonError.New(commonError.BadRequest, err.Error())
	}

	if res.Info == nil {
		return nil, fmt.Errorf("res info nil")
	}

	info := res.Info

	partnership := domain.Partnership{
		Id:       pId,
		FullName: info.FullName,
	}

	return &partnership, nil
}
