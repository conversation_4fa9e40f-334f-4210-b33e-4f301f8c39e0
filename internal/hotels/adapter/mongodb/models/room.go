package models

import (
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

const (
	RoomCollectionRoomIDLanguageIndex = "__room_id__language__"
	RoomCollectionLanguageIndex       = "for_search_language"
)

type RoomGroupExt struct {
	Class    int `bson:"class"`
	Quality  int `bson:"quality"`
	Sex      int `bson:"sex"`
	Bathroom int `bson:"bathroom"`
	Bedding  int `bson:"bedding"`
	Family   int `bson:"family"`
	Capacity int `bson:"capacity"`
	Club     int `bson:"club"`
	Bedrooms int `bson:"bedrooms"`
	Balcony  int `bson:"balcony"`
	Floor    int `bson:"floor"`
	View     int `bson:"view"`
}

// Room struct
type Room struct {
	Base         `bson:",inline"`
	HotelRef     primitive.ObjectID            `bson:"hotel_ref,omitempty"`
	RoomID       string                        `bson:"room_id,omitempty"`
	Name         string                        `bson:"name,omitempty"`
	Language     string                        `bson:"language,omitempty"`
	Descriptions *RoomDescriptions             `bson:"descriptions,omitempty"`
	Amenities    []*Amenity                    `bson:"amenities,omitempty"`
	Images       []*Image                      `bson:"images,omitempty"`
	BedGroups    []*BedGroup                   `bson:"bed_groups,omitempty"`
	Area         *Area                         `bson:"area,omitempty"`
	Views        []*View                       `bson:"views,omitempty"`
	Occupancy    *Occupancy                    `bson:"occupancy,omitempty"`
	ProviderIds  map[enum.HotelProvider]string `bson:"provider_ids,omitempty"`
	RoomGroupExt RoomGroupExt                  `bson:"rg_ex"`
	Version      string                        `bson:"version"`
	HotelID      string                        `bson:"hotel_id,omitempty"`
}

type RoomDescriptions struct {
	Overview string `bson:"overview,omitempty"`
}

type View struct {
	ID   string `bson:"id,omitempty"`
	Name string `bson:"name,omitempty"`
}

type BedGroup struct {
	ID            string              `bson:"id,omitempty"`
	Description   string              `bson:"description,omitempty"`
	Configuration []*BedConfiguration `bson:"configuration,omitempty"`
}

type BedConfiguration struct {
	Quantity float64 `bson:"quantity,omitempty"`
	Size     string  `bson:"size,omitempty"`
	Type     string  `bson:"type,omitempty"`
}

type Area struct {
	SquareMeters float64 `bson:"square_meters,omitempty"`
	SquareFeet   float64 `bson:"square_feet,omitempty"`
}

type Occupancy struct {
	MaxAllowed    *MaxAllowed    `bson:"max_allowed,omitempty"`
	AgeCategories []*AgeCategory `bson:"age_categories,omitempty"`
}

type MaxAllowed struct {
	Total    float64 `bson:"total,omitempty"`
	Children float64 `bson:"children,omitempty"`
	Adults   float64 `bson:"adults,omitempty"`
}

type AgeCategory struct {
	Name       string  `bson:"name,omitempty"`
	MinimumAge float64 `bson:"minimum_age,omitempty"`
}
