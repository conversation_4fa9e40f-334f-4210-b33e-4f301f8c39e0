package models

import "go.mongodb.org/mongo-driver/bson/primitive"

const (
	RegionCollectionRegionIDLanguageIndex = "__region_id__language__"
	RegionCollectionCountryCodeIndex      = "__country_code__"
	RegionCollectionGeoCoordinatesIndex   = "__geo_coordinates__"
)

type RegionCoordinates struct {
	CenterLongitude      float64            `bson:"center_longitude,omitempty"`
	CenterLatitude       float64            `bson:"center_latitude,omitempty"`
	BoundingPolygonIDRef primitive.ObjectID `bson:"bounding_polygon_id_ref,omitempty"`
}

type Ancestor struct {
	ID   string `bson:"id,omitempty"`
	Type string `bson:"type,omitempty"`
}

type Region struct {
	Base                   `bson:",inline,omitempty"`
	RegionID               string              `bson:"region_id,omitempty"`
	Type                   string              `bson:"type,omitempty"`
	Name                   string              `bson:"name,omitempty"`
	FullName               string              `bson:"name_full,omitempty"`
	Descriptor             string              `bson:"descriptor,omitempty"`
	IATAAirportCode        string              `bson:"iata_airport_code,omitempty"`
	IATAAirportMetroCode   string              `bson:"iata_airport_metro_code,omitempty"`
	CountryCode            string              `bson:"country_code,omitempty"`
	CountrySubdivisionCode string              `bson:"country_subdivision_code,omitempty"`
	Coordinates            *RegionCoordinates  `bson:"coordinates,omitempty"`
	Associations           map[string][]string `bson:"associations,omitempty"`
	Ancestors              []*Ancestor         `bson:"ancestors,omitempty"`
	Descendants            map[string][]string `bson:"descendants,omitempty"`
	PropertyIDs            []string            `bson:"property_ids,omitempty"`
	PropertyIDsExpanded    []string            `bson:"property_ids_expanded,omitempty"`
	Categories             []string            `bson:"categories,omitempty"`
	Tags                   []string            `bson:"tags,omitempty"`
	Language               string              `bson:"language"`
	Version                string              `bson:"version,omitempty"`
}
