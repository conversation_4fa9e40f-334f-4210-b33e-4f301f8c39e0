package models

import "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"

type HubSearchChildren struct {
	Number uint   `bson:"number"`
	Age    []uint `bson:"age"`
}

type HubSearchOccupancy struct {
	OccupancyIndex uint               `bson:"occupancy_index"`
	Adults         uint               `bson:"adults"`
	Children       *HubSearchChildren `bson:"children"`
	Rooms          uint               `bson:"rooms"`
}
type HotelSearchResult struct {
	Base               `bson:",inline"`
	SearchKey          string              `bson:"search_key"`
	Provider           enum.HotelProvider  `bson:"provider"`
	SaleScenario       string              `bson:"sale_scenario"`
	SaleEnv            string              `bson:"sale_env"`
	ExpiredAt          int64               `bson:"expired_at"`
	HotelSearchRequest *HotelSearchRequest `bson:"hotel_search_request"`
	Hotels             []*HubHotel         `bson:"hotel"`
}

type HotelSearchRequest struct {
	Stay                HubSearchStay         `bson:"stay"`
	Occupancies         []*HubSearchOccupancy `bson:"occupancies"`
	CountryCode         string                `bson:"country_code"`
	Language            string                `bson:"language"`
	Currency            string                `bson:"currency"`
	EndUserIPAddress    string                `bson:"end_user_ip_address"`
	EndUserBrowserAgent string                `bson:"end_user_browser_agent"`
	DefaultLanguage     string                `bson:"default_language"`
	DetailRate          bool                  `bson:"detail_rate"`
}

type HubSearchStay struct {
	CheckIn   string `bson:"check_in"`
	CheckOut  string `bson:"check_out"`
	DayCount  int    `bson:"day_count"`
	RoomCount int    `bson:"room_count"`
}
