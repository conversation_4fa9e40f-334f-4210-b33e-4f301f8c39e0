package models

import (
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
)

const (
	IndexDefault = "_id_"
)

type LogRequest struct {
	Base         `bson:",inline"`
	Provider     enum.HotelProvider `bson:"provider"`
	RequestID    string             `bson:"request_id"`
	RelativePath string             `bson:"relative_path"`
	Method       string             `bson:"method"`
	Body         string             `bson:"body"`
	Headers      map[string]string  `bson:"headers"`
	Response     string             `bson:"response"`
	Duration     int64              `bson:"duration"`
	StatusCode   int                `bson:"status_code"`
	Action       string             `bson:"action"`
	ErrorMsg     string             `bson:"error_msg,omitempty"`
	TracingID    string             `bson:"tracing_id,omitempty"`
}
