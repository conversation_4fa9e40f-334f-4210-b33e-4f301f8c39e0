package models

import "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"

type ExpediaWebhookHeader struct {
	Signature string `bson:"signature"`
	Timestamp int64  `bson:"timestamp"`
}
type ExpediaWebhookNotificationRequest struct {
	Base                  `bson:",inline,omitempty"`
	*ExpediaWebhookHeader `bson:",inline,omitempty"`
	EventID               string                       `bson:"event_id"`
	EventType             enum.ExpediaWebhookEventType `bson:"event_type"`
	EventTime             string                       `bson:"event_time"`
	ItineraryID           string                       `bson:"itinerary_id"`
	Email                 string                       `bson:"email"`
	Message               string                       `bson:"message"`
	AffiliateReferenceID  string                       `bson:"affiliate_reference_id"`
	TopicTags             enum.ExpediaWebhookTopicTag  `bson:"topic_tags"`
	Rooms                 []*ExpediaNotificationRoom   `bson:"rooms"`
	Key                   string                       `bson:"key"`
}

type ExpediaNotificationRoom struct {
	ConfirmationInfo *ConfirmationInfo `bson:"confirmation_id"`
}

type ConfirmationInfo struct {
	Expedia  string `bson:"expedia"`
	Property string `bson:"property"`
}
