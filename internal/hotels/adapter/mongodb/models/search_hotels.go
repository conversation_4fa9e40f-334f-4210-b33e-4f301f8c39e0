package models

import (
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type HubSearchHotelCache struct {
	Base         `bson:",inline"`
	IsParent     bool               `bson:"is_parent,omitempty"`
	ParentID     primitive.ObjectID `bson:"parent_id,omitempty"`
	SearchKey    string             `bson:"search_key,omitempty"`
	Provider     enum.HotelProvider `bson:"provider,omitempty"`
	ExpiredAt    int64              `bson:"expired_at,omitempty"`
	HotelSummary *HotelSummary      `bson:"hotel_summary,omitempty"`
	Index        int                `bson:"index,omitempty"`
}

type PricePerNight struct {
	DiscountPrice float64 `bson:"discount_price"`
	OriginalPrice float64 `bson:"original_price"`
}

type Price struct {
	PricePerNight   *PricePerNight `bson:"price_per_night"`
	Total           float64        `bson:"total_price"`
	IsIncludeTax    bool           `bson:"is_include_tax"`
	SaleScenario    []string       `bson:"sale_scenario"`
	Currency        string         `bson:"currency"`
	PayAtHotel      []*PayAtHotel  `bson:"pay_at_hotel"`
	TotalPayAtHotel float64        `bson:"total_pay_at_hotel"`
	HasBreakfast    bool           `json:"has_breakfast"`
	HasExtraBed     bool           `json:"has_extra_bed"`
	NonSmoking      bool           `json:"non_smoking"`
	Refundable      bool           `json:"refundable"`
}

type HotelSummaryImageLink struct {
	Px70   string `bson:"px70,omitempty"`
	Px200  string `bson:"px200,omitempty"`
	Px350  string `bson:"px350,omitempty"`
	Px1000 string `bson:"px1000,omitempty"`
}

type HotelSummaryImage struct {
	HeroImage bool                  `bson:"hero_image,omitempty"`
	Category  float64               `bson:"category,omitempty"`
	Links     HotelSummaryImageLink `bson:"links,omitempty"`
	Caption   string                `bson:"caption,omitempty"`
}

type HotelSummary struct {
	ID           string               `bson:"id"`
	Name         string               `bson:"name"`
	Location     string               `bson:"location"`
	Review       *HotelReview         `bson:"review"`
	Amenities    []*Amenity           `bson:"amenities"`
	CategoryType string               `bson:"category_type"`
	ThumbnailURL []*HotelSummaryImage `bson:"thumbnail_url"`
	Rating       float64              `bson:"rating"`
	Price        *Price               `bson:"price"`
	CenterInfo   *CenterInfo          `bson:"center_info"`
	Available    bool                 `bson:"available"`
	RoomLeft     int                  `bson:"room_left"`
	CountryCode  string               `bson:"country_code"`
	Provider     enum.HotelProvider   `bson:"provider,omitempty"`
	MatchKey     string               `bson:"match_key,omitempty"`
	CheckIn      string               `bson:"check_in"`
	CheckOut     string               `bson:"check_out"`
}

type CenterInfo struct {
	CenterName       string  `bson:"center_name"`
	DistanceToCenter float64 `bson:"distance_to_center"`
	Unit             string  `bson:"unit"`
}

type FilterOption struct {
	Value string `bson:"value"`
	Count int32  `bson:"count"`
}

type DistanceRange struct {
	Label       string  `bson:"label"`
	MinDistance float64 `bson:"min_distance"`
	MaxDistance float64 `bson:"max_distance"`
}

type HotelReview struct {
	Rate        float64            `bson:"rate"`
	ReviewCount int32              `bson:"review_count"`
	Label       string             `bson:"label"`
	Detail      *HotelReviewDetail `bson:"detail"`
}

type HotelReviewDetail struct {
	Reviews []*Review       `bson:"reviews"`
	Rating  *CriteriaRating `bson:"rating"`
}

type Review struct {
	ReviewID     string   `bson:"review_id"`
	UserName     string   `bson:"user_ame"`
	UserType     string   `bson:"user_type"`
	Date         string   `bson:"date"`
	Rating       int32    `bson:"rating"`
	Title        string   `bson:"title"`
	Content      string   `bson:"content"`
	Liked        []string `bson:"liked"`
	StayDuration string   `bson:"stay_duration"`
}

type CriteriaRating struct {
	Cleanliness                  float64 `bson:"cleanliness"`
	StaffService                 float64 `bson:"staff_service"`
	Amenities                    float64 `bson:"amenities"`
	PropertyConditionsFacilities float64 `bson:"property_conditions_facilities"`
	EcoFriendliness              float64 `bson:"eco_friendliness"`
}

type FilterLocation struct {
	Lat         float64 `bson:"lat"`
	Lon         float64 `bson:"long"`
	MaxDistance float64 `bson:"max_distance"`
	MinDistance float64 `bson:"min_distance"`
}
