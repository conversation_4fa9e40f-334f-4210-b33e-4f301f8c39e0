package models

type TourmindJobsQueue struct {
	Base         `bson:",inline"`
	CountryCode  string   `bson:"country_code"`
	PageIndex    int      `bson:"page_index"`
	BatchSize    int      `bson:"batch_size"`
	CompletedAt  int64    `bson:"completed_at,omitempty"`
	CannotMapIds []string `bson:"cannot_map_ids,omitempty"`
	SuccessIds   []string `bson:"success_ids,omitempty"`
	DurationMs   int      `bson:"duration_ms,omitempty"`
	LastItem     bool     `bson:"last_item,omitempty"`
	TotalItems   int      `bson:"total_items,omitempty"`
}
