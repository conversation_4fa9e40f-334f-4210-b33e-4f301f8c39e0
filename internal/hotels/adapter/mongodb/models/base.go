package models

import (
	"fmt"
	"time"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

const (
	DefaultIndex                      = "_id_"
	CollectionProviderExpediaIDIndex  = "__provider_ids.11__language__"
	CollectionProviderTourMindIDIndex = "__provider_ids.12__language__"
	CollectionProviderPKFareIDIndex   = "__provider_ids.13__language__"
	HotelRoomV2_HotelIDLanguage       = "__hotel_id_language__"
)

type Base struct {
	ID        primitive.ObjectID `bson:"_id,omitempty"`
	CreatedAt int64              `bson:"created_at,omitempty"`
	UpdatedAt int64              `bson:"updated_at,omitempty"`
	DeletedAt int64              `bson:"deleted_at,omitempty"`
	CreatedBy string             `bson:"created_by,omitempty"`
	UpdatedBy string             `bson:"updated_by,omitempty"`
	DeletedBy string             `bson:"deleted_by,omitempty"`
}

func (b *Base) BeforeCreate() {
	if b.ID.IsZero() {
		b.ID = primitive.NewObjectID()
	}
	b.CreatedAt = time.Now().UnixMilli()
	b.UpdatedAt = time.Now().UnixMilli()
}

func (b *Base) BeforeUpdate() {
	b.UpdatedAt = time.Now().UnixMilli()
}

func GetProviderIdsLanguageSorterIndex(provider enum.HotelProvider) string {
	return fmt.Sprintf("__provider_ids.%d_language_sorter__", provider)
}
