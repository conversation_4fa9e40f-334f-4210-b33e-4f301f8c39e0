package repositories

import (
	"context"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const regionCollectionName = "regions"

type RegionRepository interface {
	Create(ctx context.Context, req *domain.Region, language string) error
	WithTransaction(ctx context.Context, fn func(tnxSession context.Context) (interface{}, error)) error
	Upsert(ctx context.Context, region *domain.Region, language string) error
	FindByRegionID(ctx context.Context, regionID, language string) (*domain.Region, error)
	FindAllByCountryCode(ctx context.Context, countryCode string) ([]*domain.Region, error)
	FindRegionsByRadiusAndTypes(ctx context.Context, longitude, latitude, distance float64, regionTypes []string, language string) ([]*domain.Region, error)
}

type regionRepository struct {
	db  mongodb.DB
	cfg *config.Schema
}

func (r *regionRepository) getCollectionName() string {
	if r.cfg.ContentVersion != "1" {
		return "regions_v2"
	}

	switch enum.HotelProviderValue[r.cfg.EnableProvider] {
	case enum.HotelProviderExpedia:
		return "expedia_regions"
	}

	return "regions"
}

func NewRegionRepository(db mongodb.DB, cfg *config.Schema,
) RegionRepository {
	return &regionRepository{
		db:  db,
		cfg: cfg,
	}
}

func (r *regionRepository) Create(ctx context.Context, req *domain.Region, language string) error {
	data := converts.FromDomainRegion(req)
	data.BeforeCreate()
	data.Language = language
	if err := r.db.Insert(ctx, r.getCollectionName(), data); err != nil {
		return errors.Wrap(err, "db.Insert")
	}
	return nil

}

func (r *regionRepository) Upsert(ctx context.Context, region *domain.Region, language string) error {
	request := make([]mongo.WriteModel, 0, 1)
	m := converts.FromDomainRegion(region)
	updateModel := converts.FromDomainRegion(region)
	m.Base.BeforeCreate()

	m.Language = language
	filter := bson.M{
		"region_id": m.RegionID,
		"language":  language,
	}

	if r.cfg.ContentVersion != "" {
		filter["version"] = r.cfg.ContentVersion
		m.Version = r.cfg.ContentVersion
		updateModel.Version = r.cfg.ContentVersion
	}

	update := bson.M{
		"$setOnInsert": m,
		"$set":         updateModel,
	}

	request = append(request, mongo.NewUpdateOneModel().
		SetFilter(filter).
		SetUpdate(update).
		SetUpsert(true).
		SetHint(models.RegionCollectionRegionIDLanguageIndex))

	err := r.db.BulkWriteRaw(ctx, r.getCollectionName(), request)
	if err != nil {
		return err
	}

	return nil
}

func (r *regionRepository) WithTransaction(ctx context.Context, fn func(tnxSession context.Context) (interface{}, error)) error {
	_, err := r.db.WithTransaction(ctx, fn)
	if err != nil {
		return err
	}
	return nil
}

func (r *regionRepository) FindByRegionID(ctx context.Context, regionID, language string) (*domain.Region, error) {
	var sf *models.Region
	filter := bson.M{
		"region_id": regionID,
		"language":  language,
	}

	if err := r.db.FindOne(ctx, r.getCollectionName(), &sf,
		mongodb.WithHint(models.RegionCollectionRegionIDLanguageIndex),
		mongodb.WithFilter(filter)); err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, err
	}

	return converts.ToDomainRegion(sf), nil
}

func (r *regionRepository) FindAllByCountryCode(ctx context.Context, countryCode string) ([]*domain.Region, error) {
	sf := []*models.Region{}

	projection := bson.M{
		"region_id":                    1,
		"name_full":                    1,
		"name":                         1,
		"type":                         1,
		"country_code":                 1,
		"language":                     1,
		"coordinates.center_latitude":  1,
		"coordinates.center_longitude": 1,
		"version":                      1,
	}

	filter := bson.M{
		"country_code": countryCode,
		"deleted_at":   nil,
	}

	if err := r.db.Find(ctx, r.getCollectionName(), &sf,
		mongodb.WithHint(models.RegionCollectionCountryCodeIndex),
		mongodb.WithProjection(projection),
		mongodb.WithFilter(filter),
	); err != nil {
		return nil, err
	}

	return converts.ToDomainRegions(sf), nil
}

func (r *regionRepository) FindRegionsByRadiusAndTypes(ctx context.Context, longitude, latitude, distance float64, regionTypes []string, language string) ([]*domain.Region, error) {
	pipeline := mongo.Pipeline{
		{
			{Key: "$geoNear", Value: bson.D{
				{Key: "near", Value: bson.D{
					{Key: "type", Value: "Point"},
					{Key: "coordinates", Value: []float64{longitude, latitude}},
				}},
				{Key: "distanceField", Value: "distance"},
				{Key: "maxDistance", Value: distance},
				{Key: "spherical", Value: true},
				{Key: "query", Value: bson.D{
					{Key: "type", Value: bson.D{{Key: "$in", Value: regionTypes}}},
					{Key: "language", Value: language},
				}},
			}},
		},
		{
			{Key: "$project", Value: bson.D{
				{Key: "region_id", Value: 1},
				{Key: "name", Value: 1},
				{Key: "name_full", Value: 1},
				{Key: "type", Value: 1},
				{Key: "language", Value: 1},
				{Key: "distance", Value: 1},
			}},
		},
	}
	var regions []*models.Region
	cursor, err := r.db.Aggregate(ctx, r.getCollectionName(), pipeline, &options.AggregateOptions{
		Hint: models.RegionCollectionGeoCoordinatesIndex,
	})
	if err != nil {
		return nil, err
	}

	defer cursor.Close(ctx)

	if err := cursor.All(ctx, &regions); err != nil {
		return nil, err
	}

	return converts.ToDomainRegions(regions), nil
}
