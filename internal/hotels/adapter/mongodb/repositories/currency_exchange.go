package repositories

import (
	"context"

	"gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"go.mongodb.org/mongo-driver/bson"
)

const currencyExchangeColName = "currency_exchange"

const (
	ceIndexFrom = "__from__"
)

type CurrencyExchangeRepository interface {
	Find(ctx context.Context, from string) ([]*domain.CurrencyExchange, error)
}

type currencyExchangeRepository struct {
	db mongodb.DB
}

func NewCurrencyExchangeRepository(db mongodb.DB) CurrencyExchangeRepository {
	return &currencyExchangeRepository{db}
}

func (r *currencyExchangeRepository) Find(ctx context.Context, from string) ([]*domain.CurrencyExchange, error) {
	m := []*models.CurrencyExchange{}

	filter := bson.M{
		"from": from,
	}

	otps := []mongodb.Option{
		mongodb.WithFilter(filter),
		mongodb.WithHint(ceIndexFrom),
	}

	err := r.db.Find(ctx, currencyExchangeColName, &m, otps...)
	if err != nil {
		return nil, err
	}

	return converts.ToDomainCurrencyExchanges(m), nil
}
