package repositories

import (
	"context"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

const expediaWebhookNotificationColName = "expedia_webhook_notifications"

type ExpediaWebhookNotificationRepository interface {
	Insert(ctx context.Context, req *domain.ExpediaWebhookNotificationRequest) error
	FindByKey(ctx context.Context, key string) (*domain.ExpediaWebhookNotificationRequest, error)
}

type expediaWebhookNotificationRepository struct {
	db mongodb.DB
	expediaWebhookNotificationIndices
}
type expediaWebhookNotificationIndices struct {
	defaultIndex string
	keyIndex     string
}

func newExpediaWebhookNotificationIndices() expediaWebhookNotificationIndices {
	return expediaWebhookNotificationIndices{
		defaultIndex: "_id",
		keyIndex:     "__key__",
	}
}
func NewExpediaWebhookNotificationRepository(db mongodb.DB) ExpediaWebhookNotificationRepository {
	return &expediaWebhookNotificationRepository{
		db,
		newExpediaWebhookNotificationIndices(),
	}
}

func (r *expediaWebhookNotificationRepository) Insert(ctx context.Context, req *domain.ExpediaWebhookNotificationRequest) error {
	data := converts.FromDomainExpediaWebhookNotificationRequest(req)
	data.BeforeCreate()

	if err := r.db.Insert(ctx, expediaWebhookNotificationColName, data); err != nil {
		log.Error("hotelRepository Create err", log.Any("err", err), log.Any("data", data))
		return err
	}
	return nil
}

func (r *expediaWebhookNotificationRepository) FindByKey(ctx context.Context, key string) (*domain.ExpediaWebhookNotificationRequest, error) {
	m := &models.ExpediaWebhookNotificationRequest{}

	filter := bson.M{
		"key": key,
	}

	opts := []mongodb.Option{
		mongodb.WithFilter(filter),
		mongodb.WithHint(r.keyIndex),
	}

	err := r.db.FindOne(ctx, expediaWebhookNotificationColName, &m, opts...)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}

		return nil, errors.Wrap(err, "r.db.FindOne")
	}

	return converts.ToDomainExpediaWebhookNotificationRequest(m), nil
}
