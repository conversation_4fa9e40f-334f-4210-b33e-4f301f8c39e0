package repositories

import (
	"context"
	"errors"

	"gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

type TAMappingsRepository interface {
	FindOneByExternalID(ctx context.Context, externalID string) (*domain.TAMapping, error)
}

type taMappingsIndexes struct {
	indexDefault string
	externalID   string
}

type taMappingsRepository struct {
	db      mongodb.DB
	index   taMappingsIndexes
	colName string
}

func NewTAMappingsRepository(db mongodb.DB) TAMappingsRepository {
	index := taMappingsIndexes{
		indexDefault: "_id_",
		externalID:   "__external_id__",
	}

	colName := "ta_hotel_mappings"

	return &taMappingsRepository{db, index, colName}
}

func (r *taMappingsRepository) FindOneByExternalID(ctx context.Context, externalID string) (*domain.TAMapping, error) {

	filter := bson.M{
		"externalId": externalID,
	}

	m := &models.TAMapping{}

	err := r.db.FindOne(ctx, r.colName, m, mongodb.WithFilter(filter), mongodb.WithHint(r.index.externalID))
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}

		return nil, err
	}

	return converts.ToDomainTAMapping(m), nil
}
