package repositories

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const providerSearchHotelsColName = "provider_search_hotels"

const (
	searchHotelsIndexKey = "__search_key__"
)

type ProviderSearchHotelsRepository interface {
	InsertMany(ctx context.Context, reqs []*domain.HotelSearchResult) error
	FindByKey(ctx context.Context, key string, providers []enum.HotelProvider) ([]*domain.HotelSearchResult, error)
	FindByID(ctx context.Context, key, hotelID, roomID, rateID string) (*domain.HotelSearchResult, error)
	FindByIDWithoutExpire(ctx context.Context, key, hotelID, roomID, rateID, bedOptionID string) (*domain.HotelSearchResult, error)
	UpdateRateInDocument(ctx context.Context, id string, hotelID string, roomID string, rateID string, rateData *domain.HubRateData) error
}

type providerSearchHotelsRepository struct {
	db mongodb.DB
}

func NewProviderSearchHotelsRepository(db mongodb.DB) ProviderSearchHotelsRepository {
	return &providerSearchHotelsRepository{db}
}

func (r *providerSearchHotelsRepository) InsertMany(ctx context.Context, reqs []*domain.HotelSearchResult) error {
	request := make([]mongo.WriteModel, 0, len(reqs))
	for _, req := range reqs {
		m, err := converts.FromDomainHubSearchResult(req)
		if err != nil {
			return errors.Wrap(err, "converts.FromDomainSearchResult")
		}

		m.BeforeCreate()

		request = append(request, mongo.NewInsertOneModel().SetDocument(m))
	}

	if len(request) == 0 {
		return nil
	}

	err := r.db.BulkWriteRaw(ctx, providerSearchHotelsColName, request)
	if err != nil {
		return errors.Wrap(err, "db.BulkWriteRaw")
	}

	return nil
}

func (r *providerSearchHotelsRepository) FindByKey(ctx context.Context, key string, providers []enum.HotelProvider) ([]*domain.HotelSearchResult, error) {
	m := []*models.HotelSearchResult{}

	filter := bson.M{
		"search_key": key,
		"provider": bson.M{
			"$in": providers,
		},
		"expired_at": bson.M{
			"$gt": time.Now().UnixMilli(),
		},
	}

	opts := []mongodb.Option{
		mongodb.WithFilter(filter),
		mongodb.WithHint(searchHotelsIndexKey),
	}

	err := r.db.Find(ctx, providerSearchHotelsColName, &m, opts...)
	if err != nil {
		return nil, errors.Wrap(err, "r.db.Find")
	}

	res := converts.ToDomainHubSearchResults(m)

	return res, nil
}

func (r *providerSearchHotelsRepository) FindByID(ctx context.Context, key, hotelID, roomID, rateID string) (*domain.HotelSearchResult, error) {
	var m *models.HotelSearchResult

	filter := bson.M{
		"search_key":                    key,
		"hotel.hotel_id":                hotelID,
		"hotel.rooms.room_id":           roomID,
		"hotel.rooms.rate_data.rate_id": rateID,
		"expired_at": bson.M{
			"$gt": time.Now().UnixMilli(),
		},
	}

	opts := []mongodb.Option{
		mongodb.WithFilter(filter),
		mongodb.WithHint(searchHotelsIndexKey),
	}

	err := r.db.FindOne(ctx, providerSearchHotelsColName, &m, opts...)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, errors.Wrap(err, "r.db.Find")
	}

	res := converts.ToDomainHubSearchResult(m)

	return res, nil
}

func (r *providerSearchHotelsRepository) FindByIDWithoutExpire(ctx context.Context, key, hotelID, roomID, rateID, bedOptionID string) (*domain.HotelSearchResult, error) {
	var m *models.HotelSearchResult

	filter := bson.M{
		"search_key":                                  key,
		"hotel.hotel_id":                              hotelID,
		"hotel.rooms.room_id":                         roomID,
		"hotel.rooms.rate_data.rate_id":               rateID,
		"hotel.rooms.rate_data.bed_options.option_id": bedOptionID,
	}

	opts := []mongodb.Option{
		mongodb.WithFilter(filter),
		mongodb.WithHint(searchHotelsIndexKey),
	}

	err := r.db.FindOneWithRetry(ctx, constants.MongoRetryCount, constants.MongoRetryDelay, providerSearchHotelsColName, &m, opts...)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, errors.Wrap(err, "r.db.Find")
	}

	res := converts.ToDomainHubSearchResult(m)

	return res, nil
}

func (r *providerSearchHotelsRepository) UpdateRateInDocument(ctx context.Context, id string, hotelID string, roomID string, rateID string, rateData *domain.HubRateData) error {
	objID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return errors.Wrap(err, "primitive.ObjectIDFromHex")
	}

	filter := bson.M{
		"_id": objID,
	}

	var update bson.M

	switch {
	case rateData == nil:
		// If rateData is nil, remove the rate
		update = bson.M{
			"$pull": bson.M{
				"hotel.$[hotel].rooms.$[room].rate_data": bson.M{"rate_id": rateID},
			},
			"$set": bson.M{
				"updated_at": time.Now().UnixMilli(),
			},
		}
	case rateData.IsSoldOut:
		// If rate is sold out, only update the IsSoldOut field
		update = bson.M{
			"$set": bson.M{
				"hotel.$[hotel].rooms.$[room].rate_data.$[rate].is_sold_out": true,
				"updated_at": time.Now().UnixMilli(),
			},
		}
	default:
		// Update the entire rate data
		rateDataModel := converts.FromDomainHubRateData(rateData)

		update = bson.M{
			"$set": bson.M{
				"hotel.$[hotel].rooms.$[room].rate_data.$[rate]": rateDataModel,
				"updated_at": time.Now().UnixMilli(),
			},
		}
	}

	// Set array filters to target the specific rate
	arrayFilters := options.ArrayFilters{
		Filters: []interface{}{
			bson.M{"hotel.hotel_id": hotelID},
			bson.M{"room.room_id": roomID},
			bson.M{"rate.rate_id": rateID},
		},
	}

	updateOpts := &options.UpdateOptions{
		ArrayFilters: &arrayFilters,
		Hint:         searchHotelsIndexKey,
	}

	return r.db.UpdateOneRaw(ctx, providerSearchHotelsColName, filter, update, updateOpts)
}

// Use for sorting
type HotelSlice []*models.Hotel

func (h HotelSlice) Len() int {
	return len(h)
}

func (h HotelSlice) Less(i, j int) bool {
	if h[i].Ratings == nil || h[i].Ratings.Guest == nil {
		return false
	}
	if h[j].Ratings == nil || h[j].Ratings.Guest == nil {
		return true
	}
	return h[i].Ratings.Guest.Overall > h[j].Ratings.Guest.Overall
}

func (h HotelSlice) Swap(i, j int) {
	h[i], h[j] = h[j], h[i]
}
