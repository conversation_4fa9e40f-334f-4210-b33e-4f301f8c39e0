package repositories

import (
	"context"
	"errors"
	"fmt"
	"time"

	"gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/apps/common/log"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
)

const (
	hiddenHiddenServiceFeeCollection string = "hidden_service_fees"
)

type HiddenServiceFeeRepository interface {
	Create(ctx context.Context, i *domain.HiddenServiceFee) (string, error)
	Update(ctx context.Context, i *domain.HiddenServiceFee) (*domain.HiddenServiceFee, error)
	FindOne(ctx context.Context, req *domain.HiddenServiceFee) (*domain.HiddenServiceFee, error)
	// FindByHotelID(ctx context.Context, hotelID, officeID string) (*domain.HiddenServiceFee, error)
	FindAllByCondition(ctx context.Context, req *domain.ListHiddenServiceFeeReq) ([]*domain.HiddenServiceFee, error)
	FindAllByConditionAndFilter(ctx context.Context, req *domain.ListHiddenServiceFeeReq) ([]*domain.HiddenServiceFee, error)
	Delete(ctx context.Context, ID, userID string) error
	FindByID(ctx context.Context, id string) (*domain.HiddenServiceFee, error)
	Deletes(ctx context.Context, ids []string, userID string) error
}

type hiddenHiddenServiceFeeRepository struct {
	db mongodb.DB
}

func NewHiddenServiceFeeRepository(db mongodb.DB) HiddenServiceFeeRepository {
	return &hiddenHiddenServiceFeeRepository{
		db: db,
	}
}

// func (r *hiddenHiddenServiceFeeRepository) FindByHotelID(ctx context.Context, hotelID, officeID string) (*domain.HiddenServiceFee, error) {
// 	var e *models.HiddenServiceFee

// 	filter := bson.M{
// 		"deleted_at": bson.M{
// 			"$exists": false,
// 		},
// 		"office_id": officeID,
// 		"hotel_id":  hotelID,
// 	}

// 	err := r.db.FindOne(ctx, hiddenHiddenServiceFeeCollection, &e,
// 		mongodb.WithHint(models.DefaultIndex),
// 		mongodb.WithFilter(filter))

// 	if errors.Is(err, mongo.ErrNoDocuments) {
// 		return nil, nil
// 	}

// 	if err != nil {
// 		return nil, fmt.Errorf("FindOne : %w", err)
// 	}

// 	return converts.ToDomainHiddenServiceFee(e), nil
// }

func (r *hiddenHiddenServiceFeeRepository) FindAllByCondition(ctx context.Context, req *domain.ListHiddenServiceFeeReq) ([]*domain.HiddenServiceFee, error) {
	var e []*models.HiddenServiceFee

	filter := bson.M{
		"deleted_at": bson.M{
			"$exists": false,
		},
	}

	if req.OfficeID != "" {
		filter["office_id"] = bson.M{
			"$in": []string{"", req.OfficeID},
		}
	}

	if req.OfficeID == "*" { // filter config all office
		filter["office_id"] = ""
	}

	// if req.Provider != enum.HotelProviderNone && req.Provider != 0 {
	// 	filter["provider"] = bson.M{
	// 		"$in": []enum.HotelProvider{req.Provider, enum.HotelProviderNone},
	// 	}
	// }

	if len(req.Rating) > 0 && req.Rating[0] != -1 { // -1 is filter all rating(1,2,3,4,5,0,"")
		filter["config.rating"] = bson.M{
			"$in": req.Rating,
		}
	}

	if len(req.Rating) > 0 && req.Rating[0] == -1 { // -1 is filter all rating(1,2,3,4,5,0,"")
		filter["config.rating"] = nil
	}

	if len(req.CountryCode) > 0 {
		filter["config.country_code"] = bson.M{
			"$in": req.CountryCode,
		}
	}

	if len(req.CountryCode) > 0 && req.CountryCode[0] == "*" {
		filter["config.country_code"] = nil
	}

	if len(req.HotelType) > 0 && req.HotelType[0] != "*" {
		filter["config.hotel_type"] = bson.M{
			"$in": req.HotelType,
		}
	}

	if len(req.HotelType) > 0 && req.HotelType[0] == "*" { // array empty is apply all hotel type
		filter["config.hotel_type"] = nil
	}

	if req.LocationType != enum.HotelTypeNone {
		filter["config.location_type"] = req.LocationType
	}

	if req.Type != enum.HiddenFeeTypeNone {
		filter["type"] = req.Type
	}

	if req.HotelName != "" {
		filter["hotel_name"] = bson.M{"$regex": req.HotelName, "$options": "i"}
	}

	options := []mongodb.Option{
		mongodb.WithHint(models.DefaultIndex),
		mongodb.WithFilter(filter),
	}

	if req.Pagination != nil {
		options = append(options, mongodb.WithPaging(req.Pagination))
	}

	err := r.db.Find(ctx, hiddenHiddenServiceFeeCollection, &e,
		options...)

	if errors.Is(err, mongo.ErrNoDocuments) {
		return nil, nil
	}

	if err != nil {
		return nil, fmt.Errorf("FindOne : %w", err)
	}

	return converts.ToDomainHiddenServiceFees(e), nil
}

func (r *hiddenHiddenServiceFeeRepository) FindAllByConditionAndFilter(ctx context.Context, req *domain.ListHiddenServiceFeeReq) ([]*domain.HiddenServiceFee, error) {
	var e []*models.HiddenServiceFee

	filter := bson.M{
		"deleted_at": bson.M{
			"$exists": false,
		},
		"office_id": req.OfficeID,
	}

	if req.OfficeID == "*" { // filter config all office
		filter["office_id"] = ""
	}

	if req.Provider != enum.HotelProviderNone && req.Provider != 0 {
		filter["provider"] = bson.M{
			"$in": []enum.HotelProvider{req.Provider, enum.HotelProviderNone},
		}
	}

	if len(req.Rating) > 0 {
		if req.Rating[0] == -1 { // array empty is apply config all hotel type
			filter["config.rating"] = nil
		} else {
			filter["config.rating"] = bson.M{
				"$in": req.Rating,
			}
		}
	}

	if len(req.CountryCode) > 0 {
		if req.CountryCode[0] == "*" { // array empty is apply config all hotel type
			filter["config.country_code"] = nil
		} else {
			filter["config.country_code"] = bson.M{
				"$in": req.CountryCode,
			}
		}
	}

	if len(req.HotelType) > 0 {
		if req.HotelType[0] == "*" { // array empty is apply config all hotel type
			filter["config.hotel_type"] = nil
		} else {
			filter["config.hotel_type"] = bson.M{
				"$in": req.HotelType,
			}
		}
	}

	if req.LocationType != enum.HotelTypeNone {
		filter["config.location_type"] = req.LocationType
	}

	if req.Type != enum.HiddenFeeTypeNone {
		filter["type"] = req.Type
	}

	if req.HotelName != "" {
		filter["hotel_name"] = bson.M{"$regex": req.HotelName, "$options": "i"}
	}

	options := []mongodb.Option{
		mongodb.WithHint(models.DefaultIndex),
		mongodb.WithFilter(filter),
	}

	if req.Pagination != nil {
		options = append(options, mongodb.WithPaging(req.Pagination))
	}

	err := r.db.Find(ctx, hiddenHiddenServiceFeeCollection, &e,
		options...)

	if errors.Is(err, mongo.ErrNoDocuments) {
		return nil, nil
	}

	if err != nil {
		return nil, fmt.Errorf("FindOne : %w", err)
	}

	return converts.ToDomainHiddenServiceFees(e), nil
}

func (r *hiddenHiddenServiceFeeRepository) Create(ctx context.Context, i *domain.HiddenServiceFee) (string, error) {
	doc := converts.FromDomainHiddenServiceFee(i)

	doc.BeforeCreate()

	if err := r.db.Insert(ctx, hiddenHiddenServiceFeeCollection, doc); err != nil {
		return "", fmt.Errorf("Insert : %w", err)
	}

	return doc.ID.Hex(), nil
}

func (r *hiddenHiddenServiceFeeRepository) Update(ctx context.Context, i *domain.HiddenServiceFee) (*domain.HiddenServiceFee, error) {
	doc := converts.FromDomainHiddenServiceFee(i)

	doc.BeforeUpdate()

	if err := r.db.UpdateOne(ctx, hiddenHiddenServiceFeeCollection, bson.M{
		"_id": doc.ID,
	}, doc, &options.UpdateOptions{Hint: models.DefaultIndex}); err != nil {
		return nil, fmt.Errorf("UpdateOne : %w", err)
	}

	return converts.ToDomainHiddenServiceFee(doc), nil
}

func (r *hiddenHiddenServiceFeeRepository) Delete(ctx context.Context, ID, userID string) error {
	objID, err := primitive.ObjectIDFromHex(ID)
	if err != nil {
		log.Error("primitive.ObjectIDFromHex err", log.Any("err", err), log.String("ID", ID))
		return err
	}

	userObjID, err := primitive.ObjectIDFromHex(userID)
	if err != nil {
		log.Error("primitive.ObjectIDFromHex err", log.Any("err", err), log.String("userID", userID))
		return err
	}

	if err := r.db.UpdateOneRaw(ctx, hiddenHiddenServiceFeeCollection, bson.M{
		"_id": objID,
	}, bson.M{"$set": bson.M{
		"updated_at": time.Now().UnixMilli(),
		"updated_by": userObjID,
		"deleted_at": time.Now().UnixMilli(),
	}}, &options.UpdateOptions{Hint: models.DefaultIndex}); err != nil {
		return fmt.Errorf("Delete : %w", err)
	}

	return nil
}

func (r *hiddenHiddenServiceFeeRepository) FindByID(ctx context.Context, id string) (*domain.HiddenServiceFee, error) {
	objID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		log.Error("primitive.ObjectIDFromHex err", log.Any("err", err), log.String("ID", id))
		return nil, err
	}

	var e *models.HiddenServiceFee

	filter := bson.M{
		"_id": objID,
		"deleted_at": bson.M{
			"$exists": false,
		},
	}

	err = r.db.FindOne(ctx, hiddenHiddenServiceFeeCollection, &e,
		mongodb.WithHint(models.DefaultIndex),
		mongodb.WithFilter(filter))

	if errors.Is(err, mongo.ErrNoDocuments) {
		return nil, nil
	}

	if err != nil {
		return nil, fmt.Errorf("FindOne : %w", err)
	}

	return converts.ToDomainHiddenServiceFee(e), nil
}

func (r *hiddenHiddenServiceFeeRepository) Deletes(ctx context.Context, ids []string, userID string) error {
	objIDs := make([]primitive.ObjectID, 0, len(ids))
	for _, id := range ids {
		objID, err := primitive.ObjectIDFromHex(id)
		if err != nil {
			log.Error("primitive.ObjectIDFromHex err", log.Any("err", err), log.String("ID", id))
			return err
		}
		objIDs = append(objIDs, objID)
	}

	userObjID, err := primitive.ObjectIDFromHex(userID)
	if err != nil {
		log.Error("primitive.ObjectIDFromHex err", log.Any("err", err), log.String("userID", userID))
		return err
	}

	if err := r.db.UpdateManyRaw(ctx, hiddenHiddenServiceFeeCollection, bson.M{
		"_id": bson.M{
			"$in": objIDs,
		},
	}, bson.M{"$set": bson.M{
		"updated_at": time.Now().UnixMilli(),
		"updated_by": userObjID,
		"deleted_at": time.Now().UnixMilli(),
	}}, &options.UpdateOptions{Hint: models.DefaultIndex}); err != nil {
		return fmt.Errorf("Delete : %w", err)
	}

	return nil
}

func (r *hiddenHiddenServiceFeeRepository) FindOne(ctx context.Context, req *domain.HiddenServiceFee) (*domain.HiddenServiceFee, error) {
	var e *models.HiddenServiceFee
	filter := bson.M{
		"deleted_at": bson.M{
			"$exists": false,
		},
		"office_id":  req.OfficeID,
		"hotel_id":   req.HotelID,
		"hotel_name": req.HotelName,
	}

	if req.Type != enum.HiddenFeeTypeNone {
		filter["type"] = req.Type
	}

	if req.Provider != enum.HotelProviderNone && req.Provider != 0 {
		filter["provider"] = req.Provider
	}

	if req.Config != nil {
		if len(req.Config.Rating) > 0 {
			filter["config.rating"] = bson.M{
				"$all": req.Config.Rating,
			}
		} else {
			filter["config.rating"] = nil
		}

		if len(req.Config.CountryCode) > 0 {
			filter["config.country_code"] = bson.M{
				"$all": req.Config.CountryCode,
			}
		} else {
			filter["config.country_code"] = nil
		}

		if len(req.Config.HotelType) > 0 {
			filter["config.hotel_type"] = bson.M{
				"$all": req.Config.HotelType,
			}
		} else {
			filter["config.hotel_type"] = nil
		}

		if req.Config.LocationType != enum.HotelTypeNone {
			filter["config.location_type"] = req.Config.LocationType
		}
	}

	err := r.db.FindOne(ctx, hiddenHiddenServiceFeeCollection, &e,
		mongodb.WithHint(models.DefaultIndex),
		mongodb.WithFilter(filter))
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}

		return nil, err
	}

	return converts.ToDomainHiddenServiceFee(e), nil
}
