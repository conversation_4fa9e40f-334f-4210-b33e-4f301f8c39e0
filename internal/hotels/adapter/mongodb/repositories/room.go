package repositories

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const roomCollectionName = "hotel_rooms"

type RoomRepository interface {
	Create(ctx context.Context, req *domain.Room) error
	WithTransaction(ctx context.Context, fn func(tnxSession context.Context) (interface{}, error)) error
	CreateMany(ctx context.Context, rooms []*domain.Room, language string) error
	// FindByRoomID(ctx context.Context, hotelID, language string) (*domain.Room, error)
	// FindByRoomByExpediaID(ctx context.Context, expediaID, language string) (*domain.Room, error)
	// FindByProviderAndContentVersionHotelID(ctx context.Context, provider enum.HotelProvider, contentVersion, roomID, language string) (*domain.Room, error)
	MigrateHotelRoomName(ctx context.Context, page, from, to int, targetLanguage, sourceLanguage string) error
	Update(ctx context.Context, input *domain.Room) error
	FindByRoomIDAndHotelRef(ctx context.Context, hotelID, roomID, language string) (*domain.Room, error)
	FindByRoomIDsV2(ctx context.Context, provider enum.HotelProvider, hotelID, language string) ([]*domain.Room, error)
	FindByRoomIDs(ctx context.Context, roomIDs []string) ([]*domain.Room, error)
}

type roomRepository struct {
	db  mongodb.DB
	cfg *config.Schema
}

func NewRoomRepository(db mongodb.DB, cfg *config.Schema,
) RoomRepository {
	return &roomRepository{
		db:  db,
		cfg: cfg,
	}
}

func (r *roomRepository) getCollectionName() string {
	if r.cfg.ContentVersion != "1" {
		return "hotel_rooms_v2"
	}

	switch enum.HotelProviderValue[r.cfg.EnableProvider] {
	case enum.HotelProviderExpedia:
		return "expedia_hotel_rooms"
	}

	return "hotel_rooms"
}

func (r *roomRepository) Create(ctx context.Context, req *domain.Room) error {
	data := converts.FromDomainRoom(req)
	data.BeforeCreate()

	if err := r.db.Insert(ctx, r.getCollectionName(), data); err != nil {
		return errors.Wrap(err, "db.Insert")
	}
	return nil
}

func (r *roomRepository) Update(ctx context.Context, input *domain.Room) error {
	data := converts.FromDomainRoom(input)
	data.BeforeUpdate()

	objID, err := primitive.ObjectIDFromHex(input.ID)
	if err != nil {
		return errors.Wrap(err, "id ObjectIDFromHex")
	}

	filter := bson.M{
		"_id": objID,
	}

	if err := r.db.UpdateOne(ctx, r.getCollectionName(), filter, data, &options.UpdateOptions{
		Hint: models.DefaultIndex,
	}); err != nil {
		log.Error("roomRepository Update err", log.Any("err", err), log.Any("data", data.Name))
		return err
	}

	return nil
}

func (r *roomRepository) CreateMany(ctx context.Context, rooms []*domain.Room, language string) error {
	request := make([]mongo.WriteModel, len(rooms))
	for i, req := range rooms {
		m := converts.FromDomainRoom(req)
		m.Base.BeforeCreate()
		if language != "" {
			m.Language = language
		}

		request[i] = mongo.NewInsertOneModel().
			SetDocument(m)
	}
	err := r.db.BulkWriteRaw(ctx, r.getCollectionName(), request)
	if err != nil {
		return err
	}

	return nil
}

func (r *roomRepository) WithTransaction(ctx context.Context, fn func(tnxSession context.Context) (interface{}, error)) error {
	_, err := r.db.WithTransaction(ctx, fn)
	if err != nil {
		return err
	}
	return nil
}

func (r *roomRepository) FindByRoomID(ctx context.Context, roomID, language string) (*domain.Room, error) {
	var sf *models.Room

	filter := bson.M{
		"room_id":  roomID,
		"language": language,
	}

	if err := r.db.FindOne(ctx, r.getCollectionName(), &sf,
		mongodb.WithHint(models.RoomCollectionRoomIDLanguageIndex),
		mongodb.WithFilter(filter)); err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, err
	}

	return converts.ToDomainRoom(sf), nil
}

func (r *roomRepository) FindByRoomIDAndHotelRef(ctx context.Context, hotelID, roomID, language string) (*domain.Room, error) {
	var sf *models.Room

	objPID, err := primitive.ObjectIDFromHex(hotelID)
	if err != nil {
		log.Error("FindByRoomIDAndHotelRef primitive.ObjectIDFromHex err", log.Any("err", err), log.String("hotel_id", hotelID))
		return nil, errors.Wrap(err, "primitive.ObjectIDFromHex")
	}

	filter := bson.M{
		"hotel_ref": objPID,
		"room_id":   roomID,
		"language":  language,
	}

	if err := r.db.FindOne(ctx, r.getCollectionName(), &sf,
		mongodb.WithHint(models.RoomCollectionRoomIDLanguageIndex),
		mongodb.WithFilter(filter)); err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, err
	}

	return converts.ToDomainRoom(sf), nil
}

func (r *roomRepository) FindByRoomIDs(ctx context.Context, roomIDs []string) ([]*domain.Room, error) {
	var sf []*models.Room
	ids, err := mongodb.IdsToObjectIds(roomIDs)
	if err != nil {
		return nil, fmt.Errorf("ObjectIDFromHex error : %w", err)
	}

	if err := r.db.Find(ctx, r.getCollectionName(), &sf,
		mongodb.WithHint(models.DefaultIndex),
		mongodb.WithFilter(bson.M{
			"_id": bson.M{
				"$in": ids,
			},
		})); err != nil {
		return nil, err
	}

	return converts.ToDomainRooms(sf), nil
}

func (r *roomRepository) FindByRoomIDsV2(ctx context.Context, provider enum.HotelProvider, hotelID, language string) ([]*domain.Room, error) {
	var sf []*models.Room

	fitler := bson.M{
		"hotel_id": hotelID,
		"language": language,
	}

	if provider != enum.HotelProviderNone {
		pKey := fmt.Sprintf("provider_ids.%d", provider)
		fitler[pKey] = bson.M{"$exists": true}
	}

	if err := r.db.Find(ctx, r.getCollectionName(), &sf,
		mongodb.WithHint(models.HotelRoomV2_HotelIDLanguage),
		mongodb.WithFilter(fitler)); err != nil {
		return nil, err
	}

	return converts.ToDomainRooms(sf), nil
}

func (r *roomRepository) FindByRoomByExpediaID(ctx context.Context, expediaID, language string) (*domain.Room, error) {
	var sf *models.Room
	if err := r.db.FindOne(ctx, r.getCollectionName(), &sf,
		mongodb.WithHint(models.CollectionProviderExpediaIDIndex),
		mongodb.WithFilter(bson.M{
			"provider_ids.11": expediaID,
			"language":        language,
		})); err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, err
	}

	return converts.ToDomainRoom(sf), nil
}

func (r *roomRepository) FindByProviderAndContentVersionHotelID(ctx context.Context, provider enum.HotelProvider, contentVersion, roomID, language string) (*domain.Room, error) {
	var sf *models.Room

	if roomID == "" {
		return converts.ToDomainRoom(sf), nil
	}

	providerKey := fmt.Sprintf("provider_ids.%d", provider)
	filter := bson.M{
		providerKey: roomID,
		"language":  language,
	}

	if err := r.db.FindOne(ctx, r.getCollectionName(), &sf,
		mongodb.WithHint(models.DefaultIndex),
		mongodb.WithFilter(filter)); err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, err
	}

	return converts.ToDomainRoom(sf), nil
}

func (r *roomRepository) MigrateHotelRoomName(ctx context.Context, page, from, to int, targetLanguage, sourceLanguage string) error {
	test, err := mongo.Connect(ctx, options.Client().ApplyURI(r.cfg.CommonReadURL))
	if err != nil {
		return err
	}

	skip := int64(from)

	cursor, err := test.Database(r.cfg.CommonMongoDB).Collection(roomCollectionName).Find(ctx, bson.M{
		"language": sourceLanguage,
	}, &options.FindOptions{
		Hint: models.RoomCollectionLanguageIndex,
		Skip: &skip,
	})

	if err != nil {
		return err
	}
	defer cursor.Close(ctx)

	count := from
	for cursor.Next(ctx) && count < to {
		var enDoc struct {
			RoomID string `bson:"room_id"`
			Name   string `bson:"name"`
		}

		if err := cursor.Decode(&enDoc); err != nil {
			return err
		}

		roomId := enDoc.RoomID
		nameToMigrate := enDoc.Name

		fmt.Println("sourceLanguage", sourceLanguage, roomId)

		// Step 3: Update the document with language "vi-VN" for the same room_id
		_, err := test.Database("common-db").Collection(roomCollectionName).UpdateOne(
			ctx,
			bson.M{"language": targetLanguage, "room_id": roomId},
			bson.M{"$set": bson.M{"name": nameToMigrate, "updated_at": time.Now().UnixMilli()}},
			&options.UpdateOptions{
				Hint: models.RoomCollectionRoomIDLanguageIndex,
			},
		)
		if err != nil {
			return err
		}

		fmt.Printf("done %d/%d record\n", count, to)
		count++
	}

	// Check for errors during iteration
	if err := cursor.Err(); err != nil {
		return err
	}

	return nil
}
