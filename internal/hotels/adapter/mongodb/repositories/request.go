package repositories

import (
	"context"
	"encoding/json"
	"encoding/xml"
	"fmt"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const (
	requestCollectionName string = "requests"
	chunkSizeInByte       int32  = 15 * 1024 * 1024
	gridFSBucketName      string = "requests"
)

type Request struct {
	Provider   enum.HotelProvider
	RequestID  string
	Path       string
	Method     string
	Body       interface{}
	Headers    map[string]string
	Response   []byte
	StatusCode int
	Duration   int64
	Action     string
	IsJson     bool
	ErrorMsg   error
	TracingID  string
}

type RequestRepository interface {
	Create(ctx context.Context, req *Request) error
}

type requestRepository struct {
	db  mongodb.DB
	cfg *config.Schema
}

func NewRequestRepository(db mongodb.DB, cfg *config.Schema,
) RequestRepository {
	return &requestRepository{
		db:  db,
		cfg: cfg,
	}
}

func (r *requestRepository) Create(ctx context.Context, req *Request) error {
	body := []byte(fmt.Sprintf("%v", req.Body))

	if fmt.Sprintf("%T", req.Body) != "string" {
		if req.IsJson {
			jsonBody, err := json.Marshal(req.Body)
			if err == nil {
				body = jsonBody
			}
		} else {
			xmlBody, err := xml.Marshal(req.Body)
			if err == nil {
				body = xmlBody
			}
		}
	}

	var data *models.LogRequest

	bucketOpts := &options.BucketOptions{}
	uploadOpts := &options.UploadOptions{}
	bucketOpts.SetName(gridFSBucketName)
	bucketOpts.SetChunkSizeBytes(chunkSizeInByte)
	uploadOpts.SetChunkSizeBytes(chunkSizeInByte)

	wResponse, err := r.db.WriteFile(ctx, req.RequestID, req.Response, bucketOpts, uploadOpts)
	if err != nil {
		return errors.Wrap(err, "requestRepository.Create - Can not create gridfs request's response")
	}

	data = &models.LogRequest{
		RequestID:    req.RequestID,
		Provider:     req.Provider,
		RelativePath: req.Path,
		Method:       req.Method,
		Body:         string(body),
		Headers:      req.Headers,
		Response:     wResponse,
		Duration:     req.Duration,
		StatusCode:   req.StatusCode,
		Base:         models.Base{},
		Action:       req.Action,
		TracingID:    req.TracingID,
	}

	if str, ok := req.Body.(string); ok {
		data.Body = str
	}

	if req.ErrorMsg != nil {
		data.ErrorMsg = req.ErrorMsg.Error()
	}

	data.BeforeCreate()

	if err := r.db.Insert(ctx, requestCollectionName, data); err != nil {
		return errors.Wrap(err, "db.Insert")
	}
	return nil

}
