package repositories

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

const polygonCollectionName = "polygons"

type PolygonRepository interface {
	Create(ctx context.Context, req *domain.BoundingPolygon) (string, error)
	WithTransaction(ctx context.Context, fn func(tnxSession context.Context) (interface{}, error)) error
	CreateMany(ctx context.Context, polygons []*domain.BoundingPolygon) error
	FindByPolygonID(ctx context.Context, id string) (*domain.BoundingPolygon, error)
}

type polygonRepository struct {
	db  mongodb.DB
	cfg *config.Schema
}

func NewPolygonRepository(db mongodb.DB, cfg *config.Schema,
) PolygonRepository {
	return &polygonRepository{
		db:  db,
		cfg: cfg,
	}
}

func (r *polygonRepository) Create(ctx context.Context, req *domain.BoundingPolygon) (string, error) {
	data := converts.FromDomainPolygon(req)
	data.BeforeCreate()

	if err := r.db.Insert(ctx, polygonCollectionName, data); err != nil {
		return "", errors.Wrap(err, "db.Insert")
	}
	return data.ID.Hex(), nil

}

func (r *polygonRepository) CreateMany(ctx context.Context, polygons []*domain.BoundingPolygon) error {
	request := make([]mongo.WriteModel, len(polygons))
	for i, req := range polygons {
		m := converts.FromDomainPolygon(req)
		m.Base.BeforeCreate()
		filter := bson.M{
			"_id": m.ID,
		}
		update := bson.M{
			"$set": m,
		}
		request[i] = mongo.NewUpdateOneModel().
			SetFilter(filter).
			SetUpsert(true).
			SetHint(models.DefaultIndex).
			SetUpdate(update)
	}
	err := r.db.BulkWriteRaw(ctx, polygonCollectionName, request)
	if err != nil {
		return err
	}

	return nil
}

func (r *polygonRepository) WithTransaction(ctx context.Context, fn func(tnxSession context.Context) (interface{}, error)) error {
	_, err := r.db.WithTransaction(ctx, fn)
	if err != nil {
		return err
	}
	return nil
}

func (r *polygonRepository) FindByPolygonID(ctx context.Context, polygonID string) (*domain.BoundingPolygon, error) {
	var sf *models.BoundingPolygon
	pOID, err := primitive.ObjectIDFromHex(polygonID)
	if err != nil {
		return nil, fmt.Errorf("UpdateStatus ObjectIDFromHex pID %w", err)
	}
	if err := r.db.FindOne(ctx, polygonCollectionName, &sf,
		mongodb.WithHint(models.DefaultIndex),
		mongodb.WithFilter(bson.M{
			"_id": pOID,
		})); err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, err
	}

	return converts.ToDomainPolygon(sf), nil
}
