package converts

import (
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

func FromDomainHubHolderDetails(in []*domain.HolderDetail) []*models.HolderDetail {
	result := make([]*models.HolderDetail, 0, len(in))

	for _, item := range in {
		if item != nil {
			result = append(result, &models.HolderDetail{
				OccupancyIndex: item.OccupancyIndex,
				GivenName:      item.GivenName,
				Surname:        item.Surname,
				Email:          item.Email,
				SpecialRequest: item.SpecialRequest,
			})
		}
	}

	return result
}

func ToDomainHubHolderDetails(in []*models.HolderDetail) []*domain.HolderDetail {
	result := make([]*domain.HolderDetail, 0, len(in))

	for _, item := range in {
		if item != nil {
			result = append(result, &domain.HolderDetail{
				OccupancyIndex: item.OccupancyIndex,
				GivenName:      item.GivenName,
				Surname:        item.Surname,
				Email:          item.Email,
				SpecialRequest: item.SpecialRequest,
			})
		}
	}

	return result
}

func FromDomainHubHolderInfo(in *domain.HubHolderInfo) *models.HubHolderInfo {
	if in == nil {
		return nil
	}

	return &models.HubHolderInfo{
		PhoneNumber:  in.PhoneNumber,
		PhoneCode:    in.PhoneCode,
		HolderDetail: FromDomainHubHolderDetails(in.HolderDetail),
	}
}

func FromDomainBookingRequest(in *domain.HubPriceCheckReq) *models.BookingRequest {
	if in == nil {
		return nil
	}

	return &models.BookingRequest{
		SearchKey:   in.SearchKey,
		HotelID:     in.HotelID,
		RoomID:      in.RoomID,
		RateID:      in.RateID,
		OfficeID:    in.OfficeID,
		Currency:    in.Currency,
		BedOptionID: in.BedOptionID,
		PNRCode:     in.PNRCode,
	}
}

func FromDomainRefundData(in *domain.RefundData) *models.RefundData {
	if in == nil {
		return nil
	}

	return &models.RefundData{
		ProviderRefundAmount: in.ProviderRefundAmount,
		RefundAmount:         in.RefundAmount,
		PenaltyAmount:        in.PenaltyAmount,
		Currency:             in.Currency,
	}
}

func ToDomainRefundData(in *models.RefundData) *domain.RefundData {
	if in == nil {
		return nil
	}

	return &domain.RefundData{
		ProviderRefundAmount: in.ProviderRefundAmount,
		RefundAmount:         in.RefundAmount,
		PenaltyAmount:        in.PenaltyAmount,
		Currency:             in.Currency,
	}
}

func FromDomainHubHotelOrder(order *domain.HubHotelOrder) *models.HubHotelOrder {
	if order == nil {
		return nil
	}

	return &models.HubHotelOrder{
		Base:                   models.Base{},
		SessionID:              order.SessionID,
		OrderCode:              order.OrderCode,
		ReservationCode:        order.ReservationCode,
		ProviderBookingStatus:  order.ProviderBookingStatus,
		OfficeID:               order.OfficeID,
		AgentCode:              order.AgentCode,
		Hotel:                  FromDomainHubOrderHotelItem(order.Hotel),
		RateData:               FromDomainHubRateData(order.RateData),
		OriginalRateDataCf:     FromDomainHubRateData(order.OriginalRateDataCf),
		ExchangedRateDataCfRaw: FromDomainHubRateData(order.ExchangedRateDataCfRaw),
		ExchangedRateDataCf:    FromDomainHubRateData(order.ExchangedRateDataCf),
		Fee:                    FromDomainFee(order.Fee),
		ExchangeRateApply:      FromDomainCurrencyExchange(order.ExchangeRateApply),
		RequestHolder:          FromDomainHubHolderInfo(order.RequestHolder),
		Provider:               order.Provider,
		OldProvider:            order.OldProvider,
		LastConfirmDate:        order.LastConfirmDate,
		BookingRequest:         FromDomainBookingRequest(order.BookingRequest),
		OrderPaymentID:         order.OrderPaymentID,
		LastTransactionID:      order.LastTransactionID,
		CustomerIP:             order.CustomerIP,
		HubOrderStatus:         order.HubOrderStatus,
		BookingStatus:          order.BookingStatus,
		PendingDeadline:        order.PendingDeadline,
		CancelDeadline:         order.CancelDeadline,
		ManualIssuing:          order.ManualIssuing,
		HotelSearchRequest:     FromDomainHotelSearchRequest(order.HotelSearchRequest),
		FareDataIssuing:        FromDomainFareDataIssuing(order.FareDataIssuing),
		PendingStartAt:         order.PendingStartAt,
		RefundData:             FromDomainRefundData(order.RefundData),
		ExchangedRefundData:    FromDomainRefundData(order.ExchangedRefundData),
		Refunded:               order.Refunded,
		CancelingStartAt:       order.CancelingStartAt,
		SkipScanCheckCancel:    order.SkipScanCheckCancel,
		SearchKey:              order.SearchKey,
		CancelReason:           order.CancelReason,
		NewSessionID:           order.NewSessionID,
		TABookingID:            order.TABookingID,
	}
}

func FromDomainFareDataIssuing(in *domain.FareDataIssuing) *models.FareDataIssuing {
	if in == nil {
		return nil
	}

	return &models.FareDataIssuing{
		Amount:       in.Amount,
		AmountString: in.AmountString,
		Currency:     in.Currency,
		Type:         in.Type,
	}
}

func ToDomainHubHolderInfo(in *models.HubHolderInfo) *domain.HubHolderInfo {
	if in == nil {
		return nil
	}

	return &domain.HubHolderInfo{
		PhoneNumber:  in.PhoneNumber,
		PhoneCode:    in.PhoneCode,
		HolderDetail: ToDomainHubHolderDetails(in.HolderDetail),
	}
}

func ToDomainHubBookingRequest(in *models.BookingRequest) *domain.HubPriceCheckReq {
	if in == nil {
		return nil
	}

	return &domain.HubPriceCheckReq{
		OfficeID:  in.OfficeID,
		SearchKey: in.SearchKey,
		HotelID:   in.HotelID,
		RoomID:    in.RoomID,
		RateID:    in.RateID,
		Currency:  in.Currency,
		Stateful:  true,
		PNRCode:   in.PNRCode,
	}
}

func ToDomainHubRoomBedOption(in *models.RoomBedOption) *domain.RoomBedOption {
	if in == nil {
		return nil
	}

	return &domain.RoomBedOption{
		OptionID:   in.OptionID,
		Name:       in.Name,
		Quantity:   in.Quantity,
		BedConfigs: ToDomainBedConfigurations(in.BedConfigs),
	}
}

func FromDomainHubRoomBedOption(in *domain.RoomBedOption) *models.RoomBedOption {
	if in == nil {
		return nil
	}

	return &models.RoomBedOption{
		OptionID:   in.OptionID,
		Name:       in.Name,
		Quantity:   in.Quantity,
		BedConfigs: FromDomainBedConfigurations(in.BedConfigs),
	}
}

func FromDomainHubOrderRoomItem(domainHubRoom *domain.HubOrderRoomItem) *models.HubOrderRoomItem {
	if domainHubRoom == nil {
		return nil
	}

	return &models.HubOrderRoomItem{
		RoomID:            domainHubRoom.RoomID,
		ProviderRoomID:    domainHubRoom.ProviderRoomID,
		Name:              domainHubRoom.Name,
		RateData:          FromDomainHubRateDatas(domainHubRoom.RateData),
		Provider:          domainHubRoom.Provider,
		ConfirmationID:    domainHubRoom.ConfirmationID,
		BookingStatus:     domainHubRoom.BookingStatus,
		OccupancyType:     domainHubRoom.OccupancyType,
		OccupancyIndex:    domainHubRoom.OccupancyIndex,
		GivenName:         domainHubRoom.GivenName,
		Surname:           domainHubRoom.Surname,
		Email:             domainHubRoom.Email,
		SpecialRequest:    domainHubRoom.SpecialRequest,
		BedOption:         FromDomainHubRoomBedOption(domainHubRoom.BedOption),
		OldProviderRoomID: domainHubRoom.OldProviderRoomID,
	}
}

func FromDomainHubOrderRoomItems(domainHubRooms []*domain.HubOrderRoomItem) []*models.HubOrderRoomItem {
	result := make([]*models.HubOrderRoomItem, 0, len(domainHubRooms))
	for _, item := range domainHubRooms {
		result = append(result, FromDomainHubOrderRoomItem(item))
	}

	return result
}

func FromDomainHubOrderHotelItem(domainHubHotel *domain.HubOrderHotelItem) *models.HubOrderHotelItem {
	if domainHubHotel == nil {
		return nil
	}

	return &models.HubOrderHotelItem{
		HotelID:         domainHubHotel.HotelID,
		Address:         FromDomainAddress(domainHubHotel.Address),
		ProviderHotelID: domainHubHotel.ProviderHotelID,
		OldProviderID:   domainHubHotel.OldProviderID,
		Name:            domainHubHotel.Name,
		VAT:             domainHubHotel.VAT,
		ListRooms:       FromDomainHubOrderRoomItems(domainHubHotel.ListRooms),
		Currency:        domainHubHotel.Currency,
		CheckInTime:     domainHubHotel.CheckInTime,
		CheckOutTime:    domainHubHotel.CheckOutTime,
	}
}

func ToDomainHubOrderRoomItem(modelsHubRoom *models.HubOrderRoomItem) *domain.HubOrderRoomItem {
	if modelsHubRoom == nil {
		return nil
	}

	return &domain.HubOrderRoomItem{
		RoomID:            modelsHubRoom.RoomID,
		ProviderRoomID:    modelsHubRoom.ProviderRoomID,
		Name:              modelsHubRoom.Name,
		RateData:          ToDomainHubRateDatas(modelsHubRoom.RateData),
		Provider:          modelsHubRoom.Provider,
		ConfirmationID:    modelsHubRoom.ConfirmationID,
		BookingStatus:     modelsHubRoom.BookingStatus,
		OccupancyIndex:    modelsHubRoom.OccupancyIndex,
		OccupancyType:     modelsHubRoom.OccupancyType,
		GivenName:         modelsHubRoom.GivenName,
		Surname:           modelsHubRoom.Surname,
		Email:             modelsHubRoom.Email,
		SpecialRequest:    modelsHubRoom.SpecialRequest,
		BedOption:         ToDomainHubRoomBedOption(modelsHubRoom.BedOption),
		OldProviderRoomID: modelsHubRoom.OldProviderRoomID,
	}
}

func ToDomainHubOrderRoomItems(modelHubRooms []*models.HubOrderRoomItem) []*domain.HubOrderRoomItem {
	result := make([]*domain.HubOrderRoomItem, 0, len(modelHubRooms))
	for _, item := range modelHubRooms {
		result = append(result, ToDomainHubOrderRoomItem(item))
	}

	return result
}

func ToDomainHubOrderHotelItem(modelHubHotel *models.HubOrderHotelItem) *domain.HubOrderHotelItem {
	if modelHubHotel == nil {
		return nil
	}

	return &domain.HubOrderHotelItem{
		Address:         ToDomainAddress(modelHubHotel.Address),
		HotelID:         modelHubHotel.HotelID,
		ProviderHotelID: modelHubHotel.ProviderHotelID,
		OldProviderID:   modelHubHotel.OldProviderID,
		Name:            modelHubHotel.Name,
		VAT:             modelHubHotel.VAT,
		ListRooms:       ToDomainHubOrderRoomItems(modelHubHotel.ListRooms),
		Currency:        modelHubHotel.Currency,
		CheckInTime:     modelHubHotel.CheckInTime,
		CheckOutTime:    modelHubHotel.CheckOutTime,
	}
}

func ToDomainHubHotelOrder(order *models.HubHotelOrder) *domain.HubHotelOrder {
	if order == nil {
		return nil
	}

	return &domain.HubHotelOrder{
		ID:                     order.ID.Hex(),
		CreatedAt:              order.CreatedAt,
		SessionID:              order.SessionID,
		OrderCode:              order.OrderCode,
		ReservationCode:        order.ReservationCode,
		ProviderBookingStatus:  order.ProviderBookingStatus,
		OfficeID:               order.OfficeID,
		AgentCode:              order.AgentCode,
		Hotel:                  ToDomainHubOrderHotelItem(order.Hotel),
		RateData:               ToDomainHubRateData(order.RateData),
		OriginalRateDataCf:     ToDomainHubRateData(order.OriginalRateDataCf),
		ExchangedRateDataCfRaw: ToDomainHubRateData(order.ExchangedRateDataCfRaw),
		Fee:                    ToDomainFee(order.Fee),
		ExchangedRateDataCf:    ToDomainHubRateData(order.ExchangedRateDataCf),
		ExchangeRateApply:      toDomainCurrencyExchange(order.ExchangeRateApply),
		RequestHolder:          ToDomainHubHolderInfo(order.RequestHolder),
		Provider:               order.Provider,
		OldProvider:            order.OldProvider,
		LastConfirmDate:        order.LastConfirmDate,
		BookingRequest:         ToDomainHubBookingRequest(order.BookingRequest),
		OrderPaymentID:         order.OrderPaymentID,
		LastTransactionID:      order.LastTransactionID,
		CustomerIP:             order.CustomerIP,
		HubOrderStatus:         order.HubOrderStatus,
		BookingStatus:          order.BookingStatus,
		PendingDeadline:        order.PendingDeadline,
		CancelDeadline:         order.CancelDeadline,
		ManualIssuing:          order.ManualIssuing,
		HotelSearchRequest:     ToDomainHotelSearchRequest(order.HotelSearchRequest),
		FareDataIssuing:        ToDomainFareDataIssuing(order.FareDataIssuing),
		PendingStartAt:         order.PendingStartAt,
		RefundData:             ToDomainRefundData(order.RefundData),
		ExchangedRefundData:    ToDomainRefundData(order.ExchangedRefundData),
		Refunded:               order.Refunded,
		CancelingStartAt:       order.CancelingStartAt,
		SkipScanCheckCancel:    order.SkipScanCheckCancel,
		SearchKey:              order.SearchKey,
		CancelReason:           order.CancelReason,
		NewSessionID:           order.NewSessionID,
		TABookingID:            order.TABookingID,
	}
}

func ToDomainFareDataIssuing(fareDataIssuing *models.FareDataIssuing) *domain.FareDataIssuing {
	if fareDataIssuing == nil {
		return nil
	}

	return &domain.FareDataIssuing{
		Amount:       fareDataIssuing.Amount,
		AmountString: fareDataIssuing.AmountString,
		Currency:     fareDataIssuing.Currency,
		Type:         fareDataIssuing.Type,
	}
}

func ToDomainHubHotelOrders(orders []*models.HubHotelOrder) []*domain.HubHotelOrder {
	result := make([]*domain.HubHotelOrder, 0, len(orders))

	for _, item := range orders {
		result = append(result, ToDomainHubHotelOrder(item))
	}

	return result
}

func FromDomainFee(in *domain.Fee) *models.Fee {
	if in == nil {
		return nil
	}

	return &models.Fee{
		HiddenFee: FromDomainOrderHiddenFee(in.HiddenFee),
	}
}

func ToDomainFee(in *models.Fee) *domain.Fee {
	if in == nil {
		return nil
	}

	return &domain.Fee{
		HiddenFee: ToDomainOrderHiddenFee(in.HiddenFee),
	}
}

func FromDomainOrderHiddenFee(in *domain.OrderHiddenFee) *models.OrderHiddenFee {
	if in == nil {
		return nil
	}

	return &models.OrderHiddenFee{
		HiddenFeeConfig:   FromDomainHiddenServiceFee(in.HiddenFeeConfig),
		TotalChargeAmount: in.TotalChargeAmount,
	}
}

func ToDomainOrderHiddenFee(in *models.OrderHiddenFee) *domain.OrderHiddenFee {
	if in == nil {
		return nil
	}

	return &domain.OrderHiddenFee{
		HiddenFeeConfig:   ToDomainHiddenServiceFee(in.HiddenFeeConfig),
		TotalChargeAmount: in.TotalChargeAmount,
	}
}
