package converts

import (
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

func ToDomainTAMapping(m *models.TAMapping) *domain.TAMapping {
	return &domain.TAMapping{
		ExternalID:     m.ExternalID,
		TAID:           m.TAID,
		TARoomMappings: ToDomainTARoomMappings(m.TARoomMapping),
	}
}

func ToDomainTARoomMappings(ins []*models.TARoomMapping) []*domain.TARoomMapping {
	out := make([]*domain.TARoomMapping, 0, len(ins))

	for _, item := range ins {
		out = append(out, &domain.TARoomMapping{
			ExternalID: item.ExternalID,
			TAID:       item.TAID,
		})

	}

	return out
}
