package converts

import (
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Chuyển đổi Room từ domain sang models
func FromDomainRoom(domainRoom *domain.Room) *models.Room {
	if domainRoom == nil {
		return nil
	}
	var hotelRef primitive.ObjectID
	if domainRoom.HotelRef != "" {
		hotelRef, _ = primitive.ObjectIDFromHex(domainRoom.HotelRef)
	}
	return &models.Room{
		Base:         FromDomainBase(domainRoom.Base),
		HotelRef:     hotelRef,
		RoomID:       domainRoom.RoomID,
		Name:         domainRoom.Name,
		Language:     domainRoom.Language,
		Descriptions: FromDomainRoomDescriptions(domainRoom.Descriptions),
		Amenities:    FromDomainAmenities(domainRoom.Amenities),
		Images:       FromDomainImages(domainRoom.Images),
		BedGroups:    FromDomainBedGroups(domainRoom.BedGroups),
		Area:         FromDomainArea(domainRoom.Area),
		RoomGroupExt: models.RoomGroupExt{
			Class:    domainRoom.RoomGroupExt.Class,
			Quality:  domainRoom.RoomGroupExt.Quality,
			Sex:      domainRoom.RoomGroupExt.Sex,
			Bathroom: domainRoom.RoomGroupExt.Bathroom,
			Bedding:  domainRoom.RoomGroupExt.Bedding,
			Family:   domainRoom.RoomGroupExt.Family,
			Capacity: domainRoom.RoomGroupExt.Capacity,
			Club:     domainRoom.RoomGroupExt.Club,
			Bedrooms: domainRoom.RoomGroupExt.Bedrooms,
			Balcony:  domainRoom.RoomGroupExt.Balcony,
			Floor:    domainRoom.RoomGroupExt.Floor,
			View:     domainRoom.RoomGroupExt.View,
		},
		Views:       FromDomainViews(domainRoom.Views),
		Occupancy:   FromDomainOccupancy(domainRoom.Occupancy),
		ProviderIds: domainRoom.ProviderIDs,
		Version:     domainRoom.Version,
		HotelID:     domainRoom.HotelID,
	}
}

func FromDomainRoomDescriptions(domainDescriptions *domain.RoomDescriptions) *models.RoomDescriptions {
	if domainDescriptions == nil {
		return nil
	}
	return &models.RoomDescriptions{
		Overview: domainDescriptions.Overview,
	}
}

func FromDomainBedGroups(domainBedGroups []*domain.BedGroup) []*models.BedGroup {
	if domainBedGroups == nil {
		return nil
	}
	modelsBedGroups := make([]*models.BedGroup, len(domainBedGroups))
	for i, bedGroup := range domainBedGroups {
		modelsBedGroups[i] = &models.BedGroup{
			ID:            bedGroup.ID,
			Description:   bedGroup.Description,
			Configuration: FromDomainBedConfigurations(bedGroup.Configuration),
		}
	}
	return modelsBedGroups
}

func FromDomainBedConfigurations(domainConfigurations []*domain.BedConfiguration) []*models.BedConfiguration {
	if domainConfigurations == nil {
		return nil
	}
	modelsConfigurations := make([]*models.BedConfiguration, len(domainConfigurations))
	for i, config := range domainConfigurations {
		modelsConfigurations[i] = &models.BedConfiguration{
			Quantity: config.Quantity,
			Size:     config.Size,
			Type:     config.Type,
		}
	}
	return modelsConfigurations
}

func FromDomainArea(domainArea *domain.Area) *models.Area {
	if domainArea == nil {
		return nil
	}
	return &models.Area{
		SquareMeters: domainArea.SquareMeters,
		SquareFeet:   domainArea.SquareFeet,
	}
}

func FromDomainOccupancy(domainOccupancy *domain.ContentOccupancy) *models.Occupancy {
	if domainOccupancy == nil {
		return nil
	}
	return &models.Occupancy{
		MaxAllowed:    FromDomainMaxAllowed(domainOccupancy.MaxAllowed),
		AgeCategories: FromDomainAgeCategories(domainOccupancy.AgeCategories),
	}
}

func FromDomainMaxAllowed(domainMaxAllowed *domain.MaxAllowed) *models.MaxAllowed {
	if domainMaxAllowed == nil {
		return nil
	}
	return &models.MaxAllowed{
		Total:    domainMaxAllowed.Total,
		Children: domainMaxAllowed.Children,
		Adults:   domainMaxAllowed.Adults,
	}
}

func FromDomainAgeCategories(domainAgeCategories []*domain.AgeCategory) []*models.AgeCategory {
	if domainAgeCategories == nil {
		return nil
	}
	modelsAgeCategories := make([]*models.AgeCategory, len(domainAgeCategories))
	for i, ageCategory := range domainAgeCategories {
		modelsAgeCategories[i] = &models.AgeCategory{
			Name:       ageCategory.Name,
			MinimumAge: ageCategory.MinimumAge,
		}
	}
	return modelsAgeCategories
}

func FromDomainViews(domainViews []*domain.View) []*models.View {
	if domainViews == nil {
		return nil
	}
	modelsViews := make([]*models.View, len(domainViews))
	for i, view := range domainViews {
		modelsViews[i] = &models.View{
			ID:   view.ID,
			Name: view.Name,
		}
	}
	return modelsViews
}
