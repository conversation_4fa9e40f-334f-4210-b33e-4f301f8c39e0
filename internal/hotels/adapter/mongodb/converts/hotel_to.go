package converts

import (
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

func ToDomainHotel(d *models.Hotel) *domain.Hotel {
	if d == nil {
		return nil
	}

	return &domain.Hotel{
		Base:                           ToDomainBase(d.Base),
		Rank:                           d.Rank,
		Active:                         d.Active,
		MultiUnit:                      d.MultiUnit,
		PaymentRegistrationRecommended: d.PaymentRegistrationRecommended,
		HotelID:                        d.HotelID,
		Language:                       d.Language,
		Name:                           d.Name,
		Phone:                          d.Phone,
		Fax:                            d.Fax,
		SupplySource:                   d.SupplySource,
		RoomReferences:                 ToDomainRoomReferences(d.RoomReferences),
		Airports:                       ToDomainAirports(d.Airports),
		Address:                        ToDomainAddress(d.Address),
		Ratings:                        ToDomainRatings(d.Ratings),
		Location:                       ToDomainLocation(d.Location),
		Category:                       ToDomainCategory(d.Category),
		BusinessModel:                  ToDomainBusinessModel(d.BusinessModel),
		CheckIn:                        ToDomainCheckin(d.CheckIn),
		Checkout:                       ToDomainCheckout(d.Checkout),
		Fees:                           ToDomainFees(d.Fees),
		Policies:                       ToDomainPolicies(d.Policies),
		Attributes:                     ToDomainAttributes(d.Attributes),
		Amenities:                      ToDomainAmenities(d.Amenities),
		Images:                         ToDomainImages(d.Images),
		OnsitePayments:                 ToDomainOnsitePayments(d.OnsitePayments),
		Rates:                          ToDomainContentRates(d.Rates),
		Dates:                          ToDomainDates(d.Dates),
		Descriptions:                   ToDomainDescriptions(d.Descriptions),
		Statistics:                     ToDomainStatistics(d.Statistics),
		AllInclusive:                   ToDomainAllInclusive(d.AllInclusive),
		TaxID:                          d.TaxID,
		RegistryNumber:                 d.RegistryNumber,
		Chain:                          ToDomainChain(d.Chain),
		Brand:                          ToDomainBrand(d.Brand),
		SpokenLanguages:                ToDomainSpokenLanguages(d.SpokenLanguages),
		Themes:                         ToDomainThemes(d.Themes),
		VacationRentalDetails:          ToDomainVacationRentalDetails(d.VacationRentalDetails),
		ProviderIds:                    d.ProviderIds,
		Version:                        d.Version,
		Distance:                       d.Distance,
	}
}

func ToDomainHotels(d []*models.Hotel) []*domain.Hotel {
	result := make([]*domain.Hotel, 0, len(d))

	for _, item := range d {
		result = append(result, ToDomainHotel(item))
	}

	return result
}

func ToDomainHotelsWithDefault(d []*models.Hotel, defaultLang string) ([]*domain.Hotel, []*domain.Hotel) {
	result := []*domain.Hotel{}
	defaultResult := []*domain.Hotel{}

	for _, item := range d {
		if item.Language == defaultLang {
			defaultResult = append(defaultResult, ToDomainHotel(item))
		} else {
			result = append(result, ToDomainHotel(item))
		}
	}

	return result, defaultResult
}

func ToDomainAirports(d *models.Airports) *domain.Airports {
	if d == nil {
		return nil
	}
	return &domain.Airports{
		Preferred: ToDomainPreferredAirport(d.Preferred),
	}
}

func ToDomainPreferredAirport(d *models.PreferredAirport) *domain.PreferredAirport {
	if d == nil {
		return nil
	}
	return &domain.PreferredAirport{
		IataAirportCode: d.IataAirportCode,
	}
}

func ToDomainAddress(d *models.Address) *domain.Address {
	if d == nil {
		return nil
	}
	return &domain.Address{
		Line1:               d.Line1,
		City:                d.City,
		StateProvinceName:   d.StateProvinceName,
		PostalCode:          d.PostalCode,
		CountryCode:         d.CountryCode,
		ObfuscationRequired: d.ObfuscationRequired,
	}
}

func ToDomainRatings(d *models.Ratings) *domain.Ratings {
	if d == nil {
		return nil
	}
	return &domain.Ratings{
		Property: ToDomainPropertyRating(d.Property),
		Guest:    ToDomainGuestRating(d.Guest),
	}
}

func ToDomainPropertyRating(d *models.PropertyRating) *domain.PropertyRating {
	if d == nil {
		return nil
	}
	return &domain.PropertyRating{
		Rating: d.Rating,
		Type:   d.Type,
	}
}

func ToDomainGuestRating(d *models.GuestRating) *domain.GuestRating {
	if d == nil {
		return nil
	}
	return &domain.GuestRating{
		Count:                 d.Count,
		Overall:               d.Overall,
		Cleanliness:           d.Cleanliness,
		Service:               d.Service,
		Comfort:               d.Comfort,
		Condition:             d.Condition,
		Location:              d.Location,
		Neighborhood:          d.Neighborhood,
		Quality:               d.Quality,
		Value:                 d.Value,
		Amenities:             d.Amenities,
		RecommendationPercent: d.RecommendationPercent,
	}
}

func ToDomainLocation(d *models.Location) *domain.Location {
	if d == nil {
		return nil
	}
	return &domain.Location{
		Coordinates:         ToDomainCoordinates(d.Coordinates),
		ObfuscationRequired: d.ObfuscationRequired,
	}
}

func ToDomainCoordinates(d *models.Coordinates) *domain.Coordinates {
	if d == nil {
		return nil
	}
	return &domain.Coordinates{
		Latitude:  d.Latitude,
		Longitude: d.Longitude,
	}
}

func ToDomainCategory(d *models.Category) *domain.Category {
	if d == nil {
		return nil
	}
	return &domain.Category{
		ID:   d.ID,
		Name: d.Name,
	}
}

func ToDomainBusinessModel(d *models.BusinessModel) *domain.BusinessModel {
	if d == nil {
		return nil
	}
	return &domain.BusinessModel{
		ExpediaCollect:  d.ExpediaCollect,
		PropertyCollect: d.PropertyCollect,
	}
}

func ToDomainCheckin(d *models.CheckIn) *domain.CheckIn {
	if d == nil {
		return nil
	}
	return &domain.CheckIn{
		BeginTime:           d.BeginTime,
		EndTime:             d.EndTime,
		Instructions:        d.Instructions,
		SpecialInstructions: d.SpecialInstructions,
		MinAge:              d.MinAge,
	}
}

func ToDomainCheckout(d *models.Checkout) *domain.Checkout {
	if d == nil {
		return nil
	}
	return &domain.Checkout{
		Time: d.Time,
	}
}

func ToDomainFees(d *models.Fees) *domain.Fees {
	if d == nil {
		return nil
	}
	return &domain.Fees{
		Mandatory:       d.Mandatory,
		Optional:        d.Optional,
		TravelerService: d.TravelerService,
	}
}

func ToDomainPolicies(d *models.Policies) *domain.Policies {
	if d == nil {
		return nil
	}
	return &domain.Policies{
		KnowBeforeYouGo: d.KnowBeforeYouGo,
	}
}

func ToDomainAttributes(d *models.Attributes) *domain.Attributes {
	if d == nil {
		return nil
	}
	return &domain.Attributes{
		Pets:    ToDomainAttributeList(d.Pets),
		General: ToDomainAttributeList(d.General),
	}
}

func ToDomainAttributeList(d []*models.Attribute) []*domain.Attribute {
	if d == nil {
		return nil
	}
	var result []*domain.Attribute
	for _, attr := range d {
		result = append(result, ToDomainAttribute(attr))
	}
	return result
}

func ToDomainAttribute(d *models.Attribute) *domain.Attribute {
	if d == nil {
		return nil
	}
	return &domain.Attribute{
		ID:    d.ID,
		Name:  d.Name,
		Value: d.Value,
	}
}

func ToDomainAmenities(d []*models.Amenity) []*domain.Amenity {
	if d == nil {
		return nil
	}
	var result []*domain.Amenity
	for _, amenity := range d {
		result = append(result, ToDomainAmenity(amenity))
	}
	return result
}

func ToDomainAmenity(d *models.Amenity) *domain.Amenity {
	if d == nil {
		return nil
	}
	return &domain.Amenity{
		ID:         d.ID,
		Name:       d.Name,
		Categories: d.Categories,
		Value:      d.Value,
		GroupName:  d.GroupName,
	}
}

func ToDomainMapAmenities(d map[string]*models.Amenity) map[string]*domain.Amenity {
	if d == nil {
		return nil
	}
	result := make(map[string]*domain.Amenity)
	for key, amenity := range d {
		result[key] = ToDomainAmenity(amenity)
	}
	return result
}

func ToDomainImages(d []*models.Image) []*domain.Image {
	if d == nil {
		return nil
	}
	var result []*domain.Image
	for _, image := range d {
		result = append(result, ToDomainImage(image))
	}
	return result
}

func ToDomainImage(d *models.Image) *domain.Image {
	if d == nil {
		return nil
	}
	return &domain.Image{
		HeroImage: d.HeroImage,
		Category:  d.Category,
		Links:     ToDomainLinks(d.Links),
		Caption:   d.Caption,
	}
}

func ToDomainLinks(d map[string]*models.Link) map[string]*domain.Link {
	if d == nil {
		return nil
	}
	result := make(map[string]*domain.Link)
	for key, link := range d {
		result[key] = ToDomainLink(link)
	}
	return result
}

func ToDomainLink(d *models.Link) *domain.Link {
	if d == nil {
		return nil
	}
	return &domain.Link{
		Method: d.Method,
		Href:   d.Href,
	}
}

func ToDomainOnsitePayments(d *models.OnsitePayments) *domain.OnsitePayments {
	if d == nil {
		return nil
	}
	return &domain.OnsitePayments{
		Currency: d.Currency,
		Types:    ToDomainPaymentTypes(d.Types),
	}
}

func ToDomainPaymentTypes(d []*models.PaymentType) []*domain.PaymentType {
	if d == nil {
		return nil
	}
	var result []*domain.PaymentType
	for _, pt := range d {
		result = append(result, ToDomainPaymentType(pt))
	}
	return result
}

func ToDomainPaymentType(d *models.PaymentType) *domain.PaymentType {
	if d == nil {
		return nil
	}
	return &domain.PaymentType{
		ID:   d.ID,
		Name: d.Name,
	}
}

func ToDomainContentRates(d []*models.ContentRate) []*domain.ContentRate {
	if d == nil {
		return nil
	}
	var result []*domain.ContentRate
	for _, rate := range d {
		result = append(result, ToDomainContentRate(rate))
	}
	return result
}

func ToDomainContentRate(d *models.ContentRate) *domain.ContentRate {
	if d == nil {
		return nil
	}
	return &domain.ContentRate{
		ID:        d.ID,
		Amenities: ToDomainAmenities(d.Amenities),
	}
}

func ToDomainDates(d *models.Dates) *domain.Dates {
	if d == nil {
		return nil
	}
	return &domain.Dates{
		Added:   d.Added,
		Updated: d.Updated,
	}
}

func ToDomainDescriptions(d *models.Descriptions) *domain.Descriptions {
	if d == nil {
		return nil
	}
	return &domain.Descriptions{
		Amenities:         d.Amenities,
		Dining:            d.Dining,
		BusinessAmenities: d.BusinessAmenities,
		Rooms:             d.Rooms,
		Attractions:       d.Attractions,
		Location:          d.Location,
		Headline:          d.Headline,
	}
}

func ToDomainStatistics(d []*models.Statistic) []*domain.Statistic {
	if d == nil {
		return nil
	}
	var result []*domain.Statistic
	for _, stat := range d {
		result = append(result, ToDomainStatistic(stat))
	}
	return result
}

func ToDomainStatistic(d *models.Statistic) *domain.Statistic {
	if d == nil {
		return nil
	}
	return &domain.Statistic{
		ID:    d.ID,
		Name:  d.Name,
		Value: d.Value,
	}
}

func ToDomainChain(d *models.Chain) *domain.Chain {
	if d == nil {
		return nil
	}
	return &domain.Chain{
		ID:   d.ID,
		Name: d.Name,
	}
}

func ToDomainBrand(d *models.Brand) *domain.Brand {
	if d == nil {
		return nil
	}
	return &domain.Brand{
		ID:   d.ID,
		Name: d.Name,
	}
}

func ToDomainSpokenLanguages(d map[string]*models.Language) map[string]*domain.Language {
	if d == nil {
		return nil
	}
	result := make(map[string]*domain.Language)
	for key, lang := range d {
		result[key] = ToDomainLanguage(lang)
	}
	return result
}

func ToDomainLanguage(d *models.Language) *domain.Language {
	if d == nil {
		return nil
	}
	return &domain.Language{
		ID:   d.ID,
		Name: d.Name,
	}
}

func ToDomainRoomReferences(modelsRoomReferences []*models.RoomRefInfo) []*domain.RoomRefInfo {
	if len(modelsRoomReferences) == 0 {
		return nil
	}

	domainsRoomReferences := make([]*domain.RoomRefInfo, len(modelsRoomReferences))
	for i, roomReference := range modelsRoomReferences {
		domainsRoomReferences[i] = &domain.RoomRefInfo{
			ID:          roomReference.ID.Hex(),
			ProviderIds: roomReference.ProviderIds,
			RoomID:      roomReference.RoomID,
		}
	}

	return domainsRoomReferences
}

func ToDomainVacationRentalDetails(modelsVR *models.VacationRentalDetails) *domain.VacationRentalDetails {
	if modelsVR == nil {
		return nil
	}

	return &domain.VacationRentalDetails{
		RegistryNumber:     modelsVR.RegistryNumber,
		PrivateHost:        modelsVR.PrivateHost,
		PropertyManager:    ToDomainPropertyManager(modelsVR.PropertyManager),
		RentalAgreement:    ToDomainRentalAgreement(modelsVR.RentalAgreement),
		HouseRules:         modelsVR.HouseRules,
		EnhancedHouseRules: ToDomainEnhancedHouseRules(modelsVR.EnhancedHouseRules),
		Amenities:          ToDomainMapAmenities(modelsVR.Amenities),
		VrboSrpID:          modelsVR.VrboSrpID,
		ListingID:          modelsVR.ListingID,
		ListingNumber:      modelsVR.ListingNumber,
		ListingSource:      modelsVR.ListingSource,
		ListingUnit:        modelsVR.ListingUnit,
		IPMName:            modelsVR.IPMName,
		UnitConfigurations: ToDomainUnitConfigurations(modelsVR.UnitConfigurations),
	}
}

func ToDomainPropertyManager(modelsPM *models.PropertyManager) *domain.PropertyManager {
	if modelsPM == nil {
		return nil
	}

	return &domain.PropertyManager{
		Name:  modelsPM.Name,
		Links: ToDomainLinks(modelsPM.Links),
	}
}

func ToDomainRentalAgreement(modelsRA *models.RentalAgreement) *domain.RentalAgreement {
	if modelsRA == nil {
		return nil
	}

	return &domain.RentalAgreement{
		Links: ToDomainLinks(modelsRA.Links),
	}
}

func ToDomainEnhancedHouseRules(modelRules map[string]*models.EnhancedRule) map[string]*domain.EnhancedRule {
	if modelRules == nil {
		return nil
	}

	domainRules := make(map[string]*domain.EnhancedRule)
	for key, modelRule := range modelRules {
		domainRules[key] = &domain.EnhancedRule{
			Rule:                  modelRule.Rule,
			AdditionalInformation: modelRule.AdditionalInformation,
		}
	}

	return domainRules
}

func ToDomainUnitConfigurations(modelConfigs map[string][]*models.UnitConfig) map[string][]*domain.UnitConfig {
	if modelConfigs == nil {
		return nil
	}

	domainConfigs := make(map[string][]*domain.UnitConfig)
	for key, modelConfigList := range modelConfigs {
		var domainConfigList []*domain.UnitConfig
		for _, modelConfig := range modelConfigList {
			domainConfigList = append(domainConfigList, &domain.UnitConfig{
				Type:        modelConfig.Type,
				Description: modelConfig.Description,
				Quantity:    modelConfig.Quantity,
				FreeText:    modelConfig.FreeText,
			})
		}
		domainConfigs[key] = domainConfigList
	}
	return domainConfigs
}

func ToDomainThemes(modelsThemes []*models.Theme) []*domain.Theme {
	var domainThemes []*domain.Theme
	for _, modelsTheme := range modelsThemes {
		domainThemes = append(domainThemes, &domain.Theme{
			ID:   modelsTheme.ID,
			Name: modelsTheme.Name,
		})
	}
	return domainThemes
}

func ToDomainAllInclusive(modelsAllInclusive *models.AllInclusive) *domain.AllInclusive {
	if modelsAllInclusive == nil {
		return nil
	}
	return &domain.AllInclusive{
		AllRatePlans:  modelsAllInclusive.AllRatePlans,
		SomeRatePlans: modelsAllInclusive.SomeRatePlans,
		Details:       modelsAllInclusive.Details,
	}
}
