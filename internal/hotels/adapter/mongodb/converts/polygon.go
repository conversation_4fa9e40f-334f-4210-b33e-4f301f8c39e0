package converts

import (
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

// ToDomainPolygon converts models.BoundingPolygon to domain.BoundingPolygon
func ToDomainPolygon(model *models.BoundingPolygon) *domain.BoundingPolygon {
	if model == nil {
		return nil
	}
	return &domain.BoundingPolygon{
		Base:        ToDomainBase(model.Base),
		Type:        model.Type,
		Coordinates: model.Coordinates,
	}
}

// FromDomainPolygon converts domain.BoundingPolygon to models.BoundingPolygon
func FromDomainPolygon(domain *domain.BoundingPolygon) *models.BoundingPolygon {
	if domain == nil {
		return nil
	}
	return &models.BoundingPolygon{
		Base:        FromDomainBase(domain.Base),
		Type:        domain.Type,
		Coordinates: domain.Coordinates,
	}
}
