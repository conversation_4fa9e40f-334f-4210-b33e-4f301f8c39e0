package converts

import (
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
)

func ToDomainAreaCacheHotelInfo(in models.AreaCacheHotelInfo) domain.AreaCacheHotelInfo {
	return domain.AreaCacheHotelInfo{
		HotelID:       in.HotelID,
		HotelDistance: in.HotelDistance,
		ProviderIds:   in.ProviderIds,
	}
}

func ToDomainAreaCacheHotelInfos(in []models.AreaCacheHotelInfo) []domain.AreaCacheHotelInfo {
	result := make([]domain.AreaCacheHotelInfo, 0, len(in))

	for _, item := range in {
		result = append(result, ToDomainAreaCacheHotelInfo(item))
	}

	return result
}

func FromDomainAreaCacheHotelInfo(in domain.AreaCacheHotelInfo) models.AreaCacheHotelInfo {
	return models.AreaCacheHotelInfo{
		HotelID:       in.HotelID,
		HotelDistance: in.HotelDistance,
		ProviderIds:   in.ProviderIds,
	}
}

func FromDomainAreaCacheHotelInfos(in []domain.AreaCacheHotelInfo) []models.AreaCacheHotelInfo {
	result := make([]models.AreaCacheHotelInfo, 0, len(in))

	for _, item := range in {
		result = append(result, FromDomainAreaCacheHotelInfo(item))
	}

	return result
}

func ToDomainHotelAreaCacheItem(hotelAreaCache *models.HotelAreaCacheItem) *domain.HotelAreaCacheItem {
	return &domain.HotelAreaCacheItem{
		Hotels:   ToDomainAreaCacheHotelInfos(hotelAreaCache.Hotels),
		AreaType: enum.PlaceTypeValue[hotelAreaCache.AreaType],
		AreaID:   hotelAreaCache.AreaID,
	}
}

func FromDomainHotelAreaCacheItem(hotelAreaCache *domain.HotelAreaCacheItem) *models.HotelAreaCacheItem {
	return &models.HotelAreaCacheItem{
		Hotels:   FromDomainAreaCacheHotelInfos(hotelAreaCache.Hotels),
		AreaType: enum.PlaceTypeName[hotelAreaCache.AreaType],
		AreaID:   hotelAreaCache.AreaID,
	}
}
