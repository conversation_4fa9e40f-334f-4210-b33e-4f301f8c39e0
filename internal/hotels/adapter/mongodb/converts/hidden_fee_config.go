package converts

import (
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

func ToDomainHiddenServiceFees(ins []*models.HiddenServiceFee) []*domain.HiddenServiceFee {
	out := make([]*domain.HiddenServiceFee, 0, len(ins))

	for _, item := range ins {
		out = append(out, ToDomainHiddenServiceFee(item))
	}

	return out
}

func ToDomainHiddenServiceFee(in *models.HiddenServiceFee) *domain.HiddenServiceFee {
	if in == nil {
		return nil
	}

	base := ToDomainBase(in.Base)
	result := &domain.HiddenServiceFee{
		Base:      base,
		OfficeID:  in.OfficeID,
		Type:      in.Type,
		HotelID:   in.HotelID,
		HotelName: in.HotelName,
		Amount:    in.Amount,
		Percent:   in.Percent,
		Provider:  in.Provider,
	}

	if in.Config != nil {
		result.Config = &domain.HiddenServiceFeeConfig{
			LocationType: in.Config.LocationType,
			CountryCode:  in.Config.CountryCode,
			Rating:       in.Config.Rating,
			HotelType:    in.Config.HotelType,
		}

	}

	return result
}

func FromDomainHiddenServiceFees(ins []*domain.HiddenServiceFee) []*models.HiddenServiceFee {
	out := make([]*models.HiddenServiceFee, 0, len(ins))

	for _, item := range ins {
		out = append(out, FromDomainHiddenServiceFee(item))
	}

	return out
}

func FromDomainHiddenServiceFee(in *domain.HiddenServiceFee) *models.HiddenServiceFee {
	if in == nil {
		return nil
	}

	base := FromDomainBase(in.Base)

	result := &models.HiddenServiceFee{
		Base:      base,
		OfficeID:  in.OfficeID,
		Type:      in.Type,
		HotelID:   in.HotelID,
		HotelName: in.HotelName,
		Amount:    in.Amount,
		Percent:   in.Percent,
		Provider:  in.Provider,
	}

	if in.Config != nil {
		result.Config = &models.HiddenServiceFeeConfig{
			LocationType: in.Config.LocationType,
			CountryCode:  in.Config.CountryCode,
			Rating:       in.Config.Rating,
			HotelType:    in.Config.HotelType,
		}

	}

	return result
}
