package converts

import (
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

func ToDomainRegions(in []*models.Region) []*domain.Region {
	result := make([]*domain.Region, 0, len(in))
	for _, item := range in {
		result = append(result, ToDomainRegion(item))
	}

	return result
}

// ToDomainRegion converts models.Region to domain.Region
func ToDomainRegion(model *models.Region) *domain.Region {
	if model == nil {
		return nil
	}
	return &domain.Region{
		Base:                   ToDomainBase(model.Base),
		RegionID:               model.RegionID,
		Type:                   model.Type,
		Name:                   model.Name,
		FullName:               model.FullName,
		Descriptor:             model.Descriptor,
		IATAAirportCode:        model.IATAAirportCode,
		IATAAirportMetroCode:   model.IATAAirportMetroCode,
		CountryCode:            model.CountryCode,
		CountrySubdivisionCode: model.CountrySubdivisionCode,
		Coordinates:            ToDomainRegionCoordinates(model.Coordinates),
		Associations:           model.Associations,
		Ancestors:              ToDomainAncestors(model.Ancestors),
		Descendants:            model.Descendants,
		PropertyIDs:            model.PropertyIDs,
		PropertyIDsExpanded:    model.PropertyIDsExpanded,
		Categories:             model.Categories,
		Tags:                   model.Tags,
		Language:               model.Language,
		Version:                model.Version,
	}
}

// ToDomainRegionCoordinates converts models.RegionCoordinates to domain.RegionCoordinates
func ToDomainRegionCoordinates(model *models.RegionCoordinates) *domain.RegionCoordinates {
	if model == nil {
		return nil
	}
	return &domain.RegionCoordinates{
		CenterLongitude:      model.CenterLongitude,
		CenterLatitude:       model.CenterLatitude,
		BoundingPolygonIDRef: model.BoundingPolygonIDRef.Hex(),
	}
}

// ToDomainAncestor converts models.Ancestor to domain.Ancestor
func ToDomainAncestor(model *models.Ancestor) *domain.Ancestor {
	if model == nil {
		return nil
	}
	return &domain.Ancestor{
		ID:   model.ID,
		Type: model.Type,
	}
}

// ToDomainAncestors converts a slice of models.Ancestor to a slice of domain.Ancestor
func ToDomainAncestors(models []*models.Ancestor) []*domain.Ancestor {
	if models == nil {
		return nil
	}
	domainAncestors := make([]*domain.Ancestor, len(models))
	for i, model := range models {
		domainAncestors[i] = ToDomainAncestor(model)
	}
	return domainAncestors
}
