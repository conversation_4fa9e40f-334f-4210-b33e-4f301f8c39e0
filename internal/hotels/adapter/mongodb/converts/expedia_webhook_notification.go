package converts

import (
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

func FromDomainExpediaWebhookNotificationRequest(info *domain.ExpediaWebhookNotificationRequest) *models.ExpediaWebhookNotificationRequest {
	if info == nil {
		return nil
	}
	return &models.ExpediaWebhookNotificationRequest{
		ExpediaWebhookHeader: FromDomainExpediaWebhookHeader(info.ExpediaWebhookHeader),
		EventID:              info.EventID,
		EventType:            info.EventType,
		EventTime:            info.EventTime,
		ItineraryID:          info.ItineraryID,
		Email:                info.Email,
		Message:              info.Message,
		AffiliateReferenceID: info.AffiliateReferenceID,
		TopicTags:            info.TopicTags,
		Rooms:                FromDomainExpediaNotificationRooms(info.Rooms),
		Key:                  info.Key,
	}
}

func FromDomainExpediaWebhookHeader(info *domain.ExpediaWebhookHeader) *models.ExpediaWebhookHeader {
	if info == nil {
		return nil
	}
	return &models.ExpediaWebhookHeader{
		Signature: info.Signature,
		Timestamp: info.Timestamp,
	}
}
func FromDomainExpediaNotificationRoom(info *domain.ExpediaNotificationRoom) *models.ExpediaNotificationRoom {
	if info == nil {
		return nil
	}
	return &models.ExpediaNotificationRoom{
		ConfirmationInfo: nil,
	}
}

func FromDomainExpediaNotificationRooms(infos []*domain.ExpediaNotificationRoom) []*models.ExpediaNotificationRoom {
	if infos == nil {
		return nil
	}
	var result []*models.ExpediaNotificationRoom
	for _, info := range infos {
		result = append(result, FromDomainExpediaNotificationRoom(info))
	}
	return result
}

func FromDomainConfirmationInfo(info *domain.ConfirmationInfo) *models.ConfirmationInfo {
	if info == nil {
		return nil
	}
	return &models.ConfirmationInfo{
		Expedia:  info.Expedia,
		Property: info.Property,
	}
}

func FromDomainConfirmationInfos(infos []*domain.ConfirmationInfo) []*models.ConfirmationInfo {
	if infos == nil {
		return nil
	}
	var result []*models.ConfirmationInfo
	for _, info := range infos {
		result = append(result, FromDomainConfirmationInfo(info))
	}
	return result
}

//

func ToDomainExpediaWebhookNotificationRequest(info *models.ExpediaWebhookNotificationRequest) *domain.ExpediaWebhookNotificationRequest {
	if info == nil {
		return nil
	}
	return &domain.ExpediaWebhookNotificationRequest{
		ExpediaWebhookHeader: ToDomainExpediaWebhookHeader(info.ExpediaWebhookHeader),
		EventID:              info.EventID,
		EventType:            info.EventType,
		EventTime:            info.EventTime,
		ItineraryID:          info.ItineraryID,
		Email:                info.Email,
		Message:              info.Message,
		AffiliateReferenceID: info.AffiliateReferenceID,
		TopicTags:            info.TopicTags,
		Rooms:                ToDomainExpediaNotificationRooms(info.Rooms),
		Key:                  info.Key,
	}
}

func ToDomainExpediaWebhookHeader(info *models.ExpediaWebhookHeader) *domain.ExpediaWebhookHeader {
	if info == nil {
		return nil
	}
	return &domain.ExpediaWebhookHeader{
		Signature: info.Signature,
		Timestamp: info.Timestamp,
	}
}

func ToDomainExpediaNotificationRoom(info *models.ExpediaNotificationRoom) *domain.ExpediaNotificationRoom {
	if info == nil {
		return nil
	}
	return &domain.ExpediaNotificationRoom{
		ConfirmationInfo: ToDomainConfirmationInfo(info.ConfirmationInfo),
	}
}

func ToDomainExpediaNotificationRooms(infos []*models.ExpediaNotificationRoom) []*domain.ExpediaNotificationRoom {
	if infos == nil {
		return nil
	}
	var result []*domain.ExpediaNotificationRoom
	for _, info := range infos {
		result = append(result, ToDomainExpediaNotificationRoom(info))
	}
	return result
}

func ToDomainConfirmationInfo(info *models.ConfirmationInfo) *domain.ConfirmationInfo {
	if info == nil {
		return nil
	}
	return &domain.ConfirmationInfo{
		Expedia:  info.Expedia,
		Property: info.Property,
	}
}

func ToDomainConfirmationInfos(infos []*models.ConfirmationInfo) []*domain.ConfirmationInfo {
	if infos == nil {
		return nil
	}
	var result []*domain.ConfirmationInfo
	for _, info := range infos {
		result = append(result, ToDomainConfirmationInfo(info))
	}
	return result
}
