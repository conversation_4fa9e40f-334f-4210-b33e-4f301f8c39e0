package converts

import (
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

func ConvertModelsHotelSummaryToDomainHotelSummaries(modelSummaries []*models.HotelSummary) []*domain.HotelSummary {
	result := make([]*domain.HotelSummary, 0, len(modelSummaries))

	for _, item := range modelSummaries {
		result = append(result, ConvertModelsHotelSummaryToDomainHotelSummary(item))
	}
	return result
}

func convertModelSummaryImagesToDomain(modelsImages []*models.HotelSummaryImage) []*domain.HotelSummaryImage {
	var domainImages []*domain.HotelSummaryImage
	for _, img := range modelsImages {
		domainImages = append(domainImages, &domain.HotelSummaryImage{
			HeroImage: img.HeroImage,
			Category:  img.Category,
			Links: domain.HotelSummaryImageLink{
				Px70:   img.Links.Px70,
				Px200:  img.Links.Px200,
				Px350:  img.Links.Px350,
				Px1000: img.Links.Px1000,
			},
			Caption: img.Caption,
		})
	}
	return domainImages
}

func ConvertModelsHotelSummaryToDomainHotelSummary(modelSummary *models.HotelSummary) *domain.HotelSummary {
	if modelSummary == nil {
		return nil
	}

	domainSummary := &domain.HotelSummary{
		ID:           modelSummary.ID,
		Name:         modelSummary.Name,
		Location:     modelSummary.Location,
		Review:       convertReview(modelSummary.Review),
		Amenities:    convertAmenities(modelSummary.Amenities),
		CategoryType: modelSummary.CategoryType,
		Rating:       modelSummary.Rating,
		ThumbnailURL: convertModelSummaryImagesToDomain(modelSummary.ThumbnailURL),
		Price:        convertPrice(modelSummary.Price),
		CenterInfo:   convertCenterInfo(modelSummary.CenterInfo),
		Available:    modelSummary.Available,
		RoomLeft:     modelSummary.RoomLeft,
		CountryCode:  modelSummary.CountryCode,
		Provider:     modelSummary.Provider,
		MatchKey:     modelSummary.MatchKey,
		CheckIn:      modelSummary.CheckIn,
		CheckOut:     modelSummary.CheckOut,
	}

	return domainSummary
}

func convertReview(modelReview *models.HotelReview) *domain.HotelReview {
	if modelReview == nil {
		return nil
	}

	return &domain.HotelReview{
		Rate:        modelReview.Rate,
		ReviewCount: modelReview.ReviewCount,
		Label:       modelReview.Label,
		Detail:      convertReviewDetail(modelReview.Detail),
	}
}

func convertReviewDetail(modelDetail *models.HotelReviewDetail) *domain.HotelReviewDetail {
	if modelDetail == nil {
		return nil
	}

	return &domain.HotelReviewDetail{
		Reviews: convertReviews(modelDetail.Reviews),
		Rating:  convertCriteriaRating(modelDetail.Rating),
	}
}

func convertReviews(modelReviews []*models.Review) []*domain.Review {
	var domainReviews []*domain.Review
	for _, r := range modelReviews {
		domainReviews = append(domainReviews, &domain.Review{
			ReviewID:     r.ReviewID,
			UserName:     r.UserName,
			UserType:     r.UserType,
			Date:         r.Date,
			Rating:       r.Rating,
			Title:        r.Title,
			Content:      r.Content,
			Liked:        r.Liked,
			StayDuration: r.StayDuration,
		})
	}
	return domainReviews
}

func convertCriteriaRating(modelRating *models.CriteriaRating) *domain.CriteriaRating {
	if modelRating == nil {
		return nil
	}

	return &domain.CriteriaRating{
		Cleanliness:                  modelRating.Cleanliness,
		StaffService:                 modelRating.StaffService,
		Amenities:                    modelRating.Amenities,
		PropertyConditionsFacilities: modelRating.PropertyConditionsFacilities,
		EcoFriendliness:              modelRating.EcoFriendliness,
	}
}

func convertAmenities(modelAmenities []*models.Amenity) []*domain.Amenity {
	var domainAmenities []*domain.Amenity
	for _, a := range modelAmenities {
		domainAmenities = append(domainAmenities, &domain.Amenity{
			// Assuming Amenity has similar fields
			ID:         a.ID,
			Name:       a.Name,
			Categories: a.Categories,
			Value:      a.Value,
			GroupName:  a.GroupName,
		})
	}
	return domainAmenities
}

func convertImages(modelImages []*models.Image) []*domain.Image {
	var domainImages []*domain.Image
	for _, img := range modelImages {
		domainImages = append(domainImages, &domain.Image{
			HeroImage: img.HeroImage,
			Category:  img.Category,
			Links:     convertLinks(img.Links),
			Caption:   img.Caption,
		})
	}
	return domainImages
}

func convertLinks(modelLinks map[string]*models.Link) map[string]*domain.Link {
	domainLinks := make(map[string]*domain.Link)
	for key, link := range modelLinks {
		domainLinks[key] = &domain.Link{
			Method: link.Method,
			Href:   link.Href,
		}
	}
	return domainLinks
}

func convertPrice(modelPrice *models.Price) *domain.Price {
	if modelPrice == nil {
		return nil
	}

	return &domain.Price{
		PricePerNight:   convertPricePerNight(modelPrice.PricePerNight),
		Total:           modelPrice.Total,
		IsIncludeTax:    modelPrice.IsIncludeTax,
		Currency:        modelPrice.Currency,
		SaleScenario:    modelPrice.SaleScenario,
		PayAtHotel:      convertPayAtHotel(modelPrice.PayAtHotel),
		TotalPayAtHotel: modelPrice.TotalPayAtHotel,
		HasBreakfast:    modelPrice.HasBreakfast,
		HasExtraBed:     modelPrice.HasExtraBed,
		NonSmoking:      modelPrice.NonSmoking,
		Refundable:      modelPrice.Refundable,
	}
}

func convertPayAtHotel(modelPayAtHotel []*models.PayAtHotel) []*domain.PayAtHotel {
	if len(modelPayAtHotel) == 0 {
		return nil
	}
	var domainPayAtHotel []*domain.PayAtHotel
	for _, p := range modelPayAtHotel {
		domainPayAtHotel = append(domainPayAtHotel, &domain.PayAtHotel{
			Amount:      p.Amount,
			Description: p.Description,
			Currency:    p.Currency,
		})
	}
	return domainPayAtHotel
}

func convertPricePerNight(modelPricePerNight *models.PricePerNight) *domain.PricePerNight {
	if modelPricePerNight == nil {
		return nil
	}

	return &domain.PricePerNight{
		DiscountPrice: modelPricePerNight.DiscountPrice,
		OriginalPrice: modelPricePerNight.OriginalPrice,
	}
}

func convertCenterInfo(modelCenterInfo *models.CenterInfo) *domain.CenterInfo {
	if modelCenterInfo == nil {
		return nil
	}

	return &domain.CenterInfo{
		CenterName:       modelCenterInfo.CenterName,
		DistanceToCenter: modelCenterInfo.DistanceToCenter,
		Unit:             modelCenterInfo.Unit,
	}
}
