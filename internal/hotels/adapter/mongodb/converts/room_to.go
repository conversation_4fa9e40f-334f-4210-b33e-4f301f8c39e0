package converts

import (
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

func ToDomainRooms(modelsRooms []*models.Room) []*domain.Room {
	if modelsRooms == nil {
		return nil
	}
	domainRooms := make([]*domain.Room, len(modelsRooms))
	for i, modelRoom := range modelsRooms {
		domainRooms[i] = ToDomainRoom(modelRoom)
	}
	return domainRooms
}

// Chuyển đổi Room từ models sang domain
func ToDomainRoom(modelsRoom *models.Room) *domain.Room {
	if modelsRoom == nil {
		return nil
	}

	return &domain.Room{
		Base:         ToDomainBase(modelsRoom.Base),
		HotelRef:     modelsRoom.HotelRef.Hex(),
		RoomID:       modelsRoom.RoomID,
		Name:         modelsRoom.Name,
		Language:     modelsRoom.Language,
		Descriptions: ToDomainRoomDescriptions(modelsRoom.Descriptions),
		Amenities:    ToDomainAmenities(modelsRoom.Amenities),
		Images:       ToDomainImages(modelsRoom.Images),
		BedGroups:    ToDomainBedGroups(modelsRoom.BedGroups),
		Area:         ToDomainArea(modelsRoom.Area),
		Occupancy:    ToDomainOccupancy(modelsRoom.Occupancy),
		Views:        ToDomainViews(modelsRoom.Views),
		ProviderIDs:  modelsRoom.ProviderIds,
		RoomGroupExt: domain.RoomGroupExt{
			Class:    modelsRoom.RoomGroupExt.Class,
			Quality:  modelsRoom.RoomGroupExt.Quality,
			Sex:      modelsRoom.RoomGroupExt.Sex,
			Bathroom: modelsRoom.RoomGroupExt.Bathroom,
			Bedding:  modelsRoom.RoomGroupExt.Bedding,
			Family:   modelsRoom.RoomGroupExt.Family,
			Capacity: modelsRoom.RoomGroupExt.Capacity,
			Club:     modelsRoom.RoomGroupExt.Club,
			Bedrooms: modelsRoom.RoomGroupExt.Bedrooms,
			Balcony:  modelsRoom.RoomGroupExt.Balcony,
			Floor:    modelsRoom.RoomGroupExt.Floor,
			View:     modelsRoom.RoomGroupExt.View,
		},
		Version: modelsRoom.Version,
		HotelID: modelsRoom.HotelID,
	}
}

func ToDomainRoomDescriptions(modelsDescriptions *models.RoomDescriptions) *domain.RoomDescriptions {
	if modelsDescriptions == nil {
		return nil
	}
	return &domain.RoomDescriptions{
		Overview: modelsDescriptions.Overview,
	}
}

func ToDomainBedGroups(modelsBedGroups []*models.BedGroup) []*domain.BedGroup {
	if modelsBedGroups == nil {
		return nil
	}
	domainBedGroups := make([]*domain.BedGroup, len(modelsBedGroups))
	for i, bedGroup := range modelsBedGroups {
		domainBedGroups[i] = &domain.BedGroup{
			ID:            bedGroup.ID,
			Description:   bedGroup.Description,
			Configuration: ToDomainBedConfigurations(bedGroup.Configuration),
		}
	}
	return domainBedGroups
}

func ToDomainBedConfigurations(modelsConfigurations []*models.BedConfiguration) []*domain.BedConfiguration {
	if modelsConfigurations == nil {
		return nil
	}
	domainConfigurations := make([]*domain.BedConfiguration, len(modelsConfigurations))
	for i, config := range modelsConfigurations {
		domainConfigurations[i] = &domain.BedConfiguration{
			Quantity: config.Quantity,
			Size:     config.Size,
			Type:     config.Type,
		}
	}
	return domainConfigurations
}

func ToDomainArea(modelsArea *models.Area) *domain.Area {
	if modelsArea == nil {
		return nil
	}
	return &domain.Area{
		SquareMeters: modelsArea.SquareMeters,
		SquareFeet:   modelsArea.SquareFeet,
	}
}

func ToDomainOccupancy(modelsOccupancy *models.Occupancy) *domain.ContentOccupancy {
	if modelsOccupancy == nil {
		return nil
	}
	return &domain.ContentOccupancy{
		MaxAllowed:    ToDomainMaxAllowed(modelsOccupancy.MaxAllowed),
		AgeCategories: ToDomainAgeCategories(modelsOccupancy.AgeCategories),
	}
}

func ToDomainMaxAllowed(modelsMaxAllowed *models.MaxAllowed) *domain.MaxAllowed {
	if modelsMaxAllowed == nil {
		return nil
	}
	return &domain.MaxAllowed{
		Total:    modelsMaxAllowed.Total,
		Children: modelsMaxAllowed.Children,
		Adults:   modelsMaxAllowed.Adults,
	}
}

func ToDomainAgeCategories(modelsAgeCategories []*models.AgeCategory) []*domain.AgeCategory {
	if modelsAgeCategories == nil {
		return nil
	}
	domainAgeCategories := make([]*domain.AgeCategory, len(modelsAgeCategories))
	for i, ageCategory := range modelsAgeCategories {
		domainAgeCategories[i] = &domain.AgeCategory{
			Name:       ageCategory.Name,
			MinimumAge: ageCategory.MinimumAge,
		}
	}
	return domainAgeCategories
}

func ToDomainViews(modelsViews []*models.View) []*domain.View {
	if modelsViews == nil {
		return nil
	}
	domainViews := make([]*domain.View, len(modelsViews))
	for i, view := range modelsViews {
		domainViews[i] = &domain.View{
			ID:   view.ID,
			Name: view.Name,
		}
	}
	return domainViews
}
