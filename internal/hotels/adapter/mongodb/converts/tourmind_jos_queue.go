package converts

import (
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

func FromDomainTourmindJobsQueue(in *domain.TourmindJobsQueue) *models.TourmindJobsQueue {
	return &models.TourmindJobsQueue{
		CountryCode:  in.CountryCode,
		PageIndex:    in.PageIndex,
		BatchSize:    in.BatchSize,
		CompletedAt:  in.CompletedAt,
		CannotMapIds: in.CannotMapIds,
		SuccessIds:   in.SuccessIds,
		DurationMs:   in.DurationMs,
		Base: models.Base{
			DeletedAt: in.DeletedAt,
			CreatedAt: in.CreatedAt,
		},
		LastItem:   in.LastItem,
		TotalItems: in.TotalItems,
	}
}

func ToDomainTourmindJobsQueue(in *models.TourmindJobsQueue) *domain.TourmindJobsQueue {
	return &domain.TourmindJobsQueue{
		CreatedAt:    in.CreatedAt,
		ID:           in.ID.Hex(),
		CountryCode:  in.CountryCode,
		PageIndex:    in.PageIndex,
		BatchSize:    in.BatchSize,
		CompletedAt:  in.CompletedAt,
		CannotMapIds: in.CannotMapIds,
		SuccessIds:   in.SuccessIds,
		DurationMs:   in.DurationMs,
		DeletedAt:    in.DeletedAt,
		LastItem:     in.LastItem,
		TotalItems:   in.TotalItems,
	}
}

func FromDomainTourmindJobsQueues(in []*domain.TourmindJobsQueue) []*models.TourmindJobsQueue {
	var out []*models.TourmindJobsQueue
	for _, v := range in {
		out = append(out, FromDomainTourmindJobsQueue(v))
	}
	return out
}

func ToDomainTourmindJobsQueues(in []*models.TourmindJobsQueue) []*domain.TourmindJobsQueue {
	var out []*domain.TourmindJobsQueue
	for _, v := range in {
		out = append(out, ToDomainTourmindJobsQueue(v))
	}
	return out
}
