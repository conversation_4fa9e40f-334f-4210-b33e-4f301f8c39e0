package converts

import (
	"fmt"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/ta_client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

func ToClientCreateBookingRequest(hubBooking *domain.HubHotelOrder) (*entities.CreateBookingRequest, error) {
	hubRateData := hubBooking.ExchangedRateDataCfRaw

	req := hubBooking.HotelSearchRequest

	checkIn, err := toUTCUnix(req.Stay.CheckIn)
	if err != nil {
		return nil, err
	}

	checkOut, err := toUTCUnix(req.Stay.CheckOut)
	if err != nil {
		return nil, err
	}

	reqOccus := []entities.Occupancy{}
	reqOccuMap := map[string]*entities.Occupancy{}

	genKey := func(a int, c []int) string {
		return fmt.Sprintf("%d_%v", a, c)
	}

	toIntArr := func(input []uint) []int {
		out := make([]int, len(input))
		for i, v := range input {
			out[i] = int(v)
		}
		return out
	}

	for _, occu := range req.Occupancies {
		var chd []int

		if occu.Children != nil {
			chd = toIntArr(occu.Children.Age)
		}

		key := genKey(int(occu.Adults), chd)

		if reqOccuMap[key] == nil {
			reqOccuMap[key] = &entities.Occupancy{
				Quantity:    int(occu.Rooms),
				AdultSlot:   int(occu.Adults),
				ChildrenOld: chd,
			}
		} else {
			reqOccuMap[key].Quantity += int(occu.Rooms)
		}
	}

	for _, occuReq := range reqOccuMap {
		reqOccus = append(reqOccus, *occuReq)
	}

	hotel := hubBooking.Hotel
	pivotRoom := hubBooking.Hotel.GetPivotRoom()

	reqBeds := []entities.BedGroup{}

	for _, bed := range pivotRoom.BedOption.BedConfigs {
		reqBeds = append(reqBeds, entities.BedGroup{
			Quantity: int(bed.Quantity),
			Type:     bed.Type,
		})
	}

	holder := hubBooking.RequestHolder

	bkDetailReq := entities.Detail{
		ManufacturerId:   hotel.ProviderHotelID,
		ProductId:        pivotRoom.ProviderRoomID,
		AgentCode:        "BIZITRIP",
		Quantity:         len(req.Occupancies),
		Occupancies:      reqOccus,
		Refundable:       hubRateData.Refundable,
		View:             "",
		Smoking:          !hubRateData.NonSmoking,
		IncludeBreakfast: hubRateData.HasBreakfast,
		ExtraBed:         hubRateData.HasExtraBed,
		BedGroups:        reqBeds,
		ExternalAmount:   hubRateData.PayNow,
	}

	return &entities.CreateBookingRequest{
		Currency:     hubRateData.Currency,
		CheckinTime:  checkIn,
		CheckoutTime: checkOut,
		Location:     "",
		Profile: entities.Profile{
			Email:       pivotRoom.Email,
			LastName:    pivotRoom.Surname,
			FirstName:   pivotRoom.GivenName,
			PhoneNumber: holder.PhoneCode + holder.PhoneNumber,
		},
		PaymentType:  5,
		ProviderCode: "TRAVEL",
		Details:      []entities.Detail{bkDetailReq},
		Description:  pivotRoom.SpecialRequest,
	}, nil
}
