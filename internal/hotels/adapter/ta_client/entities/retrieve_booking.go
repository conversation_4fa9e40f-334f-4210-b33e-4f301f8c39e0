package entities

type RetrieveBookingResponse struct {
	Version    string                `json:"version"`
	StatusCode int                   `json:"statusCode"`
	Message    string                `json:"message"`
	Result     RetrieveBookingResult `json:"result"`
}

type RetrieveBookingResult struct {
	ConfirmationInfos []BookingConfirmationIdInfo `json:"confirmationInfos"`
	Amount            float64                     `json:"amount"`
	BookingStatus     string                      `json:"bookingStatus"`
	BookingId         string                      `json:"bookingId"`
}

type BookingConfirmationIdInfo struct {
	DetailId       string `json:"detailId"`
	ItemId         string `json:"itemId"`
	ProductId      string `json:"productId"`
	ConfirmationId string `json:"confirmationId"`
	Used           bool   `json:"-"`
}
