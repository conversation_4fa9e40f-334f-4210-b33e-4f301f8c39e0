package entities

type CreateBookingRequest struct {
	Currency     string   `json:"currency"`
	CheckinTime  int64    `json:"checkinTime"`
	CheckoutTime int64    `json:"checkoutTime"`
	Location     string   `json:"location"`
	Profile      Profile  `json:"profile"`
	PaymentType  int      `json:"paymentType"`
	ProviderCode string   `json:"providerCode"`
	Details      []Detail `json:"details"`
	Description  string   `json:"description"`
}

type Profile struct {
	Email       string `json:"email"`
	LastName    string `json:"lastName"`
	FirstName   string `json:"firstName"`
	PhoneNumber string `json:"phoneNumber"`
}

type Detail struct {
	ManufacturerId   string      `json:"manufacturerId"`
	ProductId        string      `json:"productId"`
	AgentCode        string      `json:"agentCode"`
	Quantity         int         `json:"quantity"`
	Occupancies      []Occupancy `json:"occupancies"`
	Refundable       bool        `json:"refundable"`
	View             string      `json:"view"`
	Smoking          bool        `json:"smoking"`
	IncludeBreakfast bool        `json:"includeBreakfast"`
	ExtraBed         bool        `json:"extraBed"`
	BedGroups        []BedGroup  `json:"bedGroups"`
	ExternalAmount   float64     `json:"externalAmount"`
}

type Occupancy struct {
	AdultSlot   int   `json:"adultSlot"`
	ChildrenOld []int `json:"childrenOld"`
	Quantity    int   `json:"quantity"`
}

type BedGroup struct {
	Quantity int    `json:"quantity"`
	Type     string `json:"type"`
}

type CreateBookingResponse struct {
	Version    string              `json:"version"`
	StatusCode int                 `json:"statusCode"`
	Message    string              `json:"message"`
	Result     CreateBookingResult `json:"result"`
}

type CreateBookingResult struct {
	Amount        float64 `json:"amount"`
	BookingStatus string  `json:"bookingStatus"`
	BookingId     string  `json:"bookingId"`
}

type DeleteBookingResponse struct {
	Version    string `json:"version"`
	StatusCode int    `json:"statusCode"`
	Message    string `json:"message"`
	// Result     string `json:"result"`
}
