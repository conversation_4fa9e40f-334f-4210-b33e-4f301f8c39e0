package entities

type GetPriceResponse struct {
	Version    string         `json:"version"`
	StatusCode int            `json:"statusCode"`
	Message    string         `json:"message"`
	Result     GetPriceResult `json:"result"`
}

type GetPriceResult struct {
	ProductId string  `json:"productId"`
	Available bool    `json:"available"`
	Amount    float64 `json:"amount"`
}

type GetPriceRequest struct {
	ManufacturerId   string             `json:"manufacturerId"`
	ProductId        string             `json:"productId,omitempty"`
	ProductName      string             `json:"productName,omitempty"`
	Currency         string             `json:"currency"`
	CheckinTime      int64              `json:"checkinTime"`
	CheckoutTime     int64              `json:"checkoutTime"`
	ProviderCode     string             `json:"providerCode"`
	AgentCode        string             `json:"agentCode"`
	Occupancies      []RequestOccupancy `json:"occupancies"`
	IncludeBreakfast bool               `json:"includeBreakfast"`
	Refundable       bool               `json:"refundable"`
	Smoking          bool               `json:"smoking"`
	ExtraBed         bool               `json:"extraBed"`
	View             string             `json:"view"`
	BedGroups        []RequestBedGroup  `json:"bedGroups"`
	ExternalAmount   float64            `json:"externalAmount"`
	PricingType      int64              `json:"pricingType"`
}

type RequestOccupancy struct {
	AdultSlot    int   `json:"adultSlot"`
	ChildrenOlds []int `json:"childrenOlds"`
	Quantity     int   `json:"quantity"`
}

type RequestBedGroup struct {
	Quantity int    `json:"quantity"`
	Type     string `json:"type"`
}
