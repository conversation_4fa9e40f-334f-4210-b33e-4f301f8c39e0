package ta_client

import (
	"context"
	"fmt"

	"gitlab.deepgate.io/apps/common/adapter/redis"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/ta_client/client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/ta_client/constants"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/ta_client/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
)

type TAAdapter interface {
	HasPrice(ctx context.Context, req *domain.CacheCheckAvailabilityRequest, hotelID, roomID, roomName string, rateData *domain.HubRateData, bedOptionID, tracingID string) (bool, float64, error)
	Book(ctx context.Context, hubBooking *domain.HubHotelOrder, tracingID string) (string, []*domain.HubRetrieveConfirmationID, enum.BookingStatus, error)
	Retrieve(ctx context.Context, order *domain.HubHotelOrder, tracingID string) ([]*domain.HubRetrieveConfirmationID, enum.BookingStatus, error)
	Cancel(ctx context.Context, reservervationCode, tracingID string) error
	ConfirmBook(ctx context.Context, reservervationCode, tracingID string) (float64, error)
}

func NewTAAdapter(cfg *config.Schema, requestRepo repositories.RequestRepository, redis redis.IRedis) TAAdapter {
	client := client.NewTAClient(cfg, requestRepo, redis)

	return &taAdapter{cfg, client}
}

type taAdapter struct {
	cfg    *config.Schema
	client client.TAClient
}

func (a *taAdapter) HasPrice(ctx context.Context, req *domain.CacheCheckAvailabilityRequest, hotelID, roomID, roomName string, rateData *domain.HubRateData, bedOptionID, tracingID string) (bool, float64, error) {
	clientReq, err := converts.ToGetPriceRequest(req, hotelID, roomID, roomName, rateData, bedOptionID)
	if err != nil {
		return false, 0, err
	}

	res, err := a.client.GetPrice(ctx, clientReq, tracingID)
	if err != nil {
		return false, 0, err
	}

	return res.Result.Available, res.Result.Amount, nil
}

func (a *taAdapter) Book(ctx context.Context, hubBooking *domain.HubHotelOrder, tracingID string) (string, []*domain.HubRetrieveConfirmationID, enum.BookingStatus, error) {
	clientReq, err := converts.ToClientCreateBookingRequest(hubBooking)
	if err != nil {
		return "", nil, enum.BookingStatusNone, domain.ErrInvalidValue
	}

	res, err := a.client.CreateBooking(ctx, clientReq, tracingID)
	if err != nil {
		return "", nil, enum.BookingStatusNone, domain.ErrInvalidValue
	}

	// Logic affect to domain service, careful with return params
	if res == nil || res.Result.BookingId == "" {
		hubBooking.CancelReason = "invalid book response"
		return "", nil, enum.BookingStatusPending, nil
	}

	hubBooking.TABookingID = res.Result.BookingId

	rateCfRaw := hubBooking.ExchangedRateDataCfRaw
	if res.Result.Amount > rateCfRaw.PayNow {
		hubBooking.CancelReason = fmt.Sprintf("book amount > paynow: %f > %f", res.Result.Amount, rateCfRaw.PayNow)

		if err := a.Cancel(ctx, res.Result.BookingId, tracingID); err != nil {
			return "", nil, enum.BookingStatusNone, err
		}

		return "", nil, enum.BookingStatusPending, nil
	}

	return res.Result.BookingId, nil, enum.BookingStatusPending, nil
}

func (a *taAdapter) Retrieve(ctx context.Context, order *domain.HubHotelOrder, tracingID string) ([]*domain.HubRetrieveConfirmationID, enum.BookingStatus, error) {
	res, err := a.client.RetrieveBooking(ctx, order.ReservationCode, tracingID)
	if err != nil {
		return nil, enum.BookingStatusNone, err
	}

	if res == nil {
		return nil, enum.BookingStatusPending, domain.ErrInvalidValue
	}

	var comfirmIds []*domain.HubRetrieveConfirmationID

	if res.Result.BookingStatus == constants.BookingStatusCompleted {
		comfirmIds = converts.MapReservationCode(order, res.Result.ConfirmationInfos)
	}

	return comfirmIds, converts.MapProviderBookingStatus(res.Result.BookingStatus), nil
}

func (a *taAdapter) Cancel(ctx context.Context, reservervationCode, tracingID string) error {
	return a.client.DeleteBooking(ctx, reservervationCode, tracingID)
}

func (a *taAdapter) ConfirmBook(ctx context.Context, reservervationCode, tracingID string) (float64, error) {
	res, err := a.client.ConfirmBooking(ctx, reservervationCode, tracingID)
	if err != nil {
		return 0, err
	}

	if res.Result.BookingStatus == constants.BookingStatusCanceled {
		return 0, domain.ErrBookingCanceled
	}

	return res.Result.Amount, nil
}
