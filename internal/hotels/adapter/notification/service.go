package notification

import (
	"context"

	"github.com/pkg/errors"
	backendPb "gitlab.deepgate.io/apps/api/gen/go/notification/backend"

	"gitlab.deepgate.io/apps/common/server"
	tracingGRPC "gitlab.deepgate.io/apps/common/tracing/grpc"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"google.golang.org/grpc/metadata"
)

type notificationServiceClient struct {
	cfg *config.Schema
}

type SendMessageRequest struct {
	EntityType  string
	EntityID    string
	ServiceCode string
	Action      string
	Data        map[string]string
}
type NotificationServiceClient interface {
	SendMessage(ctx context.Context, req *SendMessageRequest) error
}

func NewNotificationServiceClient(cfg *config.Schema) NotificationServiceClient {
	return &notificationServiceClient{
		cfg: cfg,
	}
}

func (s *notificationServiceClient) SendMessage(ctx context.Context, req *SendMessageRequest) error {
	conn, err := tracingGRPC.DialWithTrace(ctx, s.cfg.NotificationServiceEndpoint)
	if err != nil {
		return errors.Wrap(err, "Cannot connect order service")
	}
	defer conn.Close()

	md := server.WithMetadataInternalPartnership(s.cfg.InternalSecretToken, s.cfg.HubPartnershipID)
	newCtx := metadata.NewOutgoingContext(ctx, md)

	client := backendPb.NewTelegramServiceClient(conn)
	res, err := client.SendMessage(
		newCtx,
		&backendPb.SendMessageRequest{
			EntityType:  req.EntityType,
			EntityId:    req.EntityID,
			ServiceCode: req.ServiceCode,
			Action:      req.Action,
			Data:        req.Data,
		},
	)
	if err != nil {
		return errors.Wrap(err, "CreateHotelOrder failed")
	}

	if res == nil {
		return nil
	}

	if !res.IsSuccess {
		return errors.New(res.ErrorCode)
	}

	return nil
}
