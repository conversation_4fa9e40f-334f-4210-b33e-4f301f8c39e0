package converts

import (
	"fmt"
	"strconv"
	"time"

	"github.com/samber/lo"
	"gitlab.deepgate.io/apps/common/log"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/rate_hawk_client/client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/helpers"
	statictimezone "gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/static_timezone"
)

func ToDomainHubRooms(in *entities.Hotel, occu []*domain.HubSearchOccupancy, roomCount, daysCount int, contentRooms []*domain.Room, checkinDate string, hotelAddress *domain.Address) ([]*domain.HubRoom, error) {
	if in == nil {
		return nil, nil
	}

	contentRoomMap := map[string]*domain.Room{}
	for _, contentRoom := range contentRooms {
		roomKey := GetRoomKey(&contentRoom.RoomGroupExt, in.ID)

		contentRoomMap[roomKey] = contentRoom
	}

	roomRatesMap := map[string][]entities.Rate{}

	for _, entityRate := range in.Rates {
		domainExt := ToDomainRoomExt(&entityRate.RgExt)
		roomKey := GetRoomKey(domainExt, in.ID)

		roomRatesMap[roomKey] = append(roomRatesMap[roomKey], entityRate)
	}

	out := []*domain.HubRoom{}

	for roomKey, roomRates := range roomRatesMap {
		if roomRates == nil {
			continue
		}

		baseRate := roomRates[0]

		temp := &domain.HubRoom{
			RoomID:         "",
			ProviderRoomID: roomKey,
			Name:           baseRate.RoomName,
			RateData:       ToDomainHubRateDatas(roomRates, occu, roomCount, daysCount, roomKey, checkinDate, hotelAddress),
			Provider:       enum.HotelProviderRateHawk,
		}

		contentRoom := contentRoomMap[roomKey]
		if contentRoom != nil {
			temp.RoomID = contentRoom.RoomID
			temp.ProviderRoomID = contentRoom.ProviderIDs[enum.HotelProviderRateHawk]
		}

		out = append(out, temp)
	}

	return out, nil
}

// func isRefundable(paymentType *entities.PaymentType) bool {
// 	return paymentType.CancellationPenalties.FreeCancellationBefore != ""
// }

func getNightlyRates(days int, basicPerNight, taxAmount float64, currency string) []*domain.HubRate {
	out := []*domain.HubRate{}

	taxPerNight := taxAmount / float64(days)

	for i := 0; i < days; i++ {
		out = append(out, &domain.HubRate{
			// RateAmount: amountPerNight,
			RateBasic: basicPerNight,
			TaxAmount: taxPerNight,
			Currency:  currency,
		})
	}

	return out
}

func GetPayAtHotelByOccuRate(taxes []entities.Tax, cur, max int) []*domain.PayAtHotel {
	ratioRate := float64(cur) / float64(max)

	out := []*domain.PayAtHotel{}

	for _, tax := range taxes {
		if tax.IncludedBySupplier {
			continue
		}

		out = append(out, &domain.PayAtHotel{
			Amount:      F64(tax.Amount) * ratioRate,
			Description: tax.Name,
			Currency:    tax.CurrencyCode,
		})

	}

	return out
}

func ToDomainHubOccupancyRates(ins []*domain.HubSearchOccupancy, roomCount, daysCount int, paymentType *entities.PaymentType) []*domain.HubOccupancyRate {
	out := []*domain.HubOccupancyRate{}
	_, totalRateTaxes := calculateHubRateTaxes(paymentType.TaxData.Taxes, 1, true)
	priceTaxPerDayPerRoom := totalRateTaxes / (float64(roomCount) * float64(daysCount))

	priceAmountPerDayPerRoom := (F64(paymentType.CommissionInfo.Charge.AmountNet)) / (float64(roomCount) * float64(daysCount))
	priceNettPerDayPerRoom := priceAmountPerDayPerRoom - priceTaxPerDayPerRoom

	mapOccuRate := map[string]*domain.HubOccupancyRate{}
	for _, reqOccu := range ins {
		occuType := reqOccu.GenOccupancyType()

		if mapOccuRate[occuType] == nil {
			temp := &domain.HubOccupancyRate{
				OccupancyType: reqOccu.GenOccupancyType(),
				RoomQuantity:  1,
			}

			mapOccuRate[occuType] = temp
		} else {
			mapOccuRate[occuType].RoomQuantity++
		}
	}

	rateDiscount := &domain.HubRateDiscount{
		Amount:   F64(paymentType.CommissionInfo.Charge.AmountCommission) / float64(roomCount),
		Currency: paymentType.CurrencyCode,
		Code:     "",
		Name:     "discount on all-inclusive rate",
	}

	occuTax := getOccuTax(paymentType.TaxData.Taxes)
	occuTaxPerRoom := occuTax / float64(roomCount)

	for _, value := range mapOccuRate {
		if value == nil {
			continue
		}

		value.RateTaxes, _ = calculateHubRateTaxes(paymentType.TaxData.Taxes, roomCount, false)

		value.RateDiscounts = []*domain.HubRateDiscount{rateDiscount}

		value.PayAtHotel = GetPayAtHotelByOccuRate(paymentType.TaxData.Taxes, 1, roomCount)

		value.TotalNightlyRate = getNightlyRates(daysCount, priceNettPerDayPerRoom, occuTaxPerRoom, paymentType.CurrencyCode)

		out = append(out, value)
	}

	return out
}

func calculateHubRateTaxes(input []entities.Tax, divideBy int, isIncludeOccuTax bool) ([]*domain.HubRateTax, float64) {
	out := []*domain.HubRateTax{}

	sum := float64(0)

	for _, tax := range input {
		if !tax.IncludedBySupplier {
			continue
		}

		if !isIncludeOccuTax && tax.Name == "occupancy_tax" {
			continue
		}

		amount := F64(tax.Amount) / float64(divideBy)

		sum += amount

		out = append(out, &domain.HubRateTax{
			Amount:      &amount,
			Description: tax.Name,
			Currency:    tax.CurrencyCode,
			Included:    true,
		})

	}

	return out, sum
}

func convertToPayAtHotels(input []entities.Tax) ([]*domain.PayAtHotel, float64) {
	out := []*domain.PayAtHotel{}
	sum := float64(0)
	for _, tax := range input {
		if !tax.IncludedBySupplier {
			out = append(out, &domain.PayAtHotel{
				Amount:      F64(tax.Amount),
				Description: tax.Name,
				Currency:    tax.CurrencyCode,
			})
			sum += F64(tax.Amount)
		}
	}
	return out, sum
}

func getOccuTax(input []entities.Tax) float64 {

	sum := float64(0)

	for _, tax := range input {
		if tax.Name != "occupancy_tax" || !tax.IncludedBySupplier {
			continue
		}

		amount := F64(tax.Amount)

		sum += amount

	}

	return sum
}

func ToDomainHubCancelPolicies(input []entities.CancellationPolicy, checkinTime string, payNow string, currency string, hotelAddress *domain.Address) []*domain.HubCancelPolicy {
	out := []*domain.HubCancelPolicy{}

	for _, item := range input {
		if F64(item.CommissionInfo.Charge.AmountNet) == 0 {
			continue
		}

		out = append(out, ToDomainHubCancelPolicy(item, checkinTime, payNow, currency, hotelAddress))
	}

	return out
}

func ToDomainHubCancelPolicy(input entities.CancellationPolicy, checkinTime string, payNow string, currency string, hotelAddress *domain.Address) *domain.HubCancelPolicy {
	input.EndAt = helpers.ConvertToRFC3339(input.EndAt)
	input.StartAt = helpers.ConvertToRFC3339(input.StartAt)
	zone := ""

	if hotelAddress != nil {
		var err error
		var newEndAt, newStartAt string

		zone = statictimezone.GetTimezone(hotelAddress.City, hotelAddress.CountryCode)

		newEndAt, err = helpers.ConvertRFC3339Zone(input.EndAt, zone)
		if err != nil {
			log.Error("helpers.ConvertRFC3339Zone error", log.Any("error", err), log.Any("input.EndAt", input.EndAt), log.String("zone", zone))
		}

		newStartAt, err = helpers.ConvertRFC3339Zone(input.StartAt, zone)
		if err != nil {
			log.Error("helpers.ConvertRFC3339Zone error", log.Any("error", err), log.Any("input.StartAt", input.StartAt), log.String("zone", zone))
		}

		if newEndAt != "" {
			input.EndAt = newEndAt
		}

		if newStartAt != "" {
			input.StartAt = newStartAt
		}
	}

	amount := F64(input.AmountCharge)

	temp := &domain.HubCancelPolicy{
		StartDate: input.StartAt,
		EndDate:   input.EndAt,
		Currency:  currency,
		PenaltyInfo: domain.HubCancelPolicyPenaltyInfo{
			Amount: amount,
		},
		PartialRefund: amount != 0,
		Refundable:    input.AmountCharge != payNow,
		PenaltyAmount: amount,
	}

	if temp.EndDate == "" {
		temp.EndDate = helpers.ConvertToISO8601(checkinTime, zone)
	}

	if temp.StartDate == "" {
		loc, err := time.LoadLocation(zone)
		if err != nil {
			log.Error("helpers.ConvertRFC3339Zone error", log.Any("error", err), log.Any("input.StartAt", input.StartAt), log.String("zone", zone))
		} else {
			temp.StartDate = time.Now().In(loc).Format(time.RFC3339)
		}
	}

	return temp
}

func ToDomainHubNonrefundableDateRange(start, end string) []*domain.HubNonrefundableDateRange {
	return []*domain.HubNonrefundableDateRange{
		{
			StartDate: start,
			EndDate:   end,
		},
	}
}

func ToDomainHubRateDatas(ins []entities.Rate, reqOccu []*domain.HubSearchOccupancy, roomCount, daysCount int, roomKey string, checkinDate string, hotelAddress *domain.Address) []*domain.HubRateData {
	out := []*domain.HubRateData{}

	for _, in := range ins {
		out = append(out, ToDomainHubRateData(in, reqOccu, roomCount, daysCount, roomKey, checkinDate, hotelAddress))
	}

	return out
}

func canRefundable(ins []entities.CancellationPolicy, chargeAmount string, freeCancellationBefore string) bool {
	if len(ins) == 0 {
		return freeCancellationBefore != ""
	}

	for _, item := range ins {
		if item.StartAt != "" {
			startAtDt, err := time.Parse(constants.HubDateFormat, item.StartAt)
			if err != nil {
				startAtDt, err = time.Parse(constants.HubDateFormatWithoutTz, item.StartAt)
				if err != nil {
					log.Error("canRefundable time.Parse err", log.Any("err", err), log.String(" item.StartAt", item.StartAt))
					return false
				}
			}

			if !startAtDt.After(time.Now()) && item.CommissionInfo.Charge.AmountNet == chargeAmount {
				return false
			}
		}

		if item.StartAt == "" && item.CommissionInfo.Charge.AmountNet == chargeAmount {
			return false
		}
	}

	return true
}

func ToDomainHubRateData(in entities.Rate, reqOccu []*domain.HubSearchOccupancy, roomCount, daysCount int, roomKey string, checkinDate string, hotelAddress *domain.Address) *domain.HubRateData {
	bedName := in.RoomDataTrans.BeddingType

	if bedName == "" {
		bedName = constants.MulBedTypeDependsVi
	}

	paymentType, ok := GetPaymentType(in.PaymentOptions.PaymentTypes)
	if !ok {
		return nil
	}

	temp := &domain.HubRateData{
		RateID:         in.MatchHash,
		ProviderRateID: in.BookHash,
		Available:      strconv.Itoa(in.Allotment),
		CancelPolicies: nil,
		OccupancyRate:  ToDomainHubOccupancyRates(reqOccu, roomCount, daysCount, paymentType),
		Currency:       paymentType.CurrencyCode,
		BedOptions: []*domain.BedOption{
			{
				OptionID:        in.BookHash,
				Name:            bedName,
				Quantity:        1,
				PriceCheckToken: roomKey,
			},
		},
		Refundable:   canRefundable(paymentType.CancellationPenalties.Policies, paymentType.CommissionInfo.Charge.AmountNet, paymentType.CancellationPenalties.FreeCancellationBefore),
		HasBreakfast: in.MealData.HasBreakfast,
		HasExtraBed:  hasExtraBed(in.AmenitiesData),
		NonSmoking:   isNonSmoking(in.AmenitiesData),
	}

	if temp.Refundable {
		temp.CancelPolicies = ToDomainHubCancelPolicies(paymentType.CancellationPenalties.Policies, checkinDate, paymentType.CommissionInfo.Charge.AmountNet, paymentType.CurrencyCode, hotelAddress)
	}

	mealValue := in.MealData.Value
	nonSmoke := "non-smoking"
	if in.MealData.Value != "nomeal" {
		temp.Amenities = append(temp.Amenities, &domain.HubAmenity{
			ID:   in.MealData.Value,
			Name: constants.GetMealName(in.MealData.Value, ""),
		})
	}

	for _, amen := range in.AmenitiesData {
		if amen == mealValue || amen == nonSmoke {
			continue
		}

		temp.Amenities = append(temp.Amenities, &domain.HubAmenity{
			ID:   amen,
			Name: constants.GetRateHawkFieldName(amen, ""),
		})
	}

	return temp
}

// https://www.notion.so/deeptechjsc/BiziHUB-1-3-2-2025W3-73db83e4103f41e9af575a357dac25fb?p=80845c6570a34ff59da1d13d33cf36c7&pm=s
func isNonSmoking(amenities []string) bool {
	for _, amenity := range amenities {
		if amenity == "non-smoking" {
			return true
		}
	}
	return false
}

func hasExtraBed(amenities []string) bool {
	extraBedKeywords := []string{
		"extra-bed",
		"extra-beds",
		"extra-bed-fee",
		"extra-beds-fee",
	}

	for _, keyword := range extraBedKeywords {
		if lo.Contains(amenities, keyword) {
			return true
		}
	}
	return false
}
func GetRoomKey(roomExt *domain.RoomGroupExt, hotelID string) string {
	if hotelID == "test_hotel" {
		return fmt.Sprintf(
			"%s_%d-%d-%d-%d-%d-%d-%d-%d-%d-%d-%d-%d",
			hotelID,
			roomExt.Class,
			roomExt.Quality,
			roomExt.Sex,
			roomExt.Bathroom,
			roomExt.Bedding,
			roomExt.Family,
			roomExt.Capacity,
			0,
			roomExt.Bedrooms,
			roomExt.Balcony,
			roomExt.Floor,
			0,
		)
	}

	return fmt.Sprintf(
		"%s_%d-%d-%d-%d-%d-%d-%d-%d-%d-%d-%d-%d",
		hotelID,
		roomExt.Class,
		roomExt.Quality,
		roomExt.Sex,
		roomExt.Bathroom,
		roomExt.Bedding,
		roomExt.Family,
		roomExt.Capacity,
		roomExt.Club,
		roomExt.Bedrooms,
		roomExt.Balcony,
		roomExt.Floor,
		roomExt.View,
	)
}

func ToDomainRoomExt(in *entities.RgExt) *domain.RoomGroupExt {
	return &domain.RoomGroupExt{Class: in.Class, Quality: in.Quality, Sex: in.Sex, Bathroom: in.Bathroom, Bedding: in.Bedding, Family: in.Family, Capacity: in.Capacity, Club: in.Club, Bedrooms: in.Bedrooms, Balcony: in.Balcony, Floor: in.Floor, View: in.View}
}

// Prebook rate
