package converts

import (
	"github.com/samber/lo"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/rate_hawk_client/client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

func ToEntitiesGuests(occupancies []*domain.HubSearchOccupancy) []entities.Guest {
	guests := make([]entities.Guest, 0, len(occupancies))

	for _, item := range occupancies {
		childrens := []int{}

		if item.Children != nil {
			childrens = lo.Map(item.Children.Age, func(item uint, _ int) int { return int(item) })
		}

		guests = append(guests, entities.Guest{
			Adults:   int(item.Adults),
			Children: childrens,
		})
	}

	return guests
}
