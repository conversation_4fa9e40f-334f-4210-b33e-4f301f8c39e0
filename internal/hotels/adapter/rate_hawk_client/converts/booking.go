package converts

import (
	"fmt"
	"strings"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/rate_hawk_client/client/constants"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/rate_hawk_client/client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
)

func ToEntityOrderBookingFormFinishRequest(bookingReq *domain.HubBookReq, order *domain.HubHotelOrder, finalPayment *entities.PaymentType, cfg *config.Schema) (*entities.OrderBookingFormFinishRequest, error) {
	// firstHolder := bookingReq.Holder.HolderDetail[0]

	out := &entities.OrderBookingFormFinishRequest{
		User: entities.UserBookingRequest{
			Email:   cfg.CompanyEmail,
			Comment: MapSpecialRequests(bookingReq.Holder),
			Phone:   bookingReq.Holder.PhoneCode + bookingReq.Holder.PhoneNumber,
		},
		Partner: entities.BookingPartnerRequest{
			PartnerOrderID: order.OrderCode,
			// Comment:         "",
			// AmountSellB2B2C: "",
		},
		Language: constants.DefaultLanguage,
		// SupplierData: entities.SupplierDataRequest{},
		// Rooms:    []entities.RoomRequest{},
		// UpsellData: []entities.UpsellDataRequest{},
		PaymentType: entities.BookingPaymentTypeRequest{
			Type:         finalPayment.Type,
			Amount:       finalPayment.Amount,
			CurrencyCode: finalPayment.CurrencyCode,
		},
		TestingHashKey: bookingReq.TestingHashKey,
	}

	hodlerRoomMap := map[uint]*domain.HolderDetail{}

	for _, holder := range bookingReq.Holder.HolderDetail {
		hodlerRoomMap[holder.OccupancyIndex] = holder
	}

	for _, occu := range order.Hotel.ListRooms {
		holderInfo := hodlerRoomMap[occu.OccupancyIndex]
		if holderInfo == nil {
			return nil, fmt.Errorf("missing holder for occupancy %ds", occu.OccupancyIndex)
		}

		temp := entities.RoomRequest{
			Guests: []entities.BookingGuestInfo{
				{
					FirstName: holderInfo.GivenName,
					LastName:  holderInfo.Surname,
				},
			},
		}

		out.Rooms = append(out.Rooms, temp)
	}

	return out, nil
}

// For more details, refer to the documentation: https://www.notion.so/deeptechjsc/Hub-M-field-special_request-a7fe494ef3cf4d3db68ca6ba02085dd3
func MapSpecialRequests(holder *domain.HubHolderInfo) string {
	if holder == nil || holder.HolderDetail == nil {
		return ""
	}

	var specialRequests []string
	for _, detail := range holder.HolderDetail {
		if detail.SpecialRequest != "" {
			specialRequests = append(specialRequests, detail.SpecialRequest)
		}
	}
	return strings.Join(specialRequests, "\n")
}
