package entities

type Rate struct {
	// Hotel page
	BookHash         string         `json:"book_hash"`
	MatchHash        string         `json:"match_hash"`
	DailyPrices      []string       `json:"daily_prices"`
	Meal             string         `json:"meal"`
	MealData         MealData       `json:"meal_data"`
	PaymentOptions   PaymentOptions `json:"payment_options"`
	BarRatePriceData interface{}    `json:"bar_rate_price_data"`
	RgExt            RgExt          `json:"rg_ext"`
	RoomName         string         `json:"room_name"`
	RoomNameInfo     interface{}    `json:"room_name_info"`
	SerpFilters      []string       `json:"serp_filters"`
	SellPriceLimits  interface{}    `json:"sell_price_limits"`
	Allotment        int            `json:"allotment"`
	AmenitiesData    []string       `json:"amenities_data"`
	AnyResidency     bool           `json:"any_residency"`
	Deposit          interface{}    `json:"deposit"`
	NoShow           NoShow         `json:"no_show"`
	RoomDataTrans    RoomDataTrans  `json:"room_data_trans"`

	// Search
	SearchHash string `json:"search_hash"`
}

type RgExt struct {
	Class    int `json:"class"`
	Quality  int `json:"quality"`
	Sex      int `json:"sex"`
	Bathroom int `json:"bathroom"`
	Bedding  int `json:"bedding"`
	Family   int `json:"family"`
	Capacity int `json:"capacity"`
	Club     int `json:"club"`
	Bedrooms int `json:"bedrooms"`
	Balcony  int `json:"balcony"`
	View     int `json:"view"`
	Floor    int `json:"floor"`
}
