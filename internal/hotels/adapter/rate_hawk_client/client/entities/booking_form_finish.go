package entities

type OrderBookingFormFinishRequest struct {
	User           UserBookingRequest        `json:"user"`
	SupplierData   *SupplierDataRequest      `json:"supplier_data,omitempty"`
	Partner        BookingPartnerRequest     `json:"partner"`
	Language       string                    `json:"language"`
	Rooms          []RoomRequest             `json:"rooms"`
	UpsellData     []UpsellDataRequest       `json:"upsell_data"`
	PaymentType    BookingPaymentTypeRequest `json:"payment_type"`
	TestingHashKey string                    `json:"testing_hash_key"`
}

type UserBookingRequest struct {
	Email   string `json:"email"`
	Comment string `json:"comment"`
	Phone   string `json:"phone"`
}

type SupplierDataRequest struct {
	FirstNameOriginal string `json:"first_name_original,omitempty"`
	LastNameOriginal  string `json:"last_name_original,omitempty"`
	Phone             string `json:"phone,omitempty"`
	Email             string `json:"email,omitempty"`
}

type BookingPartnerRequest struct {
	PartnerOrderID  string `json:"partner_order_id"`
	Comment         string `json:"comment,omitempty"`
	AmountSellB2B2C string `json:"amount_sell_b2b2c,omitempty"`
}

type RoomRequest struct {
	Guests []BookingGuestInfo `json:"guests"`
}

type BookingGuestInfo struct {
	FirstName string `json:"first_name"`
	LastName  string `json:"last_name"`
}

type UpsellDataRequest struct {
	Name string `json:"name"`
	UID  string `json:"uid"`
}

type BookingPaymentTypeRequest struct {
	Type         string `json:"type"`
	Amount       string `json:"amount"`
	CurrencyCode string `json:"currency_code"`
}

// Res
type OrderBookingFormFinishResponse struct {
	Data                interface{} `json:"data"`
	Debug               interface{} `json:"debug"`
	Error               string      `json:"error"`
	Status              string      `json:"status"`
	TestingHashKeyMatch bool        `json:"testing_hash_key_match"`
}
