package entities

type OrderInformationRequest struct {
	Ordering   Ordering            `json:"ordering"`
	Pagination PaginationRequest   `json:"pagination"`
	Search     SearchFilterRequest `json:"search"`
	Language   string              `json:"language"`
}

type Ordering struct {
	OrderingType string `json:"ordering_type"`
	OrderingBy   string `json:"ordering_by"`
}

type PaginationRequest struct {
	PageSize   string `json:"page_size"`
	PageNumber string `json:"page_number"`
}

type SearchFilterRequest struct {
	CreatedAt       *FilterCreatedAt `json:"created_at,omitempty"`
	PartnerOrderIds []string         `json:"partner_order_ids,omitempty"`
}

type FilterCreatedAt struct {
	FromDate string `json:"from_date"`
}

// Res

type OrderInformationResponse struct {
	Data   OrderData   `json:"data"`
	Status string      `json:"status"`
	Error  string      `json:"error"`
	Debug  interface{} `json:"debug"`
}

type OrderData struct {
	CurrentPageNumber int     `json:"current_page_number"`
	Orders            []Order `json:"orders"`
	TotalOrders       int     `json:"total_orders"`
	TotalPages        int     `json:"total_pages"`
	FoundOrders       int     `json:"found_orders,omitempty"`
	FoundPages        int     `json:"found_pages,omitempty"`
}

type Order struct {
	AgreementNumber           string           `json:"agreement_number"`
	AmountPayable             Amount           `json:"amount_payable"`
	AmountPayableVat          Amount           `json:"amount_payable_vat"`
	AmountRefunded            Amount           `json:"amount_refunded"`
	AmountSell                Amount           `json:"amount_sell"`
	AmountSellB2B2C           Amount           `json:"amount_sell_b2b2c"`
	AmountSellB2B2CCommission Amount           `json:"amount_sell_b2b2c_commission,omitempty"`
	ApiAuthKeyID              int              `json:"api_auth_key_id"`
	CancellationInfo          CancellationInfo `json:"cancellation_info"`
	CheckinAt                 string           `json:"checkin_at"`
	CheckoutAt                string           `json:"checkout_at"`
	ContractSlug              string           `json:"contract_slug"`
	CreatedAt                 string           `json:"created_at"`
	HasTickets                bool             `json:"has_tickets"`
	HotelData                 HotelData        `json:"hotel_data"`
	InvoiceID                 string           `json:"invoice_id"`
	IsCancellable             *bool            `json:"is_cancellable,omitempty"`
	IsChecked                 bool             `json:"is_checked"`
	MetaData                  MetaData         `json:"meta_data"`
	ModifiedAt                string           `json:"modified_at"`
	Nights                    int              `json:"nights"`
	OrderID                   int              `json:"order_id"`
	OrderType                 string           `json:"order_type"`
	PartnerData               PartnerData      `json:"partner_data"`
	PaymentData               PaymentData      `json:"payment_data"`
	Roomnights                int              `json:"roomnights"`
	RoomsData                 []RoomData       `json:"rooms_data"`
	Source                    string           `json:"source"`
	Status                    string           `json:"status"`
	SupplierData              SupplierData     `json:"supplier_data"`
	Taxes                     []OrderTaxInfo   `json:"taxes"`
	TotalVat                  TotalVat         `json:"total_vat,omitempty"`
	Upsells                   []OrderUpsell    `json:"upsells"`
	UserData                  OrderUserData    `json:"user_data"`
	CancelledAt               interface{}      `json:"cancelled_at"`
}

type Amount struct {
	Amount       string `json:"amount"`
	CurrencyCode string `json:"currency_code"`
}

type CancellationInfo struct {
	FreeCancellationBefore string   `json:"free_cancellation_before"`
	Policies               []Policy `json:"policies"`
}

type Policy struct {
	Penalty Penalty `json:"penalty"`
	EndAt   string  `json:"end_at"`
	StartAt string  `json:"start_at"`
}

type Penalty struct {
	Amount       string     `json:"amount"`
	AmountInfo   AmountInfo `json:"amount_info,omitempty"`
	CurrencyCode string     `json:"currency_code"`
}

type AmountInfo struct {
	AmountCommission string `json:"amount_commission"`
	AmountGross      string `json:"amount_gross"`
	AmountNet        string `json:"amount_net"`
}

type HotelData struct {
	ID      string `json:"id"`
	Hid     int    `json:"hid,omitempty"`
	OrderID string `json:"order_id"`
}

type MetaData struct {
	VoucherOrderComment interface{} `json:"voucher_order_comment"`
}

type PartnerData struct {
	OrderComment string `json:"order_comment,omitempty"`
	OrderID      string `json:"order_id"`
}

type PaymentData struct {
	InvoiceID      int         `json:"invoice_id"`
	InvoiceIDV2    string      `json:"invoice_id_v2,omitempty"`
	PaidAt         string      `json:"paid_at,omitempty"`
	PaymentDue     string      `json:"payment_due"`
	PaymentPending string      `json:"payment_pending"`
	PaymentType    string      `json:"payment_type"`
	PaymentBy      interface{} `json:"payment_by"`
}

type RoomData struct {
	BeddingName  []string  `json:"bedding_name"`
	GuestData    GuestData `json:"guest_data"`
	MealName     string    `json:"meal_name"`
	RoomIdx      int       `json:"room_idx"`
	RoomName     string    `json:"room_name"`
	HasBreakfast bool      `json:"has_breakfast,omitempty"`
	NoChildMeal  bool      `json:"no_child_meal,omitempty"`
}

type GuestData struct {
	AdultsNumber   int              `json:"adults_number"`
	ChildrenNumber int              `json:"children_number"`
	Guests         []OrderGuestInfo `json:"guests"`
}

type OrderGuestInfo struct {
	FirstName         string      `json:"first_name"`
	FirstNameOriginal string      `json:"first_name_original"`
	IsChild           bool        `json:"is_child"`
	LastName          string      `json:"last_name"`
	LastNameOriginal  string      `json:"last_name_original"`
	Age               interface{} `json:"age"`
}

type SupplierData struct {
	OrderID        string      `json:"order_id"`
	ConfirmationID string      `json:"confirmation_id,omitempty"`
	Name           interface{} `json:"name"`
}

type OrderTaxInfo struct {
	AmountTax  Amount `json:"amount_tax"`
	IsIncluded bool   `json:"is_included"`
	Name       string `json:"name"`
}

type TotalVat struct {
	Amount       string `json:"amount"`
	CurrencyCode string `json:"currency_code"`
	Included     bool   `json:"included"`
}

type OrderUpsell struct {
	AmountPayable          Amount      `json:"amount_payable"`
	AmountPayableVat       Amount      `json:"amount_payable_vat"`
	AmountRefunded         Amount      `json:"amount_refunded"`
	AmountSell             Amount      `json:"amount_sell"`
	AmountSellB2B2C        Amount      `json:"amount_sell_b2b2c"`
	CancelledAt            string      `json:"cancelled_at"`
	CreatedAt              string      `json:"created_at"`
	FreeCancellationBefore string      `json:"free_cancellation_before"`
	OrderID                int         `json:"order_id"`
	OrderType              string      `json:"order_type"`
	PaymentData            PaymentData `json:"payment_data"`
	Status                 string      `json:"status"`
	Type                   string      `json:"type"`
}

type OrderUserData struct {
	Email           string      `json:"email"`
	UserComment     string      `json:"user_comment,omitempty"`
	ArrivalDatetime interface{} `json:"arrival_datetime,omitempty"`
}
