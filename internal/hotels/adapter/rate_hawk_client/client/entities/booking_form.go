package entities

type OrderBookingFormRequest struct {
	PartnerOrderID string `json:"partner_order_id"`
	BookHash       string `json:"book_hash"`
	Language       string `json:"language"`
	UserIP         string `json:"user_ip"`
	TestingHashKey string `json:"testing_hash_key"`
}

// Response
type OrderBookingFormResponse struct {
	Data   OrderBookingFormData `json:"data"`
	Error  string               `json:"error"`
	Status string               `json:"status"`
	// Debug  interface{}          `json:"debug"`
	TestingHashKeyMatch bool `json:"testing_hash_key_match"`
}

type OrderBookingFormData struct {
	ItemID                        int           `json:"item_id"`
	OrderID                       int           `json:"order_id"`
	PartnerOrderID                string        `json:"partner_order_id"`
	IsGenderSpecificationRequired bool          `json:"is_gender_specification_required"`
	PaymentTypes                  []PaymentType `json:"payment_types"`
	UpsellData                    []UpsellData  `json:"upsell_data"`
}

type UpsellData struct {
	ChargePrice ChargePrice   `json:"charge_price"`
	Data        UpsellDetails `json:"data"`
	Name        string        `json:"name"`
	RuleID      int           `json:"rule_id"`
	UID         string        `json:"uid"`
}

type ChargePrice struct {
	Amount       string `json:"amount"`
	CurrencyCode string `json:"currency_code"`
}

type UpsellDetails struct {
	CheckoutTime string `json:"checkout_time,omitempty"`
	CheckinTime  string `json:"checkin_time,omitempty"`
}
