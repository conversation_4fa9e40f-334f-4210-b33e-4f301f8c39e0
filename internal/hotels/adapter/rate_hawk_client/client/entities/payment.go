package entities

type PaymentType struct {
	Amount                string                `json:"amount"`
	ShowAmount            string                `json:"show_amount"`
	CurrencyCode          string                `json:"currency_code"`
	ShowCurrencyCode      string                `json:"show_currency_code"`
	By                    interface{}           `json:"by"`
	IsNeedCreditCardData  bool                  `json:"is_need_credit_card_data"`
	IsNeedCVC             bool                  `json:"is_need_cvc"`
	Type                  string                `json:"type"`
	VatData               VatData               `json:"vat_data"`
	TaxData               TaxData               `json:"tax_data"`
	Perks                 interface{}           `json:"perks"`
	CommissionInfo        CommissionInfo        `json:"commission_info"`
	CancellationPenalties CancellationPenalties `json:"cancellation_penalties"`
	RecommendedPrice      RecommendedPrice      `json:"recommended_price"`
}

type RecommendedPrice struct {
	Amount           string `json:"amount"`
	ShowAmount       string `json:"show_amount"`
	CurrencyCode     string `json:"currency_code"`
	ShowCurrencyCode string `json:"show_currency_code"`
}

type PaymentOptions struct {
	PaymentTypes []PaymentType `json:"payment_types"`
}

type TaxData struct {
	Taxes []Tax `json:"taxes"`
}

type Tax struct {
	Name               string `json:"name"`
	IncludedBySupplier bool   `json:"included_by_supplier"`
	Amount             string `json:"amount"`
	CurrencyCode       string `json:"currency_code"`
}

type VatData struct {
	Included     bool   `json:"included"`
	Applied      bool   `json:"applied"`
	Amount       string `json:"amount"`
	CurrencyCode string `json:"currency_code"`
	Value        string `json:"value"`
}
