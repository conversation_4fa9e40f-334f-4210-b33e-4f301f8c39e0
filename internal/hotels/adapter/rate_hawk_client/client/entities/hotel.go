package entities

type Hotel struct {
	ID           string `json:"id"`
	HID          int    `json:"hid"`
	Rates        []Rate `json:"rates"`
	BarPriceData string `json:"bar_rate_price_data"`
	// Hotelpage
	Deposit       Deposit       `json:"deposit"`
	NoShow        NoShow        `json:"no_show"`
	RoomDataTrans RoomDataTrans `json:"room_data_trans"`
}

type RoomDataTrans struct {
	MainRoomType string `json:"main_room_type"`
	MainName     string `json:"main_name"`
	Bathroom     string `json:"bathroom"`
	BeddingType  string `json:"bedding_type"`
	MiscRoomType string `json:"misc_room_type"`
}

type NoShow struct {
	Amount       string `json:"amount"`
	CurrencyCode string `json:"currency_code"`
	FromTime     string `json:"from_time"`
}

type Deposit struct {
	Amount       string `json:"amount"`
	CurrencyCode string `json:"currency_code"`
	IsRefundable bool   `json:"is_refundable"`
}
