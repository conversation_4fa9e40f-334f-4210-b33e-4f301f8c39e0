package entities

type PrebookRequest struct {
	Hash                 string  `json:"hash"`
	PriceIncreasePercent float64 `json:"price_increase_percent"`
}

type PrebookResponse struct {
	Data   PrebookData  `json:"data"`
	Debug  PreBookDebug `json:"debug"`
	Status string       `json:"status"`
	Error  string       `json:"error"`
}

type PrebookData struct {
	Changes Changes `json:"changes"`
	Hotels  []Hotel `json:"hotels"`
}

type Changes struct {
	PriceChanged bool `json:"price_changed"`
}

type PreBookDebug struct {
	Request         PreBookDebugRequest `json:"request"`
	KeyID           int                 `json:"key_id"`
	ValidationError interface{}         `json:"validation_error"`
}

type PreBookDebugRequest struct {
	Hash                 string `json:"hash"`
	BookHash             string `json:"book_hash"`
	PriceIncreasePercent int    `json:"price_increase_percent"`
}
