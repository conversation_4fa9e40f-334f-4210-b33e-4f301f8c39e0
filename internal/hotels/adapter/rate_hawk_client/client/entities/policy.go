package entities

type MealData struct {
	Value        string `json:"value"`
	HasBreakfast bool   `json:"has_breakfast"`
	NoChildMeal  bool   `json:"no_child_meal"`
}

type CancellationPolicy struct {
	StartAt      string `json:"start_at"`
	EndAt        string `json:"end_at"`
	AmountCharge string `json:"amount_charge"`
	AmountShow   string `json:"amount_show"`
	// ShowCommission   *CommissionInfo `json:"show"`   // need verify
	// ChargeCommission *CommissionInfo `json:"charge"` // need verify
	CommissionInfo CommissionInfo `json:"commission_info"`
}

type CancellationPenalties struct {
	Policies               []CancellationPolicy `json:"policies"`
	FreeCancellationBefore string               `json:"free_cancellation_before"`
}
