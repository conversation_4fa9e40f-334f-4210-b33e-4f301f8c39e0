package entities

type HotelpageRequest struct {
	Checkin   string  `json:"checkin"`
	Checkout  string  `json:"checkout"`
	Residency string  `json:"residency"`
	Language  string  `json:"language"`
	Guests    []Guest `json:"guests"`
	ID        string  `json:"id"`
	Currency  string  `json:"currency"`
}

type HotelpageResponse struct {
	Data   HotelpageData  `json:"data"`
	Debug  HotelpageDebug `json:"debug"`
	Status string         `json:"status"`
	Error  string         `json:"error"`
}

type HotelpageData struct {
	Hotels []Hotel `json:"hotels"`
}

type HotelpageDebug struct {
	Request         HotelpageRequestDebug `json:"request"`
	KeyID           int                   `json:"key_id"`
	ValidationError interface{}           `json:"validation_error"`
}

type HotelpageRequestDebug struct {
	Checkin   string  `json:"checkin"`
	Checkout  string  `json:"checkout"`
	Residency string  `json:"residency"`
	Language  string  `json:"language"`
	Guests    []Guest `json:"guests"`
	ID        string  `json:"id"`
	Currency  string  `json:"currency"`
}
