package client

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"net/http"
	"net/url"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/log"
	tracingHttp "gitlab.deepgate.io/apps/common/tracing/http"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/rate_hawk_client/client/constants"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/rate_hawk_client/client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	pkgConstants "gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/helpers"
)

const (
	successfulCode        = 200
	methodGet             = "GET"
	methodPost            = "POST"
	defaultRequestTimeout = 60
	successStatus         = "ok"
	errorStatus           = "error"
	pendingStatus         = "PENDING"
)

type RateHawkClient interface {
	HotelsSearchEngineResultsPage(ctx context.Context, req *entities.SearchEngineResultsPageRequest, tracingID string) (*entities.SearchEngineResultsPageResponse, error)
	Hotelpage(ctx context.Context, req *entities.HotelpageRequest, tracingID string) (*entities.HotelpageResponse, error)
	Prebook(ctx context.Context, req *entities.PrebookRequest, tracingID string) (*entities.PrebookResponse, error)
	OrderBookingForm(ctx context.Context, req *entities.OrderBookingFormRequest, tracingID string) (*entities.OrderBookingFormResponse, error)
	OrderBookingFormFinish(ctx context.Context, req *entities.OrderBookingFormFinishRequest, tracingID string) (*entities.OrderBookingFormFinishResponse, error)
	OrderInformation(ctx context.Context, req *entities.OrderInformationRequest, tracingID string) (*entities.OrderInformationResponse, error)
	OrderBookingFinishStatus(ctx context.Context, req *entities.OrderBookingFinishStatusRequest, tracingID string) (*entities.OrderBookingFinishStatusResponse, error)
	OrderCancellation(ctx context.Context, req *entities.OrderCancellationRequest, tracingID string) (*entities.OrderCancellationResponse, error)
}

type rateHawkClient struct {
	baseUrl     string
	password    string
	username    string
	requestRepo repositories.RequestRepository
}

func NewRateHawkClient(cfg *config.Schema, requestRepo repositories.RequestRepository) RateHawkClient {
	return &rateHawkClient{
		baseUrl:     cfg.RateHawKBaseURL,
		password:    cfg.RateHawkPassword,
		username:    cfg.RateHawkUsername,
		requestRepo: requestRepo,
	}
}

func (c *rateHawkClient) OrderCancellation(ctx context.Context, req *entities.OrderCancellationRequest, tracingID string) (*entities.OrderCancellationResponse, error) {
	resData := &entities.OrderCancellationResponse{}
	header := c.getHeader()

	if err := c.doRequest(ctx, methodPost, constants.APIOrderCancellation, header, req, tracingID, resData); err != nil {
		return nil, errors.Wrap(err, "doRequest")
	}

	if resData.Status != successStatus {
		if resData.Error == "order_not_cancellable" {
			return nil, domain.ErrBookingNotAllowedToCancel
		}

		return nil, c.handleErr(resData.Error)
	}

	return resData, nil
}

func (c *rateHawkClient) OrderBookingFinishStatus(ctx context.Context, req *entities.OrderBookingFinishStatusRequest, tracingID string) (*entities.OrderBookingFinishStatusResponse, error) {
	if req.TestingHashKey != "" {
		res_mock := &entities.OrderBookingFinishStatusResponse{
			Data: entities.OrderBookingFinishStatusData{
				PartnerOrderID: req.PartnerOrderID,
				Percent:        0,
			},
			Status: "failed",
		}

		if value := pkgConstants.GetTestCaseValue(enum.HotelProviderRateHawk, pkgConstants.ActionOrderBookingFinishStatus, req.TestingHashKey); value != "" {
			res_mock.TestingHashKeyMatch = true
			res_mock.Error = value
			if value == "NOT DEFINED" {
				res_mock.Status = pendingStatus
				return res_mock, nil
			}

			return res_mock, c.handleErr(res_mock.Error)
		}

	}

	resData := &entities.OrderBookingFinishStatusResponse{}
	header := c.getHeader()

	if err := c.doRequest(ctx, methodPost, constants.APIOrderBookingFinishStatus, header, req, tracingID, resData); err != nil {
		resData.Status = pendingStatus
		log.Error("RH doRequest", log.Any("err", err))
		return resData, nil
	}

	if resData.Status == errorStatus {
		switch resData.Error {
		case "soldout":
			return nil, domain.ErrRoomSoldOut
		case "order_not_found":
			return nil, domain.ErrBookingNotFound
		case "3ds", "block", "charge", "book_limit", "not_allowed", "provider", "booking_finish_did_not_succeed", "decoding_json", "endpoint_exceeded_limit",
			"endpoint_not_active",
			"endpoint_not_found",
			"incorrect_credentials",
			"invalid_auth_header",
			"invalid_params",
			"lock",
			"no_auth_header",
			"overdue_debt",
			"unexpected_method":
			return nil, c.handleErr(resData.Error)

		default:
			resData.Status = pendingStatus
			log.Error("RH OrderBookingFinishStatus", log.Any("PartnerOrderID", req.PartnerOrderID), log.Any("err", resData.Error))
			return resData, nil
		}
		// return nil, c.handleErr(resData.Error)
	}

	return resData, nil
}

func (c *rateHawkClient) OrderInformation(ctx context.Context, req *entities.OrderInformationRequest, tracingID string) (*entities.OrderInformationResponse, error) {
	resData := &entities.OrderInformationResponse{}
	header := c.getHeader()

	if err := c.doRequest(ctx, methodPost, constants.APIOrderInformation, header, req, tracingID, resData); err != nil {
		return nil, errors.Wrap(err, "doRequest")
	}

	if resData.Status != successStatus {
		return nil, c.handleErr(resData.Error)
	}

	return resData, nil
}

func (c *rateHawkClient) OrderBookingFormFinish(ctx context.Context, req *entities.OrderBookingFormFinishRequest, tracingID string) (*entities.OrderBookingFormFinishResponse, error) {
	resData := &entities.OrderBookingFormFinishResponse{}
	header := c.getHeader()

	if err := c.doRequest(ctx, methodPost, constants.APIOrderBookingFinish, header, req, tracingID, resData); err != nil {
		resData.Status = pendingStatus
		log.Error("RH doRequest", log.Any("err", err))
		return resData, nil
	}

	if resData.Status == errorStatus {
		switch resData.Error {
		case "incorrect_guest_name", "incorrect_guests_number", "incorrect_rooms_number":
			return nil, domain.ErrEmptyHolder
		case "rate_not_found":
			return nil, domain.ErrRoomSoldOut
		case "book_hash_not_found", "order_not_found":
			return nil, domain.ErrBookingNotFound
		case "double_booking_finish":
			return nil, domain.ErrBookingExisted
		default:
			return nil, c.handleErr(resData.Error)
		}
		// return nil, c.handleErr(resData.Error)
	}

	return resData, nil
}

func (c *rateHawkClient) OrderBookingForm(ctx context.Context, req *entities.OrderBookingFormRequest, tracingID string) (*entities.OrderBookingFormResponse, error) {
	if req.TestingHashKey != "" {
		res_mock := &entities.OrderBookingFormResponse{
			Data: entities.OrderBookingFormData{
				ItemID:                        0,
				OrderID:                       0,
				PartnerOrderID:                req.PartnerOrderID,
				IsGenderSpecificationRequired: false,
				PaymentTypes: []entities.PaymentType{
					{
						Amount:       "0",
						ShowAmount:   "0",
						CurrencyCode: "VND",
					},
				},
				UpsellData: []entities.UpsellData{
					{
						ChargePrice: entities.ChargePrice{
							Amount:       "0",
							CurrencyCode: "VND",
						},
						Data: entities.UpsellDetails{
							CheckoutTime: "2025-04-01T12:00:00+07:00",
							CheckinTime:  "2025-04-12T12:00:00+07:00",
						},
						Name:   "test",
						RuleID: 0,
						UID:    "test",
					},
				},
			},
			Status: "failed",
		}

		if value := pkgConstants.GetTestCaseValue(enum.HotelProviderRateHawk, pkgConstants.ActionOrderBookingForm, req.TestingHashKey); value != "" {
			res_mock.TestingHashKeyMatch = true
			res_mock.Error = value

			if value == "NOT DEFINED" {
				res_mock.Status = pendingStatus
				return res_mock, nil
			}

			return res_mock, c.handleErr(res_mock.Error)
		}

	}

	resData := &entities.OrderBookingFormResponse{}
	header := c.getHeader()

	if err := c.doRequest(ctx, methodPost, constants.APIOrderBookingForm, header, req, tracingID, resData); err != nil {
		resData.Status = pendingStatus
		log.Error("RH doRequest", log.Any("err", err))
		return resData, nil
	}

	if resData.Status == errorStatus {
		switch resData.Error {
		case "rate_not_found", "hotel_not_found":
			return nil, domain.ErrRoomSoldOut
		case "double_booking_form", "duplicate_reservation":
			return nil, domain.ErrBookingExisted
		case "contract_mismatch", "reservation_is_not_allowed", "sandbox_restriction":
			return nil, c.handleErr(resData.Error)
		default:
			resData.Status = pendingStatus
			log.Error("RH OrderBookingForm", log.Any("PartnerOrderID", req.PartnerOrderID), log.Any("err", resData.Error))
			return resData, nil
		}
	}

	return resData, nil
}

func (c *rateHawkClient) Prebook(ctx context.Context, req *entities.PrebookRequest, tracingID string) (*entities.PrebookResponse, error) {
	resData := &entities.PrebookResponse{}
	header := c.getHeader()

	if err := c.doRequest(ctx, methodPost, constants.APIPrebook, header, req, tracingID, resData); err != nil {
		return nil, errors.Wrap(err, "doRequest")
	}

	if resData.Status != successStatus {
		switch resData.Error {
		case "no_available_rates", "rate_not_found":
			return nil, domain.ErrRoomSoldOut
		default:
			return nil, c.handleErr(resData.Error)
		}
		// return nil, c.handleErr(resData.Error)
	}

	return resData, nil
}

func (c *rateHawkClient) Hotelpage(ctx context.Context, req *entities.HotelpageRequest, tracingID string) (*entities.HotelpageResponse, error) {
	resData := &entities.HotelpageResponse{}
	header := c.getHeader()

	if err := c.doRequest(ctx, methodPost, constants.APIHotelpage, header, req, tracingID, resData); err != nil {
		return nil, errors.Wrap(err, "doRequest")
	}

	if resData.Status != successStatus {
		if resData.Error == "child's age must be in range 0 to 17 incl" {
			return nil, domain.ErrInvalidChildAge
		}

		return nil, c.handleErr(resData.Error)
	}

	return resData, nil
}

func (c *rateHawkClient) HotelsSearchEngineResultsPage(ctx context.Context, req *entities.SearchEngineResultsPageRequest, tracingID string) (*entities.SearchEngineResultsPageResponse, error) {
	resData := &entities.SearchEngineResultsPageResponse{}
	header := c.getHeader()

	if err := c.doRequest(ctx, methodPost, constants.APIHotelsSearchEngineResultsPage, header, req, tracingID, resData); err != nil {
		return nil, errors.Wrap(err, "doRequest")
	}

	if resData.Status != successStatus {
		if resData.Error == "child's age must be in range 0 to 17 incl" {
			return nil, domain.ErrInvalidChildAge
		}

		return nil, c.handleErr(resData.Error)
	}

	return resData, nil
}

func (c *rateHawkClient) getHeader() map[string]string {
	return map[string]string{
		"Content-Type":  "application/json",
		"Authorization": helpers.GenerateBasicAuthHeader(c.username, c.password),
	}
}

func (c *rateHawkClient) handleErr(errMsg string) error {
	return errors.New(errMsg)
}

func (c *rateHawkClient) do(
	ctx context.Context,
	header map[string]string,
	fullPath string,
	method string,
	action string,
	body interface{},
) (_ []byte, _ int, duration int64, _ error) {
	beginAt := time.Now().UnixMilli()

	defer func() {
		duration = time.Now().UnixMilli() - beginAt
	}()

	jsonData, err := json.Marshal(body)
	if err != nil {
		log.Error("rateHawk Marshal error",
			log.Any("error", err),
			log.String("relativePath", fullPath),
			log.String("action", action),
			log.Any("req", body))
		return nil, 0, duration, err
	}

	var response *http.Response

	payload := bytes.NewBuffer(jsonData)
	response, err = tracingHttp.RawRequest(ctx, fullPath, method, payload, header)
	if err != nil {
		return nil, 0, duration, errors.Wrap(err, "tracingHttp.RawRequest")
	}

	defer response.Body.Close()

	resBody, err := io.ReadAll(response.Body)
	if err != nil {
		return resBody, response.StatusCode, duration, errors.Wrap(err, "io.ReadAll")
	}

	if response.StatusCode != successfulCode {
		err := errors.New(response.Status)
		return resBody, response.StatusCode, duration, errors.Wrap(err, "response status code not success")
	}

	return resBody, response.StatusCode, duration, nil
}

func (c *rateHawkClient) doRequest(
	ctx context.Context,
	method string,
	apiPath string,
	header map[string]string,
	body interface{},
	tracingID string,
	responseData interface{},
) error {
	var err error

	fullPath, err := url.JoinPath(c.baseUrl, apiPath)
	if err != nil {
		return errors.Wrap(err, "Parse base URL")
	}

	response, statusCode, duration, err := c.do(ctx, header, fullPath, method, apiPath, body)

	headerClone := map[string]string{}

	for key, val := range header {
		headerClone[key] = val
	}

	go func(headerClone map[string]string) {
		path := fullPath
		bCtx, cancel := context.WithTimeout(context.Background(), pkgConstants.RequestRepoCtxTimeout)

		defer cancel()

		requestID := uuid.NewString()

		if c.requestRepo == nil {
			return
		}

		doErr := err

		if err := c.requestRepo.Create(bCtx, &repositories.Request{
			RequestID:  requestID,
			Path:       path,
			Method:     methodPost,
			Body:       body,
			Headers:    headerClone,
			Response:   response,
			StatusCode: statusCode,
			Duration:   duration,
			Action:     apiPath,
			Provider:   enum.HotelProviderRateHawk,
			TracingID:  tracingID,
			IsJson:     true,
			ErrorMsg:   doErr,
		}); err != nil {
			log.Error("RateHawk requestRepo.CreateV2 error",
				log.Any("error", err),
				log.String("requestID", requestID),
				log.String("path", path),
				log.String("action", apiPath),
				log.Any("req", body),
			)
		}
	}(headerClone)

	if err != nil {
		return errors.Wrap(err, "c.do")
	}

	if err := json.Unmarshal(response, responseData); err != nil {
		return errors.Wrap(err, "Unmarshal")
	}

	return nil
}

func MockCase(adapter enum.HotelProvider, action, key string) (bool, string) {
	value := pkgConstants.GetTestCaseValue(adapter, action, key)
	if value == "" {
		return false, ""
	}

	return true, value
}
