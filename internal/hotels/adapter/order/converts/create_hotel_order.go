package converts

import (
	backendPb "gitlab.deepgate.io/apps/api/gen/go/order/backend"
	"gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

func FromDomainCustomerInfo(info *domain.CustomerInfo) *backendPb.OrderCustomerInfo {
	if info == nil {
		return nil
	}

	return &backendPb.OrderCustomerInfo{
		Name:        info.Name,
		FirstName:   info.FirstName,
		LastName:    info.LastName,
		Email:       info.Email,
		PhoneCode:   info.PhoneCode,
		PhoneNumber: info.PhoneNumber,
	}
}

func FromDomainCustomersInfo(items []*domain.CustomerInfo) []*backendPb.OrderCustomerInfo {
	result := make([]*backendPb.OrderCustomerInfo, 0, len(items))
	for _, v := range items {
		result = append(result, FromDomainCustomerInfo(v))
	}

	return result
}

func FromDomainCurrency(info *domain.OrderCurrencyInfo) *backendPb.OrderCurrencyInfo {
	if info == nil {
		return nil
	}
	return &backendPb.OrderCurrencyInfo{
		Id:   info.Id,
		Name: info.Name,
		Code: info.Code,
		Icon: info.Icon,
	}
}

func FromDomainHotelOrder(info *domain.HotelOrder) *backendPb.HotelOrder {
	if info == nil {
		return nil
	}
	return &backendPb.HotelOrder{
		UserId:         info.UserID,
		HotelOrderCode: info.OrderCode,
		Service:        info.Service,
		Currency:       FromDomainCurrency(info.Currency),
		TotalAmount:    info.TotalPrice,
		Customer:       FromDomainCustomerInfo(info.CustomerInfo),
		Note:           info.Note,
	}
}

func ToDomainTransactionInfo(info *backendPb.PlaceOrderRes) *domain.TransactionInfo {
	if info == nil || info.TransactionInfo == nil {
		return nil
	}

	return &domain.TransactionInfo{
		ID:        info.TransactionInfo.Id,
		Type:      enum.TransactionType(info.TransactionInfo.Type),
		Amount:    info.TransactionInfo.Amount,
		CreatedAt: info.TransactionInfo.CreatedAt,
	}
}
