package webhook

import (
	"bytes"
	"context"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"time"

	tracingHttp "gitlab.deepgate.io/apps/common/tracing/http"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	commonError "gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/webhook/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/webhook/entity"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
)

type WebhookAdapter interface {
	SendTransaction(ctx context.Context, transaction *domain.Transaction, key, url string) error
}

type webhookAdapter struct {
	cfg         *config.Schema
	requestRepo repositories.RequestRepository
}

func NewWebhookAdapter(
	cfg *config.Schema,
	requestRepo repositories.RequestRepository,
) WebhookAdapter {
	return &webhookAdapter{cfg, requestRepo}
}

func (a *webhookAdapter) doRequest(
	ctx context.Context,
	action string,
	webhookURL string,
	body interface{},
) error {
	response, statusCode, duration, doErr := a.do(ctx, webhookURL, methodPost, body, headers)

	go func() {
		path := webhookURL
		bCtx, cancel := context.WithTimeout(context.Background(), constants.RequestRepoCtxTimeout)

		defer cancel()

		requestID := uuid.NewString()

		if a.requestRepo == nil {
			return
		}

		logReq := &repositories.Request{
			RequestID:  requestID,
			Path:       path,
			Method:     methodPost,
			Body:       body,
			Headers:    headers,
			Response:   response,
			StatusCode: statusCode,
			Provider:   enum.HotelProviderNone,
			Duration:   duration.Milliseconds(),
			Action:     action,
			ErrorMsg:   doErr,
			IsJson:     true,
		}

		if err := a.requestRepo.Create(bCtx, logReq); err != nil {
			log.Error("requestRepo.Create error", log.Any("error", err), log.Any("logReq", logReq))
		}
	}()

	if doErr != nil {
		return errors.Wrap(doErr, "a.do")
	}

	return nil
}

func (a *webhookAdapter) do(
	ctx context.Context,
	webhookURL string,
	method string,
	body interface{},
	headers map[string]string,
) ([]byte, int, time.Duration, error) {
	var duration time.Duration
	beginAt := time.Now()
	jsonData, err := json.Marshal(body)
	if err != nil {
		return nil, 0, duration, errors.Wrap(err, "xml.Marshal")
	}

	response, err := tracingHttp.RawRequest(ctx, webhookURL, method, bytes.NewBuffer(jsonData), headers)
	if err != nil {
		return nil, 0, duration, errors.Wrap(err, "tracingHttp.RawRequest")
	}

	duration = time.Since(beginAt)

	defer response.Body.Close()

	resBody, err := io.ReadAll(response.Body)
	if err != nil {
		return resBody, response.StatusCode, duration, errors.Wrap(err, "io.ReadAll")
	}

	if response.StatusCode != successfulCode {
		err := fmt.Errorf("status %v", response.Status)
		return resBody, response.StatusCode, duration, errors.Wrap(err, "response status code not success")
	}

	return resBody, response.StatusCode, duration, nil
}

func (a *webhookAdapter) createTransactionSignature(transaction *entity.Transaction, key string) (string, error) {
	if transaction == nil {
		return "", commonError.WithMsg(commonError.ErrInvalidInput, "transaction nil")
	}

	signStr := fmt.Sprintf("%s|%s|%s|%s|%s|%d|%s",
		transaction.OfficeID,
		transaction.TransactionID,
		transaction.OrderCode,
		transaction.SessionID,
		transaction.Amount,
		transaction.TransactionType,
		key,
	)

	hash := md5.Sum([]byte(signStr))
	return hex.EncodeToString(hash[:]), nil
}

func (a *webhookAdapter) SendTransaction(ctx context.Context, transaction *domain.Transaction, key, url string) error {
	if key == "" || url == "" {
		return commonError.WithMsg(commonError.ErrInvalidInput, "webhookCfg nil")
	}

	payload := converts.ToTransactionEntity(transaction)

	signature, err := a.createTransactionSignature(payload, key)
	if err != nil {
		return errors.Wrap(err, "createTransactionSignature")
	}

	payload.Signature = signature

	err = a.doRequest(ctx, "webhook_send_transaction", url, payload)
	if err != nil {
		return errors.Wrap(err, "doRequest")
	}

	return nil
}
