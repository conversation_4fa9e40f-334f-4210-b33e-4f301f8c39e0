package converts

import (
	"fmt"

	commonConstants "gitlab.deepgate.io/apps/common/constants"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/webhook/entity"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

func ToPaymentMethod(t *domain.PaymentMethod) *entity.PaymentMethod {
	if t == nil {
		return nil
	}

	return &entity.PaymentMethod{
		ID:         t.ID,
		Code:       t.Code,
		Name:       t.Name,
		DisplayFee: t.Display<PERSON>ee,
		Fee:        t.<PERSON>e,
		Method:     t.Method,
		Icon:       t.Icon,
	}
}

func ToTransactionEntity(t *domain.Transaction) *entity.Transaction {
	if t == nil {
		return nil
	}

	currency := commonConstants.CurrencyCodeVND

	return &entity.Transaction{
		TransactionID:   t.TransactionID,
		OrderCode:       t.OrderCode,
		SessionID:       t.SessionID,
		CreatedAt:       t.CreatedAt,
		Amount:          fmt.Sprintf("%f", t.Amount),
		PaymentMethod:   ToPaymentMethod(t.PaymentMethod),
		TransactionType: t.Type,
		Currency:        currency,
		OfficeID:        t.OfficeID,
	}
}
