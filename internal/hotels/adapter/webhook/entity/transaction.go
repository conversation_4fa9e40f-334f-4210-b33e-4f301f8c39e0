package entity

import (
	commonEnum "gitlab.deepgate.io/apps/common/enum"
)

type PaymentMethod struct {
	ID         string                   `json:"id"`
	Name       string                   `json:"name"`
	Code       string                   `json:"code"`
	Method     commonEnum.PaymentMethod `json:"method"`
	Fee        float64                  `json:"fee"`
	DisplayFee string                   `json:"display_fee"`
	Icon       string                   `json:"icon"`
}

type Transaction struct {
	OfficeID        string                     `json:"office_id"`
	TransactionID   string                     `json:"transaction_id"`
	SessionID       string                     `json:"session_id"`
	OrderCode       string                     `json:"order_code"`
	CreatedAt       int64                      `json:"created_at"`
	Amount          string                     `json:"amount"`
	Currency        string                     `json:"currency"`
	PaymentMethod   *PaymentMethod             `json:"payment_method,omitempty"`
	TransactionType commonEnum.TransactionType `json:"transaction_type"`
	Signature       string                     `json:"signature"`
}
