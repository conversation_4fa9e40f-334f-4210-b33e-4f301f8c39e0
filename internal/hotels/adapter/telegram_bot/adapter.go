package telegram_bot

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"net/http"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
)

const pathSendMessage = "/sendMessage"
const pathBot = "/bot"

type TelegramBotAdapter interface {
	SendMessage(ctx context.Context, channelID string, message string) error
}

type telegramBotAdapter struct {
	cfg *config.Schema
}

func NewTelegramBotAdapter(cfg *config.Schema) TelegramBotAdapter {
	return &telegramBotAdapter{
		cfg: cfg,
	}
}

type payload struct {
	ChatID    string `json:"chat_id"`
	Text      string `json:"text"`
	ParseMode string `json:"parse_mode"`
}

func (tg *telegramBotAdapter) SendMessage(ctx context.Context, chatID string, message string) error {

	if chatID == "" {
		return errors.New("no chat id found")
	}

	requestURL := tg.cfg.TelegramURL + pathBot + tg.cfg.TelegramManualBookingBotToken + pathSendMessage
	payloadSend := payload{
		ChatID:    chatID,
		Text:      message,
		ParseMode: "MarkdownV2",
	}

	payloadJSON, err := json.Marshal(payloadSend)
	if err != nil {
		log.Error("[Telegram][SendMessage] marshal failed", log.String("url", requestURL))
		return err
	}

	res, err := http.Post(requestURL, "application/json", bytes.NewBuffer(payloadJSON))
	if err != nil {
		log.Error("[Telegram][SendMessage] request failed", log.Any("err", err),
			log.String("url", requestURL), log.String("message", message))
		return err
	}

	body, err := io.ReadAll(res.Body)
	if err != nil {
		log.Error("[Telegram][SendMessage] Response Unmarshal failed",
			log.String("url", requestURL),
			log.Any("response: ", body))
		return err
	}

	if res.StatusCode != 200 {
		log.Error("[Telegram][SendMessage] StatusCode != 200",
			log.String("url", requestURL), log.Any("payload", payloadSend), log.String("body", string(body)))
	}

	return nil
}
