package converts

import (
	walletBackendPb "gitlab.deepgate.io/apps/api/gen/go/wallet/backend"
	"gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

func ToDomainTransactionInfo(in *walletBackendPb.RequestRefundRes) *domain.TransactionInfo {
	if in == nil || in.TransactionInfo == nil {
		return nil
	}

	return &domain.TransactionInfo{
		ID:        in.TransactionInfo.Id,
		Type:      enum.TransactionType(in.TransactionInfo.Type),
		Amount:    in.TransactionInfo.Amount,
		CreatedAt: in.TransactionInfo.CreatedAt,
	}
}
