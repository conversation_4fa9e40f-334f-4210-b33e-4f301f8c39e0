package models

import "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"

type PlaceType string

type Place struct {
	PlaceID            string         `json:"id"`
	Type               enum.PlaceType `json:"type"`
	Location           *Location      `json:"location"`
	SearchName         string         `json:"search_name"`
	SearchNameVi       string         `json:"search_name_vi"`
	SearchNameViNormal string         `json:"search_name_vi_normal"`
	Name               string         `json:"name"`
	NameVi             string         `json:"name_vi"`
	Address            string         `json:"address"`
	CountryCode        string         `json:"country_code"`
	Version            string         `json:"version"`
}

type Location struct {
	Lat float64 `json:"lat"`
	Lon float64 `json:"lon"`
}
