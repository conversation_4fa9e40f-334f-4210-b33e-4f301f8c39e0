package es_repositories

import (
	"context"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/adapter/elasticsearch"
	commonDomain "gitlab.deepgate.io/apps/common/domain"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/es/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
)

type PlaceElasticRepository interface {
	Search(ctx context.Context, req *domain.SearchDestinationReq, contentVersion string) ([]*domain.Place, *commonDomain.Pagination, error)
	Bulk(ctx context.Context, index string, body []byte) error
}
type placeElasticRepository struct {
	es elasticsearch.ES
}

func NewPlaceElasticRepository(es elasticsearch.ES) PlaceElasticRepository {
	return &placeElasticRepository{
		es: es,
	}
}

func (r *placeElasticRepository) Search(ctx context.Context, req *domain.SearchDestinationReq, contentVersion string) ([]*domain.Place, *commonDomain.Pagination, error) {
	var q string
	var err error

	if contentVersion != "" {
		q, err = converts.BuildSearchQueryWithVersion(req.Query, contentVersion, enum.PlaceTypeName[req.PlaceType])
		if err != nil {
			return nil, req.Pagination, errors.Wrap(err, "esPlaceRepository.Search: converts.BuildSearchQuery")
		}
	} else {
		return nil, req.Pagination, errors.New("content version is empty")
	}

	esRes, _, err := r.es.Search(ctx, config.PlaceIndexName, config.PlaceIndexName, q, 20, 0)
	if err != nil {
		log.Error("es.Search err", log.Any("err", err), log.Any("index", config.PlaceIndexName), log.Any("q", q))
		return nil, req.Pagination, errors.Wrap(err, "esPlaceRepository.Search: es.Search")
	}

	places, err := converts.ToSearchModelPlace(esRes)
	if err != nil {
		return nil, req.Pagination, errors.Wrap(err, "esPlaceRepository.Search: converts.ToSearchModelPlaces")
	}

	req.Pagination.TotalRecord = int64(esRes["total"].(map[string]interface{})["value"].(float64))
	req.Pagination.TotalPage = req.Pagination.TotalRecord / req.Pagination.PageLimit
	domainPlaces := converts.ToDomainPlaces(places, req.Language)
	return domainPlaces, req.Pagination, nil
}

func (r *placeElasticRepository) Bulk(ctx context.Context, index string, body []byte) error {
	_, err := r.es.Bulk(ctx, index, index, body)
	return errors.Wrap(err, "esPlaceRepository.Bulk: es.Bulk")
}
