package converts

import (
	"encoding/json"
	"strings"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/helpers"

	"github.com/pkg/errors"
	"github.com/samber/lo"

	commonHelper "gitlab.deepgate.io/apps/common/helpers"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/es/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
)

func FromDomainHotel(in *domain.Hotel, localizeData []*domain.Hotel) *models.Place {
	hotelModel := &models.Place{
		PlaceID: in.HotelID,
		Type:    enum.PlaceTypeHotel,
		Version: in.Version,
	}

	if in.Address != nil {
		addressArr := []string{in.Address.Line1, in.Address.StateProvinceName, in.Address.City}

		hotelModel.Address = strings.Join(lo.Filter[string](addressArr, func(item string, _ int) bool {
			return item != ""
		}), " ,")

		hotelModel.CountryCode = in.Address.CountryCode
	}

	for _, data := range localizeData {
		name := strings.ToLower(data.Name)

		noAccents := helpers.RemoveAccents(name)

		switch data.Language {
		case "vi-VN":
			hotelModel.NameVi = data.Name
			hotelModel.SearchNameVi = name
			hotelModel.SearchNameViNormal = noAccents
		case "en-US":
			hotelModel.Name = data.Name
			hotelModel.SearchName = name
			if hotelModel.SearchNameViNormal == "" {
				hotelModel.SearchNameViNormal = noAccents
			}
		}

		if hotelModel.SearchName == hotelModel.SearchNameVi {
			hotelModel.SearchNameVi = ""
		}
	}

	if in.Location != nil && in.Location.Coordinates != nil {
		hotelModel.Location = &models.Location{
			Lat: in.Location.Coordinates.Latitude,
			Lon: in.Location.Coordinates.Longitude,
		}
	}

	return hotelModel
}

func FromDomainRegion(in *domain.Region, localizeData []*domain.Region) *models.Place {
	regionModel := &models.Place{
		PlaceID:     in.RegionID,
		Type:        enum.PlaceTypeValue[in.Type],
		CountryCode: in.CountryCode,
		Version:     in.Version,
	}

	for _, data := range localizeData {
		name := strings.ToLower(data.FullName)

		noAccents := helpers.RemoveAccents(name)
		if noAccents == name {
			noAccents = ""
		}

		switch data.Language {
		case "vi-VN":
			regionModel.NameVi = data.Name
			regionModel.SearchNameVi = name
			regionModel.Address = data.FullName
			regionModel.SearchNameViNormal = noAccents
		case "en-US":
			regionModel.Name = data.Name
			regionModel.SearchName = name
			regionModel.Address = data.FullName
			if regionModel.SearchNameViNormal == "" {
				regionModel.SearchNameViNormal = noAccents
			}
		}

		if regionModel.SearchName == regionModel.SearchNameVi {
			regionModel.SearchNameVi = ""
		}
	}

	if in.Coordinates != nil {
		regionModel.Location = &models.Location{
			Lat: in.Coordinates.CenterLatitude,
			Lon: in.Coordinates.CenterLongitude,
		}
	}

	return regionModel
}

func ToSearchModelPlace(esRes map[string]interface{}) ([]*models.Place, error) {
	hits := esRes["hits"].([]interface{})
	places := []*models.Place{}

	for _, hit := range hits {
		hitMap := hit.(map[string]interface{})
		source := hitMap["_source"]

		var place models.Place
		err := commonHelper.Copy(&place, &source)
		if err != nil {
			return nil, errors.Wrap(err, "ToSearchModelPlace: helpers.Copy")
		}
		places = append(places, &place)
	}

	return places, nil
}

func ToDomainPlace(in *models.Place, language string) *domain.Place {
	if in == nil {
		return nil
	}

	result := &domain.Place{
		PlaceID:     in.PlaceID,
		Type:        in.Type,
		Lang:        language,
		Address:     in.Address,
		CountryCode: in.CountryCode,
	}

	if language == "" {
		language = constants.DefaultLanguage
	}

	switch language {
	case "vi-VN":
		result.Name = in.NameVi
	default:
		result.Name = in.Name
	}

	if result.Name == "" {
		result.Name = in.Name
	}

	if in.Location != nil {
		result.Location = &domain.Coordinates{
			Latitude:  in.Location.Lat,
			Longitude: in.Location.Lon,
		}
	}

	return result
}

func ToDomainPlaces(in []*models.Place, language string) []*domain.Place {
	res := make([]*domain.Place, 0, len(in))
	for _, item := range in {
		res = append(res, ToDomainPlace(item, language))
	}

	return res
}

func escapeSpecialChars(query string) string {
	specialChars := []string{"\\", "+", "-", "=", "&&", "||", ">", "<", "!", "(", ")", "{", "}", "[", "]", "^", "\"", "~", "*", "?", ":"}
	for _, char := range specialChars {
		query = strings.ReplaceAll(query, char, "\\"+char)
	}
	return query
}

const (
	OperatorAnd = "and"
	OperatorOr  = "or"
)

func BuildSearchQueryWithVersion(text string, version, placeType string) (string, error) {
	searchText := strings.ToLower(text)

	query := map[string]interface{}{
		"_source": []string{"name", "name_vi", "address", "type", "location", "country_code", "id"},
		"query": map[string]interface{}{
			"function_score": map[string]interface{}{
				"query": map[string]interface{}{
					"multi_match": map[string]interface{}{
						"query":    searchText,
						"fields":   []string{"search_name", "search_name_vi", "search_name_vi_normal"},
						"type":     "bool_prefix",
						"operator": OperatorAnd,
					},
				},
				"functions": []map[string]interface{}{
					{
						"filter": map[string]interface{}{
							"term": map[string]interface{}{
								"type": "province_state",
							},
						},
						"weight": 3.5,
					},
					{
						"filter": map[string]interface{}{
							"term": map[string]interface{}{
								"type": "city",
							},
						},
						"weight": 3,
					},
					{
						"filter": map[string]interface{}{
							"term": map[string]interface{}{
								"type": "neighborhood",
							},
						},
						"weight": 2.5,
					},
				},
				"score_mode": "sum",
				"boost_mode": "multiply",
			},
		},
	}

	qByte, err := json.Marshal(query)
	if err != nil {
		return "", errors.Wrap(err, "BuildSearchQueryWithVersion: helpers.Copy")
	}
	return string(qByte), nil
}
