package test

import (
	"context"
	"fmt"
	"time"

	"gitlab.deepgate.io/apps/common/adapter/elasticsearch"
	commonDomain "gitlab.deepgate.io/apps/common/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/es/es_repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"golang.org/x/exp/rand"
)

const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"

func GenerateRandomString() string {
	rand.Seed(uint64(time.Now().UnixNano())) // Seed the random number generator
	length := rand.Intn(3) + 3               // Random length between 3 and 5
	result := make([]byte, length)

	for i := range result {
		result[i] = charset[rand.Intn(len(charset))] // Randomly select a character
	}

	return string(result)
}

func Test(es elasticsearch.ES) {
	repo := es_repositories.NewPlaceElasticRepository(es)
	errCount := 0

	for i := 0; i < 100; i++ {
		request := &domain.SearchDestinationReq{
			Query: "muong thanh da nang", //GenerateRandomString(),
			Pagination: &commonDomain.Pagination{
				PageLimit:   10,
				PageCurrent: 1,
			},
		}

		fmt.Println("Run test with query: ", request.Query)
		_, _, err := repo.Search(context.Background(), request, "1")
		if err != nil {
			fmt.Println("err", err)
			errCount++
		}
	}

	fmt.Println("Test error count ", errCount)
}
