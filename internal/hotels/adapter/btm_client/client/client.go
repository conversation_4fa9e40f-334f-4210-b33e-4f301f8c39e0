package client

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/log"
	tracingHttp "gitlab.deepgate.io/apps/common/tracing/http"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/btm_client/client/constants"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/btm_client/client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	pkgConstants "gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
)

const (
	successfulCode        = 200
	methodGet             = "GET"
	methodPost            = "POST"
	defaultRequestTimeout = 60
)

type BTMClient interface {
	GetToken(ctx context.Context, tracingID string) (*entities.TokenResponse, error)
	WebhookNotification(ctx context.Context, req *entities.ExpediaWebhookNotificationRequest, tracingID string) (*entities.WebhookResponse, error)
}

type btmClient struct {
	tokenUrl    string
	webhookUrl  string
	clientID    string
	secretKey   string
	scope       string
	requestRepo repositories.RequestRepository
}

func NewBTMClient(cfg *config.Schema, requestRepo repositories.RequestRepository) BTMClient {
	return &btmClient{
		tokenUrl:    cfg.BTMTokenURL,
		webhookUrl:  cfg.BTMWebhookURL,
		clientID:    cfg.BTMClientID,
		secretKey:   cfg.BTMSecretKey,
		scope:       cfg.BTMScope,
		requestRepo: requestRepo,
	}
}

func (c *btmClient) getHeader(token string) map[string]string {
	return map[string]string{
		"Content-Type":  "application/json",
		"Authorization": "Bearer " + token,
	}
}

func (c *btmClient) handleErr(errMsg string) error {
	return errors.New(errMsg)
}

func (c *btmClient) GetToken(ctx context.Context, tracingID string) (*entities.TokenResponse, error) {
	res := &entities.TokenResponse{}
	tokenURL := c.tokenUrl + constants.APIRouteToken

	data := url.Values{}
	data.Set("grant_type", "client_credentials")
	data.Set("client_id", c.clientID)
	data.Set("client_secret", c.secretKey)
	data.Set("scope", c.scope)

	resp, err := http.PostForm(tokenURL, data)
	if err != nil {
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("failed to get token: %s", resp.Status)
	}

	json.NewDecoder(resp.Body).Decode(&res)

	return res, nil
}

func (c *btmClient) WebhookNotification(ctx context.Context, req *entities.ExpediaWebhookNotificationRequest, tracingID string) (*entities.WebhookResponse, error) {
	res := &entities.WebhookResponse{}

	tokenRes, err := c.GetToken(ctx, tracingID)
	if err != nil {
		return nil, errors.Wrap(err, "GetToken")
	}

	if err := c.doRequest(ctx, methodPost, c.webhookUrl+constants.APIRouteWebhook, c.getHeader(tokenRes.AccessToken), req, tracingID, res); err != nil {
		return nil, errors.Wrap(err, "doRequest")
	}

	if res.StatusCode != 200 && res.StatusCode != 201 {
		log.Error("send webhook error", log.Any("res", res))
		return nil, fmt.Errorf("send webhook error: %s", res.Message)
	}

	return res, nil
}
func (c *btmClient) do(
	ctx context.Context,
	header map[string]string,
	fullPath string,
	method string,
	action string,
	body interface{},
) (_ []byte, _ int, duration int64, _ error) {
	beginAt := time.Now().UnixMilli()

	defer func() {
		duration = time.Now().UnixMilli() - beginAt
	}()

	jsonData, err := json.Marshal(body)
	if err != nil {
		log.Error("tourmind Marshal error",
			log.Any("error", err),
			log.String("relativePath", fullPath),
			log.String("action", action),
			log.Any("req", body))
		return nil, 0, duration, err
	}

	var response *http.Response

	payload := bytes.NewBuffer(jsonData)
	response, err = tracingHttp.RawRequest(ctx, fullPath, method, payload, header)
	if err != nil {
		return nil, 0, duration, errors.Wrap(err, "tracingHttp.RawRequest")
	}

	defer response.Body.Close()

	resBody, err := io.ReadAll(response.Body)
	if err != nil {
		return resBody, response.StatusCode, duration, errors.Wrap(err, "io.ReadAll")
	}

	if response.StatusCode != successfulCode {
		err := errors.New(response.Status)
		return resBody, response.StatusCode, duration, errors.Wrap(err, "response status code not success")
	}

	return resBody, response.StatusCode, duration, nil
}

func (c *btmClient) doRequest(
	ctx context.Context,
	method string,
	fullPath string,
	header map[string]string,
	body interface{},
	tracingID string,
	responseData interface{},
) error {
	var err error

	response, statusCode, duration, err := c.do(ctx, header, fullPath, method, fullPath, body)

	headerClone := map[string]string{}

	for key, val := range header {
		headerClone[key] = val
	}

	go func(headerClone map[string]string) {
		path := fullPath
		bCtx, cancel := context.WithTimeout(context.Background(), pkgConstants.RequestRepoCtxTimeout)

		defer cancel()

		requestID := uuid.NewString()

		if c.requestRepo == nil {
			return
		}

		doErr := err

		if err := c.requestRepo.Create(bCtx, &repositories.Request{
			RequestID:  requestID,
			Path:       path,
			Method:     methodPost,
			Body:       body,
			Headers:    headerClone,
			Response:   response,
			StatusCode: statusCode,
			Duration:   duration,
			Action:     fullPath,
			Provider:   enum.HotelProviderTourMind,
			TracingID:  tracingID,
			IsJson:     true,
			ErrorMsg:   doErr,
		}); err != nil {
			log.Error("Tourmind requestRepo.CreateV2 error",
				log.Any("error", err),
				log.String("requestID", requestID),
				log.String("path", path),
				log.String("action", fullPath),
				log.Any("req", body),
			)
		}
	}(headerClone)

	if err != nil {
		return errors.Wrap(err, "c.do")
	}

	if err := json.Unmarshal(response, responseData); err != nil {
		return errors.Wrap(err, "Unmarshal")
	}

	return nil
}
