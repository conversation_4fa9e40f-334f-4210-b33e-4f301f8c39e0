package entities

import (
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
)

type ExpediaWebhookHeader struct {
	APIKey    string `json:"api_key"`
	Signature string `json:"signature"`
	Timestamp int64  `json:"timestamp"`
}
type ExpediaWebhookNotificationRequest struct {
	*ExpediaWebhookHeader
	EventID              string                       `json:"event_id"`
	EventType            enum.ExpediaWebhookEventType `json:"event_type"`
	EventTime            string                       `json:"event_time"`
	ItineraryID          string                       `json:"itinerary_id"`
	Email                string                       `json:"email"`
	Message              string                       `json:"message"`
	AffiliateReferenceID string                       `json:"affiliate_reference_id"`
	TopicTags            enum.ExpediaWebhookTopicTag  `json:"topic_tags"`
	Rooms                []*ExpediaNotificationRoom   `json:"rooms"` // Array of Room structs
	Nonce                string                       `json:"-"`
}

type ExpediaNotificationRoom struct {
	ConfirmationInfo *ConfirmationInfo `json:"confirmation_id"`
}

type ConfirmationInfo struct {
	Expedia  string `json:"expedia"`
	Property string `json:"property"`
}
