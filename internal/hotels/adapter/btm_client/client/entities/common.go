package entities

type TokenRequest struct {
	ClientID string `json:"clientId"`
	Secret   string `json:"secret"`
	Scope    string `json:"scope"`
}

type TokenResponse struct {
	AccessToken string `json:"access_token"`
	ExpiresIn   int    `json:"expires_in"`
	Scope       string `json:"scope"`
	TokenType   string `json:"token_type"`
}

type WebhookResponse struct {
	Version    string `json:"version"`
	StatusCode int    `json:"statusCode"`
	Message    string `json:"message"`
	Result     string `json:"result"`
}

type ErrorDetails struct {
	ErrorCode    string `json:"ErrorCode"`
	ErrorMessage string `json:"ErrorMessage"`
}
