package converts

import (
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/btm_client/client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

func FromDomainExpediaWebhookNotificationRequest(info *domain.ExpediaWebhookNotificationRequest) *entities.ExpediaWebhookNotificationRequest {
	if info == nil {
		return nil
	}
	return &entities.ExpediaWebhookNotificationRequest{
		ExpediaWebhookHeader: FromDomainExpediaWebhookHeader(info.ExpediaWebhookHeader),
		EventID:              info.EventID,
		EventType:            info.EventType,
		EventTime:            info.EventTime,
		ItineraryID:          info.ItineraryID,
		Email:                info.Email,
		Message:              info.Message,
		AffiliateReferenceID: info.AffiliateReferenceID,
		TopicTags:            info.TopicTags,
		Rooms:                FromDomainExpediaNotificationRooms(info.Rooms),
		Nonce:                info.Key,
	}
}

func FromDomainExpediaWebhookHeader(info *domain.ExpediaWebhookHeader) *entities.ExpediaWebhookHeader {
	if info == nil {
		return nil
	}
	return &entities.ExpediaWebhookHeader{
		APIKey:    info.APIKey,
		Signature: info.Signature,
		Timestamp: info.Timestamp,
	}
}
func FromDomainExpediaNotificationRoom(info *domain.ExpediaNotificationRoom) *entities.ExpediaNotificationRoom {
	if info == nil {
		return nil
	}
	return &entities.ExpediaNotificationRoom{
		ConfirmationInfo: nil,
	}
}

func FromDomainExpediaNotificationRooms(infos []*domain.ExpediaNotificationRoom) []*entities.ExpediaNotificationRoom {
	if infos == nil {
		return nil
	}
	var result []*entities.ExpediaNotificationRoom
	for _, info := range infos {
		result = append(result, FromDomainExpediaNotificationRoom(info))
	}
	return result
}

func FromDomainConfirmationInfo(info *domain.ConfirmationInfo) *entities.ConfirmationInfo {
	if info == nil {
		return nil
	}
	return &entities.ConfirmationInfo{
		Expedia:  info.Expedia,
		Property: info.Property,
	}
}

func FromDomainConfirmationInfos(infos []*domain.ConfirmationInfo) []*entities.ConfirmationInfo {
	if infos == nil {
		return nil
	}
	var result []*entities.ConfirmationInfo
	for _, info := range infos {
		result = append(result, FromDomainConfirmationInfo(info))
	}
	return result
}

//

func ToDomainExpediaWebhookNotificationRequest(info *entities.ExpediaWebhookNotificationRequest) *domain.ExpediaWebhookNotificationRequest {
	if info == nil {
		return nil
	}
	return &domain.ExpediaWebhookNotificationRequest{
		ExpediaWebhookHeader: ToDomainExpediaWebhookHeader(info.ExpediaWebhookHeader),
		EventID:              info.EventID,
		EventType:            info.EventType,
		EventTime:            info.EventTime,
		ItineraryID:          info.ItineraryID,
		Email:                info.Email,
		Message:              info.Message,
		AffiliateReferenceID: info.AffiliateReferenceID,
		TopicTags:            info.TopicTags,
		Rooms:                ToDomainExpediaNotificationRooms(info.Rooms),
		Key:                  info.Nonce,
	}
}

func ToDomainExpediaWebhookHeader(info *entities.ExpediaWebhookHeader) *domain.ExpediaWebhookHeader {
	if info == nil {
		return nil
	}
	return &domain.ExpediaWebhookHeader{
		APIKey:    info.APIKey,
		Signature: info.Signature,
		Timestamp: info.Timestamp,
	}
}

func ToDomainExpediaNotificationRoom(info *entities.ExpediaNotificationRoom) *domain.ExpediaNotificationRoom {
	if info == nil {
		return nil
	}
	return &domain.ExpediaNotificationRoom{
		ConfirmationInfo: ToDomainConfirmationInfo(info.ConfirmationInfo),
	}
}

func ToDomainExpediaNotificationRooms(infos []*entities.ExpediaNotificationRoom) []*domain.ExpediaNotificationRoom {
	if infos == nil {
		return nil
	}
	var result []*domain.ExpediaNotificationRoom
	for _, info := range infos {
		result = append(result, ToDomainExpediaNotificationRoom(info))
	}
	return result
}

func ToDomainConfirmationInfo(info *entities.ConfirmationInfo) *domain.ConfirmationInfo {
	if info == nil {
		return nil
	}
	return &domain.ConfirmationInfo{
		Expedia:  info.Expedia,
		Property: info.Property,
	}
}

func ToDomainConfirmationInfos(infos []*entities.ConfirmationInfo) []*domain.ConfirmationInfo {
	if infos == nil {
		return nil
	}
	var result []*domain.ConfirmationInfo
	for _, info := range infos {
		result = append(result, ToDomainConfirmationInfo(info))
	}
	return result
}
