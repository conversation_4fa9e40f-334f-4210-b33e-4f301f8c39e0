package btm_client

import (
	"context"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/btm_client/client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/btm_client/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
)

type BTMAdapter interface {
	SendWebhookNotification(ctx context.Context, req *domain.ExpediaWebhookNotificationRequest) error
}

type btmAdapter struct {
	cfg    *config.Schema
	client client.BTMClient
}

func NewBTMAdapter(cfg *config.Schema, reqRepo repositories.RequestRepository) BTMAdapter {
	return &btmAdapter{
		cfg:    cfg,
		client: client.NewBTMClient(cfg, reqRepo),
	}
}

func (a *btmAdapter) SendWebhookNotification(ctx context.Context, req *domain.ExpediaWebhookNotificationRequest) error {
	payload := converts.FromDomainExpediaWebhookNotificationRequest(req)
	_, err := a.client.WebhookNotification(ctx, payload, payload.Nonce)
	if err != nil {
		return err
	}

	return nil
}
