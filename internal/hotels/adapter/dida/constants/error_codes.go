package constants

// DidaErrorCode represents error codes from Dida booking responses.
type DidaErrorCode string

const (
	// Availability and search errors (2000 series).
	ErrUnexpected                 DidaErrorCode = "2000" // Unexpected Error
	ErrNoHotelOrDestination       DidaErrorCode = "2001" // No Hotel Or Destination
	ErrNoHotelFound               DidaErrorCode = "2002" // No Hotel Found
	ErrIncorrectDate              DidaErrorCode = "2003" // Incorrect Date
	ErrIncorrectRoomCount         DidaErrorCode = "2004" // Incorrect RoomCount
	ErrRoomSoldOut                DidaErrorCode = "2005" // No Available Room
	ErrNoRatePlanFound            DidaErrorCode = "2006" // No RatePlan Found
	ErrIncorrectRoomNum           DidaErrorCode = "2007" // Incorrect RoomNum
	ErrIncorrectChildAgeList      DidaErrorCode = "2008" // Incorrect ChildAgeList
	ErrCheckAvailabilityTimeout   DidaErrorCode = "2009" // Check Availability Timeout
	ErrIncorrectHotelID           DidaErrorCode = "2010" // Incorrect HotelID
	ErrIncorrectOccupancy         DidaErrorCode = "2011" // Incorrect Occupancy
	ErrIncorrectDateRange         DidaErrorCode = "2012" // Incorrect DateRange
	ErrClientRefExceedMaxLength2  DidaErrorCode = "2013" // Client Reference Exceed Max Length
	ErrClientRefNotProvided2      DidaErrorCode = "2014" // Client Reference Not Provided
	ErrIncorrectStayDateRange     DidaErrorCode = "2015" // Incorrect Range For StayDate
	ErrEanRequestIsNull2          DidaErrorCode = "2016" // EanRequest Is Null
	ErrClientAuthFailed           DidaErrorCode = "2017" // Client Auth Failed
	ErrCurrencyNotSupported       DidaErrorCode = "2018" // Currency Not Supported
	ErrLimitationOfDelivery       DidaErrorCode = "2019" // Limitation Of Delivery
	ErrIncorrectRatePlanID        DidaErrorCode = "2020" // Incorrect RatePlanID
	ErrIncorrectHotelCount        DidaErrorCode = "2021" // Incorrect HotelCount
	ErrLimitedCall                DidaErrorCode = "2022" // Limited Call (Exceed QPS limitation)
	ErrNationalityNeeded          DidaErrorCode = "2023" // Nationality Needed
	ErrIncorrectRealTimeOccupancy DidaErrorCode = "2024" // Incorrect RealTime And Occupancy
	ErrNationalityForbidden       DidaErrorCode = "2025" // Nationality Forbidden
	ErrIncorrectCityCode2         DidaErrorCode = "2026" // Incorrect CityCode
	ErrCityCodeLowestPriceOnly    DidaErrorCode = "2027" // CityCode Should With Lowest PriceOnly
	ErrOutOfMemoryException       DidaErrorCode = "2028" // Out Of Memory Exception
	ErrHotelStopSell              DidaErrorCode = "2029" // Hotel Stop Sell
	ErrPriceNotApplicable         DidaErrorCode = "2030" // Price Not Applicable
	ErrIncorrectNationality       DidaErrorCode = "2031" // Incorrect Nationality
	ErrIncorrectTimeoutSetting    DidaErrorCode = "2033" // Incorrect TimeoutSetting
	ErrIncorrectMetadata          DidaErrorCode = "2034" // Incorrect Metadata

	// Invoice errors (8000 series).
	ErrApplyIDAlreadyExists       DidaErrorCode = "8000" // ApplyID Already Exists
	ErrInvoiceApplicationNotExist DidaErrorCode = "8001" // Invoice Application Does Not Exist
	ErrOrderNumberNotExist        DidaErrorCode = "8002" // The Order Number Submitted in the Invoice Application Does Not Exist

	// General booking errors (3000 series).
	ErrBookingUnexpected        DidaErrorCode = "3000" // Unexpected Error
	ErrIncorrectBookingInfo     DidaErrorCode = "3001" // Incorrect Booking Information
	ErrIncorrectReferenceNo     DidaErrorCode = "3002" // Incorrect ReferenceNo
	ErrIncorrectBookingID       DidaErrorCode = "3003" // Incorrect BookingID
	ErrIncorrectCancelConfirmID DidaErrorCode = "3004" // Incorrect CancelConfirmID
	ErrIncorrectOccupancyInfo   DidaErrorCode = "3005" // Incorrect OccupancyInfo
	ErrBookingExpired           DidaErrorCode = "3006" // Booking Expired
	ErrCancelExpired            DidaErrorCode = "3007" // Cancel Expired
	ErrIncorrectNumOfRooms      DidaErrorCode = "3008" // Incorrect Num Of Rooms
	ErrIncorrectCityCode        DidaErrorCode = "3009" // Incorrect CityCode
	ErrFailedToUpdateStatus     DidaErrorCode = "3010" // Failed To Update Status
	ErrNoContactInfo            DidaErrorCode = "3011" // No Contact Info
	ErrDBError                  DidaErrorCode = "3012" // DB Error
	ErrIncorrectStatus          DidaErrorCode = "3013" // Incorrect Status

	// Credit and availability errors.
	ErrNotEnoughCredit          DidaErrorCode = "3014" // Not Enough Credit (Account credit limit insufficient or not topped up)
	ErrAvailabilityPriceInvalid DidaErrorCode = "3015" // Availability Or Price Invalid
	ErrFailedToConfirmBooking   DidaErrorCode = "3016" // Failed To Confirm Booking

	// Booking state errors.
	ErrBookingConfirmed DidaErrorCode = "3017" // Booking Confirmed
	ErrBookingCanceled  DidaErrorCode = "3018" // Booking Canceled

	// Reference and booking identification errors.
	ErrDuplicateClientReference DidaErrorCode = "3019" // Duplicate ClientReference
	ErrPendingBooking           DidaErrorCode = "3020" // Pending Booking
	ErrIsStopSell               DidaErrorCode = "3021" // Is Stop Sell
	ErrIsInvalidCheckIn         DidaErrorCode = "3022" // Is Invalid CheckIn
	ErrEanRequestIsNull         DidaErrorCode = "3023" // EanRequest Is Null
	ErrClientRefExceedMaxLength DidaErrorCode = "3025" // ClientReference Exceed Max Length
	ErrClientRefNotProvided     DidaErrorCode = "3026" // ClientReference Not Provided
	ErrValueAddItemNotMatch     DidaErrorCode = "3027" // ValueAdd Item Not Match
	ErrFailedToUpdateClientRef  DidaErrorCode = "3028" // Failed To Update ClientReference

	// Cancellation and confirmation errors.
	ErrCancelAfterCheckIn    DidaErrorCode = "3031" // Cancel After CheckIn
	ErrBookingPreConfirm     DidaErrorCode = "3033" // Booking Pre-Confirm
	ErrCouponConsumeNotMatch DidaErrorCode = "3034" // Coupon Consume Record Not Match

	// Guest and booking validation errors.
	ErrInvalidGuestName      DidaErrorCode = "3035" // Invalid Guest Name
	ErrBookingPaymentExpired DidaErrorCode = "3036" // Booking Payment Expired
	ErrGuestNameNotAllow     DidaErrorCode = "3038" // Guest Name Not Allow
	ErrDuplicateBooking      DidaErrorCode = "3039" // Duplicate Booking
	ErrPriceOutOfTolerance   DidaErrorCode = "3040" // Price Out Of Tolerance
	ErrMealTypeNotMatch      DidaErrorCode = "3041" // MealType Not Match

	// Rate plan and booking confirmation errors.
	ErrNoRatePlanRateProvided  DidaErrorCode = "3050" // No RatePlan Rate Provided
	ErrOnRequestBookingTimeout DidaErrorCode = "3060" // OnRequest Booking Confirm TimeOut
	ErrOnRequestBookingFailed  DidaErrorCode = "3070" // OnRequest Booking Confirm Failed
	ErrBlockRoundTripBooking   DidaErrorCode = "3080" // Block RoundTrip Booking
	ErrPayFailed               DidaErrorCode = "3090" // Pay Failed

	// Cancel and high-risk errors.
	ErrCancelFailed             DidaErrorCode = "4000" // Cancel Failed
	ErrHighRiskOrderBehavior    DidaErrorCode = "4010" // High Risk Order Behavior
	ErrOFACSanctionedGuest      DidaErrorCode = "4030" // Suspected to be guests sanctioned by OFAC
	ErrNonCancellableNotAllowed DidaErrorCode = "4031" // Client does not allow non-cancellable bookings
)

// FailedBookingCodes maps error codes to their booking failure status for Book operations.
var FailedBookingCodes = map[DidaErrorCode]bool{
	ErrIncorrectBookingInfo:     true,
	ErrIncorrectReferenceNo:     true,
	ErrIncorrectOccupancyInfo:   true,
	ErrIncorrectNumOfRooms:      true,
	ErrIncorrectCityCode:        true,
	ErrNoContactInfo:            true,
	ErrNotEnoughCredit:          true,
	ErrAvailabilityPriceInvalid: true,
	ErrDuplicateClientReference: true,
	ErrIsStopSell:               true,
	ErrIsInvalidCheckIn:         true,
	ErrClientRefExceedMaxLength: true,
	ErrClientRefNotProvided:     true,
	ErrInvalidGuestName:         true,
	ErrBookingPaymentExpired:    true,
	ErrGuestNameNotAllow:        true,
	ErrDuplicateBooking:         true,
	ErrPriceOutOfTolerance:      true,
	ErrMealTypeNotMatch:         true,
	ErrNoRatePlanRateProvided:   true,
	ErrOnRequestBookingTimeout:  true,
	ErrOnRequestBookingFailed:   true,
	ErrPayFailed:                true,
}

// BookingSearchFailedCodes maps error codes that indicate a failed booking when returned from BookingSearch.
var BookingSearchFailedCodes = map[DidaErrorCode]bool{
	// Including all the original booking failed codes
	ErrIncorrectBookingInfo:     true,
	ErrIncorrectReferenceNo:     true,
	ErrIncorrectBookingID:       true,
	ErrIncorrectCancelConfirmID: true,
	ErrIncorrectOccupancyInfo:   true,
	ErrBookingExpired:           true,
	ErrCancelExpired:            true,
	ErrIncorrectNumOfRooms:      true,
	ErrIncorrectCityCode:        true,
	ErrFailedToUpdateStatus:     true,
	ErrNoContactInfo:            true,
	ErrDBError:                  true,
	ErrFailedToConfirmBooking:   true,
	ErrBookingCanceled:          true, // Including this as a failed booking for search
	ErrDuplicateClientReference: true,
	ErrIsStopSell:               true,
	ErrIsInvalidCheckIn:         true,
	ErrClientRefExceedMaxLength: true,
	ErrClientRefNotProvided:     true,
	ErrFailedToUpdateClientRef:  true,
	ErrInvalidGuestName:         true,
	ErrBookingPaymentExpired:    true,
	ErrGuestNameNotAllow:        true,
	ErrDuplicateBooking:         true,
	ErrPriceOutOfTolerance:      true,
	ErrMealTypeNotMatch:         true,
	ErrNoRatePlanRateProvided:   true,
	ErrOnRequestBookingTimeout:  true,
	ErrOnRequestBookingFailed:   true,
	ErrPayFailed:                true,
	ErrNonCancellableNotAllowed: true,
}

// Some errors from BookingSearch don't indicate a failure, just a different state.
var NonFailedBookingSearchCodes = map[DidaErrorCode]bool{
	ErrPendingBooking:    true, // Pending is not failed
	ErrBookingConfirmed:  true, // Confirmed is not failed
	ErrBookingPreConfirm: true, // Pre-confirmed is not failed
}

// IsFailedBookingCode checks if an error code represents a failed booking.
func IsFailedBookingCode(code DidaErrorCode) bool {
	return FailedBookingCodes[code]
}

// IsBookingSearchFailedCode checks if an error code from BookingSearch indicates a failed booking.
func IsBookingSearchFailedCode(code DidaErrorCode) bool {
	// First check if it's in the non-failed list
	if NonFailedBookingSearchCodes[code] {
		return false
	}

	// Then check if it's in the failed list
	return BookingSearchFailedCodes[code]
}
