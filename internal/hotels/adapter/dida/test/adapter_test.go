package test

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	commonMongoDB "gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/dida"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
)

// Tạo một mock repository nếu cần
type mockRequestRepository struct {
	repositories.RequestRepository
}

func setupAdapter(t *testing.T) dida.Adapter {
	// Lấy config từ file
	cfg := &config.Schema{
		MongoDB:               "skyhub-hotels-db-dev",
		WriteURL:              "mongodb://localhost:27017/",
		ReadURL:               "mongodb://localhost:27017/",
		DidaAPIURL:            "https://apiint.didatravel.com/api",
		DidaBasicAuthUsename:  "bizitriptest",
		DidaBasicAuthPassword: "TCsDWWAQcqi3LZM",
	}

	db, err := commonMongoDB.New(&commonMongoDB.Config{
		WriteURL: cfg.WriteURL,
		ReadURL:  cfg.ReadURL,
		Database: cfg.MongoDB,
	})
	if err != nil {
		panic(err)
	}
	requestRepo := repositories.NewRequestRepository(db, cfg)

	return dida.NewAdapter(cfg, requestRepo)
}

func TestSearchHotelIntegration(t *testing.T) {
	adapter := setupAdapter(t)
	ctx := context.Background()

	// Chuẩn bị dữ liệu đầu vào thực tế
	checkIn := time.Now().Add(24 * time.Hour)
	checkOut := checkIn.Add(3 * 24 * time.Hour)

	// Tạo request theo cấu trúc thực tế của domain
	req := &domain.SearchAdapterReq{
		HubRequest: &domain.HubSearchHotelRequest{
			Stay: domain.HubSearchStay{
				CheckIn:   checkIn.Format(time.DateOnly),
				CheckOut:  checkOut.Format(time.DateOnly),
				RoomCount: 1,
			},
			Occupancies: []*domain.HubSearchOccupancy{
				{
					Adults: 2,
				},
			},
			CountryCode: "VN",
		},
		ProviderHotelIds: []string{
			"119071",
			"119266",
			"120259",
			"121747",
			"141744",
			"150198",
		},
		Timeout: 30,
	}

	// Đảm bảo CountDays được gọi nếu cần
	if err := req.HubRequest.Stay.CountDays(); err != nil {
		t.Fatalf("Failed to count days: %v", err)
	}

	// Thực hiện cuộc gọi thực tế
	results, err := adapter.SearchHotel(ctx, req, "test-search-key")

	// Kiểm tra kết quả
	if err != nil {
		t.Logf("Error during search: %v", err)
	} else {
		t.Logf("Found %d hotels", len(results))
		for i, hotel := range results {
			t.Logf("Hotel %d: %s", i+1, hotel.Name)
		}

		// Kiểm tra cấu trúc kết quả cơ bản
		if len(results) > 0 {
			for _, hotel := range results {
				assert.NotEmpty(t, hotel.Name, "Hotel name should not be empty")
			}
		}
	}
}

func TestCheckAvailabilityIntegration(t *testing.T) {
	adapter := setupAdapter(t)
	ctx := context.Background()

	// Chuẩn bị dữ liệu đầu vào thực tế
	checkIn := time.Now().Add(24 * time.Hour)
	checkOut := checkIn.Add(3 * 24 * time.Hour)

	// Tạo request theo cấu trúc thực tế
	req := &domain.CheckAvaiAdapterReq{
		HubRequest: &domain.HubCheckAvailabilityReq{
			Stay: domain.HubSearchStay{
				CheckIn:   checkIn.Format(time.DateOnly),
				CheckOut:  checkOut.Format(time.DateOnly),
				RoomCount: 1,
			},
			Occupancies: []*domain.HubSearchOccupancy{
				{
					Adults: 2,
				},
			},
			CountryCode: "VN",
		},
		ProviderHotelID: "150198",
		Address: &domain.Address{
			City:        "Ho Chi Minh",
			CountryCode: "VN",
		},
		TracingID: "test-tracing-id",
	}

	// Đảm bảo CountDays được gọi nếu cần
	if err := req.HubRequest.Stay.CountDays(); err != nil {
		t.Fatalf("Failed to count days: %v", err)
	}

	// Thực hiện cuộc gọi thực tế
	rooms, _, err := adapter.CheckAvailability(ctx, req)

	// Kiểm tra kết quả
	if err != nil {
		t.Logf("Error during check availability: %v", err)
	} else {
		t.Logf("Found %d available rooms", len(rooms))
		for i, room := range rooms {
			// Sử dụng các trường có trong HubRoom
			roomName := room.Name
			t.Logf("Room %d: %s", i+1, roomName)
		}

		// Kiểm tra cấu trúc kết quả cơ bản
		if len(rooms) > 0 {
			for _, room := range rooms {
				assert.NotEmpty(t, room.RoomID, "Room ID should not be empty")
				assert.NotEmpty(t, room.Name, "Room name should not be empty")
			}
		}
	}
}

// TestPriceCheckIntegration kiểm thử PriceCheck với dữ liệu thực
func TestPriceCheckIntegration(t *testing.T) {
	adapter := setupAdapter(t)
	ctx := context.Background()

	// Chuẩn bị dữ liệu đầu vào thực tế
	checkIn := "2025-05-29"
	checkOut := "2025-05-31"

	// Tạo request theo cấu trúc thực tế
	req := &domain.CacheCheckAvailabilityRequest{
		Stay: domain.HubSearchStay{
			CheckIn:  checkIn,
			CheckOut: checkOut,
		},
		Occupancies: []*domain.HubSearchOccupancy{
			{
				Adults:         1,
				OccupancyIndex: 1,
			},
			{
				Adults:         2,
				OccupancyIndex: 2,
			},
			{
				Adults:         2,
				OccupancyIndex: 3,
				Children: &domain.HubSearchChildren{
					Age: []uint{2, 6},
				},
			},
		},
		Currency:    "USD",
		CountryCode: "VN",
	}

	hotelID := "4896027"
	address := &domain.Address{
		Line1:               "HITEC City, Near Cyber Towers",
		City:                "Hyderabad",
		StateProvinceName:   "Andhra Pradesh",
		PostalCode:          "500 081",
		CountryCode:         "IN",
		ObfuscationRequired: false,
	}
	ratePlanID := "3834712821599402933"
	tracingID := "-3834712821599402933"

	// Thực hiện cuộc gọi thực tế
	hubRateData, session, err := adapter.PriceCheck(ctx, req, hotelID, address, &domain.HubRateData{
		ProviderRateID: ratePlanID,
	}, tracingID)

	// Kiểm tra kết quả
	if err != nil {
		t.Logf("Error during price check: %v", err)
	} else {
		t.Logf("Price check successful")

		// Kiểm tra cấu trúc kết quả cơ bản
		assert.NotNil(t, session, "Session should not be nil")
		assert.NotEmpty(t, session.ReferenceNo, "ReferenceNo should not be empty")

		if hubRateData != nil {
			t.Logf("Price check returned valid rate data")
			// Thử truy cập các trường quan trọng
			assert.NotNil(t, hubRateData, "Rate data should not be nil")

			// Log các chi tiết về giá và chính sách hủy nếu có
			if len(hubRateData.CancelPolicies) > 0 {
				t.Logf("Cancellation policies: %d", len(hubRateData.CancelPolicies))
			}

			// Hiển thị giá phải trả ngay bây giờ
			t.Logf("Pay now amount: %f", hubRateData.PayNow)
		}
	}
}

// TestBookIntegration kiểm thử Book với dữ liệu thực
func TestBookIntegration(t *testing.T) {
	// Skip test by default as it would create actual bookings
	t.Skip("Skipping booking test to avoid creating real bookings")

	adapter := setupAdapter(t)
	ctx := context.Background()

	// Chuẩn bị dữ liệu đầu vào thực tế
	checkIn := time.Now().Add(7 * 24 * time.Hour) // Book 1 week ahead
	checkOut := checkIn.Add(2 * 24 * time.Hour)   // 2 nights stay

	// 1. Trước tiên cần tìm kiếm phòng
	checkAvailReq := &domain.CheckAvaiAdapterReq{
		HubRequest: &domain.HubCheckAvailabilityReq{
			Stay: domain.HubSearchStay{
				CheckIn:   checkIn.Format(time.DateOnly),
				CheckOut:  checkOut.Format(time.DateOnly),
				RoomCount: 1,
			},
			Occupancies: []*domain.HubSearchOccupancy{
				{
					Adults: 2,
				},
			},
			CountryCode: "VN",
		},
		ProviderHotelID: "150198", // Sử dụng ID khách sạn có thật
		Address: &domain.Address{
			City:        "Ho Chi Minh",
			CountryCode: "VN",
		},
		TracingID: "test-book-avail-" + time.Now().Format(time.RFC3339),
	}

	// Đảm bảo CountDays được gọi
	if err := checkAvailReq.HubRequest.Stay.CountDays(); err != nil {
		t.Fatalf("Failed to count days: %v", err)
	}

	// Lấy danh sách phòng
	rooms, _, err := adapter.CheckAvailability(ctx, checkAvailReq)
	if err != nil || len(rooms) == 0 {
		t.Fatalf("Failed to find available rooms: %v", err)
	}

	// Lấy phòng đầu tiên để kiểm tra giá
	selectedRoom := rooms[0]
	t.Logf("Selected room: %s", selectedRoom.Name)

	// 2. Kiểm tra giá
	priceCheckReq := &domain.CacheCheckAvailabilityRequest{
		Stay: domain.HubSearchStay{
			CheckIn:   checkIn.Format(time.DateOnly),
			CheckOut:  checkOut.Format(time.DateOnly),
			RoomCount: 1,
			DayCount:  checkAvailReq.HubRequest.Stay.DayCount,
		},
		Occupancies: checkAvailReq.HubRequest.Occupancies,
	}

	tracingID := "test-book-price-" + time.Now().Format(time.RFC3339)
	rate := &domain.HubRateData{
		ProviderRateID: selectedRoom.RoomID,
	}
	_, session, err := adapter.PriceCheck(ctx, priceCheckReq, checkAvailReq.ProviderHotelID, checkAvailReq.Address, rate, tracingID)
	if err != nil || session == nil {
		t.Fatalf("Failed to check price: %v", err)
	}

	t.Logf("Price check successful, Reference: %s", session.ReferenceNo)

	// 3. Chuẩn bị đặt phòng
	bookingReq := &domain.HubBookReq{
		Holder: &domain.HubHolderInfo{
			HolderDetail: []*domain.HolderDetail{
				{
					GivenName:      "Test",
					Surname:        "User",
					Email:          "<EMAIL>",
					OccupancyIndex: 0,
				},
			},
			PhoneCode:   "+84",
			PhoneNumber: "987654321",
		},
	}

	// Tạo order
	order := &domain.HubHotelOrder{
		OrderCode:  "TEST-" + time.Now().Format("20060102150405"),
		CustomerIP: "127.0.0.1",
		Hotel: &domain.HubOrderHotelItem{
			ProviderHotelID: checkAvailReq.ProviderHotelID,
			ListRooms: []*domain.HubOrderRoomItem{
				{
					ProviderRoomID: selectedRoom.ProviderRoomID,
					Name:           selectedRoom.Name,
				},
			},
		},
		HotelSearchRequest: &domain.CacheCheckAvailabilityRequest{
			Stay:        checkAvailReq.HubRequest.Stay,
			Occupancies: checkAvailReq.HubRequest.Occupancies,
		},
	}

	// 4. Thực hiện đặt phòng
	bookTracingID := "test-booking-" + time.Now().Format(time.RFC3339)
	bookingID, confirmationIDs, bookingStatus, providerStatus, err := adapter.Book(ctx, bookingReq, session, order, bookTracingID)

	// Kiểm tra kết quả
	t.Logf("Booking result: ID=%s, Status=%s, Provider Status=%s", bookingID, bookingStatus, providerStatus)
	if err != nil {
		t.Logf("Booking error: %v", err)
	}

	if len(confirmationIDs) > 0 {
		for i, conf := range confirmationIDs {
			t.Logf("Confirmation %d: ID=%s, Status=%s", i+1, conf.ConfirmationID, conf.BookStatus)
		}
	}

	// Kiểm tra cấu trúc kết quả cơ bản
	if bookingStatus == enum.BookingStatusSuccess {
		assert.NotEmpty(t, bookingID, "Booking ID should not be empty when status is success")
		assert.Greater(t, len(confirmationIDs), 0, "Should have at least one confirmation ID")
	}
}
