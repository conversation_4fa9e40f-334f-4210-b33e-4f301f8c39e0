package dida

import (
	"context"
	"fmt"
	"sync"

	"github.com/pkg/errors"
	commonErrors "gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/dida/client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/dida/client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/dida/constants"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/dida/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/dida/helpers"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
)

type Adapter interface {
	SearchHotel(ctx context.Context, req *domain.SearchAdapterReq, searchKey string) ([]*domain.HotelSummary, error)
	CheckAvailability(ctx context.Context, req *domain.CheckAvaiAdapterReq) ([]*domain.HubRoom, string, error)
	PriceCheck(ctx context.Context, req *domain.CacheCheckAvailabilityRequest, hotelID string, address *domain.Address, rate *domain.HubRateData, tracingID string) (*domain.HubRateData, *domain.DidaSessionInfo, error)
	Retrieve(ctx context.Context, order *domain.HubHotelOrder, tracingID string) ([]*domain.HubRetrieveConfirmationID, enum.BookingStatus, error)
	Book(ctx context.Context, req *domain.HubBookReq, session *domain.DidaSessionInfo, order *domain.HubHotelOrder, tracingID string) (string, []*domain.HubRetrieveConfirmationID, enum.BookingStatus, string, error)
	PreCancel(ctx context.Context, bookingID, tracingID string) (string, float64, error)
	Cancel(ctx context.Context, bookingID, confirmID, tracingID string) error
	RetrieveCancelStatus(ctx context.Context, bookingID string, penaltyAmount float64, tracingID string) (bool, bool, *domain.RefundData, error)
}

type adapter struct {
	cfg    *config.Schema
	client client.DidaClient
}

func NewAdapter(cfg *config.Schema, requestRepo repositories.RequestRepository) Adapter {
	return &adapter{
		cfg:    cfg,
		client: client.NewDidaClient(cfg, requestRepo),
	}
}

func (a *adapter) SearchHotel(ctx context.Context, req *domain.SearchAdapterReq, searchKey string) ([]*domain.HotelSummary, error) {
	if req.ProviderHotelIds == nil || req.HubRequest == nil {
		return nil, commonErrors.ErrInvalidInput
	}

	batchSize := 50
	totalHotels := len(req.ProviderHotelIds)
	batchCount := (totalHotels + batchSize - 1) / batchSize // Ceiling division

	var wg sync.WaitGroup
	results := make([][]*domain.HotelSummary, batchCount)
	errs := make([]error, batchCount)

	for i := 0; i < batchCount; i++ {
		wg.Add(1)

		go func(batchIndex int) {
			defer wg.Done()

			start := batchIndex * batchSize

			end := (batchIndex + 1) * batchSize
			if end > totalHotels {
				end = totalHotels
			}

			batchReq := &domain.SearchAdapterReq{
				HubRequest:       req.HubRequest,
				ProviderHotelIds: req.ProviderHotelIds[start:end],
				Timeout:          req.Timeout,
			}

			clientReq := converts.ToDidaSearchHotelReq(batchReq, a.cfg.Env)

			if batchReq.Timeout == 0 {
				batchReq.Timeout = 10
			}

			batchKey := fmt.Sprintf("%s_batch_%d", searchKey, batchIndex)

			res, err := a.client.PriceSearch(ctx, clientReq, batchKey)
			if err != nil {
				errs[batchIndex] = errors.Wrap(err, fmt.Sprintf("client.PriceSearch batch %d", batchIndex))
				return
			}

			results[batchIndex] = converts.ToDomainHotelSummaries(res, batchReq.HubRequest.Stay.DayCount, len(batchReq.HubRequest.Occupancies))
		}(i)
	}

	wg.Wait()

	for _, err := range errs {
		if err != nil {
			return nil, err // Return the first error encountered
		}
	}

	var allHotels []*domain.HotelSummary
	for _, batch := range results {
		allHotels = append(allHotels, batch...)
	}

	return allHotels, nil
}

func (a *adapter) CheckAvailability(ctx context.Context, req *domain.CheckAvaiAdapterReq) ([]*domain.HubRoom, string, error) {
	if req.ProviderHotelID == "" {
		return nil, "", commonErrors.ErrInvalidInput
	}

	clientReq, err := converts.ToDidaCheckAvailabilityReq(req, a.cfg.Env)
	if err != nil {
		return nil, "", errors.Wrap(err, "converts.ToDidaCheckAvailabilityReq")
	}

	res, err := a.client.PriceSearch(ctx, clientReq, req.TracingID)
	if err != nil {
		return nil, "", errors.Wrap(err, "client.PriceSearch")
	}

	if res.Error != nil {
		if res.Error.Code == constants.ErrRoomSoldOut {
			return nil, "", domain.ErrRoomSoldOut
		}

		return nil, "", errors.Wrap(err, "client.PriceSearch")
	}

	domainRooms := converts.ToDomainRooms(res, req.HubRequest.Occupancies, req.HubRequest.Stay.CheckIn, req.Address.City, req.Address.CountryCode, req.HubRequest.Stay.DayCount)

	return domainRooms, helpers.GetDefaultCurrency(a.cfg.Env), nil
}

func (a *adapter) PriceCheck(ctx context.Context, req *domain.CacheCheckAvailabilityRequest, hotelID string, address *domain.Address, rate *domain.HubRateData, tracingID string) (*domain.HubRateData, *domain.DidaSessionInfo, error) {
	priceConfirmReq, err := converts.ToDidaPriceCheckReq(req, hotelID, rate.ProviderRateID, a.cfg.Env)
	if err != nil {
		log.Error("DidaPriceCheck", log.Any("Error", err))
		return nil, nil, errors.Wrap(err, "Fail to convert")
	}

	res, err := a.client.PriceConfirm(ctx, priceConfirmReq, tracingID)
	if err != nil {
		log.Error("PriceCheck client error",
			log.String("error", err.Error()),
			log.String("tracing_id", tracingID))

		return nil, nil, errors.Wrap(err, "client.PriceCheck")
	}

	if res.Error != nil {
		log.Error("PriceCheck client error",
			log.String("error", res.Error.Message),
			log.String("tracing_id", tracingID))

		if res.Error.Code == constants.ErrRoomSoldOut {
			return nil, nil, domain.ErrRoomSoldOut
		}

		return nil, nil, errors.Wrap(err, "client.PriceCheck")
	}

	if res.Success == nil {
		log.Error("PriceCheck client error",
			log.String("error", "No response success"),
			log.String("tracing_id", tracingID))

		return nil, nil, errors.Wrap(err, "client.PriceCheck")
	}

	session := &domain.DidaSessionInfo{
		ReferenceNo: res.Success.PriceDetails.ReferenceNo,
	}

	hubRateData := converts.ToHubRateData(res.Success, req.Occupancies, req.Stay.CheckIn, address.City, address.CountryCode)

	if hubRateData == nil || hubRateData.HasBreakfast != rate.HasBreakfast {
		return nil, nil, domain.ErrRoomSoldOut
	}

	return hubRateData, session, nil
}

func (a *adapter) Retrieve(ctx context.Context, order *domain.HubHotelOrder, tracingID string) ([]*domain.HubRetrieveConfirmationID, enum.BookingStatus, error) {
	if order == nil || order.Hotel == nil || order.Hotel.ListRooms == nil {
		return nil, enum.BookingStatusNone, domain.ErrInvalidValue
	}

	bookingSearchReq := converts.ToBookingSearchReq(order.ReservationCode)

	res, err := a.client.BookingSearch(ctx, bookingSearchReq, tracingID)
	if err != nil {
		log.Error("Didi adapter BookingSearch", log.Any("Error", err))
		return nil, enum.BookingStatusPending, nil
	}

	if res == nil {
		return nil, enum.BookingStatusPending, nil
	}

	bookingStatus := helpers.CheckStatusBookingSearch(res)
	if bookingStatus == enum.BookingStatusFailed {
		return nil, bookingStatus, errors.New("booking failed")
	}

	if res.Success == nil || res.Success.BookingDetailsList == nil || len(res.Success.BookingDetailsList) == 0 {
		return nil, enum.BookingStatusPending, nil
	}

	bookingDetail := res.Success.BookingDetailsList[0]
	if bookingDetail.Status == entities.BookingStatusConfirmed {
		bookingStatus = enum.BookingStatusSuccess
	}

	hubRooms := order.Hotel.ListRooms

	out := make([]*domain.HubRetrieveConfirmationID, 0, len(hubRooms))
	for _, hubRoom := range hubRooms {
		out = append(out, converts.ToHubRetrieveConfirmationID(hubRoom, bookingDetail.Status, bookingDetail.ConfirmationCode))
	}

	return out, bookingStatus, nil
}

func (a *adapter) PreCancel(ctx context.Context, bookingID, tracingID string) (string, float64, error) {
	res, err := a.client.PreCancelBooking(ctx, &entities.PreCancelBookingReq{BookingID: bookingID}, tracingID)
	if err != nil {
		log.Error("Didi adapter PreCancelBooking", log.Any("Error", err))
		return "", 0, errors.Wrap(err, "client.PreCancelBooking")
	}

	if res == nil {
		return "", 0, entities.ErrSomethingError
	}

	if res.Error != nil {
		log.Error("Didi adapter PreCancelBooking", log.Any("Error", res.Error.Code))
		return "", 0, entities.ErrSomethingError
	}

	if res.Success == nil {
		log.Error("Didi adapter PreCancelBooking, Error: No response success")
		return "", 0, entities.ErrSomethingError
	}

	return res.Success.ConfirmID, res.Success.Amount, nil
}

func (a *adapter) Cancel(ctx context.Context, bookingID, confirmID, tracingID string) error {
	cancelReq := &entities.CancelBookingReq{
		BookingID: bookingID,
		ConfirmID: confirmID,
	}

	res, err := a.client.CancelBooking(ctx, cancelReq, tracingID)
	if res.Error != nil {
		log.Error("Didi adapter PreCancelBooking", log.Any("Error", res.Error.Code))
		return errors.Wrap(err, "client.CancelBooking")
	}

	if res == nil {
		return entities.ErrSomethingError
	}

	if res.Success == nil {
		log.Error("Didi adapter CancelBooking, Error: No response success")
		return entities.ErrSomethingError
	}

	return nil
}

func (a *adapter) RetrieveCancelStatus(ctx context.Context, bookingID string, penaltyAmount float64, tracingID string) (bool, bool, *domain.RefundData, error) {
	res, err := a.client.BookingSearch(ctx, converts.ToBookingSearchReq(bookingID), tracingID)
	if err != nil {
		return false, false, nil, errors.Wrap(err, "client.Retrieve")
	}

	if res == nil {
		return false, false, nil, entities.ErrSomethingError
	}

	if res.Error != nil {
		return false, false, nil, errors.New(res.Error.Message)
	}

	if res.Success == nil || len(res.Success.BookingDetailsList) == 0 {
		return false, false, nil, entities.ErrSomethingError
	}

	resData := res.Success.BookingDetailsList[0]

	currency := "VND"
	if resData.Hotel != nil && len(resData.Hotel.RatePlanList) > 0 {
		currency = resData.Hotel.RatePlanList[0].Currency
	}

	return resData.Status == entities.BookingStatusCanceled, penaltyAmount == 0, &domain.RefundData{
		ProviderRefundAmount: resData.TotalPrice,
		RefundAmount:         resData.TotalPrice - penaltyAmount,
		PenaltyAmount:        penaltyAmount,
		Currency:             currency,
	}, nil
}

func (a *adapter) Book(ctx context.Context, req *domain.HubBookReq, session *domain.DidaSessionInfo, order *domain.HubHotelOrder, tracingID string) (string, []*domain.HubRetrieveConfirmationID, enum.BookingStatus, string, error) {
	if req == nil || req.Holder == nil || order == nil || order.Hotel == nil || order.HotelSearchRequest == nil {
		return "", nil, enum.BookingStatusNone, "", domain.ErrInvalidValue
	}

	if req.Holder.HolderDetail == nil {
		return "", nil, enum.BookingStatusNone, "", errors.New("holder detail nil")
	}

	if order.CustomerIP == "" {
		order.CustomerIP = "0.0.0.0"
	}

	clientBookReq, err := converts.ToDidaBookingConfirmReq(req, session, order, a.cfg)
	if err != nil {
		return "", nil, enum.BookingStatusNone, "", errors.Wrap(err, "converts.ToDidaBookingConfirmReq")
	}

	// Call the client to make the booking
	res, err := a.client.BookingConfirm(ctx, clientBookReq, tracingID)
	if err != nil {
		// If API client returns an error, treat as pending
		log.Warn("BookingConfirm API error, treating as pending",
			log.String("order_code", order.OrderCode),
			log.String("tracing_id", tracingID),
			log.Any("error", err))

		return "", nil, enum.BookingStatusFailed, "", errors.Wrap(err, "client.BookingConfirm")
	}
	// Extract booking details from the response
	var bookingID string
	var confirmationCode string
	var providerBookingStatus string
	// Check if there's a clear booking failure based on error code
	isFailed, errorMessage, bookingStatus := converts.IsBookingFailed(res)
	if isFailed {
		log.Error("Booking failed with known error",
			log.String("error_message", errorMessage),
			log.String("order_code", order.OrderCode))

		return "", nil, bookingStatus, errorMessage, errors.New(errorMessage)
	}

	if res.Success != nil && res.Success.BookingDetails != nil {
		bookingID = res.Success.BookingDetails.BookingID
		confirmationCode = res.Success.BookingDetails.ConfirmationCode // Get the actual confirmation code

		if confirmationCode == "" {
			confirmationCode = bookingID
		}

		providerBookingStatus = fmt.Sprint(rune(res.Success.BookingDetails.Status))

		// Log the confirmation code
		log.Info("Booking confirmation received",
			log.String("booking_id", bookingID),
			log.String("confirmation_code", confirmationCode),
			log.String("order_code", order.OrderCode))
	}

	if confirmationCode == "" {
		confirmationCode = bookingID
		log.Info("Using bookingID as confirmationCode",
			log.String("booking_id", bookingID),
			log.String("order_code", order.OrderCode))
	}

	if providerBookingStatus == fmt.Sprint(rune(entities.BookingStatusConfirmed)) {
		// Perform booking verification
		searchReq := &entities.BookingSearchReq{
			Header: &entities.Header{},
			SearchBy: &entities.SearchBy{
				BookingID: bookingID,
			},
			TestingHashKey: req.TestingHashKey,
		}

		searchRes, err := a.client.BookingSearch(ctx, searchReq, tracingID)
		if err != nil {
			log.Error("Failed to verify booking, keeping as pending",
				log.String("booking_id", bookingID),
				log.Any("error", err))
			return bookingID, nil, enum.BookingStatusPending, "", nil
		}

		if searchRes == nil {
			return bookingID, nil, enum.BookingStatusPending, "", nil
		}

		bookingStatus := helpers.CheckStatusBookingSearch(searchRes)
		if bookingStatus != enum.BookingStatusFailed && searchRes.Success != nil && searchRes.Success.BookingDetailsList != nil && len(searchRes.Success.BookingDetailsList) > 0 {
			details := searchRes.Success.BookingDetailsList[0]
			providerBookingStatus = fmt.Sprint(rune(details.Status))
			if details.Status == entities.BookingStatusConfirmed {
				bookingStatus = enum.BookingStatusSuccess
			}

			if details.ConfirmationCode != "" {
				confirmationCode = details.ConfirmationCode
				log.Info("Updated confirmation code from search",
					log.String("confirmation_code", confirmationCode))
			}

			if confirmationCode == "" {
				confirmationCode = bookingID
			}
		} else {
			log.Error("Booking verification failed", log.Any("searchRes", searchRes))
			return bookingID, nil, enum.BookingStatusPending, "", nil
		}
		// Create confirmation IDs for each room
		confirmationIDs := a.createConfirmationIDs(bookingID, confirmationCode, bookingStatus, req, order)

		return bookingID, confirmationIDs, bookingStatus, providerBookingStatus, nil
	} else {
		return bookingID, nil, enum.BookingStatusPending, providerBookingStatus, nil
	}
}

// createConfirmationIDs creates confirmation IDs for each room.
func (a *adapter) createConfirmationIDs(bookingID, confirmationCode string, bookingStatus enum.BookingStatus,
	req *domain.HubBookReq, order *domain.HubHotelOrder,
) []*domain.HubRetrieveConfirmationID {
	confirmationIDs := make([]*domain.HubRetrieveConfirmationID, 0)

	// Only create confirmation IDs if we have a booking ID
	if bookingID == "" {
		return confirmationIDs
	}

	for i, room := range order.Hotel.ListRooms {
		confirmationID := &domain.HubRetrieveConfirmationID{
			ProviderRoomID: room.ProviderRoomID,
			ConfirmationID: confirmationCode, // Use the confirmation code here instead of booking ID
			BookStatus:     bookingStatus,
		}

		// If we have guest details available, use them
		if i < len(req.Holder.HolderDetail) {
			holderDetail := req.Holder.HolderDetail[i]
			confirmationID.GivenName = holderDetail.GivenName
			confirmationID.Surname = holderDetail.Surname
		}

		confirmationIDs = append(confirmationIDs, confirmationID)
	}

	return confirmationIDs
}
