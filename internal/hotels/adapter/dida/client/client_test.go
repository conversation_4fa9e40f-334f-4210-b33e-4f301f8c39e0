package client

import (
	"context"
	"testing"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/dida/client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
)

var (
	mockConfig = &config.Schema{
		DidaAPIURL:            "https://apiint.didatravel.com/api",
		DidaBasicAuthUsename:  "bizitriptest",
		DidaBasicAuthPassword: "TCsDWWAQcqi3LZM",
	}
	reqs = []*repositories.Request{}

	checkInDate  = "2025-05-25"
	checkOutDate = "2025-05-27"

	priceSearchRes      *entities.PriceSearchRes
	priceConfirmRes     *entities.PriceConfirmRes
	bookingConfirmRes   *entities.BookingConfirmRes
	bookingSearchRes    *entities.BookingSearchRes
	preCancelBookingRes *entities.PreCancelBookingRes
	cancelBookingRes    *entities.CancelBookingRes
)

type mockRepo struct {
}

func NewMockRepo() repositories.RequestRepository {
	return &mockRepo{}
}

func (r *mockRepo) Create(ctx context.Context, req *repositories.Request) error {
	reqs = append(reqs, req)
	return nil
}

func TestPriceSearch(t *testing.T) {
	client := NewDidaClient(mockConfig, NewMockRepo())
	ctx := context.Background()
	tracingID := "tracing-id"

	testcases := []struct {
		name    string
		req     *entities.PriceSearchReq
		wantErr bool
	}{
		{
			name: "success",
			req: &entities.PriceSearchReq{
				HotelIDList:  []int{1250, 512},
				CheckInDate:  checkInDate,
				CheckOutDate: checkOutDate,
				IsRealTime: &entities.IsRealTime{
					Value:     true,
					RoomCount: 1,
				},
				RealTimeOccupancy: &entities.RealTimeOccupancy{
					AdultCount:      3,
					ChildCount:      0,
					ChildAgeDetails: []int{},
				},
				IsNeedOnRequest: false,
				Currency:        "USD",
				Nationality:     "VN",
			},
			wantErr: false,
		},
	}

	for _, tc := range testcases {
		t.Run(tc.name, func(t *testing.T) {
			res, err := client.PriceSearch(ctx, tc.req, tracingID)
			if (err != nil) != tc.wantErr {
				t.Errorf("PriceSearch() error = %v, wantErr = %v", err, tc.wantErr)
			}
			if (res.Error != nil) != tc.wantErr {
				t.Errorf("PriceSearch() error = %v, wantErr = %v", res.Error, tc.wantErr)
			}
			if !tc.wantErr {
				priceSearchRes = res
			}
		})
	}
}

func TestPriceConfirm(t *testing.T) {
	client := NewDidaClient(mockConfig, NewMockRepo())
	ctx := context.Background()
	tracingID := "tracing-id"

	testcases := []struct {
		name    string
		req     *entities.PriceConfirmReq
		wantErr bool
	}{
		{
			name: "success",
			req: &entities.PriceConfirmReq{
				PreBook:      true,
				CheckInDate:  checkInDate,
				CheckOutDate: checkOutDate,
				NumOfRooms:   1,
				HotelID:      priceSearchRes.Success.PriceDetails.HotelList[0].HotelID,
				OccupancyDetails: []*entities.OccupancyDetail{
					{
						AdultCount:      3,
						ChildCount:      0,
						RoomNum:         1,
						ChildAgeDetails: []int{},
					},
				},
				Currency:        "USD",
				Nationality:     "VN",
				RatePlanID:      priceSearchRes.Success.PriceDetails.HotelList[0].RatePlanList[0].RatePlanID,
				IDNeedOnRequest: false,
			},
			wantErr: false,
		},
	}

	for _, tc := range testcases {
		t.Run(tc.name, func(t *testing.T) {
			res, err := client.PriceConfirm(ctx, tc.req, tracingID)
			if (err != nil) != tc.wantErr {
				t.Errorf("PriceSearch() error = %v, wantErr = %v", err, tc.wantErr)
			}

			if (res.Error != nil) != tc.wantErr {
				t.Errorf("PriceSearch() error = %v, wantErr = %v", res.Error, tc.wantErr)
			}

			if !tc.wantErr {
				priceConfirmRes = res
			}
		})
	}
}

func TestBookingConfirm(t *testing.T) {
	client := NewDidaClient(mockConfig, NewMockRepo())
	ctx := context.Background()
	tracingID := "tracing-id"

	testcases := []struct {
		name    string
		req     *entities.BookingConfirmReq
		wantErr bool
	}{
		{
			name: "success",
			req: &entities.BookingConfirmReq{
				CheckInDate:  checkInDate,
				CheckOutDate: checkOutDate,
				NumOfRooms:   1,
				GuestList: []*entities.Guest{
					{
						RoomNum: 1,
						GuestInfo: []*entities.GuestInfo{
							{
								Name: &entities.Name{
									Last:  "Si",
									First: "Tran",
								},
								IsAdult: true,
							},
						},
					},
				},
				Contact: &entities.Contact{
					Name: &entities.Name{
						Last:  "bizitrip",
						First: "Company",
					},
					Email: "<EMAIL>",
				},
				CustomerRequest: "",
				ReferenceNo:     priceConfirmRes.Success.PriceDetails.ReferenceNo,
			},
			wantErr: false,
		},
	}

	for _, tc := range testcases {
		t.Run(tc.name, func(t *testing.T) {
			res, err := client.BookingConfirm(ctx, tc.req, tracingID)
			if (err != nil) != tc.wantErr {
				t.Errorf("PriceSearch() error = %v, wantErr = %v", err, tc.wantErr)
			}

			if (res.Error != nil) != tc.wantErr {
				t.Errorf("PriceSearch() error = %v, wantErr = %v", res.Error, tc.wantErr)
			}

			if !tc.wantErr {
				bookingConfirmRes = res
			}
		})
	}
}

func TestBookingSearch(t *testing.T) {
	client := NewDidaClient(mockConfig, NewMockRepo())
	ctx := context.Background()
	tracingID := "tracing-id"

	testcases := []struct {
		name    string
		req     *entities.BookingSearchReq
		wantErr bool
	}{
		{
			name: "success",
			req: &entities.BookingSearchReq{
				SearchBy: &entities.SearchBy{
					BookingID: bookingConfirmRes.Success.BookingDetails.BookingID,
				},
			},
			wantErr: false,
		},
	}

	for _, tc := range testcases {
		t.Run(tc.name, func(t *testing.T) {
			res, err := client.BookingSearch(ctx, tc.req, tracingID)
			if (err != nil) != tc.wantErr {
				t.Errorf("PriceSearch() error = %v, wantErr = %v", err, tc.wantErr)
			}

			if (res.Error != nil) != tc.wantErr {
				t.Errorf("PriceSearch() error = %v, wantErr = %v", res.Error, tc.wantErr)
			}

			if !tc.wantErr {
				bookingSearchRes = res
			}
		})
	}
}

func TestPreCancelBooking(t *testing.T) {
	client := NewDidaClient(mockConfig, NewMockRepo())
	ctx := context.Background()
	tracingID := "tracing-id"

	testcases := []struct {
		name    string
		req     *entities.PreCancelBookingReq
		wantErr bool
	}{
		{
			name: "success",
			req: &entities.PreCancelBookingReq{
				BookingID: bookingSearchRes.Success.BookingDetailsList[0].BookingID,
			},
			wantErr: false,
		},
	}

	for _, tc := range testcases {
		t.Run(tc.name, func(t *testing.T) {
			res, err := client.PreCancelBooking(ctx, tc.req, tracingID)
			if (err != nil) != tc.wantErr {
				t.Errorf("PriceSearch() error = %v, wantErr = %v", err, tc.wantErr)
			}

			if (res.Error != nil) != tc.wantErr {
				t.Errorf("PriceSearch() error = %v, wantErr = %v", res.Error, tc.wantErr)
			}

			if !tc.wantErr {
				preCancelBookingRes = res
			}
		})
	}
}

func TestCancelBooking(t *testing.T) {
	client := NewDidaClient(mockConfig, NewMockRepo())
	ctx := context.Background()
	tracingID := "tracing-id"

	testcases := []struct {
		name    string
		req     *entities.CancelBookingReq
		wantErr bool
	}{
		{
			name: "success",
			req: &entities.CancelBookingReq{
				BookingID: bookingSearchRes.Success.BookingDetailsList[0].BookingID,
				ConfirmID: preCancelBookingRes.Success.ConfirmID,
			},
			wantErr: false,
		},
	}

	for _, tc := range testcases {
		t.Run(tc.name, func(t *testing.T) {
			res, err := client.CancelBooking(ctx, tc.req, tracingID)
			if (err != nil) != tc.wantErr {
				t.Errorf("PriceSearch() error = %v, wantErr = %v", err, tc.wantErr)
			}

			if (res.Error != nil) != tc.wantErr {
				t.Errorf("PriceSearch() error = %v, wantErr = %v", res.Error, tc.wantErr)
			}

			if !tc.wantErr {
				cancelBookingRes = res
			}
		})
	}
}
