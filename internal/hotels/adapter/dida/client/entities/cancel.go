// Code generated ... DO NOT EDIT.

//nolint:tagliatelle
package entities

type PreCancelBookingReq struct {
	Header    *Header `json:"Header"`
	BookingID string  `json:"BookingID"`
	TestingHashKey string `json:"-"`
}

type PreCancelBookingRes struct {
	Error     *Error            `json:"Error"`
	Success   *PreCancelSuccess `json:"Success"`
	AuditData *AuditData        `json:"AuditData"`
}

type PreCancelSuccess struct {
	BookingID string  `json:"BookingID"`
	ConfirmID string  `json:"ConfirmID"`
	Currency  string  `json:"Currency"`
	Amount    float64 `json:"Amount"`
}

type CancelBookingReq struct {
	Header    *Header `json:"Header"`
	BookingID string  `json:"BookingID"`
	ConfirmID string  `json:"ConfirmID"`
	TestingHashKey string `json:"-"`
}

type CancelBookingRes struct {
	Error     *Error
	Success   *CancelSuccess `json:"Success"`
	AuditData *AuditData     `json:"AuditData"`
}

type CancelSuccess struct {
}
