package helpers

import (
	"fmt"
	"sort"
	"strconv"

	commonConstants "gitlab.deepgate.io/apps/common/constants"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/dida/client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/dida/constants"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
)

func ConvertHotelIDs(stringIDs []string) []int {
	if len(stringIDs) == 0 {
		return nil
	}

	hotelIDs := make([]int, 0, len(stringIDs))
	for _, id := range stringIDs {
		intID, err := strconv.Atoi(id)
		if err == nil && intID > 0 {
			hotelIDs = append(hotelIDs, intID)
		}
	}
	return hotelIDs
}

// ProcessOccupancies tính toán số người lớn, trẻ em và tuổi trẻ em theo quy tắc:
// 1. T<PERSON>m phòng có tổng số người nhiều nhất (max_people)
// 2. Tìm số người lớn nhiều nhất từ các phòng (max_adults)
// 3. Số trẻ em = max_people - max_adults
// 4. Tuổi trẻ em lấy từ phòng có số trẻ em nhiều nhất
func ProcessOccupancies(occupancies []*domain.HubSearchOccupancy) (adultCount int, childCount int, childAges []int) {
	if len(occupancies) == 0 {
		return 0, 0, nil
	}

	maxAdults := 0
	maxPeople := 0
	maxChildrenCount := 0
	var maxChildrenRoom *domain.HubSearchOccupancy
	sortAges := make([]uint, 0, len(occupancies))
	for _, occ := range occupancies {
		adults := int(occ.Adults)
		childrenCount := 0
		if occ.Children != nil {
			childrenCount = int(occ.Children.Number)
			sortAges = append(sortAges, occ.Children.Age...)
		}

		totalPeople := adults + childrenCount

		if adults > maxAdults {
			maxAdults = adults
		}

		if totalPeople > maxPeople {
			maxPeople = totalPeople
		}

		if childrenCount > maxChildrenCount {
			maxChildrenCount = childrenCount
			maxChildrenRoom = occ
		}
	}

	childCount = maxPeople - maxAdults
	if childCount < 0 {
		childCount = 0
	}

	sort.Slice(sortAges, func(i, j int) bool {
		return sortAges[i] > sortAges[j]
	})

	childAges = make([]int, 0, childCount)
	if childCount > 0 && maxChildrenRoom != nil && maxChildrenRoom.Children != nil {
		for _, age := range sortAges {
			childAges = append(childAges, int(age))
			if len(childAges) == childCount {
				break
			}
		}
	}

	return maxAdults, childCount, childAges
}

// FindLowestRatePlan tìm rate plan có giá thấp nhất
func FindLowestRatePlan(ratePlans []*entities.RatePlan) *entities.RatePlan {
	if len(ratePlans) == 0 {
		return nil
	}

	var lowestRatePlan *entities.RatePlan
	for _, ratePlan := range ratePlans {
		if ratePlan == nil {
			continue
		}

		if lowestRatePlan == nil || ratePlan.TotalPrice < lowestRatePlan.TotalPrice {
			lowestRatePlan = ratePlan
		}
	}

	return lowestRatePlan
}

func CreateRealTimeInfo(roomCount int) *entities.IsRealTime {
	return &entities.IsRealTime{
		Value:     true,
		RoomCount: roomCount,
	}
}

func CreateRealTimeOccupancy(adultCount, childCount int, childAges []int) *entities.RealTimeOccupancy {
	return &entities.RealTimeOccupancy{
		AdultCount:      adultCount,
		ChildCount:      childCount,
		ChildAgeDetails: childAges,
	}
}

// MapRoomID creates a Dida-specific room ID with the format "dd{hotel_id}-{room_id}".
func MapRoomID(hotelID, roomID string) string {
	return fmt.Sprintf("dd%s-%s", hotelID, roomID)
}

func GetDefaultCurrency(env string) string {
	if env == commonConstants.ProductionEnvName {
		return constants.DefaultSGDCurrency
	}
	return constants.DefaultUSDCurrency
}

// IsBookingSearchFailed checks if a booking search response indicates a failed booking.
func CheckStatusBookingSearch(res *entities.BookingSearchRes) enum.BookingStatus {
	if res == nil || res.Error == nil {
		return enum.BookingStatusPending
	}
	// Use the centralized error codes from constants package
	if constants.IsBookingSearchFailedCode(res.Error.Code) {
		return enum.BookingStatusFailed
	}

	// Check if it's a confirmed booking
	if res.Error.Code == constants.ErrBookingConfirmed {
		return enum.BookingStatusSuccess
	}

	// For error codes not in the map, consider them as pending by default
	return enum.BookingStatusPending
}
