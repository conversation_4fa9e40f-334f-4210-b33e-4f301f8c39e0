package redis

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	goRedis "github.com/redis/go-redis/v9"
	"gitlab.deepgate.io/apps/common/adapter/redis"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
)

const (
	checkAvailabilityLockKey        = "lock_check_availability_request_%s"
	checkAvailabilityCachingLockKey = "lock_check_availability_caching_%s"
	checkAvailabilityLockTTL        = time.Second * 70
)

type CheckAvailabilityRepository interface {
	WaitForLockToRelease(key string, sleeptime time.Duration, attemps int) error
	AcquireLock(key string) (bool, error)
	ReleaseLock(hash string) error
	// Caching lock
	AcquireCachingLock(searchKey string) (bool, error)
	ReleaseCachingLock(searchKey string) error
	WaitCachingLockRelease(searchKey string) error
}

type checkAvailabilityRepository struct {
	redis redis.IRedis
}

func NewCheckAvailabilityRepository(redis redis.IRedis) CheckAvailabilityRepository {
	return &checkAvailabilityRepository{redis}
}

func (r *checkAvailabilityRepository) AcquireCachingLock(searchKey string) (bool, error) {
	key := r.genKeyForCachingLock(searchKey)

	lock, err := r.redis.AcquireLock(key, "", constants.CachedSearchResultTimeout)
	if err != nil {
		return false, errors.Wrap(err, "AcquireLock")
	}

	return lock, nil
}

func (r *checkAvailabilityRepository) ReleaseCachingLock(searchKey string) error {
	fullKey := r.genKeyForCachingLock(searchKey)

	if err := r.redis.Remove(fullKey); err != nil {
		return errors.Wrap(err, " r.db.Remove(key)")
	}

	return nil
}

func (r *checkAvailabilityRepository) WaitCachingLockRelease(searchKey string) error {
	sleepTime := time.Millisecond * 500
	attemps := int(constants.CachedSearchResultTimeout / sleepTime)

	key := r.genKeyForCachingLock(searchKey)

	for i := 0; i < attemps; i++ {
		ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
		defer cancel()

		val := r.redis.CMD().Get(ctx, key)
		err := val.Err()

		if err != nil {
			if errors.Is(err, goRedis.Nil) {
				return nil
			}

			log.Error("WaitForLockToRelease r.redis.CMD.Get error", log.Any("error", err), log.Int("attemps", i), log.String("key", key))
		}

		time.Sleep(sleepTime)
	}

	return nil
}

func (*checkAvailabilityRepository) genKey(key string) string {
	return fmt.Sprintf(checkAvailabilityLockKey, key)
}

func (*checkAvailabilityRepository) genKeyForCachingLock(key string) string {
	return fmt.Sprintf(cachingLockKey, key)
}

func (r *checkAvailabilityRepository) ReleaseLock(hash string) error {
	fullKey := r.genKey(hash)

	time.Sleep(200 * time.Millisecond)
	if err := r.redis.Remove(fullKey); err != nil {
		return errors.Wrap(err, " r.db.Remove(key)")
	}

	return nil
}

func (r *checkAvailabilityRepository) AcquireLock(hash string) (bool, error) {
	key := r.genKey(hash)

	lock, err := r.redis.AcquireLock(key, "", lockTTL)
	if err != nil {
		return false, errors.Wrap(err, "AcquireLock")
	}

	return lock, nil
}

func (r *checkAvailabilityRepository) WaitForLockToRelease(hash string, sleeptime time.Duration, attemps int) error {
	key := r.genKey(hash)

	for i := 0; i < attemps; i++ {
		ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
		defer cancel()

		val := r.redis.CMD().Get(ctx, key)
		err := val.Err()

		if err != nil {
			if errors.Is(err, goRedis.Nil) {
				return nil
			}

			log.Error("WaitForLockToRelease r.redis.CMD.Get error", log.Any("error", err), log.Int("attemps", i), log.String("key", key))
		}

		time.Sleep(sleeptime)
	}

	return nil
}
