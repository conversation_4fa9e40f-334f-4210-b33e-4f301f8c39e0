package redis

import (
	"fmt"
	"time"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/adapter/redis"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

const (
	cancelBookingLockPrefix = "lock_cancel_booking_%s"
	cancelBookingLockTTL    = time.Minute
)

type CancelRepository interface {
	AcquireCancelBookingLock(sessionID string) error
	ReleaseCancelBookingLock(sessionID string) error
}

type cancelRepository struct {
	redis redis.IRedis
}

func NewCancelRedisRepository(redis redis.IRedis) CancelRepository {
	return &cancelRepository{
		redis: redis,
	}
}

func (r *cancelRepository) AcquireCancelBookingLock(id string) error {
	locked, err := r.redis.AcquireLock(r.generateCancelBookingLockUpdateKey(id), "", cancelBookingLockTTL)
	if err != nil {
		return errors.Wrap(err, "r.db.AcquireLock")
	}

	if !locked {
		return domain.ErrLockAlreadyHeld
	}

	return nil
}

func (r *cancelRepository) ReleaseCancelBookingLock(sessionID string) error {
	key := r.generateCancelBookingLockUpdateKey(sessionID)

	if err := r.redis.Remove(key); err != nil {
		return errors.Wrap(err, " r.db.Remove(key)")
	}

	return nil
}

func (r *cancelRepository) generateCancelBookingLockUpdateKey(sessionID string) string {
	return fmt.Sprintf(cancelBookingLockPrefix, sessionID)
}
