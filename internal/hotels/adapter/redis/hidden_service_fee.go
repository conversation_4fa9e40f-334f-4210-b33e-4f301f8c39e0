package redis

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/pkg/errors"
	goRedis "github.com/redis/go-redis/v9"
	"gitlab.deepgate.io/apps/common/adapter/redis"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
)

const (
	hiddenServiceFeeCacheTime = time.Hour
)

type HiddenServiceFeeRepository interface {
	Set(ctx context.Context, from string, provider enum.HotelProvider, val []*domain.HiddenServiceFee) error
	Get(ctx context.Context, from string, provider enum.HotelProvider) ([]*domain.HiddenServiceFee, error)
}

type hiddenServiceFeeRepository struct {
	redisClient redis.IRedis
}

func NewHiddenServiceFeeRepository(
	redisClient redis.IRedis,
) HiddenServiceFeeRepository {
	return &hiddenServiceFeeRepository{
		redisClient,
	}
}

func (r *hiddenServiceFeeRepository) getKey(officeID string, provider enum.HotelProvider) string {
	return fmt.Sprintf("hidden_fee_key_%s_%d", officeID, provider)
}

func (r *hiddenServiceFeeRepository) Get(ctx context.Context, from string, provider enum.HotelProvider) ([]*domain.HiddenServiceFee, error) {
	out := []*domain.HiddenServiceFee{}

	key := r.getKey(from, provider)

	cmd := r.redisClient.CMD().Get(ctx, key)
	err := cmd.Err()
	if err != nil {
		if errors.Is(err, goRedis.Nil) {
			return nil, nil
		}

		return nil, err
	}

	err = json.Unmarshal([]byte(cmd.Val()), &out)
	if err != nil {
		return nil, errors.Wrap(err, "json.Unmarshal")
	}

	return out, nil
}

func (r *hiddenServiceFeeRepository) Set(_ context.Context, from string, provider enum.HotelProvider, val []*domain.HiddenServiceFee) error {
	data, err := json.Marshal(val)
	if err != nil {
		return errors.Wrap(err, "json.Marshal")
	}

	key := r.getKey(from, provider)

	err = r.redisClient.SetWithExpiration(key, data, hiddenServiceFeeCacheTime)
	if err != nil {
		return errors.Wrap(err, "SetWithExpiration")
	}

	return nil
}
