package redis

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	goRedis "github.com/redis/go-redis/v9"
	"gitlab.deepgate.io/apps/common/adapter/redis"
	"gitlab.deepgate.io/apps/common/log"
)

const (
	priceCheckCacheKeyPrefix = "price_check_result_%s_%s_%s_%s_%s" // searchKey_hotelID_roomID_rateID_bedOptionID.
	priceCheckLockKeyPrefix  = "lock_price_check_%s_%s_%s_%s_%s"   // searchKey_hotelID_roomID_rateID_bedOptionID

	priceCheckCacheTTL = 5 * time.Minute
	priceCheckLockTTL  = 10 * time.Second
)

type PriceCheckRepository interface {
	AcquirePriceCheckLock(ctx context.Context, searchKey, hotelID, roomID, rateID, bedOptionID string) (bool, error)
	ReleasePriceCheckLock(ctx context.Context, searchKey, hotelID, roomID, rateID, bedOptionID string) error
	WaitForPriceCheckLockRelease(ctx context.Context, searchKey, hotelID, roomID, rateID, bedOptionID string, sleepTime time.Duration, attempts int) error
}

type priceCheckRepository struct {
	redis redis.IRedis
}

func NewPriceCheckRepository(redis redis.IRedis) PriceCheckRepository {
	return &priceCheckRepository{
		redis: redis,
	}
}

func (r *priceCheckRepository) AcquirePriceCheckLock(_ context.Context, searchKey, hotelID, roomID, rateID, bedOptionID string) (bool, error) {
	key := r.generateLockKey(searchKey, hotelID, roomID, rateID, bedOptionID)

	lock, err := r.redis.AcquireLock(key, "", priceCheckLockTTL)
	if err != nil {
		return false, errors.Wrap(err, "redis.AcquireLock")
	}

	return lock, nil
}

func (r *priceCheckRepository) ReleasePriceCheckLock(_ context.Context, searchKey, hotelID, roomID, rateID, bedOptionID string) error {
	key := r.generateLockKey(searchKey, hotelID, roomID, rateID, bedOptionID)

	err := r.redis.Remove(key)
	if err != nil {
		return errors.Wrap(err, "redis.Remove")
	}

	return nil
}

func (r *priceCheckRepository) WaitForPriceCheckLockRelease(ctx context.Context, searchKey, hotelID, roomID, rateID, bedOptionID string, sleepTime time.Duration, attempts int) error {
	key := r.generateLockKey(searchKey, hotelID, roomID, rateID, bedOptionID)

	for i := 0; i < attempts; i++ {
		const lockTimeout = 2 * time.Second
		timeoutCtx, cancel := context.WithTimeout(ctx, lockTimeout)

		defer cancel()

		val := r.redis.CMD().Get(timeoutCtx, key)
		err := val.Err()
		//nolint:wsl
		if err != nil {
			if errors.Is(err, goRedis.Nil) {
				return nil
			}

			log.Error("WaitForPriceCheckLockRelease error",
				log.Any("error", err),
				log.Int("attempt", i),
				log.String("key", key))
		}

		time.Sleep(sleepTime)
	}

	return errors.New("lock was not released after all attempts")
}

// Removed unused function: generateCacheKey

func (r *priceCheckRepository) generateLockKey(searchKey, hotelID, roomID, rateID, bedOptionID string) string {
	return fmt.Sprintf(priceCheckLockKeyPrefix, searchKey, hotelID, roomID, rateID, bedOptionID)
}
