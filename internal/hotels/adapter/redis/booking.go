package redis

import (
	"fmt"
	"time"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/adapter/redis"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

const (
	createBookingLockPrefix  = "lock_create_booking_%s"
	createBookingLockTTL     = time.Minute
	resolvePendingLockPrefix = "lock_resolve_pending_%s"
	resolvePendingLockTTL    = time.Minute
)

type BookingRepository interface {
	AcquireCreateBookingLock(sessionID string) error
	ReleaseCreateBookingLock(sessionID string) error

	AcquireResolvePendingLock(bookingCode string) error
	ReleaseResolvePendingLock(bookingCode string) error
}

type bookingRepository struct {
	redis redis.IRedis
}

func NewBookingRedisRepository(redis redis.IRedis) BookingRepository {
	return &bookingRepository{
		redis: redis,
	}
}

func (r *bookingRepository) AcquireResolvePendingLock(bookingCode string) error {
	locked, err := r.redis.AcquireLock(r.generateResolvePendingKey(bookingCode), "", resolvePendingLockTTL)
	if err != nil {
		return errors.Wrap(err, "r.db.AcquireLock")
	}

	if !locked {
		return domain.ErrLockAlreadyHeld
	}

	return nil
}

func (r *bookingRepository) ReleaseResolvePendingLock(bookingCode string) error {
	key := r.generateResolvePendingKey(bookingCode)

	if err := r.redis.Remove(key); err != nil {
		return errors.Wrap(err, " r.db.Remove(key)")
	}

	return nil
}

func (r *bookingRepository) AcquireCreateBookingLock(id string) error {
	locked, err := r.redis.AcquireLock(r.generateCreateBookingLockUpdateKey(id), "", createBookingLockTTL)
	if err != nil {
		return errors.Wrap(err, "r.db.AcquireLock")
	}

	if !locked {
		return domain.ErrLockAlreadyHeld
	}

	return nil
}

func (r *bookingRepository) ReleaseCreateBookingLock(sessionID string) error {
	key := r.generateCreateBookingLockUpdateKey(sessionID)

	if err := r.redis.Remove(key); err != nil {
		return errors.Wrap(err, " r.db.Remove(key)")
	}

	return nil
}

func (r *bookingRepository) generateCreateBookingLockUpdateKey(sessionID string) string {
	return fmt.Sprintf(createBookingLockPrefix, sessionID)
}

func (r *bookingRepository) generateResolvePendingKey(bookingCode string) string {
	return fmt.Sprintf(resolvePendingLockPrefix, bookingCode)
}
