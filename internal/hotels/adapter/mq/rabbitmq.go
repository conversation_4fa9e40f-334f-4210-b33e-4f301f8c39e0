package mq

import (
	"context"
	"encoding/json"
	"fmt"

	commonRabbitMQ "gitlab.deepgate.io/apps/common/adapter/rabbitmq"
	"gitlab.deepgate.io/apps/common/constants"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/apps/common/schemas"
)

type Adapter interface {
	BookOldProvider(ctx context.Context, orderCode string) error
}

type adapter struct {
	mq commonRabbitMQ.MQ
}

func NewAdapter(mq commonRabbitMQ.MQ) Adapter {
	return &adapter{
		mq: mq,
	}
}

func (a *adapter) BookOldProvider(ctx context.Context, orderCode string) error {
	data := schemas.BookOldProviderMsg{
		OrderCode: orderCode,
	}

	mByte, err := json.Marshal(data)
	if err != nil {
		log.Error("Adapter BookOldProvider Marshal error", log.Any("req", data), log.Any("error", err))
		return fmt.Errorf("json.Marshal : %w", err)
	}

	if err = a.mq.PushRawMessage(constants.SkyhubHotelsExchangeName, constants.SkyhubHotelsBookOldProviderKey, mByte); err != nil {
		log.Error("Adapter BookOldProvider PushRawMessage error", log.Any("req", data), log.Any("error", err),
			log.String("exchange", constants.SkyhubHotelsExchangeName), log.String("routing", constants.SkyhubHotelsBookOldProviderKey))

		return fmt.Errorf("a.mq.PushRawMessage : %w", err)
	}

	return nil
}
