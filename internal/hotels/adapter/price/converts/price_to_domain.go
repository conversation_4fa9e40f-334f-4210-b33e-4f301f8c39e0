package converts

import (
	"gitlab.deepgate.io/apps/api/gen/go/price"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
)

// TO DOMAIN
func ToDomainHubOccupancyRates(info []*price.OccupancyRate, rawInfo []*domain.HubOccupancyRate) []*domain.HubOccupancyRate {
	if len(info) == 0 {
		return nil
	}
	var result []*domain.HubOccupancyRate
	for index, item := range info {
		result = append(result, ToDomainHubOccupancyRate(item, rawInfo[index]))
	}
	return result
}

func ToDomainHubOccupancyRate(info *price.OccupancyRate, rawInfo *domain.HubOccupancyRate) *domain.HubOccupancyRate {
	return &domain.HubOccupancyRate{
		OccupancyType:    info.OccupancyType,
		RoomQuantity:     uint(info.RoomQuantity),
		TotalNightlyRate: ToDomainHubRates(info.TotalNightlyRate),
		RateTaxes:        ToDomainHubRateTaxes(info.RateTaxes),
		Surcharges:       info.Surcharges,
		PayAtHotel:       ToDomainPayAtHotels(info.PayAtHotel),
		RateDiscounts:    ToDomainHubRateDiscounts(info.RateDiscounts),
		MarketingFee:     rawInfo.MarketingFee,
	}
}

func ToDomainHubRateDiscounts(info []*price.RateDiscount) []*domain.HubRateDiscount {
	if len(info) == 0 {
		return nil
	}
	var result []*domain.HubRateDiscount
	for _, item := range info {
		result = append(result, ToDomainHubRateDiscount(item))
	}
	return result
}

func ToDomainHubRateDiscount(info *price.RateDiscount) *domain.HubRateDiscount {
	if info == nil {
		return nil
	}
	return &domain.HubRateDiscount{
		Currency: info.Currency,
		Amount:   info.Amount,
		Code:     info.Code,
		Name:     info.Name,
	}
}

func ToDomainHubRateTaxes(info []*price.RateTax) []*domain.HubRateTax {
	if len(info) == 0 {
		return nil
	}
	var result []*domain.HubRateTax
	for _, item := range info {
		result = append(result, ToDomainHubRateTax(item))
	}
	return result
}

func ToDomainHubRateTax(info *price.RateTax) *domain.HubRateTax {
	if info == nil {
		return nil
	}
	return &domain.HubRateTax{
		Currency:    info.Currency,
		Amount:      &info.Amount,
		Included:    info.Included,
		Type:        enum.TaxType(info.Type),
		Description: info.Description,
	}
}

func ToDomainHubRates(info []*price.Rate) []*domain.HubRate {
	if len(info) == 0 {
		return nil
	}
	var result []*domain.HubRate
	for _, item := range info {
		result = append(result, ToDomainHubRate(item))
	}
	return result
}

func ToDomainHubRate(info *price.Rate) *domain.HubRate {
	if info == nil {
		return nil
	}
	return &domain.HubRate{
		RateAmount: info.RateAmount,
		RateBasic:  info.RateBasic,
		TaxAmount:  info.TaxAmount,
		Currency:   info.Currency,
	}
}

func ToDomainPayAtHotels(info []*price.PayAtHotel) []*domain.PayAtHotel {
	if len(info) == 0 {
		return nil
	}
	var result []*domain.PayAtHotel
	for _, item := range info {
		result = append(result, ToDomainPayAtHotel(item))
	}
	return result
}

func ToDomainPayAtHotel(info *price.PayAtHotel) *domain.PayAtHotel {
	if info == nil {
		return nil
	}
	return &domain.PayAtHotel{
		Amount:      info.Amount,
		Description: info.Description,
		Currency:    info.Currency,
	}
}

func ToDomainNonRefundableDateRanges(info []*price.NonrefundableDateRange) []*domain.HubNonrefundableDateRange {
	if len(info) == 0 {
		return nil
	}
	var result []*domain.HubNonrefundableDateRange
	for _, item := range info {
		result = append(result, ToDomainNonRefundableDateRange(item))
	}
	return result
}

func ToDomainNonRefundableDateRange(info *price.NonrefundableDateRange) *domain.HubNonrefundableDateRange {
	if info == nil {
		return nil
	}
	return &domain.HubNonrefundableDateRange{
		StartDate: info.StartDate,
		EndDate:   info.EndDate,
	}
}

func ToDomainHubCancelPolicies(info []*price.CancelPolicy) []*domain.HubCancelPolicy {
	if len(info) == 0 {
		return nil
	}
	var result []*domain.HubCancelPolicy
	for _, item := range info {
		result = append(result, ToDomainHubCancelPolicy(item))
	}
	return result
}

func ToDomainHubCancelPolicy(info *price.CancelPolicy) *domain.HubCancelPolicy {
	if info == nil {
		return nil
	}

	if info.CancelKey == "" {
		return nil
	}

	return &domain.HubCancelPolicy{
		Currency:               info.Currency,
		StartDate:              info.StartDate,
		EndDate:                info.EndDate,
		NonrefundableDateRange: ToDomainNonRefundableDateRanges(info.NonrefundableDateRanges),
		PenaltyAmount:          info.Amount,
		Key:                    info.CancelKey,
	}
}
