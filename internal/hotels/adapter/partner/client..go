package partner

import (
	"context"
	"fmt"

	bPartnerPb "gitlab.deepgate.io/apps/api/gen/go/partner/backend"

	commonError "gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/apps/common/server"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
)

type PartnerClient interface {
	AuthByOffice(ctx context.Context, req *domain.LoginReq) (*domain.PartnerUser, error)
	GetOfficeInfo(ctx context.Context, officeID, partnershipID string) (*domain.PartnerShopInfo, error)
}

type partnerClient struct {
	cfg         *config.Schema
	partnerConn *grpc.ClientConn
}

func NewPartnerClient(cfg *config.Schema, partnerConn *grpc.ClientConn) PartnerClient {
	return &partnerClient{cfg, partnerConn}
}

// GetOfficeInfo implements partnerClient.
func (c *partnerClient) GetOfficeInfo(ctx context.Context, officeID, partnershipID string) (*domain.PartnerShopInfo, error) {
	client := bPartnerPb.NewPartnerShopServiceClient(c.partnerConn)
	hubPartnershipID := c.cfg.HubPartnershipID
	if partnershipID != "" {
		hubPartnershipID = partnershipID
	}
	md := server.WithMetadataInternalPartnership(c.cfg.InternalSecretToken, hubPartnershipID)
	grpcCtx := metadata.NewOutgoingContext(ctx, md)

	res, err := client.GetByOfficeID(grpcCtx, &bPartnerPb.GetByOfficeIDReq{OfficeId: officeID})
	if err != nil {
		return nil, commonError.New(commonError.BadRequest, err.Error())
	}

	if res.Info == nil {
		return nil, fmt.Errorf("res info nil")
	}

	info := res.Info

	out := &domain.PartnerShopInfo{
		ID:          info.Id,
		Name:        info.Name,
		OwnerID:     info.Owner,
		PartnerType: info.PartnerType,
		Code:        info.Code,
		OfficeID:    info.OfficeId,
	}

	if info.Webhook != nil {
		webhookURLCfg := domain.WebhookURLCfg{
			Transaction: "",
		}

		if info.Webhook.UrlConfig != nil {
			webhookURLCfg.Transaction = info.Webhook.UrlConfig.Transaction
		}

		out.WebhookCfg = &domain.WebhookCfg{
			WebhookKey:    info.Webhook.Key,
			WebhookURLCfg: webhookURLCfg,
		}
	}

	if info.Hotel != nil {
		providers := make([]*domain.ProviderConfig, len(info.Hotel.Providers))
		for i, provider := range info.Hotel.Providers {
			providers[i] = &domain.ProviderConfig{
				Provider: enum.HotelProvider(provider.Provider),
				Enable:   provider.Enable,
			}
		}
		out.Hotel = &domain.PartnerHotelInfo{
			Enable:          info.Hotel.Enable,
			ProviderConfigs: providers,
			DefaultLanguage: info.Hotel.DefaultLanguage,
		}

		pCf := info.Hotel.PriceConditionConfig
		if pCf != nil {

			out.Hotel.PriceConfig = &domain.HotelPriceConditionConfig{
				Percent:       pCf.Percent,
				Amount:        pCf.Amount,
				ProviderOrder: pCf.ProviderOrder,
				Active:        pCf.Active,
			}
		}
	}
	return out, nil
}

func (c *partnerClient) AuthByOffice(ctx context.Context, req *domain.LoginReq) (*domain.PartnerUser, error) {
	client := bPartnerPb.NewAuthServiceClient(c.partnerConn)
	partnershipID := c.cfg.HubPartnershipID
	if req.PartnershipID != "" {
		partnershipID = req.PartnershipID
	}
	md := server.WithMetadataInternalPartnership(c.cfg.InternalSecretToken, partnershipID)
	grpcCtx := metadata.NewOutgoingContext(ctx, md)

	res, err := client.AuthenByOfficeID(grpcCtx, &bPartnerPb.AuthenByOfficeIDReq{
		OfficeId: req.OfficeID,
		ApiKey:   req.APIKey,
	})
	if err != nil {
		log.Error("partnerClient AuthByOffice AuthenByOfficeID error", log.Any("error", err), log.Any("req", req))
		return nil, commonError.New(commonError.BadRequest, err.Error())
	}

	if !res.IsSuccess {
		log.Error("partnerClient AuthByOffice AuthenByOfficeID response error", log.Any("res", res), log.Any("req", req))
		return nil, commonError.New(commonError.BadRequest, res.ErrorCode)
	}

	if res.Data == nil {
		log.Error("partner.UserInfo nil error", log.Any("res", res), log.Any("req", req))
		return nil, commonError.ErrSomethingOccurred
	}

	out := &domain.PartnerUser{
		ID:            res.Data.Id,
		CreatedAt:     res.Data.CreatedAt,
		UpdatedAt:     res.Data.UpdatedAt,
		CreatedBy:     res.Data.CreatedBy,
		UpdatedBy:     res.Data.UpdatedBy,
		Email:         res.Data.Email,
		Name:          res.Data.Name,
		PartnershipID: res.Data.PartnershipId,
		PartnerShopID: res.Data.PartnerShopId,
	}

	if res.WebhookCfg != nil {
		webhookURLCfg := domain.WebhookURLCfg{
			Transaction: "",
		}

		if res.Data.WebhookUrlConfig != nil {
			webhookURLCfg.Transaction = res.Data.WebhookUrlConfig.Transaction
		}

		out.WebhookCfg = domain.WebhookCfg{
			WebhookKey:    res.WebhookCfg.Key,
			WebhookURLCfg: webhookURLCfg,
		}
	}

	return out, nil
}
