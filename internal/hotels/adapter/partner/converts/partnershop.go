package converts

import (
	"gitlab.deepgate.io/apps/api/gen/go/partner"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
)

func ToDomainProviderConfigs(ins []*partner.ProviderConfig) []*domain.ProviderConfig {
	out := []*domain.ProviderConfig{}

	for _, item := range ins {
		out = append(out, &domain.ProviderConfig{
			Provider: enum.HotelProvider(item.Value),
			Enable:   item.Enabled,
		})
	}

	return out
}
