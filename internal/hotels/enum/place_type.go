package enum

import (
	"bytes"
	"fmt"
)

type PlaceType int

const (
	PlaceTypeContinent PlaceType = iota
	PlaceTypeCountry
	PlaceTypeProvinceState
	PlaceTypeHighLevelRegion
	PlaceTypeMultiCityVicinity
	PlaceTypeCity
	PlaceTypeNeighborhood
	PlaceTypeAirport
	PlaceTypePointOfInterest
	PlaceTypeTrainStation
	PlaceTypeMetroStation
	PlaceTypeBusStation
	PlaceTypeHotel
)

var PlaceTypeName = map[PlaceType]string{
	PlaceTypeContinent:         "continent",
	PlaceTypeCountry:           "country",
	PlaceTypeProvinceState:     "province_state",
	PlaceTypeHighLevelRegion:   "high_level_region",
	PlaceTypeMultiCityVicinity: "multi_city_vicinity",
	PlaceTypeCity:              "city",
	PlaceTypeNeighborhood:      "neighborhood",
	PlaceTypeAirport:           "airport",
	PlaceTypePointOfInterest:   "point_of_interest",
	PlaceTypeTrainStation:      "train_station",
	PlaceTypeMetroStation:      "metro_station",
	PlaceTypeBusStation:        "bus_station",
	PlaceTypeHotel:             "hotel",
}

var PlaceTypeValue = func() map[string]PlaceType {
	value := map[string]PlaceType{}

	for k, v := range PlaceTypeName {
		value[v] = k
		value[fmt.Sprintf("%v", k)] = k
	}

	return value
}()

func (e PlaceType) MarshalJSON() ([]byte, error) {
	v, ok := PlaceTypeName[e]

	if !ok {
		return []byte("\"\""), nil
	}

	buffer := bytes.NewBufferString(`"`)
	buffer.WriteString(v)
	buffer.WriteString(`"`)

	return buffer.Bytes(), nil
}

func (e *PlaceType) UnmarshalJSON(data []byte) error {
	data = bytes.Trim(data, "\"")
	v, ok := PlaceTypeValue[string(data)]

	if !ok {
		return fmt.Errorf("enum '%s' is not registered, must be one of: %v", data, e.EnumDescriptions())
	}

	*e = v

	return nil
}

func (*PlaceType) EnumDescriptions() []string {
	vals := []string{}

	for _, name := range PlaceTypeName {
		vals = append(vals, name)
	}

	return vals
}
