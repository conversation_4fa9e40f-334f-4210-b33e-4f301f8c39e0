package enum

import (
	"bytes"
	"fmt"
)

type RegionType int

const (
	Continent RegionType = iota
	Country
	ProvinceState
	HighLevelRegion
	MultiCityVicinity
	City
	Neighborhood
	Airport
	PointOfInterest
	TrainStation
	MetroStation
	BusStation
	Street
)

var RegionTypeName = map[RegionType]string{
	Continent:         "continent",
	Country:           "country",
	ProvinceState:     "province_state",
	HighLevelRegion:   "high_level_region",
	MultiCityVicinity: "multi_city_vicinity",
	City:              "city",
	Neighborhood:      "neighborhood",
	Airport:           "airport",
	PointOfInterest:   "point_of_interest",
	TrainStation:      "train_station",
	MetroStation:      "metro_station",
	BusStation:        "bus_station",
	Street:            "street",
}

var RegionTypeValue = func() map[string]RegionType {
	value := map[string]RegionType{}

	for k, v := range RegionTypeName {
		value[v] = k
		value[fmt.Sprintf("%v", k)] = k
	}

	return value
}()

func (e RegionType) MarshalJSON() ([]byte, error) {
	v, ok := RegionTypeName[e]

	if !ok {
		return []byte("\"\""), nil
	}

	buffer := bytes.NewBufferString(`"`)
	buffer.WriteString(v)
	buffer.WriteString(`"`)

	return buffer.Bytes(), nil
}

func (e *RegionType) UnmarshalJSON(data []byte) error {
	data = bytes.Trim(data, "\"")
	v, ok := RegionTypeValue[string(data)]

	if !ok {
		return fmt.Errorf("enum '%s' is not registered, must be one of: %v", data, e.EnumDescriptions())
	}

	*e = v

	return nil
}

func (*RegionType) EnumDescriptions() []string {
	vals := []string{}

	for _, name := range RegionTypeName {
		vals = append(vals, name)
	}

	return vals
}
