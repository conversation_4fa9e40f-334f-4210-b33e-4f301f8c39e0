package enum

type RatingFilterOption string

const (
	RatingFilterOptionNone       = ""
	RatingFilterOptionExcellence = "excellence"
	RatingFilterOptionVeryGood   = "very_good"
	RatingFilterOptionGood       = "good"
	RatingFilterOptionAny        = "any"
)

var RatingFilterOptionRange = map[CustomerRatingFilterOption][]float64{
	RatingFilterOptionNone:       {0, 10},
	RatingFilterOptionExcellence: {9, 10},
	RatingFilterOptionVeryGood:   {8, 8.9},
	RatingFilterOptionGood:       {7, 7.9},
	RatingFilterOptionAny:        {0, 6.9},
}
