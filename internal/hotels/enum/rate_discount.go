package enum

import (
	"bytes"
	"fmt"
)

type RateDiscountName string

const (
	RateDiscountNameBase = "base"
	RateDiscountNameTax  = "tax"
	RateDiscountNameAll  = "all"
)

var RateDiscountNameName = map[RateDiscountName]string{
	RateDiscountNameBase: "discount on base rate",
	RateDiscountNameTax:  "discount on tax-inclusive rate",
	RateDiscountNameAll:  "discount on all-inclusive rate",
}

var RateDiscountNameValue = func() map[string]RateDiscountName {
	value := map[string]RateDiscountName{}

	for k, v := range RateDiscountNameName {
		value[v] = k
		value[fmt.Sprintf("%v", k)] = k
	}

	return value
}()

func (e RateDiscountName) MarshalJSON() ([]byte, error) {
	v, ok := RateDiscountNameName[e]

	if !ok {
		return []byte("\"\""), nil
	}

	buffer := bytes.NewBufferString(`"`)
	buffer.WriteString(v)
	buffer.WriteString(`"`)

	return buffer.Bytes(), nil
}

func (e *RateDiscountName) UnmarshalJSON(data []byte) error {
	data = bytes.Trim(data, "\"")
	v, ok := RateDiscountNameValue[string(data)]

	if !ok {
		return fmt.Errorf("enum '%s' is not register, must be one of: %v", data, e.EnumDescriptions())
	}

	*e = v

	return nil
}

func (*RateDiscountName) EnumDescriptions() []string {
	vals := []string{}

	for _, name := range RateDiscountNameName {
		vals = append(vals, name)
	}

	return vals
}
