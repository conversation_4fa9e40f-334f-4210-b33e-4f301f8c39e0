package enum

import "fmt"

type BookingStatus string

const (
	BookingStatusNone      BookingStatus = ""
	BookingStatusSuccess   BookingStatus = "Success"
	BookingStatusPending   BookingStatus = "Pending"
	BookingStatusFailed    BookingStatus = "Failed"
	BookingStatusCanceling BookingStatus = "Canceling"
	BookingStatusCancel    BookingStatus = "Canceled"
)

var BookingStatusName = map[BookingStatus]string{
	BookingStatusSuccess:   "Success",
	BookingStatusPending:   "Pending",
	BookingStatusFailed:    "Failed",
	BookingStatusCanceling: "Canceling",
	BookingStatusCancel:    "Canceled",
}

var BookingStatusNumber = map[BookingStatus]int32{
	BookingStatusSuccess:   1,
	BookingStatusPending:   2,
	BookingStatusFailed:    3,
	BookingStatusCanceling: 4,
	BookingStatusCancel:    5,
}

var BookingStatusIntMap = map[int32]BookingStatus{
	1: BookingStatusSuccess,
	2: BookingStatusPending,
	3: BookingStatusFailed,
	4: BookingStatusCanceling,
	5: BookingStatusCancel,
}
var BookingStatusValue = func() map[string]BookingStatus {
	value := map[string]BookingStatus{}

	for k, v := range BookingStatusName {
		value[v] = k
		value[fmt.Sprintf("%v", k)] = k
	}

	return value
}()
