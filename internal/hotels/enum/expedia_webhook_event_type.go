package enum

type ExpediaWebhookEventType string

// Enum values for EventType
const (
	ItineraryAgentCreate     ExpediaWebhookEventType = "itinerary.agent.create"
	ItineraryAgentChange     ExpediaWebhookEventType = "itinerary.agent.change"
	ItineraryAgentCancel     ExpediaWebhookEventType = "itinerary.agent.cancel"
	ItinerarySupplierCancel  ExpediaWebhookEventType = "itinerary.supplier.cancel"
	ItinerarySupplierConfirm ExpediaWebhookEventType = "itinerary.supplier.confirm"
	ItineraryFraudCancel     ExpediaWebhookEventType = "itinerary.fraud.cancel"
	ItinerarySupplierChange  ExpediaWebhookEventType = "itinerary.supplier.change"
	ItineraryTravelerNoShow  ExpediaWebhookEventType = "itinerary.traveler.noshow"
	ItinerarySupplierRefund  ExpediaWebhookEventType = "itinerary.supplier.refund"
	ItineraryMessageReceived ExpediaWebhookEventType = "itinerary.message.received"
)
