package enum

import (
	"bytes"
	"fmt"
)

type HotelProvider uint

const (
	HotelProviderNone HotelProvider = iota + 10
	HotelProviderExpedia
	HotelProviderTourMind
	HotelProviderPKFare
	HotelProviderRateHawk
	HotelProviderTA
	HotelProviderDida

	// Custom
	HotelProviderExpediaManual = 99
)

var HotelProviderName = map[HotelProvider]string{
	HotelProviderNone:          "",
	HotelProviderExpedia:       "expedia",
	HotelProviderTourMind:      "tour-mind",
	HotelProviderPKFare:        "pk-fare",
	HotelProviderExpediaManual: "expedia-manual",
	HotelProviderRateHawk:      "rate-hawk",
	HotelProviderTA:            "ta",
	HotelProviderDida:          "dida",
}

var hotelProviderHash = map[string]HotelProvider{
	"9bf31c7ff062936a96d3c8bd1f8f2ff3": HotelProviderTA,
	"6512bd43d9caa6e02c990b0a82652dca": HotelProviderExpedia,
	"c20ad4d76fe97759aa27a0c99bff6710": HotelProviderTourMind,
	"c51ce410c124a10e0db5e4b97fc2af39": HotelProviderPKFare,
	"ac627ab1ccbdb62ec96e702f07f6425b": HotelProviderExpediaManual,
	"aab3238922bcc25a6f606eb525ffdc56": HotelProviderRateHawk,
	"c74d97b01eae257e44aa9d5bade97baf": HotelProviderDida,
	"":                                 HotelProviderNone,
}

func HotelProviderToHash(value HotelProvider) string {
	for key, val := range hotelProviderHash {
		if val == value {
			return key
		}
	}

	return ""
}

func HotelProviderFromHash(value string) HotelProvider {
	return hotelProviderHash[value]
}

var HotelProviderValue = func() map[string]HotelProvider {
	value := map[string]HotelProvider{}

	for k, v := range HotelProviderName {
		value[v] = k
		value[fmt.Sprintf("%v", k)] = k
	}

	return value
}()

func (e HotelProvider) MarshalJSON() ([]byte, error) {
	v, ok := HotelProviderName[e]

	if !ok {
		return []byte("\"\""), nil
	}

	buffer := bytes.NewBufferString(`"`)
	buffer.WriteString(v)
	buffer.WriteString(`"`)

	return buffer.Bytes(), nil
}

func (e *HotelProvider) UnmarshalJSON(data []byte) error {
	data = bytes.Trim(data, "\"")
	v, ok := HotelProviderValue[string(data)]

	if !ok {
		return fmt.Errorf("enum '%s' is not register, must be one of: %v", data, e.EnumDescriptions())
	}

	*e = v

	return nil
}

func (*HotelProvider) EnumDescriptions() []string {
	vals := []string{}

	for _, name := range HotelProviderName {
		vals = append(vals, name)
	}

	return vals
}
