package enum

import (
	"bytes"
	"fmt"
)

type HiddenFeeType uint

const (
	HiddenFeeTypeNone HiddenFeeType = iota
	HiddenFeeTypeConfig
	HiddenFeeTypeHotel
)

var HiddenFeeTypeName = map[HiddenFeeType]string{
	HiddenFeeTypeNone:   "",
	HiddenFeeTypeConfig: "config",
	HiddenFeeTypeHotel:  "hotel",
}

var HiddenFeeTypeValue = func() map[string]HiddenFeeType {
	value := map[string]HiddenFeeType{}

	for k, v := range HiddenFeeTypeName {
		value[v] = k
		value[fmt.Sprintf("%v", k)] = k
	}

	return value
}()

func (e HiddenFeeType) MarshalJSON() ([]byte, error) {
	v, ok := HiddenFeeTypeName[e]

	if !ok {
		return []byte("\"\""), nil
	}

	buffer := bytes.NewBufferString(`"`)
	buffer.WriteString(v)
	buffer.WriteString(`"`)

	return buffer.Bytes(), nil
}

func (e *HiddenFeeType) UnmarshalJSON(data []byte) error {
	data = bytes.Trim(data, "\"")
	v, ok := HiddenFeeTypeValue[string(data)]

	if !ok {
		return fmt.Errorf("enum '%s' is not register, must be one of: %v", data, e.EnumDescriptions())
	}

	*e = v

	return nil
}

func (*HiddenFeeType) EnumDescriptions() []string {
	vals := []string{}

	for _, name := range HiddenFeeTypeName {
		vals = append(vals, name)
	}

	return vals
}
