package enum

import "fmt"

type HubOrderStatus string

const (
	HubOrderStatusNone      HubOrderStatus = ""
	HubOrderStatusDraft     HubOrderStatus = "Draft"
	HubOrderStatusConfirmed HubOrderStatus = "Confirmed"
	HubOrderStatusCancelled HubOrderStatus = "Cancelled"
)

var HubOrderStatusName = map[HubOrderStatus]string{
	HubOrderStatusDraft:     "Draft",
	HubOrderStatusConfirmed: "Confirmed",
	HubOrderStatusCancelled: "Cancelled",
}

var HubOrderStatusValue = func() map[string]HubOrderStatus {
	value := map[string]HubOrderStatus{}

	for k, v := range HubOrderStatusName {
		value[v] = k
		value[fmt.Sprintf("%v", k)] = k
	}

	return value
}()
