package enum

type CustomerRatingFilterOption string

const (
	CustomerRatingFilterOptionNone       = ""
	CustomerRatingFilterOptionExcellence = "excellence"
	CustomerRatingFilterOptionVeryGood   = "very_good"
	CustomerRatingFilterOptionGood       = "good"
	CustomerRatingFilterOptionAny        = "any"
)

// var CustomerRatingFilterOptionRange = map[CustomerRatingFilterOption][]float64{
// 	CustomerRatingFilterOptionNone:       {0, 10},
// 	CustomerRatingFilterOptionExcellence: {9, 10},
// 	CustomerRatingFilterOptionVeryGood:   {8, 8.9},
// 	CustomerRatingFilterOptionGood:       {7, 7.9},
// 	CustomerRatingFilterOptionAny:        {0, 6.9},
// }

var CustomerRatingFilterOptionRange = map[CustomerRatingFilterOption][]float64{
	CustomerRatingFilterOptionNone:       {0, 5},
	CustomerRatingFilterOptionExcellence: {4.5, 5},
	CustomerRatingFilterOptionVeryGood:   {4, 4.4},
	CustomerRatingFilterOptionGood:       {3.5, 3.9},
	CustomerRatingFilterOptionAny:        {0, 3.4},
}
