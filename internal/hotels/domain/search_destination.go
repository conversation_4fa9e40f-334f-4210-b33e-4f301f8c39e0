package domain

import (
	commonDomain "gitlab.deepgate.io/apps/common/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
)

type SearchDestinationReq struct {
	Query           string                   `json:"query"`
	Pagination      *commonDomain.Pagination `json:"pagination"`
	OrderBy         *map[string]int          `json:"order_by"`
	OfficeID        string                   `json:"-"`
	Language        string                   `json:"-"`
	PlaceType       enum.PlaceType           `json:"-"`
	DefaultLanguage string                   `json:"-"`
	EnableProviders []enum.HotelProvider     `json:"-"`
}

type SearchDestinationRes struct {
	ErrorRes
	Pagination   *commonDomain.Pagination `json:"pagination"`
	Destinations []*Place                 `json:"destinations"`
	Language     string                   `json:"language"`
}
