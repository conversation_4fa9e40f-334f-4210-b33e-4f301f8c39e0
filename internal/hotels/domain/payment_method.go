package domain

import (
	commonEnum "gitlab.deepgate.io/apps/common/enum"
)

type PaymentMethod struct {
	ID         string                   `json:"id"`
	Name       string                   `json:"name"`
	Code       string                   `json:"code"`
	Method     commonEnum.PaymentMethod `json:"method"`
	Fee        float64                  `json:"fee"`
	DisplayFee string                   `json:"display_fee"`
	Icon       string                   `json:"icon"`
}
