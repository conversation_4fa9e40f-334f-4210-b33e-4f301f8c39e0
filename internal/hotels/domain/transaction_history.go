package domain

import (
	commonEnum "gitlab.deepgate.io/apps/common/enum"
)

type TransactionHistoryReq struct {
	From          int64                    `json:"from,omitempty"`
	To            int64                    `json:"to,omitempty"`
	BookingCode   string                   `json:"booking_code,omitempty"`
	Refund        *bool                    `json:"refund,omitempty"`
	PaymentMethod commonEnum.PaymentMethod `json:"payment_method,omitempty"`
	Pagination    *Pagination              `json:"pagination,omitempty"`
}

type TransactionHistoryRes struct {
	Transactions []*Transaction `json:"transactions,omitempty"`
	Pagination   *Pagination    `json:"pagination,omitempty"`
	ErrorRes
}
