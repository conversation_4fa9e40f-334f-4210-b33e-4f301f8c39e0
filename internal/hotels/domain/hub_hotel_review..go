package domain

import "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"

type HubHotelReviewReq struct {
	Language string             `json:"language" validate:"required"`
	HotelID  string             `json:"hotel_id" validate:"required"`
	Provider enum.HotelProvider `json:"-"`
	OfficeID string             `json:"-"`
}

type HubManagerResponse struct {
	Date string `json:"date"`
	Text string `json:"text"`
}

type HubHotelReviewItem struct {
	VerificationSource  string               `json:"verification_source"`
	DateSubmitted       string               `json:"date_submitted"`
	Rating              string               `json:"rating"`
	ReviewerName        string               `json:"reviewer_name"`
	TravelCompanion     string               `json:"travel_companion"`
	TripReason          string               `json:"trip_reason"`
	Text                string               `json:"text"`
	Night               string               `json:"night"`
	StayDate            string               `json:"stay_date"`
	HubManagerResponses []HubManagerResponse `json:"manager_responses"`
}

type HubHotelReviewRes struct {
	ErrorRes
	Reviews []*HubHotelReviewItem `json:"reviews"`
}
