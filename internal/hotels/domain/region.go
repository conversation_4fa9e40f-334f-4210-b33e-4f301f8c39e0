package domain

type RegionCoordinates struct {
	CenterLongitude      float64          `json:"center_longitude"`
	CenterLatitude       float64          `json:"center_latitude"`
	BoundingPolygon      *BoundingPolygon `json:"bounding_polygon"`
	BoundingPolygonIDRef string           `json:"bounding_polygon_id_ref,omitempty"`
}

type BoundingPolygon struct {
	Base
	Type        string      `json:"type"`
	Coordinates interface{} `json:"coordinates"`
}

type Ancestor struct {
	ID   string `json:"id"`
	Type string `json:"type"`
}

type Region struct {
	Base
	RegionID               string              `json:"region_id"`
	Type                   string              `json:"type"`
	Name                   string              `json:"name"`
	FullName               string              `json:"name_full"`
	Descriptor             string              `json:"descriptor"`
	IATAAirportCode        string              `json:"iata_airport_code"`
	IATAAirportMetroCode   string              `json:"iata_airport_metro_code"`
	CountryCode            string              `json:"country_code"`
	CountrySubdivisionCode string              `json:"country_subdivision_code"`
	Coordinates            *RegionCoordinates  `json:"coordinates"`
	Associations           map[string][]string `json:"associations"`
	Ancestors              []*Ancestor         `json:"ancestors"`
	Descendants            map[string][]string `json:"descendants"`
	PropertyIDs            []string            `json:"property_ids"`
	PropertyIDsExpanded    []string            `json:"property_ids_expanded"`
	Categories             []string            `json:"categories"`
	Tags                   []string            `json:"tags"`
	Language               string              `json:"language"`
	Version                string              `json:"version,omitempty"`
}

type RegionsRequest struct {
	Include                []string `json:"include"`
	Language               string   `json:"language"`
	AncestorID             string   `json:"ancestor_id"`
	Area                   string   `json:"area"`
	CountryCode            []string `json:"country_code"`
	CountrySubdivisionCode []string `json:"country_subdivision_code"`
	IATALocationCode       string   `json:"iata_location_code"`
	Limit                  int      `json:"limit"`
	SupplySource           string   `json:"supply_source"`
	Type                   []string `json:"type"`
	BillingTerms           string   `json:"billing_terms"`
	PartnerPointOfSale     string   `json:"partner_point_of_sale"`
	PaymentTerms           string   `json:"payment_terms"`
	PlatformName           string   `json:"platform_name"`
}

type TourmindRegion struct {
	CountryCode string `json:"country_code"`
	SeqNum      int    `json:"seq_num"`
	CompletedAt int64  `json:"complete_at"`
}
