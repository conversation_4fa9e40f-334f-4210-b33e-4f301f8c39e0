package domain

import (
	"gitlab.deepgate.io/apps/common/errors"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
)

type WarningRes struct {
	WarningCode string `json:"warning_code,omitempty"`
	WarningMsg  string `json:"warning_msg,omitempty"`
}

type ErrorRes struct {
	IsSuccess  bool   `json:"is_success"`
	ErrorCode  string `json:"error_code,omitempty"`
	ErrorMsg   string `json:"error_msg,omitempty"`
	StatusCode int    `json:"-"`
}

// Common app errs
var (
	ErrLockAlreadyHeld          = errors.New(errors.BadRequest, "LOCK_ALREADY_HELD")
	ErrInvalidValue             = errors.New(errors.BadRequest, "INVALID_VALUE")
	ErrSessionInvalidOrExpired  = errors.NewV2(errors.BadRequest, "INVALID_SESSION_OR_SESSION_EXPIRED", constants.ErrMsgInvalidSessionOrExpired)
	ErrAnotherRequestInProgress = errors.NewV2(errors.BadRequest, "ANOTHER_REQUEST_INPROCESS", constants.ErrMsgAnotherRequestInprocess)
	ErrMissingClientHeaderInfo  = errors.New(errors.BadRequest, "MISSING_CLIENT_HEADER_INFO")
	ErrMissinDCPInfo            = errors.New(errors.BadRequest, "MISSING_DCP_INFO")
	ErrInvalidDateFormat        = errors.New(errors.BadRequest, "INVALID_DEPART_DATE")
	ErrNoProviderForOfficeID    = errors.New(errors.BadRequest, "NO_PROVIDER_FOR_OFFICE_ID")
	ErrPlaceNotFound            = errors.New(errors.BadRequest, "PLACE_NOT_FOUND")
)

// Search
var (
	ErrHotelPropertyNotFound    = errors.New(errors.BadRequest, "HOTEL_PROPERTY_NO_FOUND")
	ErrCheckInDateInvalid       = errors.New(errors.BadRequest, "CHECK_IN_DATE_INVALID")
	ErrCheckOutDateInvalid      = errors.New(errors.BadRequest, "CHECK_OUT_DATE_INVALID")
	ErrInvalidRequestRoomAmount = errors.New(errors.NotFound, "INVALID_ROOM_AMOUNT")
	ErrStayDaysInvalid          = errors.New(errors.BadRequest, "STAY_MUST_LESS_THAN_30")
	ErrInvalidChildAge          = errors.NewV2(errors.BadRequest, "INVALID_CHILDREN_AGE", "Invalid child age")
	ErrInvalidOccupancy         = errors.New(errors.BadRequest, "INVALID_OCCUPANCY")
	ErrCountryCodeNotSupport    = errors.New(errors.BadRequest, "COUNTRY_CODE_NOT_SUPPORT")
	ErrLanguageNotSupport       = errors.New(errors.BadRequest, "LANGUAGE_NOT_SUPPORT")
	ErrCurrencyNotSupport       = errors.New(errors.BadRequest, "CURRENCY_NOT_SUPPORT")
	ErrExceedHotelIDsLimit      = errors.New(errors.BadRequest, "EXCEED_HOTEL_IDS_LIMIT")
	ErrPlaceRequired            = errors.New(errors.BadRequest, "PLACE_REQUIRED")
	ErrHotelIdsRequired         = errors.New(errors.BadRequest, "HOTEL_IDS_REQUIRED")
	ErrRoomsExceedAdultsAmount  = errors.New(errors.BadRequest, "ROOMS_EXCEED_ADULTS_AMOUNT")
)

// PriceCheck
var (
	ErrSearchKeyNotFound = errors.New(errors.BadRequest, "SEARCH_KEY_EXPIRE_OR_INVALID")
	ErrRoomSoldOut       = errors.NewV2(errors.BadRequest, "ROOM_SOLD_OUT", "Room sold out")
	ErrBedOptionNotFound = errors.New(errors.BadRequest, "BED_OPTION_NOT_FOUND")
)

// Book
var (
	ErrInsufficientBalance           = errors.NewV2(errors.BadRequest, "INSUFFICIENT_BALANCE", constants.ErrMsgInsufficientBalance)
	ErrBookingCanceling              = errors.New(errors.BadRequest, "BOOKING_CANCELING")
	ErrBookingCanceled               = errors.New(errors.BadRequest, "BOOKING_CANCELED")
	ErrBookingCompleted              = errors.New(errors.BadRequest, "BOOKING_COMPLETED")
	ErrBookingPending                = errors.New(errors.BadRequest, "BOOKING_PENDING")
	ErrBookingFailed                 = errors.New(errors.BadRequest, "BOOKING_FAILED")
	ErrBookingNotFound               = errors.New(errors.NotFound, "BOOKING_NOT_FOUND")
	ErrBookingFailedNeedManualRefund = errors.NewV2(errors.Internal, "ISSUE_FAILED_NEED_MANUAL_REFUND", "The issue failed and requires manual refund processing.")
	ErrRoomRateHasBeenChanged        = errors.NewV2(errors.BadRequest, "ROOM_RATE_HAS_BEEN_CHANGED", "Rate has been changed")
	ErrBookingNotAllowedToCancel     = errors.NewV2(errors.BadRequest, "BOOKING_NOT_ALLOWED_TO_CANCEL", "Booking not allowed to cancel")
	ErrEmptyHolder                   = errors.NewV2(errors.BadRequest, "EMPTY_HOLDER", "Holder info is empty")
	ErrBookingExisted                = errors.NewV2(errors.BadRequest, "BOOKING_EXISTED", "Booking is already created")
	ErrNotFound                      = errors.New(errors.NotFound, "NOT_FOUND")
)

var (
	ErrBookingCannotCancel = errors.New(errors.BadRequest, "BOOKING_CANNOT_CANCEL")
	ErrBookingCancelFailed = errors.New(errors.BadRequest, "BOOKING_CANCEL_FAILED")
)

var (
	ErrInvalidRefundAmount = errors.New(errors.BadRequest, "INVALID_REFUND_AMOUNT")
)

// Update

var (
	ErrInvalidBookingStatus        = errors.New(errors.BadRequest, "INVALID_BOOKING_STATUS")
	ErrInvalidConfirmationIDAmount = errors.New(errors.NotFound, "INVALID_CONFIRMATION_ID_AMOUNT")
)

// HiddenServiceFee
var (
	ErrHiddenServiceFeeNotFound  = errors.New(errors.NotFound, "HIDDEN_SERVICE_FEE_NOT_FOUND")
	ErrDuplicateHiddenServiceFee = errors.New(errors.BadRequest, "DUPLICATE_HIDDEN_SERVICE_FEE")
)

var (
	ErrInvalidMatchKey    = errors.New(errors.BadRequest, "INVALID_MATCH_KEY")
	ErrProviderNotAllowed = errors.New(errors.PermissionDenied, "KEY_NOT_ALLOWED")
	ErrInvalidSignature   = errors.New(errors.PermissionDenied, "INVALID_SIGNATURE")
)

var (
	ErrTooManyRequests = errors.New(errors.TooManyRequests, "TOO_MANY_REQUESTS")
)

var (
	ErrInvalidTestingKey = errors.New(errors.BadRequest, "INVALID_TESTING_KEY")
)
