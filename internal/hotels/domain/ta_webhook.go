package domain

const (
	TABookingStatusConfirmed   = "confirmed"
	TABookingStatusCompleted   = "completed"
	TABookingStatusCanceled    = "canceled"
	TABookingStatusPriceChange = "price_change"
)

type TAWebhookMsg struct {
	BookingId         string                      `json:"bookingId"`
	Status            string                      `json:"status"`
	Amount            float64                     `json:"amount"`
	ConfirmationInfos []BookingConfirmationIdInfo `json:"confirmationInfos"`
	// UpdateTime        string                      `json:"updateTime"`
	Note string `json:"note"`
}

type BookingConfirmationIdInfo struct {
	// DetailId int64 `json:"detailId"`
	ProductId      string `json:"productId"`
	ConfirmationId string `json:"confirmId"`
	Used           bool   `json:"-"`
}
