package domain

import (
	"time"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
)

// Root struct
type Hotel struct {
	Base                           `json:",inline"`
	Rank                           float64                       `json:"rank,omitempty"`
	Active                         bool                          `json:"active,omitempty"`
	MultiUnit                      bool                          `json:"multi_unit,omitempty"`
	PaymentRegistrationRecommended bool                          `json:"-"`
	HotelID                        string                        `json:"hotel_id,omitempty"`
	Language                       string                        `json:"-"`
	Name                           string                        `json:"name,omitempty"`
	Phone                          string                        `json:"phone,omitempty"`
	Fax                            string                        `json:"fax,omitempty"`
	SupplySource                   string                        `json:"-"`
	RoomReferences                 []*RoomRefInfo                `json:"-"`
	Airports                       *Airports                     `json:"airports,omitempty"`
	Address                        *Address                      `json:"address,omitempty"`
	Ratings                        *Ratings                      `json:"ratings,omitempty"`
	Location                       *Location                     `json:"location,omitempty"`
	Category                       *Category                     `json:"category,omitempty"`
	BusinessModel                  *BusinessModel                `json:"business_model,omitempty"`
	CheckIn                        *CheckIn                      `json:"check_in,omitempty"`
	Checkout                       *Checkout                     `json:"checkout,omitempty"`
	Fees                           *Fees                         `json:"fees,omitempty"`
	Policies                       *Policies                     `json:"policies,omitempty"`
	Attributes                     *Attributes                   `json:"attributes,omitempty"`
	Amenities                      []*Amenity                    `json:"amenities,omitempty"`
	Images                         []*Image                      `json:"images,omitempty"`
	OnsitePayments                 *OnsitePayments               `json:"onsite_payments,omitempty"`
	Rates                          []*ContentRate                `json:"rates,omitempty"`
	Dates                          *Dates                        `json:"dates,omitempty"`
	Descriptions                   *Descriptions                 `json:"descriptions,omitempty"`
	Statistics                     []*Statistic                  `json:"statistics,omitempty"`
	AllInclusive                   *AllInclusive                 `json:"all_inclusive,omitempty"`
	TaxID                          string                        `json:"tax_id,omitempty"`
	RegistryNumber                 string                        `json:"registry_number,omitempty"`
	Chain                          *Chain                        `json:"chain,omitempty"`
	Brand                          *Brand                        `json:"brand,omitempty"`
	SpokenLanguages                map[string]*Language          `json:"spoken_languages,omitempty"`
	Rooms                          []*Room                       `json:"-"`
	Themes                         []*Theme                      `json:"themes,omitempty"`
	VacationRentalDetails          *VacationRentalDetails        `json:"vacation_rental_details,omitempty"`
	ProviderIds                    map[enum.HotelProvider]string `json:"-"`
	Timezone                       string                        `json:"-"`
	Version                        string                        `json:"-"`
	Distance                       float64                       `json:"-"`                    // handle distance aggregate in search geo
	DataRooms                      []*RoomView                   `json:"data_rooms,omitempty"` // room view for hotel-content api
}

// RoomView struct for hotel-content api
type RoomView struct {
	RoomID       string            `json:"room_id,omitempty"`
	Name         string            `json:"name,omitempty"`
	Descriptions *RoomDescriptions `json:"descriptions,omitempty"`
	Amenities    []*Amenity        `json:"amenities,omitempty"`
	Occupancy    *ContentOccupancy `json:"occupancy,omitempty"`
	Area         *Area             `json:"area,omitempty"`
	Views        []*View           `json:"views,omitempty"`
	Images       []*Image          `json:"images,omitempty"`
}

type RoomRefInfo struct {
	ID          string                        `json:"id,omitempty"`
	ProviderIds map[enum.HotelProvider]string `json:"provider_ids,omitempty"`
	RoomID      string                        `json:"room_id,omitempty"`
}

// Address struct
type Address struct {
	Line1               string `json:"line_1,omitempty"`
	City                string `json:"city,omitempty"`
	StateProvinceName   string `json:"state_province_name,omitempty"`
	PostalCode          string `json:"postal_code,omitempty"`
	CountryCode         string `json:"country_code,omitempty"`
	ObfuscationRequired bool   `json:"obfuscation_required,omitempty"`
}

// Ratings struct
type Ratings struct {
	Property *PropertyRating `json:"property,omitempty"`
	Guest    *GuestRating    `json:"guest,omitempty"`
}

type PropertyRating struct {
	Rating string `json:"rating,omitempty"`
	Type   string `json:"type,omitempty"`
}

type GuestRating struct {
	Count                 int    `json:"count,omitempty"`
	Overall               string `json:"overall,omitempty"`
	Cleanliness           string `json:"cleanliness,omitempty"`
	Service               string `json:"service,omitempty"`
	Comfort               string `json:"comfort,omitempty"`
	Condition             string `json:"condition,omitempty"`
	Location              string `json:"location,omitempty"`
	Neighborhood          string `json:"neighborhood,omitempty"`
	Quality               string `json:"quality,omitempty"`
	Value                 string `json:"value,omitempty"`
	Amenities             string `json:"amenities,omitempty"`
	RecommendationPercent string `json:"recommendation_percent,omitempty"`
}

// Location struct
type Location struct {
	Coordinates           *Coordinates `json:"coordinates,omitempty"`
	ObfuscatedCoordinates *Coordinates `json:"obfuscated_coordinates,omitempty"`
	ObfuscationRequired   bool         `json:"obfuscation_required,omitempty"`
}

type Coordinates struct {
	Latitude  float64 `json:"latitude,omitempty"`
	Longitude float64 `json:"longitude,omitempty"`
}

// Category struct
type Category struct {
	ID   string `json:"id,omitempty"`
	Name string `json:"name,omitempty"`
}

// BusinessModel struct
type BusinessModel struct {
	ExpediaCollect  bool `json:"expedia_collect,omitempty"`
	PropertyCollect bool `json:"property_collect,omitempty"`
}

// CheckIn struct
type CheckIn struct {
	TwentyFourHour      string  `json:"24_hour,omitempty"`
	BeginTime           string  `json:"begin_time,omitempty"`
	EndTime             string  `json:"end_time,omitempty"`
	Instructions        string  `json:"instructions,omitempty"`
	SpecialInstructions string  `json:"special_instructions,omitempty"`
	MinAge              float64 `json:"min_age,omitempty"`
}

// Checkout struct
type Checkout struct {
	Time string `json:"time,omitempty"`
}

// Fees struct
type Fees struct {
	Mandatory       string `json:"mandatory,omitempty"`
	Optional        string `json:"optional,omitempty"`
	TravelerService string `json:"traveler_service,omitempty"`
}

// Policies struct
type Policies struct {
	KnowBeforeYouGo string `json:"know_before_you_go,omitempty"`
}

// Attributes struct
type Attributes struct {
	Pets    []*Attribute `json:"pets,omitempty"`
	General []*Attribute `json:"general,omitempty"`
}

type Attribute struct {
	ID    string `json:"id,omitempty"`
	Name  string `json:"name,omitempty"`
	Value string `json:"value,omitempty"`
}

// Amenity struct
type Amenity struct {
	ID         string   `json:"id,omitempty"`
	Name       string   `json:"name,omitempty"`
	Categories []string `json:"categories,omitempty"`
	Value      string   `json:"value,omitempty"`
	GroupName  string   `json:"group_name"`
}

// Image struct
type Image struct {
	HeroImage bool             `json:"hero_image,omitempty"`
	Category  float64          `json:"category,omitempty"`
	Links     map[string]*Link `json:"links,omitempty"`
	Caption   string           `json:"caption,omitempty"`
}

type Link struct {
	Method  string `json:"method,omitempty"`
	Href    string `json:"href,omitempty"`
	Expires string `json:"expires,omitempty"` // optional field
}

// OnsitePayments struct
type OnsitePayments struct {
	Currency string         `json:"currency,omitempty"`
	Types    []*PaymentType `json:"types,omitempty"`
}

type PaymentType struct {
	ID   string `json:"id,omitempty"`
	Name string `json:"name,omitempty"`
}

// ContentRate struct
type ContentRate struct {
	ID        string     `json:"id,omitempty"`
	Amenities []*Amenity `json:"amenities,omitempty"`
}

// Dates struct
type Dates struct {
	Added   time.Time `json:"added,omitempty"`
	Updated time.Time `json:"updated,omitempty"`
}

// Descriptions struct
type Descriptions struct {
	Amenities         string `json:"amenities,omitempty"`
	Dining            string `json:"dining,omitempty"`
	Renovations       string `json:"renovations,omitempty"`
	NationalRatings   string `json:"national_ratings,omitempty"`
	BusinessAmenities string `json:"business_amenities,omitempty"`
	Rooms             string `json:"rooms,omitempty"`
	Attractions       string `json:"attractions,omitempty"`
	Location          string `json:"location,omitempty"`
	Headline          string `json:"headline,omitempty"`
}

// Statistic struct
type Statistic struct {
	ID    string `json:"id,omitempty"`
	Name  string `json:"name,omitempty"`
	Value string `json:"value,omitempty"`
}

// Chain and Brand struct
type Chain struct {
	ID   string `json:"id,omitempty"`
	Name string `json:"name,omitempty"`
}

type Brand struct {
	ID   string `json:"id,omitempty"`
	Name string `json:"name,omitempty"`
}

// Language struct
type Language struct {
	ID   string `json:"id,omitempty"`
	Name string `json:"name,omitempty"`
}

type GetHotelDetailReq struct {
	// Language string `json:"language" validate:"required"`
	Language string `json:"language"`
	HotelID  string `json:"hotel_id"`
	OfficeID string `json:"-"`
}

type GetHotelDetailRes struct {
	Hotel *Hotel `json:"hotel,omitempty"`
}

type VacationRentalDetails struct {
	RegistryNumber     string                   `json:"registry_number"`
	PrivateHost        bool                     `json:"private_host"`
	PropertyManager    *PropertyManager         `json:"property_manager"`
	RentalAgreement    *RentalAgreement         `json:"rental_agreement"`
	HouseRules         []string                 `json:"house_rules"`
	EnhancedHouseRules map[string]*EnhancedRule `json:"enhanced_house_rules"`
	Amenities          map[string]*Amenity      `json:"amenities"`
	VrboSrpID          string                   `json:"vrbo_srp_id"`
	ListingID          string                   `json:"listing_id"`
	ListingNumber      string                   `json:"listing_number"`
	ListingSource      string                   `json:"listing_source"`
	ListingUnit        string                   `json:"listing_unit"`
	IPMName            string                   `json:"ipm_name"`
	UnitConfigurations map[string][]*UnitConfig `json:"unit_configurations"`
}

func (h *Hotel) GetProviderIDs() map[enum.HotelProvider]string {
	if h == nil {
		return nil
	}

	return h.ProviderIds
}

type FindHotelsWithFilterReq struct {
	HotelKeyStage1 string
	HotelKeyStage2 string
	Lat            float64
	Long           float64
	Radius         float64
}
