package domain

import (
	commonDomain "gitlab.deepgate.io/apps/common/domain"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
)

type HubBookReq struct {
	OfficeID            string         `json:"-"`
	HubOfficeID         string         `json:"hub_office_id" validate:"required"`
	SessionID           string         `json:"session_id" validate:"required"`
	Holder              *HubHolderInfo `json:"holder" validate:"required"`
	EndUserIPAddress    string         `json:"-"`
	EndUserBrowserAgent string         `json:"-"`
	AgentID             string         `json:"-"`
	OrderCode           string         `json:"-"` // Send to provider
	TestingHashKey      string         `json:"testing_hash_key"`
}

type HubBookRes struct {
	OrderCode       string             `json:"order_code"`
	BookingStatus   enum.BookingStatus `json:"booking_status"`
	PendingDeadline int64              `json:"pending_deadline,omitempty"`
	ErrorR<PERSON>
}

type ListBookingRequest struct {
	Pagination       *commonDomain.Pagination
	BookingStatus    enum.BookingStatus
	OrderStatus      enum.HubOrderStatus
	OrderKey         string
	OrderVal         int
	PendingStartAtLt int64
}

type HubBookOldProviderReq struct {
	OrderCode string
	ID        string
}

type ListOrderFilter struct {
	Pagination      *commonDomain.Pagination
	BookingStatuses []enum.BookingStatus
	From            int64
	To              int64
	OfficeID        *string
	OrderCode       string
	NotInStatuses   []enum.BookingStatus
}
