package domain

import (
	"fmt"
	"strings"
	"time"

	"gitlab.deepgate.io/apps/common/log"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
)

type HubRateDiscount struct {
	Amount   float64 `json:"amount"`
	Currency string  `json:"currency,omitempty"`
	Code     string  `json:"code,omitempty"`
	Name     string  `json:"name"`
}

type HubPromotion struct {
	Code        string `json:"code,omitempty"`
	Name        string `json:"name"`
	Remark      string `json:"remark,omitempty"`
	OfferType   string `json:"offer_type,omitempty"`
	Frequency   string `json:"frequency,omitempty"`
	PersonCount string `json:"person_count"`
}

type PayAtHotel struct {
	Amount      float64 `json:"amount"`
	Description string  `json:"description"`
	Currency    string  `json:"currency"`
}

type HubOccupancyRate struct {
	OccupancyType    string             `json:"occupancy_type"`
	RoomQuantity     uint               `json:"room_quantity"`
	TotalNightlyRate []*HubRate         `json:"total_nightly_rate"`
	RateTaxes        []*HubRateTax      `json:"rate_taxes"`
	PayAtHotel       []*PayAtHotel      `json:"pay_at_hotel"`
	Surcharges       float64            `json:"surcharges"`
	RateDiscounts    []*HubRateDiscount `json:"rate_discounts"`
	MarketingFee     float64            `json:"-"`
}

type HubRate struct {
	RateAmount float64 `json:"-"`
	RateBasic  float64 `json:"rate_basic"`
	TaxAmount  float64 `json:"tax_amount"`
	Currency   string  `json:"currency"`
}

type HubRateSupplement struct {
	Amount    float64 `json:"amount"`
	Code      string  `json:"code"`
	From      string  `json:"from"`
	Name      string  `json:"name"`
	Nights    uint    `json:"nights"`
	PaxNumber uint    `json:"pax_number"`
	To        string  `json:"to"`
}

type HubRateTax struct {
	Amount      *float64     `json:"amount"`
	Currency    string       `json:"currency"`
	Included    bool         `json:"-"`
	Type        enum.TaxType `json:"type"`
	Description string       `json:"description"`
}

type HubAmenity struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

type BedOption struct {
	OptionID        string              `json:"option_id"`
	Name            string              `json:"name"`
	Quantity        uint                `json:"quantity"`
	PriceCheckToken string              `json:"-"`
	BedConfigs      []*BedConfiguration `json:"-"`
}

type HubRateData struct {
	RateID                 string                       `json:"rate_id,omitempty"`
	ProviderRateID         string                       `json:"-"`
	Available              string                       `json:"available,omitempty"`
	CancelPolicies         []*HubCancelPolicy           `json:"cancellation_policies"`
	Commission             float64                      `json:"-"`
	CommissionPCT          float64                      `json:"-"`
	CommissionVAT          float64                      `json:"-"`
	Promotions             []*HubPromotion              `json:"promotions"`
	OccupancyRate          []*HubOccupancyRate          `json:"occupancy_rate"`
	Currency               string                       `json:"currency"`
	TotalRateAmount        float64                      `json:"-"`
	TotalRateBasic         float64                      `json:"-"`
	TotalTaxAmount         float64                      `json:"-"`
	PayNow                 float64                      `json:"pay_now"`
	TotalPayAtHotel        float64                      `json:"total_pay_at_hotel"`
	PayAtHotelCurrency     string                       `json:"pay_at_hotel_currency"`
	BedOptions             []*BedOption                 `json:"bed_options"`
	Offer                  []*HubPromotion              `json:"offer,omitempty"`
	Amenities              []*HubAmenity                `json:"amenities,omitempty"`
	Refundable             bool                         `json:"refundable"`
	SaleScenario           []string                     `json:"sale_scenario"`
	HasBreakfast           bool                         `json:"has_breakfast"`
	HasExtraBed            bool                         `json:"has_extra_bed"`
	NonSmoking             bool                         `json:"non_smoking"`
	NonrefundableDateRange []*HubNonrefundableDateRange `json:"nonrefundable_date_ranges"`
	HiddenFeeAmount        float64                      `json:"-"`
	DiscountAmount         float64                      `json:"-"`
	AppliedHiddenFee       interface{}                  `json:"-"`
	AppliedDiscount        interface{}                  `json:"-"`
	FakeNonRefund          bool                         `json:"-"`
	IsSoldOut              bool                         `json:"-"`
}

func (r *HubRateData) GetAmenityNames() []string {
	if r == nil || len(r.Amenities) == 0 {
		return nil
	}

	amenityNames := make([]string, 0, len(r.Amenities))
	for _, amenity := range r.Amenities {
		if amenity != nil {
			amenityNames = append(amenityNames, amenity.Name)
		}
	}
	return amenityNames
}

type HubNonrefundableDateRange struct {
	StartDate string `json:"start_date"`
	EndDate   string `json:"end_date"`
}

type HubCancelPolicyPenaltyInfo struct {
	Amount         float64 `json:"amount,omitempty"`
	Percent        float64 `json:"percent,omitempty"`
	NumberOfNights string  `json:"number_of_nights,omitempty"`
}

type HubCancelPolicy struct {
	Key                    string                       `json:"-"`
	StartDate              string                       `json:"start_date"` // HubDateFormat 2006-01-02T15:04:05Z07:00
	EndDate                string                       `json:"end_date"`
	StartDt                time.Time                    `json:"-"`
	EndDt                  time.Time                    `json:"-"`
	Currency               string                       `json:"currency,omitempty"`
	NonrefundableDateRange []*HubNonrefundableDateRange `json:"nonrefundable_date_ranges,omitempty"`
	PenaltyInfo            HubCancelPolicyPenaltyInfo   `json:"-"`
	PartialRefund          bool                         `json:"-"`
	Refundable             bool                         `json:"-"`
	RawPenaltyAmount       float64                      `json:"-"`
	PenaltyAmount          float64                      `json:"penalty_amount"`
}

func (s *HubCancelPolicy) GenKey() string {
	if s == nil {
		return ""
	}

	return fmt.Sprintf("%s-%s", s.StartDate, s.EndDate)
}

type HubRoom struct {
	RoomID            string             `json:"room_id"`
	ProviderRoomID    string             `json:"-"`
	OldProviderRoomID string             `json:"-"`
	Name              string             `json:"name"`
	NameEn            string             `json:"name_en"`
	RateData          []*HubRateData     `json:"rate_data,omitempty"`
	OccupancyIndex    uint               `json:"occupancy_index,omitempty"`
	OccupancyType     string             `json:"occupancy_type,omitempty"`
	Provider          enum.HotelProvider `json:"-"`
	Descriptions      *RoomDescriptions  `json:"descriptions,omitempty"`
	Amenities         []*Amenity         `json:"amenities,omitempty"`
	Images            []*Image           `json:"images,omitempty"`
	BedGroups         []*BedGroup        `json:"bed_groups,omitempty"`
	Area              *Area              `json:"area,omitempty"`
	Occupancy         *ContentOccupancy  `json:"occupancy,omitempty"`
	Views             []*View            `json:"views,omitempty"`
}

type HubHotelReview struct {
	Rate        float64 `json:"rate"`
	ReviewCount int64   `json:"review_count"`
}

type HubHotel struct {
	Address         *Address   `json:"address"`
	HotelID         string     `json:"hotel_id"`
	ProviderHotelID string     `json:"-"`
	OldProviderID   string     `json:"-"`
	Rating          float64    `json:"rating"`
	HotelType       string     `json:"-"`
	Name            string     `json:"name"`
	VAT             bool       `json:"vat"`
	ListRooms       []*HubRoom `json:"rooms"`
	Currency        string     `json:"-"`
	CheckInTime     string     `json:"-"`
	CheckOutTime    string     `json:"-"`
}

type HubSearchStay struct {
	CheckIn   string `json:"check_in" validate:"required"`
	CheckOut  string `json:"check_out" validate:"required"`
	DayCount  int    `json:"-"`
	RoomCount int    `json:"-"`
}

func (s *HubSearchStay) CountDays() error {
	if s == nil {
		return nil
	}

	checkIn, err := time.Parse(constants.HubSearchDateFormat, s.CheckIn)
	if err != nil {
		log.Error("parse time err", log.Any("err", err), log.String("check-in", s.CheckIn))
		return ErrCheckInDateInvalid
	}

	checkOut, err := time.Parse(constants.HubSearchDateFormat, s.CheckOut)
	if err != nil {
		log.Error("parse time err", log.Any("err", err), log.String("check-out", s.CheckOut))
		return ErrCheckOutDateInvalid
	}

	s.DayCount = int(checkOut.Sub(checkIn).Hours() / 24)

	return nil
}

type HubSearchChildren struct {
	Number uint   `json:"number"`
	Age    []uint `json:"age"`
}

type HubSearchOccupancy struct {
	Adults         uint               `json:"adults"`
	Children       *HubSearchChildren `json:"children"`
	Rooms          uint               `json:"rooms"`
	OccupancyIndex uint               `json:"occupancy_index" validate:"required"`
}

type HubSearchGeneralOccupancy struct {
	Priority bool               `json:"priority"`
	Adults   uint               `json:"adults"`
	Children *HubSearchChildren `json:"children"`
	Rooms    uint               `json:"rooms"`
}

func (s *HubSearchOccupancy) GenOccupancyType() string {
	if s == nil {
		return ""
	}

	var childrenStr string

	if s.Children != nil {
		for _, age := range s.Children.Age {
			childrenStr += fmt.Sprintf("%d,", age)
		}
	}

	occupancyType := fmt.Sprintf("%dadt", s.Adults)
	if childrenStr != "" {
		occupancyType += fmt.Sprintf(",%dchd-%s", s.Children.Number, strings.TrimSuffix(childrenStr, ","))
	}

	return occupancyType
}

// AreCancelPoliciesDifferent compares two slices of HubCancelPolicy.
// and returns true if they differ in StartDate, EndDate, or PenaltyInfo.Amount.
func AreCancelPoliciesDifferent(policies1, policies2 []*HubCancelPolicy) bool {
	// Different lengths means they're different
	if len(policies1) != len(policies2) {
		return true
	}

	// Create maps to easily compare policies
	policyMap1 := make(map[string]*HubCancelPolicy)
	policyMap2 := make(map[string]*HubCancelPolicy)

	// Populate the first map
	for _, policy := range policies1 {
		if policy != nil {
			key := policy.GenKey()
			policyMap1[key] = policy
		}
	}

	// Populate the second map
	for _, policy := range policies2 {
		if policy != nil {
			key := policy.GenKey()
			policyMap2[key] = policy
		}
	}

	// Check if all policies from the first slice exist in the second
	// and have the same PenaltyInfo.Amount
	for key, policy1 := range policyMap1 {
		policy2, exists := policyMap2[key]
		if !exists || policy1.PenaltyInfo.Amount != policy2.PenaltyInfo.Amount {
			return true
		}
	}

	// Check if all policies from the second slice exist in the first
	for key := range policyMap2 {
		if _, exists := policyMap1[key]; !exists {
			return true
		}
	}

	return false
}
