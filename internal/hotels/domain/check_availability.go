package domain

import (
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"sort"
	"strings"

	"gitlab.deepgate.io/apps/common/logger"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
)

type CacheCheckAvailabilityRequest struct {
	Stay                HubSearchStay
	Occupancies         []*HubSearchOccupancy
	CountryCode         string
	Language            string
	Currency            string
	EndUserIPAddress    string
	EndUserBrowserAgent string
	DefaultLanguage     string
	DetailRate          bool
}

// Provider
type HotelSearchResult struct {
	ID                 string
	CreatedAt          int64
	SearchKey          string
	Provider           enum.HotelProvider
	ExpireAt           int64
	Hotels             []*HubHotel
	HotelSearchRequest *CacheCheckAvailabilityRequest
	SaleScenario       string
	SaleEnv            string
}

// Hub in/out
type HubCheckAvailabilityReq struct {
	PartnershipID       string                `json:"partnership_id"`
	Stay                HubSearchStay         `json:"stay" validate:"required"`
	Occupancies         []*HubSearchOccupancy `json:"occupancies"`
	ListHotels          []string              `json:"list_hotels"`
	HotelID             string                `json:"hotel_id"`
	DetailRate          bool                  `json:"detail_rate"`
	CountryCode         string                `json:"country_code"`
	Language            string                `json:"-"`
	Currency            string                `json:"currency"`
	SaleChannel         enum.SalesChannel     `json:"sale_channel" validate:"required"`
	TravelPurpose       enum.TravelPurpose    `json:"travel_purpose"`
	SalesEnv            enum.SalesEnvironment `json:"sales_environment"  validate:"required"`
	OfficeID            string                `json:"-"`
	EndUserIPAddress    string                `json:"-"`
	EndUserBrowserAgent string                `json:"-"`
	EnableProviders     []enum.HotelProvider  `json:"-"`
	DefaultLanguage     string                `json:"-"`
	RatePlanCount       int                   `json:"-"`
	MatchKey            string                `json:"match_key"`
	Nationality         string                `json:"-"`
	GroupBy             []enum.RateDataGroup  `json:"group_by"` // group by filter
}

func (d *HubCheckAvailabilityReq) ToHotelSearchRequest() *CacheCheckAvailabilityRequest {
	if d == nil {
		return nil
	}

	return &CacheCheckAvailabilityRequest{
		Stay:                d.Stay,
		Occupancies:         d.Occupancies,
		CountryCode:         d.CountryCode,
		Language:            d.Language,
		Currency:            d.Currency,
		EndUserIPAddress:    d.EndUserIPAddress,
		EndUserBrowserAgent: d.EndUserBrowserAgent,
		DefaultLanguage:     d.DefaultLanguage,
		DetailRate:          d.DetailRate,
	}
}

type HubCheckAvailabilityRes struct {
	IsSuccess            bool        `json:"is_success"`
	ErrorCode            string      `json:"error_code"`
	ErrorMsg             string      `json:"error_msg"`
	SearchKey            string      `json:"search_key"`
	Hotels               []*HubHotel `json:"hotels"`
	AvailableHotelReturn uint        `json:"available_hotel_return"`
}

func (d HubCheckAvailabilityReq) CountRooms() int {
	roomCount := 0

	for _, occupancy := range d.Occupancies {
		roomCount += int(occupancy.Rooms)
	}

	d.Stay.RoomCount = roomCount

	return roomCount
}

func (d HubCheckAvailabilityReq) FormatRateDataGroups() []enum.RateDataGroup {
	seen := make(map[enum.RateDataGroup]struct{})
	unique := make([]enum.RateDataGroup, 0, len(d.GroupBy))

	for _, item := range d.GroupBy {
		if _, exists := seen[item]; !exists {
			seen[item] = struct{}{}

			unique = append(unique, item)
		}
	}

	sort.Slice(unique, func(i, j int) bool {
		return enum.RateDataGroupOrder[unique[i]] < enum.RateDataGroupOrder[unique[j]]
	})

	return unique
}

func (h *HubCheckAvailabilityReq) GenSearchKey() (string, error) {
	sort.Strings(h.ListHotels)
	occupancyHash := make([]string, 0, len(h.Occupancies))

	for _, item := range h.Occupancies {
		if item == nil {
			logger.Error("HubSearchHotelReq GenSearchKey occupancy nil")
			return "", ErrInvalidValue
		}
		childrenStr := ""

		if item.Children != nil {
			for _, age := range item.Children.Age {
				childrenStr += fmt.Sprintf("%d|", age)
			}
		}

		occupancyHash = append(occupancyHash, fmt.Sprintf("%d|%s|%d", item.Adults, childrenStr, item.Rooms))
	}
	hash := md5.Sum([]byte(fmt.Sprintf(
		"%s__%s__%s__%s__%s",
		h.Stay.CheckIn,
		h.Stay.CheckOut,
		strings.Join(occupancyHash, "_"),
		strings.Join(h.ListHotels, ","),
		h.SalesEnv,
	)))

	hashString := hex.EncodeToString(hash[:])

	return hashString, nil
}

type SearchHotelRequestData struct {
	Hotels           []*Hotel
	ProviderHotelIds []string
	Rooms            map[string][]*Room
	SearchReq        *HubCheckAvailabilityReq
}
