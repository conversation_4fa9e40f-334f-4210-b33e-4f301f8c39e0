package domain

import (
	"math"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
)

type LoginReq struct {
	OfficeID      string `json:"office_id"`
	APIKey        string `json:"api_key"`
	PartnershipID string `json:"partnership_id"`
}

type PartnerUser struct {
	ID            string
	CreatedAt     int64
	UpdatedAt     int64
	CreatedBy     string
	UpdatedBy     string
	Email         string
	Name          string
	PartnershipID string
	PartnerShopID string
	WebhookCfg    WebhookCfg
}

type PartnerDCPs struct {
}

type PartnerShopInfo struct {
	ID          string
	Name        string
	OwnerID     string
	PartnerType int64
	Code        string
	OfficeID    string
	WebhookCfg  *WebhookCfg
	DCPs        *PartnerDCPs
	Hotel       *PartnerHotelInfo
}

type ProviderConfig struct {
	Provider enum.HotelProvider `json:"provider" bson:"provider"`
	Enable   bool               `json:"enable" bson:"enable"`
}

type PartnerHotelInfo struct {
	Enable          bool                       `json:"enable" bson:"enabled"`
	Webhook         *WebhookURLCfg             `json:"webhook" bson:"webhook"`
	ProviderConfigs []*ProviderConfig          `json:"providers" bson:"providers"`
	DefaultLanguage string                     `json:"default_language" bson:"default_language"`
	PriceConfig     *HotelPriceConditionConfig `json:"price_config" bson:"price_config"`
}

type HotelPriceConditionConfig struct {
	Percent       float64
	Amount        float64
	ProviderOrder []int64
	Active        bool
}

func (d *HotelPriceConditionConfig) SelectHotelProviderByOrder(hotels []*HotelSummary) *HotelSummary {
	if d == nil {
		return nil
	}

	for _, p := range d.ProviderOrder {
		enumP := enum.HotelProvider(p)
		if enumP == enum.HotelProviderNone {
			continue
		}

		for _, hotel := range hotels {
			if hotel.Provider == enumP {
				return hotel
			}
		}

	}

	return hotels[0]
}

func (d *HotelPriceConditionConfig) IsInPriceGap(p1, p2 float64) bool {
	if d == nil {
		return false
	}

	if d.Percent != 0 {
		return (1 - p1/p2) <= d.Percent
	}

	if d.Amount != 0 {
		return math.Abs(p1-p2) <= d.Amount
	}

	return p1 == p2
}

type WebhookURLCfg struct {
	Transaction string
}

type WebhookCfg struct {
	WebhookURLCfg WebhookURLCfg
	WebhookKey    string
}
