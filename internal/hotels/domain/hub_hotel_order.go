package domain

import (
	"fmt"
	"time"

	"gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
	statictimezone "gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/static_timezone"
)

type HolderDetail struct {
	OccupancyIndex uint   `json:"occupancy_index" validate:"required"`
	GivenName      string `json:"given_name"  validate:"required"`
	Surname        string `json:"surname"  validate:"required"`
	Email          string `json:"email" validate:"required"`
	SpecialRequest string `json:"special_request"`
}

type HubHolderInfo struct {
	PhoneNumber  string          `json:"phone_number" validate:"required"`
	PhoneCode    string          `json:"phone_code"  validate:"required"`
	HolderDetail []*HolderDetail `json:"holder_details" validate:"required,min=1,dive"`
}

type RoomBedOption struct {
	OptionID   string              `json:"option_id"`
	Name       string              `json:"name"`
	Quantity   uint                `json:"quantity"`
	BedConfigs []*BedConfiguration `json:"-"`
}

type HubOrderRoomItem struct {
	RoomID            string             `json:"room_id"`
	ProviderRoomID    string             `json:"-"`
	OldProviderRoomID string             `json:"-"`
	Name              string             `json:"name"`
	RateData          []*HubRateData     `json:"rate_data,omitempty"`
	Provider          enum.HotelProvider `json:"-"`
	ConfirmationID    string             `json:"confirmation_id,omitempty"`
	OccupancyIndex    uint               `json:"occupancy_index,omitempty"`
	OccupancyType     string             `json:"occupancy_type"`
	GivenName         string             `json:"given_name,omitempty"`
	Surname           string             `json:"surname,omitempty"`
	Email             string             `json:"email,omitempty"`
	SpecialRequest    string             `json:"special_request,omitempty"`
	BedOption         *RoomBedOption     `json:"bed_option"`
	BookingStatus     enum.BookingStatus `json:"booking_status"`
}

type HubOrderHotelItem struct {
	HotelID         string              `json:"hotel_id"`
	ProviderHotelID string              `json:"-"`
	OldProviderID   string              `json:"-"`
	Name            string              `json:"name"`
	VAT             bool                `json:"vat"`
	Address         *Address            `json:"-"`
	ListRooms       []*HubOrderRoomItem `json:"rooms"`
	Currency        string              `json:"-"`
	Rating          float64             `json:"-"`
	Star            int                 `json:"-"`
	CheckInTime     string              `json:"-"`
	CheckOutTime    string              `json:"-"`
}

func (d *HubOrderHotelItem) GetPivotRoom() *HubOrderRoomItem {
	if len(d.ListRooms) == 0 {
		return nil
	}

	for _, room := range d.ListRooms {
		if room.OccupancyIndex == 0 {
			return room
		}
	}

	return d.ListRooms[0]
}

type FareDataIssuing struct {
	Amount       float64 `json:"-"`
	AmountString string  `json:"-"`
	Currency     string  `json:"-"`
	Type         string  `json:"-"`
}

type OrderHiddenFee struct {
	HiddenFeeConfig   *HiddenServiceFee `json:"-"`
	TotalChargeAmount float64           `json:"-"`
}
type Fee struct {
	HiddenFee *OrderHiddenFee `json:"-"`
}

type RefundData struct {
	ProviderRefundAmount float64 `json:"provider_refund_amount"`
	RefundAmount         float64 `json:"refund_amount"`
	PenaltyAmount        float64 `json:"penalty_amount"`
	Currency             string  `json:"currency"`
}

type HubHotelOrder struct {
	ID                     string                         `json:"-"`
	CreatedAt              int64                          `json:"-"`
	SessionID              string                         `json:"session_id"`
	NewSessionID           string                         `json:"-"`
	OrderCode              string                         `json:"order_code"`
	ReservationCode        string                         `json:"-"` //  itineraryID // Provider booking code(ratehawk)
	ProviderBookingStatus  string                         `json:"-"`
	OfficeID               string                         `json:"-"`
	AgentCode              string                         `json:"-"`
	HubOfficeID            string                         `json:"-"`
	Hotel                  *HubOrderHotelItem             `json:"hotel,omitempty"`
	RateData               *HubRateData                   `json:"-"`
	OriginalRateDataCf     *HubRateData                   `json:"-"`
	ExchangedRateDataCfRaw *HubRateData                   `json:"-"`
	Fee                    *Fee                           `json:"-"`
	ExchangedRateDataCf    *HubRateData                   `json:"exchanged_rate_data_cf"`
	ExchangeRateApply      *CurrencyExchange              `json:"-"`
	RequestHolder          *HubHolderInfo                 `json:"-"`
	Provider               enum.HotelProvider             `json:"-"`
	OldProvider            enum.HotelProvider             `json:"-"`
	LastConfirmDate        int64                          `json:"last_confirm_date"`
	BookingRequest         *HubPriceCheckReq              `json:"-"`
	OrderPaymentID         string                         `json:"-"`
	LastTransactionID      string                         `json:"-"`
	CustomerIP             string                         `json:"-"`
	HubOrderStatus         enum.HubOrderStatus            `json:"status"`
	BookingStatus          enum.BookingStatus             `json:"booking_status,omitempty"`
	PendingDeadline        int64                          `json:"pending_deadline,omitempty"`
	CancelDeadline         int64                          `json:"cancel_deadline,omitempty"`
	ManualIssuing          bool                           `json:"-"`
	HotelSearchRequest     *CacheCheckAvailabilityRequest `json:"-"`
	FareDataIssuing        *FareDataIssuing               `json:"-"`
	PendingStartAt         int64                          `json:"-"`
	RefundData             *RefundData                    `json:"-"`
	ExchangedRefundData    *RefundData                    `json:"-"`
	Refunded               bool                           `json:"refunded"`
	CancelingStartAt       int64                          `json:"-"`
	SkipScanCheckCancel    bool                           `json:"-"`
	SearchKey              string                         `json:"-"`
	CancelReason           string                         `json:"-"`
	TABookingID            string                         `json:"-"`
	ShouldUpdate           bool                           `json:"-"` // internal use only
}

func (bk *HubHotelOrder) CanBook() error {
	if bk == nil {
		return errors.ErrNotFound
	}

	if bk.BookingStatus == enum.BookingStatusCancel {
		return ErrBookingCanceled
	}

	if bk.BookingStatus == enum.BookingStatusCanceling {
		return ErrBookingCanceling
	}

	if bk.BookingStatus == enum.BookingStatusPending {
		return ErrBookingPending
	}

	if bk.BookingStatus == enum.BookingStatusSuccess {
		return ErrBookingCompleted
	}

	if bk.HubOrderStatus == enum.HubOrderStatusCancelled {
		return ErrBookingFailed
	}

	return nil
}

func GetMinPol(policies []*HubCancelPolicy) (*HubCancelPolicy, error) {
	if len(policies) == 0 {
		return nil, errors.ErrInvalidInput
	}

	minPol := policies[0]

	for _, policy := range policies {
		if policy.StartDate == "" || policy.EndDate == "" {
			continue
		}

		tStartDate, err := time.Parse(constants.HubDateFormat, policy.StartDate)
		if err != nil {
			return nil, err
		}

		tEndDate, err := time.Parse(constants.HubDateFormat, policy.EndDate)
		if err != nil {
			return nil, err
		}

		policy.StartDt = tStartDate
		policy.EndDt = tEndDate

		if policy.StartDt.Before(minPol.StartDt) {
			minPol = policy
		}
	}

	return minPol, nil
}

func GetMaxPol(policies []*HubCancelPolicy) (*HubCancelPolicy, error) {
	if len(policies) == 0 {
		return nil, errors.ErrInvalidInput
	}

	maxPol := policies[0]

	for _, policy := range policies {
		if policy.StartDate == "" || policy.EndDate == "" {
			continue
		}

		tStartDate, err := time.Parse(constants.HubDateFormat, policy.StartDate)
		if err != nil {
			return nil, err
		}

		tEndDate, err := time.Parse(constants.HubDateFormat, policy.EndDate)
		if err != nil {
			return nil, err
		}

		policy.StartDt = tStartDate
		policy.EndDt = tEndDate

		if policy.StartDt.After(maxPol.StartDt) {
			maxPol = policy
		}
	}

	return maxPol, nil
}

func CalcCancelableByPolicies(policies []*HubCancelPolicy, checkIn time.Time, stayCount int64, payNow float64) bool {
	if len(policies) == 0 {
		return time.Now().Before(checkIn)
	}

	maxPol, err := GetMaxPol(policies)
	if err != nil {
		log.Error("CalcFullRefundable  getMinPol err", log.Any("err", err))
		return false
	}

	if maxPol == nil {
		return false
	}

	if !maxPol.Refundable {
		return time.Now().Before(maxPol.StartDt)
	}

	return time.Now().Before(maxPol.EndDt)
}

func parseCheckInTime(checkInStr string, tz *time.Location) (time.Time, error) {
	parsedTime, err := time.ParseInLocation(time.RFC3339, checkInStr, tz)
	if err != nil {
		layout := "2006-01-02T15:04:05"
		parsedTime, err = time.ParseInLocation(layout, checkInStr, tz)
		if err != nil {
			layout := "2006-01-02T15:04"
			parsedTime, err = time.ParseInLocation(layout, checkInStr, tz)
			if err != nil {
				log.Error("ConvertToVietnameseDateFormat err", log.Any("err", err), log.Any("dateStr", checkInStr))
				return time.Now(), err
			}
		}
	}

	return parsedTime, nil
}

func (bk *HubHotelOrder) CanCancel() error {
	if bk == nil {
		return errors.ErrNotFound
	}

	if bk.BookingStatus == enum.BookingStatusCancel {
		return ErrBookingCanceled
	}

	if bk.BookingStatus == enum.BookingStatusCanceling {
		return ErrBookingCanceling
	}

	if bk.BookingStatus != enum.BookingStatusSuccess {
		return ErrBookingCannotCancel
	}

	if bk.HotelSearchRequest != nil {
		tz := time.UTC
		var err error

		checkInStr := bk.HotelSearchRequest.Stay.CheckIn

		if bk.Hotel != nil && bk.Hotel.Address != nil {
			tz, err = time.LoadLocation(statictimezone.GetTimezone(bk.Hotel.Address.City, bk.Hotel.Address.CountryCode))
			if err != nil {
				log.Error("LoadLocation err", log.Any("err", err), log.String("tz", statictimezone.GetTimezone(bk.Hotel.Address.City, bk.Hotel.Address.CountryCode)))
				return ErrCheckInDateInvalid
			}

			if bk.Hotel.CheckInTime != "" {
				checkInStr = fmt.Sprintf("%sT%s", checkInStr, bk.Hotel.CheckInTime)
			} else {
				checkInStr = fmt.Sprintf("%sT00:00", checkInStr)
			}
		} else {
			checkInStr = fmt.Sprintf("%sT00:00", checkInStr)
		}

		checkIn, err := parseCheckInTime(checkInStr, tz)
		if err != nil {
			log.Error("parse time err", log.Any("err", err), log.String("check-in", bk.HotelSearchRequest.Stay.CheckIn))
			return ErrCheckInDateInvalid
		}

		if checkIn.Before(time.Now().UTC()) {
			fmt.Println("checkIn time", checkIn)
			return ErrBookingCannotCancel
		}

		if bk.OriginalRateDataCf == nil {
			return ErrBookingCannotCancel
		}

		if !bk.OriginalRateDataCf.Refundable || bk.OriginalRateDataCf.FakeNonRefund {
			return ErrBookingCannotCancel
		}

		cancelableByPolicies := CalcCancelableByPolicies(bk.OriginalRateDataCf.CancelPolicies, checkIn, int64(bk.HotelSearchRequest.Stay.DayCount), bk.OriginalRateDataCf.PayNow)
		if !cancelableByPolicies {
			return ErrBookingCannotCancel
		}
	}

	return nil
}

type OccupancyUpdateInfo struct {
	OccupancyIndex int
	ConfirmationID string
}

type UpdateOrderStatusRequest struct {
	OrderCode      string             `json:"order_code"`
	Status         enum.BookingStatus `json:"status"`
	OccupancyInfos []*OccupancyUpdateInfo
}

type HubRetrieveConfirmationID struct {
	ProviderRoomID string
	ConfirmationID string
	OccupancyType  string
	GivenName      string
	Surname        string
	BedOptionID    string
	BookStatus     enum.BookingStatus
	Used           bool
}

type HubOrderUpdate struct {
	AgentCode            string
	LastTransactionID    *string
	LastTransactionIDOld *string
	NewOfficeID          *string
	OfficeIDOld          *string
}
