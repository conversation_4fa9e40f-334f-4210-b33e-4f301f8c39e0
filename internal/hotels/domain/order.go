package domain

import (
	"fmt"

	"gitlab.deepgate.io/apps/common/constants"
)

type HotelOrder struct {
	OrderCode    string
	UserID       string
	Service      string
	Currency     *OrderCurrencyInfo
	Note         string
	ExpiredTime  int64
	CustomerInfo *CustomerInfo
	TotalPrice   float64
}

type UserInfo struct {
	Id          string
	Name        string
	Email       string
	PhoneNumber string
	PhoneCode   string
}

type OrderCurrencyInfo struct {
	Id   string
	Name string
	Code string
	Icon string
}

type CustomerInfo struct {
	Name        string
	FirstName   string
	LastName    string
	Email       string
	PhoneCode   string
	PhoneNumber string
}

func ToDomainCustomerInfoFromHotel(info *HubHolderInfo) *CustomerInfo {
	if info == nil || len(info.HolderDetail) == 0 {
		return nil
	}

	return &CustomerInfo{
		Name:        fmt.Sprintf("%s %s", info.HolderDetail[0].Surname, info.HolderDetail[0].GivenName),
		LastName:    info.HolderDetail[0].Surname,
		FirstName:   info.HolderDetail[0].GivenName,
		Email:       info.HolderDetail[0].Email,
		PhoneCode:   info.PhoneCode,
		PhoneNumber: info.PhoneNumber,
	}
}

func ToDomainOrderPayment(booking *HubHotelOrder) *HotelOrder {
	return &HotelOrder{
		OrderCode: booking.OrderCode,
		Service:   constants.SkyHubHotelServiceName,
		Currency: &OrderCurrencyInfo{
			Name: booking.ExchangedRateDataCf.Currency,
			Code: booking.ExchangedRateDataCf.Currency,
		},
		CustomerInfo: ToDomainCustomerInfoFromHotel(booking.RequestHolder),
		TotalPrice:   booking.ExchangedRateDataCf.PayNow,
	}
}
