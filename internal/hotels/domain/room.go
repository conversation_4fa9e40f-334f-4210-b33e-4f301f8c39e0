package domain

import "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"

type RoomGroupExt struct {
	Class    int `json:"class"`
	Quality  int `json:"quality"`
	Sex      int `json:"sex"`
	Bathroom int `json:"bathroom"`
	Bedding  int `json:"bedding"`
	Family   int `json:"family"`
	Capacity int `json:"capacity"`
	Club     int `json:"club"`
	Bedrooms int `json:"bedrooms"`
	Balcony  int `json:"balcony"`
	Floor    int `json:"floor"`
	View     int `json:"view"`
}
type Room struct {
	Base         `json:",inline"`
	Language     string                        `json:"language,omitempty"`
	RoomID       string                        `json:"room_id,omitempty"`
	HotelRef     string                        `json:"-"`
	Name         string                        `json:"name,omitempty"`
	Descriptions *RoomDescriptions             `json:"descriptions,omitempty"`
	Amenities    []*Amenity                    `json:"amenities,omitempty"`
	Images       []*Image                      `json:"images,omitempty"`
	BedGroups    []*BedGroup                   `json:"bed_groups,omitempty"`
	Area         *Area                         `json:"area,omitempty"`
	Occupancy    *ContentOccupancy             `json:"occupancy,omitempty"`
	Views        []*View                       `json:"views,omitempty"`
	ProviderIDs  map[enum.HotelProvider]string `json:"provider_ids,omitempty"`
	RoomGroupExt RoomGroupExt                  `json:"rg_ex"`
	Version      string                        `json:"version"`
	HotelID      string                        `json:"hotel_id,omitempty"`
}

type RoomDescriptions struct {
	Overview string `json:"overview,omitempty"`
}

type BedGroup struct {
	ID            string              `json:"id,omitempty"`
	Description   string              `json:"description,omitempty"`
	Configuration []*BedConfiguration `json:"configuration,omitempty"`
}

type BedConfiguration struct {
	Quantity float64 `json:"quantity,omitempty"`
	Size     string  `json:"size,omitempty"`
	Type     string  `json:"type,omitempty"`
}

type Area struct {
	SquareMeters float64 `json:"square_meters,omitempty"`
	SquareFeet   float64 `json:"square_feet,omitempty"`
}

type ContentOccupancy struct {
	MaxAllowed    *MaxAllowed    `json:"max_allowed,omitempty"`
	AgeCategories []*AgeCategory `json:"age_categories,omitempty"`
}

type MaxAllowed struct {
	Total    float64 `json:"total,omitempty"`
	Children float64 `json:"children,omitempty"`
	Adults   float64 `json:"adults,omitempty"`
}

type AgeCategory struct {
	Name       string  `json:"name,omitempty"`
	MinimumAge float64 `json:"minimum_age,omitempty"`
}

func (r *Room) GetProviderIDs() map[enum.HotelProvider]string {
	if r == nil {
		return nil
	}

	return r.ProviderIDs
}
