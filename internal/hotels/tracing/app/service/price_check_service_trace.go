// DO NOT EDIT: code generated from 'gen-tracing.go'
package service_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/dida"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/expedia_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/rate_hawk_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/redis"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/ta_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/tourmind_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"go.opentelemetry.io/otel/codes"
)

type priceCheckServiceTrace struct {
	service.PriceCheckService
}

func NewPriceCheckService(arg1 *config.Schema,
	arg2 repositories.ProviderSearchHotelsRepository,
	arg3 service.SessionService,
	arg4 repositories.OrderRepository,
	arg5 service.CurrencyExchangeService,
	arg6 service.HiddenFeeService,
	arg7 expedia_client.ExpediaAdapter,
	arg8 tourmind_client.TourmindAdapter,
	arg9 rate_hawk_client.RateHawkAdapter,
	arg10 repositories.TAMappingsRepository,
	arg11 ta_client.TAAdapter,
	arg12 dida.Adapter,
	arg13 redis.PriceCheckRepository,

) service.PriceCheckService {
	return &priceCheckServiceTrace{
		PriceCheckService: service.NewPriceCheckService(
			arg1,
			arg2,
			arg3,
			arg4,
			arg5,
			arg6,
			arg7,
			arg8,
			arg9,
			arg10,
			arg11,
			arg12,
			arg13,
		),
	}
}

func (s *priceCheckServiceTrace) PriceCheck(ctx context.Context, arg2 *domain.HubPriceCheckReq) (*domain.HubPriceCheckRes, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "priceCheckService.PriceCheck")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.PriceCheckService.PriceCheck(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.PriceCheckService.PriceCheck failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}
