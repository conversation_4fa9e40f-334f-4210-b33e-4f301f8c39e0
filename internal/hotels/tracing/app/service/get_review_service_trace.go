// DO NOT EDIT: code generated from 'gen-tracing.go'
package service_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/expedia_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"go.opentelemetry.io/otel/codes"
)

type getReviewServiceTrace struct {
	service.GetReviewService
}

func NewGetReviewService(arg1 *config.Schema,
	arg2 expedia_client.ExpediaAdapter,

) service.GetReviewService {
	return &getReviewServiceTrace{
		GetReviewService: service.NewGetReviewService(
			arg1,
			arg2,
		),
	}
}

func (s *getReviewServiceTrace) GetHotelReview(ctx context.Context, arg2 *domain.HubHotelReviewReq) ([]*domain.HubHotelReviewItem, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "getReviewService.GetHotelReview")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.GetReviewService.GetHotelReview(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.GetReviewService.GetHotelReview failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}
