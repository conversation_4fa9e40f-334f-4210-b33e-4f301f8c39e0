// DO NOT EDIT: code generated from 'gen-tracing.go'
package service_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/price"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/redis"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"go.opentelemetry.io/otel/codes"
)

type hiddenFeeServiceTrace struct {
	service.HiddenFeeService
}

func NewHiddenFeeService(arg1 price.PriceClient,
	arg2 redis.HiddenServiceFeeRepository,

) service.HiddenFeeService {
	return &hiddenFeeServiceTrace{
		HiddenFeeService: service.NewHiddenFeeService(
			arg1,
			arg2,
		),
	}
}

func (s *hiddenFeeServiceTrace) CalculateHotelDetailPrices(ctx context.Context, arg2 *domain.CalculateHotelDetailPricesReq) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "hiddenFeeService.CalculateHotelDetailPrices")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.HiddenFeeService.CalculateHotelDetailPrices(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.HiddenFeeService.CalculateHotelDetailPrices failed")
			span.RecordError(err)
		}
	}
	return res1

}

func (s *hiddenFeeServiceTrace) CalculateHotelRateDataPrices(ctx context.Context, arg2 *domain.CalculateHotelRateDataPricesReq) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "hiddenFeeService.CalculateHotelRateDataPrices")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.HiddenFeeService.CalculateHotelRateDataPrices(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.HiddenFeeService.CalculateHotelRateDataPrices failed")
			span.RecordError(err)
		}
	}
	return res1

}

func (s *hiddenFeeServiceTrace) CalculateHotelSearchPrices(ctx context.Context, arg2 *domain.CalculateHotelSearchPricesReq) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "hiddenFeeService.CalculateHotelSearchPrices")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.HiddenFeeService.CalculateHotelSearchPrices(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.HiddenFeeService.CalculateHotelSearchPrices failed")
			span.RecordError(err)
		}
	}
	return res1

}
