// DO NOT EDIT: code generated from 'gen-tracing.go'
package service_tracing

import (
	"context"
	"reflect"

	commonDomain "gitlab.deepgate.io/apps/common/domain"
	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/dida"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/expedia_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/rate_hawk_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/redis"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"go.opentelemetry.io/otel/codes"
)

type searchHotelServiceTrace struct {
	service.SearchHotelService
}

func NewSearchHotelService(arg1 *config.Schema,
	arg2 repositories.SearchHotelCachesRepository,
	arg3 redis.SearchHotelsRepository,
	arg4 repositories.HotelRepository,
	arg5 repositories.RoomRepository,
	arg6 repositories.RegionRepository,
	arg7 expedia_client.ExpediaAdapter,
	arg8 rate_hawk_client.RateHawkAdapter,
	arg9 dida.Adapter,
	arg10 repositories.HotelAreaCacheRepository,
	arg11 service.CurrencyExchangeService,

) service.SearchHotelService {
	return &searchHotelServiceTrace{
		SearchHotelService: service.NewSearchHotelService(
			arg1,
			arg2,
			arg3,
			arg4,
			arg5,
			arg6,
			arg7,
			arg8,
			arg9,
			arg10,
			arg11,
		),
	}
}

func (s *searchHotelServiceTrace) ApplyFilterRequest(ctx context.Context, arg2 []*domain.HotelSummary, arg3 *domain.SearchHotelFilterRequest) []*domain.HotelSummary {
	ctx, span := tracing.StartSpanFromContext(ctx, "searchHotelService.ApplyFilterRequest")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1 := s.SearchHotelService.ApplyFilterRequest(ctx, arg2, arg3)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.SearchHotelService.ApplyFilterRequest failed")
			span.RecordError(err)
		}
	}
	return res1

}

func (s *searchHotelServiceTrace) ApplyPaging(ctx context.Context, arg2 []*domain.HotelSummary, arg3 *commonDomain.Pagination) []*domain.HotelSummary {
	ctx, span := tracing.StartSpanFromContext(ctx, "searchHotelService.ApplyPaging")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1 := s.SearchHotelService.ApplyPaging(ctx, arg2, arg3)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.SearchHotelService.ApplyPaging failed")
			span.RecordError(err)
		}
	}
	return res1

}

func (s *searchHotelServiceTrace) ApplySortRequest(ctx context.Context, arg2 *domain.HubSearchHotelRequest, arg3 []*domain.HotelSummary) []*domain.HotelSummary {
	ctx, span := tracing.StartSpanFromContext(ctx, "searchHotelService.ApplySortRequest")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1 := s.SearchHotelService.ApplySortRequest(ctx, arg2, arg3)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.SearchHotelService.ApplySortRequest failed")
			span.RecordError(err)
		}
	}
	return res1

}

func (s *searchHotelServiceTrace) CalcFilterRequest(ctx context.Context, arg2 []*domain.HotelSummary) *domain.SearchHotelFilterResponse {
	ctx, span := tracing.StartSpanFromContext(ctx, "searchHotelService.CalcFilterRequest")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.SearchHotelService.CalcFilterRequest(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.SearchHotelService.CalcFilterRequest failed")
			span.RecordError(err)
		}
	}
	return res1

}

func (s *searchHotelServiceTrace) Search(ctx context.Context, arg2 *domain.HubSearchHotelRequest, arg3 []enum.HotelProvider) ([]*domain.HotelSummary, bool, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "searchHotelService.Search")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2, res3 := s.SearchHotelService.Search(ctx, arg2, arg3)
	if res3 != nil {
		err, ok := reflect.ValueOf(res3).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.SearchHotelService.Search failed")
			span.RecordError(err)
		}
	}
	return res1, res2, res3

}

func (s *searchHotelServiceTrace) SearchList(ctx context.Context, arg2 *domain.HubSearchHotelRequest, arg3 []enum.HotelProvider) ([]*domain.HotelSummary, bool, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "searchHotelService.SearchList")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2, res3 := s.SearchHotelService.SearchList(ctx, arg2, arg3)
	if res3 != nil {
		err, ok := reflect.ValueOf(res3).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.SearchHotelService.SearchList failed")
			span.RecordError(err)
		}
	}
	return res1, res2, res3

}
