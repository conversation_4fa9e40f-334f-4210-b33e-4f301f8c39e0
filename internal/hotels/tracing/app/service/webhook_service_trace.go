// DO NOT EDIT: code generated from 'gen-tracing.go'
package service_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/btm_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"go.opentelemetry.io/otel/codes"
)

type webhookServiceTrace struct {
	service.WebhookService
}

func NewWebhookService(arg1 *config.Schema,
	arg2 repositories.OrderRepository,
	arg3 btm_client.BTMAdapter,

) service.WebhookService {
	return &webhookServiceTrace{
		WebhookService: service.NewWebhookService(
			arg1,
			arg2,
			arg3,
		),
	}
}

func (s *webhookServiceTrace) BTMWebhookNotification(ctx context.Context, arg2 *domain.ExpediaWebhookNotificationRequest) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "webhookService.BTMWebhookNotification")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.WebhookService.BTMWebhookNotification(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.WebhookService.BTMWebhookNotification failed")
			span.RecordError(err)
		}
	}
	return res1

}
