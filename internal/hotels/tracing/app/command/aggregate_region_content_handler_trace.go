// DO NOT EDIT: code generated from 'gen-tracing.go'
package command_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/command"
	"go.opentelemetry.io/otel/codes"
)

type aggregateRegionContentHandlerTrace struct {
	command.AggregateRegionContentHandler
}

func NewAggregateRegionContentHandler(arg1 repositories.RegionRepository,

) command.AggregateRegionContentHandler {
	return &aggregateRegionContentHandlerTrace{
		AggregateRegionContentHandler: command.NewAggregateRegionContentHandler(
			arg1,
		),
	}
}

func (s *aggregateRegionContentHandlerTrace) Handle(ctx context.Context, arg2 *command.AggregateRegionContentReq) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "aggregateRegionContentHandler.Handle")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.AggregateRegionContentHandler.Handle(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.AggregateRegionContentHandler.Handle failed")
			span.RecordError(err)
		}
	}
	return res1

}
