// DO NOT EDIT: code generated from 'gen-tracing.go'
package command_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/expedia_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/command"
	"go.opentelemetry.io/otel/codes"
)

type aggregateRegionsContentHandlerTrace struct {
	command.AggregateRegionsContentHandler
}

func NewAggregateRegionsContentHandler(arg1 repositories.RegionRepository,
	arg2 repositories.PolygonRepository,
	arg3 expedia_client.ExpediaAdapter,

) command.AggregateRegionsContentHandler {
	return &aggregateRegionsContentHandlerTrace{
		AggregateRegionsContentHandler: command.NewAggregateRegionsContentHandler(
			arg1,
			arg2,
			arg3,
		),
	}
}

func (s *aggregateRegionsContentHandlerTrace) Handle(ctx context.Context, arg2 *command.AggregateRegionsContentReq) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "aggregateRegionsContentHandler.Handle")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.AggregateRegionsContentHandler.Handle(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.AggregateRegionsContentHandler.Handle failed")
			span.RecordError(err)
		}
	}
	return res1

}
