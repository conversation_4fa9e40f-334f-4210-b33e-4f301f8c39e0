// DO NOT EDIT: code generated from 'gen-tracing.go'
package command_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mq"
	taclient "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/ta_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/command"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"go.opentelemetry.io/otel/codes"
)

type tAWebhookHandlerTrace struct {
	command.TAWebhookHandler
}

func NewTAWebhookHandler(arg1 repositories.OrderRepository,
	arg2 taclient.TAAdapter,
	arg3 mq.Adapter,

) command.TAWebhookHandler {
	return &tAWebhookHandlerTrace{
		TAWebhookHandler: command.NewTAWebhookHandler(
			arg1,
			arg2,
			arg3,
		),
	}
}

func (s *tAWebhookHandlerTrace) Handle(ctx context.Context, arg2 *domain.TAWebhookMsg) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "tAWebhookHandler.Handle")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.TAWebhookHandler.Handle(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.TAWebhookHandler.Handle failed")
			span.RecordError(err)
		}
	}
	return res1

}
