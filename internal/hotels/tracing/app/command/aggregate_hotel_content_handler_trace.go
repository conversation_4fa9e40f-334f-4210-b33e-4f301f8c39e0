// DO NOT EDIT: code generated from 'gen-tracing.go'
package command_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/command"
	"go.opentelemetry.io/otel/codes"
)

type aggregateHotelContentHandlerTrace struct {
	command.AggregateHotelContentHandler
}

func NewAggregateHotelContentHandler(arg1 repositories.HotelRepository,
	arg2 repositories.RoomRepository,

) command.AggregateHotelContentHandler {
	return &aggregateHotelContentHandlerTrace{
		AggregateHotelContentHandler: command.NewAggregateHotelContentHandler(
			arg1,
			arg2,
		),
	}
}

func (s *aggregateHotelContentHandlerTrace) Handle(ctx context.Context, arg2 *command.AggregateHotelContentReq) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "aggregateHotelContentHandler.Handle")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.AggregateHotelContentHandler.Handle(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.AggregateHotelContentHandler.Handle failed")
			span.RecordError(err)
		}
	}
	return res1

}
