// DO NOT EDIT: code generated from 'gen-tracing.go'
package command_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/partner"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/wallet"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/command"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"go.opentelemetry.io/otel/codes"
)

type aggregateHotelTransactionHandlerTrace struct {
	command.AggregateHotelTransactionHandler
}

func NewAggregateHotelTransactionHandler(arg1 *config.Schema,
	arg2 repositories.OrderRepository,
	arg3 wallet.WalletClient,
	arg4 partner.PartnerClient,

) command.AggregateHotelTransactionHandler {
	return &aggregateHotelTransactionHandlerTrace{
		AggregateHotelTransactionHandler: command.NewAggregateHotelTransactionHandler(
			arg1,
			arg2,
			arg3,
			arg4,
		),
	}
}

func (s *aggregateHotelTransactionHandlerTrace) Handle(ctx context.Context, arg2 string, arg3 string) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "aggregateHotelTransactionHandler.Handle")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1 := s.AggregateHotelTransactionHandler.Handle(ctx, arg2, arg3)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.AggregateHotelTransactionHandler.Handle failed")
			span.RecordError(err)
		}
	}
	return res1

}
