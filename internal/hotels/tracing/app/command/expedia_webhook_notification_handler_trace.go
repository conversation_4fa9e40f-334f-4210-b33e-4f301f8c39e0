// DO NOT EDIT: code generated from 'gen-tracing.go'
package command_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/notification"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/command"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"go.opentelemetry.io/otel/codes"
)

type expediaWebhookNotificationHandlerTrace struct {
	command.ExpediaWebhookNotificationHandler
}

func NewExpediaWebhookNotificationHandler(arg1 *config.Schema,
	arg2 repositories.OrderRepository,
	arg3 repositories.ExpediaWebhookNotificationRepository,
	arg4 service.WebhookService,
	arg5 notification.NotificationServiceClient,

) command.ExpediaWebhookNotificationHandler {
	return &expediaWebhookNotificationHandlerTrace{
		ExpediaWebhookNotificationHandler: command.NewExpediaWebhookNotificationHandler(
			arg1,
			arg2,
			arg3,
			arg4,
			arg5,
		),
	}
}

func (s *expediaWebhookNotificationHandlerTrace) Handle(ctx context.Context, arg2 *domain.ExpediaWebhookNotificationRequest) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "expediaWebhookNotificationHandler.Handle")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.ExpediaWebhookNotificationHandler.Handle(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.ExpediaWebhookNotificationHandler.Handle failed")
			span.RecordError(err)
		}
	}
	return res1

}
