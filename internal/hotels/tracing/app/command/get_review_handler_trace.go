// DO NOT EDIT: code generated from 'gen-tracing.go'
package command_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/command"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"go.opentelemetry.io/otel/codes"
)

type getReviewHandlerTrace struct {
	command.GetReviewHandler
}

func NewGetReviewHandler(arg1 *config.Schema,
	arg2 service.GetReviewService,

) command.GetReviewHandler {
	return &getReviewHandlerTrace{
		GetReviewHandler: command.NewGetReviewHandler(
			arg1,
			arg2,
		),
	}
}

func (s *getReviewHandlerTrace) Handle(ctx context.Context, arg2 *domain.HubHotelReviewReq) (*domain.HubHotelReviewRes, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "getReviewHandler.Handle")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.GetReviewHandler.Handle(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.GetReviewHandler.Handle failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}
