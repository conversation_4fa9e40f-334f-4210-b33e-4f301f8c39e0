// DO NOT EDIT: code generated from 'gen-tracing.go'
package command_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/command"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"go.opentelemetry.io/otel/codes"
)

type cancelBookingHandlerTrace struct {
	command.CancelBookingHandler
}

func NewCancelBookingHandler(arg1 service.CancelBookingService,

) command.CancelBookingHandler {
	return &cancelBookingHandlerTrace{
		CancelBookingHandler: command.NewCancelBookingHandler(
			arg1,
		),
	}
}

func (s *cancelBookingHandlerTrace) Handle(ctx context.Context, arg2 *domain.HubCancelBookingReq) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "cancelBookingHandler.Handle")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.CancelBookingHandler.Handle(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.CancelBookingHandler.Handle failed")
			span.RecordError(err)
		}
	}
	return res1

}
