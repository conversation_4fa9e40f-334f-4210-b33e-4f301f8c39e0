// DO NOT EDIT: code generated from 'gen-tracing.go'
package command_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/command"
	"go.opentelemetry.io/otel/codes"
)

type deleteConfigHiddenFeeHandlerTrace struct {
	command.DeleteConfigHiddenFeeHandler
}

func NewDeleteConfigHiddenFeeHandler(arg1 repositories.HiddenServiceFeeRepository,

) command.DeleteConfigHiddenFeeHandler {
	return &deleteConfigHiddenFeeHandlerTrace{
		DeleteConfigHiddenFeeHandler: command.NewDeleteConfigHiddenFeeHandler(
			arg1,
		),
	}
}

func (s *deleteConfigHiddenFeeHandlerTrace) Handle(ctx context.Context, arg2 []string, arg3 string) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "deleteConfigHiddenFeeHandler.Handle")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1 := s.DeleteConfigHiddenFeeHandler.Handle(ctx, arg2, arg3)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.DeleteConfigHiddenFeeHandler.Handle failed")
			span.RecordError(err)
		}
	}
	return res1

}
