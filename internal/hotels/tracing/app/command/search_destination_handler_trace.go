// DO NOT EDIT: code generated from 'gen-tracing.go'
package command_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/es/es_repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/command"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"go.opentelemetry.io/otel/codes"
)

type searchDestinationHandlerTrace struct {
	command.SearchDestinationHandler
}

func NewSearchDestinationHandler(arg1 *config.Schema,
	arg2 es_repositories.PlaceElasticRepository,

) command.SearchDestinationHandler {
	return &searchDestinationHandlerTrace{
		SearchDestinationHandler: command.NewSearchDestinationHandler(
			arg1,
			arg2,
		),
	}
}

func (s *searchDestinationHandlerTrace) Handle(ctx context.Context, arg2 *domain.SearchDestinationReq) (*domain.SearchDestinationRes, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "searchDestinationHandler.Handle")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.SearchDestinationHandler.Handle(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.SearchDestinationHandler.Handle failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}
