// DO NOT EDIT: code generated from 'gen-tracing.go'
package command_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/command"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"go.opentelemetry.io/otel/codes"
)

type createConfigHiddenFeeHandlerTrace struct {
	command.CreateConfigHiddenFeeHandler
}

func NewCreateConfigHiddenFeeHandler(arg1 repositories.HiddenServiceFeeRepository,

) command.CreateConfigHiddenFeeHandler {
	return &createConfigHiddenFeeHandlerTrace{
		CreateConfigHiddenFeeHandler: command.NewCreateConfigHiddenFeeHandler(
			arg1,
		),
	}
}

func (s *createConfigHiddenFeeHandlerTrace) Handle(ctx context.Context, arg2 *domain.UpsertConfigHiddenFeeRequest) (string, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "createConfigHiddenFeeHandler.Handle")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.CreateConfigHiddenFeeHandler.Handle(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.CreateConfigHiddenFeeHandler.Handle failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}
