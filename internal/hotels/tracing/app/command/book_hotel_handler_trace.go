// DO NOT EDIT: code generated from 'gen-tracing.go'
package command_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/order"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/partner"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/payment"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/redis"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/wallet"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/webhook"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/command"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"go.opentelemetry.io/otel/codes"
)

type bookHotelHandlerTrace struct {
	command.BookHotelHandler
}

func NewBookHotelHandler(arg1 *config.Schema,
	arg2 repositories.OrderRepository,
	arg3 repositories.SessionRepository,
	arg4 service.BookingService,
	arg5 redis.BookingRepository,
	arg6 order.OrderServiceClient,
	arg7 payment.PaymentClient,
	arg8 wallet.WalletClient,
	arg9 partner.PartnerClient,
	arg10 webhook.WebhookAdapter,

) command.BookHotelHandler {
	return &bookHotelHandlerTrace{
		BookHotelHandler: command.NewBookHotelHandler(
			arg1,
			arg2,
			arg3,
			arg4,
			arg5,
			arg6,
			arg7,
			arg8,
			arg9,
			arg10,
		),
	}
}

func (s *bookHotelHandlerTrace) Handle(ctx context.Context, arg2 *domain.HubBookReq, arg3 *domain.WebhookCfg) (*domain.HubBookRes, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "bookHotelHandler.Handle")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2 := s.BookHotelHandler.Handle(ctx, arg2, arg3)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.BookHotelHandler.Handle failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}
