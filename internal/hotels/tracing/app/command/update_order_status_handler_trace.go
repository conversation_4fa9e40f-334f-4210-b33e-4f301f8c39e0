// DO NOT EDIT: code generated from 'gen-tracing.go'
package command_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/command"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"go.opentelemetry.io/otel/codes"
)

type updateOrderStatusHandlerTrace struct {
	command.UpdateOrderStatusHandler
}

func NewUpdateOrderStatusHandler(arg1 repositories.OrderRepository,
	arg2 service.BookingService,

) command.UpdateOrderStatusHandler {
	return &updateOrderStatusHandlerTrace{
		UpdateOrderStatusHandler: command.NewUpdateOrderStatusHandler(
			arg1,
			arg2,
		),
	}
}

func (s *updateOrderStatusHandlerTrace) Handle(ctx context.Context, arg2 *domain.UpdateOrderStatusRequest) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "updateOrderStatusHandler.Handle")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.UpdateOrderStatusHandler.Handle(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.UpdateOrderStatusHandler.Handle failed")
			span.RecordError(err)
		}
	}
	return res1

}
