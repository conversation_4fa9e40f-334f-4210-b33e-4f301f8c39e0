// DO NOT EDIT: code generated from 'gen-tracing.go'
package command_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/tourmind_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/command"
	"go.opentelemetry.io/otel/codes"
)

type aggregateTourmindContentHandlerTrace struct {
	command.AggregateTourmindContentHandler
}

func NewAggregateTourmindContentHandler(arg1 repositories.TourmindJobsQueueRepository,
	arg2 tourmind_client.TourmindAdapter,
	arg3 repositories.HotelRepository,
	arg4 repositories.TourmindRegionsRepository,

) command.AggregateTourmindContentHandler {
	return &aggregateTourmindContentHandlerTrace{
		AggregateTourmindContentHandler: command.NewAggregateTourmindContentHandler(
			arg1,
			arg2,
			arg3,
			arg4,
		),
	}
}

func (s *aggregateTourmindContentHandlerTrace) Handle(ctx context.Context, arg2 *command.AggregateTourmindContentReq) (*command.AggregateTourmindContentRes, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "aggregateTourmindContentHandler.Handle")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.AggregateTourmindContentHandler.Handle(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.AggregateTourmindContentHandler.Handle failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}
