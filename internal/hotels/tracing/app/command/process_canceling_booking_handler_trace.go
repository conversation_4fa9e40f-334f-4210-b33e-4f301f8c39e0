// DO NOT EDIT: code generated from 'gen-tracing.go'
package command_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/notification"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/command"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"go.opentelemetry.io/otel/codes"
)

type processCancelingBookingHandlerTrace struct {
	command.ProcessCancelingBookingHandler
}

func NewProcessCancelingBookingHandler(arg1 *config.Schema,
	arg2 service.CancelBookingService,
	arg3 repositories.OrderRepository,
	arg4 notification.NotificationServiceClient,

) command.ProcessCancelingBookingHandler {
	return &processCancelingBookingHandlerTrace{
		ProcessCancelingBookingHandler: command.NewProcessCancelingBookingHandler(
			arg1,
			arg2,
			arg3,
			arg4,
		),
	}
}

func (s *processCancelingBookingHandlerTrace) Handle(ctx context.Context) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "processCancelingBookingHandler.Handle")
	defer span.End()
	tracing.AddAttributes(span, ctx)

	res1 := s.ProcessCancelingBookingHandler.Handle(ctx)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.ProcessCancelingBookingHandler.Handle failed")
			span.RecordError(err)
		}
	}
	return res1

}
