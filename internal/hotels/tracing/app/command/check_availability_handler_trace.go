// DO NOT EDIT: code generated from 'gen-tracing.go'
package command_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/command"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"go.opentelemetry.io/otel/codes"
)

type checkAvailabilityHandlerTrace struct {
	command.CheckAvailabilityHandler
}

func NewCheckAvailabilityHandler(arg1 *config.Schema,
	arg2 service.CheckAvailabilityService,
	arg3 service.CurrencyExchangeService,
	arg4 service.HiddenFeeService,

) command.CheckAvailabilityHandler {
	return &checkAvailabilityHandlerTrace{
		CheckAvailabilityHandler: command.NewCheckAvailabilityHandler(
			arg1,
			arg2,
			arg3,
			arg4,
		),
	}
}

func (s *checkAvailabilityHandlerTrace) Handle(ctx context.Context, arg2 *domain.HubCheckAvailabilityReq) (*domain.HubCheckAvailabilityRes, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "checkAvailabilityHandler.Handle")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.CheckAvailabilityHandler.Handle(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.CheckAvailabilityHandler.Handle failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}
