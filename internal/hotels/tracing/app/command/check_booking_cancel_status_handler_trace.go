// DO NOT EDIT: code generated from 'gen-tracing.go'
package command_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/command"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"go.opentelemetry.io/otel/codes"
)

type checkBookingCancelStatusHandlerTrace struct {
	command.CheckBookingCancelStatusHandler
}

func NewCheckBookingCancelStatusHandler(arg1 service.CancelBookingService,
	arg2 repositories.OrderRepository,

) command.CheckBookingCancelStatusHandler {
	return &checkBookingCancelStatusHandlerTrace{
		CheckBookingCancelStatusHandler: command.NewCheckBookingCancelStatusHandler(
			arg1,
			arg2,
		),
	}
}

func (s *checkBookingCancelStatusHandlerTrace) Handle(ctx context.Context, arg2 *domain.HubCancelBookingReq) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "checkBookingCancelStatusHandler.Handle")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.CheckBookingCancelStatusHandler.Handle(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.CheckBookingCancelStatusHandler.Handle failed")
			span.RecordError(err)
		}
	}
	return res1

}
