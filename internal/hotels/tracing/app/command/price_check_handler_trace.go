// DO NOT EDIT: code generated from 'gen-tracing.go'
package command_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/redis"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/command"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"go.opentelemetry.io/otel/codes"
)

type priceCheckHandlerTrace struct {
	command.PriceCheckHandler
}

func NewPriceCheckHandler(arg1 service.PriceCheckService,
	arg2 service.SessionService,
	arg3 service.CheckAvailabilityService,
	arg4 redis.SearchHotelsRepository,

) command.PriceCheckHandler {
	return &priceCheckHandlerTrace{
		PriceCheckHandler: command.NewPriceCheckHandler(
			arg1,
			arg2,
			arg3,
			arg4,
		),
	}
}

func (s *priceCheckHandlerTrace) Handle(ctx context.Context, arg2 *domain.HubPriceCheckReq) (*domain.HubPriceCheckRes, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "priceCheckHandler.Handle")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.PriceCheckHandler.Handle(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.PriceCheckHandler.Handle failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}
