// DO NOT EDIT: code generated from 'gen-tracing.go'
package command_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/command"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"go.opentelemetry.io/otel/codes"
)

type bookOldProviderHandlerTrace struct {
	command.BookOldProviderHandler
}

func NewBookOldProviderHandler(arg1 service.PriceCheckService,
	arg2 service.BookingService,
	arg3 repositories.OrderRepository,

) command.BookOldProviderHandler {
	return &bookOldProviderHandlerTrace{
		BookOldProviderHandler: command.NewBookOldProviderHandler(
			arg1,
			arg2,
			arg3,
		),
	}
}

func (s *bookOldProviderHandlerTrace) Handle(ctx context.Context, arg2 *domain.HubBookOldProviderReq) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "bookOldProviderHandler.Handle")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.BookOldProviderHandler.Handle(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.BookOldProviderHandler.Handle failed")
			span.RecordError(err)
		}
	}
	return res1

}
