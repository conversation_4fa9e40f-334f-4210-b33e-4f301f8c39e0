// DO NOT EDIT: code generated from 'gen-tracing.go'
package command_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/command"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service"
	"go.opentelemetry.io/otel/codes"
)

type processPendingBookingHandlerTrace struct {
	command.ProcessPendingBookingHandler
}

func NewProcessPendingBookingHandler(arg1 repositories.OrderRepository,
	arg2 repositories.SessionRepository,
	arg3 service.BookingService,

) command.ProcessPendingBookingHandler {
	return &processPendingBookingHandlerTrace{
		ProcessPendingBookingHandler: command.NewProcessPendingBookingHandler(
			arg1,
			arg2,
			arg3,
		),
	}
}

func (s *processPendingBookingHandlerTrace) Handle(ctx context.Context, arg2 *command.ProcessPendingBookingRequest) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "processPendingBookingHandler.Handle")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.ProcessPendingBookingHandler.Handle(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.ProcessPendingBookingHandler.Handle failed")
			span.RecordError(err)
		}
	}
	return res1

}
