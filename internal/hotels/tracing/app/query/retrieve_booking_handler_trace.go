// DO NOT EDIT: code generated from 'gen-tracing.go'
package query_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/query"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"go.opentelemetry.io/otel/codes"
)

type retrieveBookingHandlerTrace struct {
	query.RetrieveBookingHandler
}

func NewRetrieveBookingHandler(arg1 *config.Schema,
	arg2 repositories.OrderRepository,

) query.RetrieveBookingHandler {
	return &retrieveBookingHandlerTrace{
		RetrieveBookingHandler: query.NewRetrieveBookingHandler(
			arg1,
			arg2,
		),
	}
}

func (s *retrieveBookingHandlerTrace) Handle(ctx context.Context, arg2 *domain.HubRetrieveBookingReq) (*domain.HubRetrieveBookingRes, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "retrieveBookingHandler.Handle")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.RetrieveBookingHandler.Handle(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.RetrieveBookingHandler.Handle failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}
