// DO NOT EDIT: code generated from 'gen-tracing.go'
package query_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/partner"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/query"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"go.opentelemetry.io/otel/codes"
)

type listOrderByFilterHandlerTrace struct {
	query.ListOrderByFilterHandler
}

func NewListOrderByFilterHandler(arg1 repositories.OrderRepository,
	arg2 partner.PartnerClient,

) query.ListOrderByFilterHandler {
	return &listOrderByFilterHandlerTrace{
		ListOrderByFilterHandler: query.NewListOrderByFilterHandler(
			arg1,
			arg2,
		),
	}
}

func (s *listOrderByFilterHandlerTrace) Handle(ctx context.Context, arg2 *domain.ListOrderFilter) ([]*domain.HubHotelOrder, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "listOrderByFilterHandler.Handle")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.ListOrderByFilterHandler.Handle(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.ListOrderByFilterHandler.Handle failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}
