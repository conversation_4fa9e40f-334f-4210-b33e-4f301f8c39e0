// DO NOT EDIT: code generated from 'gen-tracing.go'
package query_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/query"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"go.opentelemetry.io/otel/codes"
)

type getHotelDetailHandlerTrace struct {
	query.GetHotelDetailHandler
}

func NewGetHotelDetailHandler(arg1 *config.Schema,
	arg2 repositories.HotelRepository,
	arg3 repositories.RoomRepository,

) query.GetHotelDetailHandler {
	return &getHotelDetailHandlerTrace{
		GetHotelDetailHandler: query.NewGetHotelDetailHandler(
			arg1,
			arg2,
			arg3,
		),
	}
}

func (s *getHotelDetailHandlerTrace) Handle(ctx context.Context, arg2 *domain.GetHotelDetailReq) (*domain.GetHotelDetailRes, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "getHotelDetailHandler.Handle")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.GetHotelDetailHandler.Handle(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.GetHotelDetailHandler.Handle failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}
