// DO NOT EDIT: code generated from 'gen-tracing.go'
package query_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/query"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"go.opentelemetry.io/otel/codes"
)

type listConfigHiddenFeeHandlerTrace struct {
	query.ListConfigHiddenFeeHandler
}

func NewListConfigHiddenFeeHandler(arg1 repositories.HiddenServiceFeeRepository,

) query.ListConfigHiddenFeeHandler {
	return &listConfigHiddenFeeHandlerTrace{
		ListConfigHiddenFeeHandler: query.NewListConfigHiddenFeeHandler(
			arg1,
		),
	}
}

func (s *listConfigHiddenFeeHandlerTrace) Handle(ctx context.Context, arg2 *domain.ListHiddenServiceFeeReq) ([]*domain.HiddenServiceFee, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "listConfigHiddenFeeHandler.Handle")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.ListConfigHiddenFeeHandler.Handle(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.ListConfigHiddenFeeHandler.Handle failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}
