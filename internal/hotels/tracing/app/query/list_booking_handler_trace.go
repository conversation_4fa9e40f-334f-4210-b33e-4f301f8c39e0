// DO NOT EDIT: code generated from 'gen-tracing.go'
package query_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/query"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"go.opentelemetry.io/otel/codes"
)

type listBookingHandlerTrace struct {
	query.ListBookingHandler
}

func NewListBookingHandler(arg1 repositories.OrderRepository,

) query.ListBookingHandler {
	return &listBookingHandlerTrace{
		ListBookingHandler: query.NewListBookingHandler(
			arg1,
		),
	}
}

func (s *listBookingHandlerTrace) Handle(ctx context.Context, arg2 *domain.ListBookingRequest) ([]*domain.HubHotelOrder, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "listBookingHandler.Handle")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.ListBookingHandler.Handle(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.ListBookingHandler.Handle failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}
