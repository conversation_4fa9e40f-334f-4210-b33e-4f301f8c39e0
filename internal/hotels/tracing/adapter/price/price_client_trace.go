// DO NOT EDIT: code generated from 'gen-tracing.go'
package price_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/price"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"go.opentelemetry.io/otel/codes"
)

type priceClientTrace struct {
	price.PriceClient
}

func NewPriceClient(arg1 *config.Schema,

) price.PriceClient {
	return &priceClientTrace{
		PriceClient: price.NewPriceClient(
			arg1,
		),
	}
}

func (s *priceClientTrace) CalculateHotelDetailPrices(ctx context.Context, arg2 *domain.CalculateHotelDetailPricesReq) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "priceClient.CalculateHotelDetailPrices")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.PriceClient.CalculateHotelDetailPrices(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.PriceClient.CalculateHotelDetailPrices failed")
			span.RecordError(err)
		}
	}
	return res1

}

func (s *priceClientTrace) CalculateHotelRateDataPrices(ctx context.Context, arg2 *domain.CalculateHotelRateDataPricesReq) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "priceClient.CalculateHotelRateDataPrices")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.PriceClient.CalculateHotelRateDataPrices(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.PriceClient.CalculateHotelRateDataPrices failed")
			span.RecordError(err)
		}
	}
	return res1

}

func (s *priceClientTrace) CalculateHotelSearchPrices(ctx context.Context, arg2 *domain.CalculateHotelSearchPricesReq) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "priceClient.CalculateHotelSearchPrices")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.PriceClient.CalculateHotelSearchPrices(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.PriceClient.CalculateHotelSearchPrices failed")
			span.RecordError(err)
		}
	}
	return res1

}

func (s *priceClientTrace) GetHotelHiddenFee(ctx context.Context, arg2 string, arg3 string, arg4 enum.HotelProvider) ([]*domain.HiddenServiceFee, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "priceClient.GetHotelHiddenFee")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3, arg4)

	res1, res2 := s.PriceClient.GetHotelHiddenFee(ctx, arg2, arg3, arg4)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.PriceClient.GetHotelHiddenFee failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}
