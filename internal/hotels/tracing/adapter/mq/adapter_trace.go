// DO NOT EDIT: code generated from 'gen-tracing.go'
package client

import (
	"context"
	"reflect"

	commonRabbitMQ "gitlab.deepgate.io/apps/common/adapter/rabbitmq"
	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mq"
	"go.opentelemetry.io/otel/codes"
)

type adapterTrace struct {
	mq.Adapter
}

func NewAdapter(arg1 commonRabbitMQ.MQ,

) mq.Adapter {
	return &adapterTrace{
		Adapter: mq.NewAdapter(
			arg1,
		),
	}
}

func (s *adapterTrace) BookOldProvider(ctx context.Context, arg2 string) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "adapter.BookOldProvider")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.Adapter.BookOldProvider(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.Adapter.BookOldProvider failed")
			span.RecordError(err)
		}
	}
	return res1

}
