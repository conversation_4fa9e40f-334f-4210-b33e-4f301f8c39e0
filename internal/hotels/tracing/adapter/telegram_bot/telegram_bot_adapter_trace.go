// DO NOT EDIT: code generated from 'gen-tracing.go'
package telegram_bot_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/telegram_bot"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"go.opentelemetry.io/otel/codes"
)

type telegramBotAdapterTrace struct {
	telegram_bot.TelegramBotAdapter
}

func NewTelegramBotAdapter(arg1 *config.Schema,

) telegram_bot.TelegramBotAdapter {
	return &telegramBotAdapterTrace{
		TelegramBotAdapter: telegram_bot.NewTelegramBotAdapter(
			arg1,
		),
	}
}

func (s *telegramBotAdapterTrace) SendMessage(ctx context.Context, arg2 string, arg3 string) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "telegramBotAdapter.SendMessage")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1 := s.TelegramBotAdapter.SendMessage(ctx, arg2, arg3)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.TelegramBotAdapter.SendMessage failed")
			span.RecordError(err)
		}
	}
	return res1

}
