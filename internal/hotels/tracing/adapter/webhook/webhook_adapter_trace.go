// DO NOT EDIT: code generated from 'gen-tracing.go'
package webhook_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/webhook"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"go.opentelemetry.io/otel/codes"
)

type webhookAdapterTrace struct {
	webhook.WebhookAdapter
}

func NewWebhookAdapter(arg1 *config.Schema,
	arg2 repositories.RequestRepository,

) webhook.WebhookAdapter {
	return &webhookAdapterTrace{
		WebhookAdapter: webhook.NewWebhookAdapter(
			arg1,
			arg2,
		),
	}
}

func (s *webhookAdapterTrace) SendTransaction(ctx context.Context, arg2 *domain.Transaction, arg3 string, arg4 string) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "webhookAdapter.SendTransaction")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3, arg4)

	res1 := s.WebhookAdapter.SendTransaction(ctx, arg2, arg3, arg4)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.WebhookAdapter.SendTransaction failed")
			span.RecordError(err)
		}
	}
	return res1

}
