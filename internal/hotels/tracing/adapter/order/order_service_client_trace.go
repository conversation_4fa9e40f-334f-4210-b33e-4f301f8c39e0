// DO NOT EDIT: code generated from 'gen-tracing.go'
package wallet_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/order"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"go.opentelemetry.io/otel/codes"
)

type orderServiceClientTrace struct {
	order.OrderServiceClient
}

func NewOrderServiceClient(arg1 *config.Schema,

) order.OrderServiceClient {
	return &orderServiceClientTrace{
		OrderServiceClient: order.NewOrderServiceClient(
			arg1,
		),
	}
}

func (s *orderServiceClientTrace) CreateHotelOrder(ctx context.Context, arg2 *domain.HotelOrder) (string, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "orderServiceClient.CreateHotelOrder")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.OrderServiceClient.CreateHotelOrder(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.OrderServiceClient.CreateHotelOrder failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *orderServiceClientTrace) PlaceHotelOrder(ctx context.Context, arg2 *domain.HubHotelOrder, arg3 *domain.PartnerShopInfo) (*domain.TransactionInfo, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "orderServiceClient.PlaceHotelOrder")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2 := s.OrderServiceClient.PlaceHotelOrder(ctx, arg2, arg3)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.OrderServiceClient.PlaceHotelOrder failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}
