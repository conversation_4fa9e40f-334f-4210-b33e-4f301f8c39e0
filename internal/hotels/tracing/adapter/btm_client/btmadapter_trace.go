// DO NOT EDIT: code generated from 'gen-tracing.go'
package client

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/btm_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"go.opentelemetry.io/otel/codes"
)

type bTMAdapterTrace struct {
	btm_client.BTMAdapter
}

func NewBTMAdapter(arg1 *config.Schema,
	arg2 repositories.RequestRepository,

) btm_client.BTMAdapter {
	return &bTMAdapterTrace{
		BTMAdapter: btm_client.NewBTMAdapter(
			arg1,
			arg2,
		),
	}
}

func (s *bTMAdapterTrace) SendWebhookNotification(ctx context.Context, arg2 *domain.ExpediaWebhookNotificationRequest) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "bTMAdapter.SendWebhookNotification")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.BTMAdapter.SendWebhookNotification(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.BTMAdapter.SendWebhookNotification failed")
			span.RecordError(err)
		}
	}
	return res1

}
