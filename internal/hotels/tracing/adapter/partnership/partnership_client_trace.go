// DO NOT EDIT: code generated from 'gen-tracing.go'
package partnership_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/partnership"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"go.opentelemetry.io/otel/codes"
)

type partnershipClientTrace struct {
	partnership.PartnershipClient
}

func NewPartnershipClient(arg1 *config.Schema,

) partnership.PartnershipClient {
	return &partnershipClientTrace{
		PartnershipClient: partnership.NewPartnershipClient(
			arg1,
		),
	}
}

func (s *partnershipClientTrace) RetrievePartnership(ctx context.Context, arg2 string) (*domain.Partnership, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "partnershipClient.RetrievePartnership")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.PartnershipClient.RetrievePartnership(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.PartnershipClient.RetrievePartnership failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}
