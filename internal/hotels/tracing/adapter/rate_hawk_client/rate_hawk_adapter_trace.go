// DO NOT EDIT: code generated from 'gen-tracing.go'
package client

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/rate_hawk_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"go.opentelemetry.io/otel/codes"
)

type rateHawkAdapterTrace struct {
	rate_hawk_client.RateHawkAdapter
}

func NewRateHawkAdapter(arg1 *config.Schema,
	arg2 repositories.RequestRepository,

) rate_hawk_client.RateHawkAdapter {
	return &rateHawkAdapterTrace{
		RateHawkAdapter: rate_hawk_client.NewRateHawkAdapter(
			arg1,
			arg2,
		),
	}
}

func (s *rateHawkAdapterTrace) Book(ctx context.Context, arg2 *domain.HubBookReq, arg3 *domain.BasicSessionInfo, arg4 *domain.HubHotelOrder, arg5 string) (string, []*domain.HubRetrieveConfirmationID, enum.BookingStatus, string, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "rateHawkAdapter.Book")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3, arg4, arg5)

	res1, res2, res3, res4, res5 := s.RateHawkAdapter.Book(ctx, arg2, arg3, arg4, arg5)
	if res5 != nil {
		err, ok := reflect.ValueOf(res5).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.RateHawkAdapter.Book failed")
			span.RecordError(err)
		}
	}
	return res1, res2, res3, res4, res5

}

func (s *rateHawkAdapterTrace) Cancel(ctx context.Context, arg2 *domain.HubHotelOrder, arg3 string, arg4 string) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "rateHawkAdapter.Cancel")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3, arg4)

	res1 := s.RateHawkAdapter.Cancel(ctx, arg2, arg3, arg4)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.RateHawkAdapter.Cancel failed")
			span.RecordError(err)
		}
	}
	return res1

}

func (s *rateHawkAdapterTrace) CheckAvailability(ctx context.Context, arg2 *domain.CheckAvaiAdapterReq) ([]*domain.HubRoom, string, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "rateHawkAdapter.CheckAvailability")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2, res3 := s.RateHawkAdapter.CheckAvailability(ctx, arg2)
	if res3 != nil {
		err, ok := reflect.ValueOf(res3).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.RateHawkAdapter.CheckAvailability failed")
			span.RecordError(err)
		}
	}
	return res1, res2, res3

}

func (s *rateHawkAdapterTrace) PriceCheck(ctx context.Context, arg2 *domain.CacheCheckAvailabilityRequest, arg3 *domain.BedOption, arg4 *domain.HubRateData, arg5 string, arg6 *domain.Address) (*domain.HubRateData, *domain.BasicSessionInfo, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "rateHawkAdapter.PriceCheck")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3, arg4, arg5, arg6)

	res1, res2, res3 := s.RateHawkAdapter.PriceCheck(ctx, arg2, arg3, arg4, arg5, arg6)
	if res3 != nil {
		err, ok := reflect.ValueOf(res3).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.RateHawkAdapter.PriceCheck failed")
			span.RecordError(err)
		}
	}
	return res1, res2, res3

}

func (s *rateHawkAdapterTrace) Retrieve(ctx context.Context, arg2 *domain.HubHotelOrder) ([]*domain.HubRetrieveConfirmationID, enum.BookingStatus, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "rateHawkAdapter.Retrieve")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2, res3 := s.RateHawkAdapter.Retrieve(ctx, arg2)
	if res3 != nil {
		err, ok := reflect.ValueOf(res3).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.RateHawkAdapter.Retrieve failed")
			span.RecordError(err)
		}
	}
	return res1, res2, res3

}

func (s *rateHawkAdapterTrace) RetrieveCancelStatus(ctx context.Context, arg2 *domain.HubHotelOrder) (bool, bool, *domain.RefundData) {
	ctx, span := tracing.StartSpanFromContext(ctx, "rateHawkAdapter.RetrieveCancelStatus")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2, res3 := s.RateHawkAdapter.RetrieveCancelStatus(ctx, arg2)
	if res3 != nil {
		err, ok := reflect.ValueOf(res3).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.RateHawkAdapter.RetrieveCancelStatus failed")
			span.RecordError(err)
		}
	}
	return res1, res2, res3

}

func (s *rateHawkAdapterTrace) SearchHotel(ctx context.Context, arg2 *domain.SearchAdapterReq, arg3 string) ([]*domain.HotelSummary, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "rateHawkAdapter.SearchHotel")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2 := s.RateHawkAdapter.SearchHotel(ctx, arg2, arg3)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.RateHawkAdapter.SearchHotel failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}
