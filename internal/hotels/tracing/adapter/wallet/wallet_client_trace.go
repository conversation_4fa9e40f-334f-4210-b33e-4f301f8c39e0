// DO NOT EDIT: code generated from 'gen-tracing.go'
package wallet_tracing

import (
	"context"
	"reflect"

	commonDomain "gitlab.deepgate.io/apps/common/domain"
	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/wallet"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"go.opentelemetry.io/otel/codes"
)

type walletClientTrace struct {
	wallet.WalletClient
}

func NewWalletClient(arg1 *config.Schema,

) wallet.WalletClient {
	return &walletClientTrace{
		WalletClient: wallet.NewWalletClient(
			arg1,
		),
	}
}

func (s *walletClientTrace) AggregateHubTransaction(ctx context.Context, arg2 string, arg3 *domain.HubHotelOrder, arg4 *domain.PartnerShopInfo) (string, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "walletClient.AggregateHubTransaction")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3, arg4)

	res1, res2 := s.WalletClient.AggregateHubTransaction(ctx, arg2, arg3, arg4)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.WalletClient.AggregateHubTransaction failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *walletClientTrace) GetBalance(ctx context.Context, arg2 string, arg3 string) (float64, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "walletClient.GetBalance")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2 := s.WalletClient.GetBalance(ctx, arg2, arg3)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.WalletClient.GetBalance failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *walletClientTrace) GetTransactionHistory(ctx context.Context, arg2 string, arg3 *domain.TransactionHistoryReq) ([]*domain.Transaction, *commonDomain.Pagination, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "walletClient.GetTransactionHistory")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2, res3 := s.WalletClient.GetTransactionHistory(ctx, arg2, arg3)
	if res3 != nil {
		err, ok := reflect.ValueOf(res3).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.WalletClient.GetTransactionHistory failed")
			span.RecordError(err)
		}
	}
	return res1, res2, res3

}

func (s *walletClientTrace) RequestRefund(ctx context.Context, arg2 string, arg3 string, arg4 *domain.PartnerShopInfo, arg5 string, arg6 float64) (*domain.TransactionInfo, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "walletClient.RequestRefund")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3, arg4, arg5, arg6)

	res1, res2 := s.WalletClient.RequestRefund(ctx, arg2, arg3, arg4, arg5, arg6)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.WalletClient.RequestRefund failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}
