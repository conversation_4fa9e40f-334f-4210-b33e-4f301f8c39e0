// DO NOT EDIT: code generated from 'gen-tracing.go'
package expedia_client_tracing

import (
	"context"
	"reflect"

	commonRedis "gitlab.deepgate.io/apps/common/adapter/redis"
	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/expedia_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"go.opentelemetry.io/otel/codes"
)

type expediaAdapterTrace struct {
	expedia_client.ExpediaAdapter
}

func NewExpediaAdapter(arg1 *config.Schema,
	arg2 commonRedis.IRedis,
	arg3 repositories.RequestRepository,

) expedia_client.ExpediaAdapter {
	return &expediaAdapterTrace{
		ExpediaAdapter: expedia_client.NewExpediaAdapter(
			arg1,
			arg2,
			arg3,
		),
	}
}

func (s *expediaAdapterTrace) Book(ctx context.Context, arg2 *domain.HubBookReq, arg3 *domain.ExpediaSessionInfo, arg4 []*domain.HubOrderRoomItem, arg5 string) (string, []*domain.HubRetrieveConfirmationID, enum.BookingStatus, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "expediaAdapter.Book")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3, arg4, arg5)

	res1, res2, res3, res4 := s.ExpediaAdapter.Book(ctx, arg2, arg3, arg4, arg5)
	if res4 != nil {
		err, ok := reflect.ValueOf(res4).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.ExpediaAdapter.Book failed")
			span.RecordError(err)
		}
	}
	return res1, res2, res3, res4

}

func (s *expediaAdapterTrace) Cancel(ctx context.Context, arg2 *domain.HubHotelOrder, arg3 string, arg4 string) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "expediaAdapter.Cancel")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3, arg4)

	res1 := s.ExpediaAdapter.Cancel(ctx, arg2, arg3, arg4)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.ExpediaAdapter.Cancel failed")
			span.RecordError(err)
		}
	}
	return res1

}

func (s *expediaAdapterTrace) GetRegions(ctx context.Context, arg2 *domain.RegionsRequest, arg3 string) ([]*domain.Region, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "expediaAdapter.GetRegions")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2 := s.ExpediaAdapter.GetRegions(ctx, arg2, arg3)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.ExpediaAdapter.GetRegions failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *expediaAdapterTrace) GetReviews(ctx context.Context, arg2 *domain.HubHotelReviewReq, arg3 string) ([]*domain.HubHotelReviewItem, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "expediaAdapter.GetReviews")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2 := s.ExpediaAdapter.GetReviews(ctx, arg2, arg3)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.ExpediaAdapter.GetReviews failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *expediaAdapterTrace) PriceCheck(ctx context.Context, arg2 string, arg3 string, arg4 []*domain.HubSearchOccupancy) (*domain.HubRateData, *domain.ExpediaSessionInfo, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "expediaAdapter.PriceCheck")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3, arg4)

	res1, res2, res3 := s.ExpediaAdapter.PriceCheck(ctx, arg2, arg3, arg4)
	if res3 != nil {
		err, ok := reflect.ValueOf(res3).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.ExpediaAdapter.PriceCheck failed")
			span.RecordError(err)
		}
	}
	return res1, res2, res3

}

func (s *expediaAdapterTrace) Retrieve(ctx context.Context, arg2 *domain.HubHotelOrder, arg3 string) ([]*domain.HubRetrieveConfirmationID, enum.BookingStatus, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "expediaAdapter.Retrieve")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2, res3 := s.ExpediaAdapter.Retrieve(ctx, arg2, arg3)
	if res3 != nil {
		err, ok := reflect.ValueOf(res3).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.ExpediaAdapter.Retrieve failed")
			span.RecordError(err)
		}
	}
	return res1, res2, res3

}

func (s *expediaAdapterTrace) RetrieveForRefund(ctx context.Context, arg2 *domain.HubHotelOrder, arg3 string) (bool, bool, *domain.RefundData) {
	ctx, span := tracing.StartSpanFromContext(ctx, "expediaAdapter.RetrieveForRefund")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2, res3 := s.ExpediaAdapter.RetrieveForRefund(ctx, arg2, arg3)
	if res3 != nil {
		err, ok := reflect.ValueOf(res3).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.ExpediaAdapter.RetrieveForRefund failed")
			span.RecordError(err)
		}
	}
	return res1, res2, res3

}

func (s *expediaAdapterTrace) SearchHotel(ctx context.Context, arg2 *domain.SearchHotelRequestData, arg3 string) (*domain.HotelSearchResult, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "expediaAdapter.SearchHotel")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2 := s.ExpediaAdapter.SearchHotel(ctx, arg2, arg3)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.ExpediaAdapter.SearchHotel failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}
