// DO NOT EDIT: code generated from 'gen-tracing.go'
package tourmind_client_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/tourmind_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/tourmind_client/client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"go.opentelemetry.io/otel/codes"
)

type tourmindAdapterTrace struct {
	tourmind_client.TourmindAdapter
}

func NewTourmindAdapter(arg1 *config.Schema,
	arg2 repositories.RequestRepository,

) tourmind_client.TourmindAdapter {
	return &tourmindAdapterTrace{
		TourmindAdapter: tourmind_client.NewTourmindAdapter(
			arg1,
			arg2,
		),
	}
}

func (s *tourmindAdapterTrace) Book(ctx context.Context, arg2 *domain.HubBookReq, arg3 *domain.TourmindSessionInfo, arg4 *domain.HubHotelOrder, arg5 string) (string, string, enum.BookingStatus, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "tourmindAdapter.Book")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3, arg4, arg5)

	res1, res2, res3, res4 := s.TourmindAdapter.Book(ctx, arg2, arg3, arg4, arg5)
	if res4 != nil {
		err, ok := reflect.ValueOf(res4).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.TourmindAdapter.Book failed")
			span.RecordError(err)
		}
	}
	return res1, res2, res3, res4

}

func (s *tourmindAdapterTrace) HotelStaticList(ctx context.Context, arg2 *entities.HotelStaicListReq, arg3 string) (*entities.HotelStaicListRes, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "tourmindAdapter.HotelStaticList")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2 := s.TourmindAdapter.HotelStaticList(ctx, arg2, arg3)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.TourmindAdapter.HotelStaticList failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *tourmindAdapterTrace) PriceCheck(ctx context.Context, arg2 *domain.CacheCheckAvailabilityRequest, arg3 *domain.HubHotel, arg4 *domain.HubRateData, arg5 string) (*domain.HubRateData, *domain.TourmindSessionInfo, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "tourmindAdapter.PriceCheck")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3, arg4, arg5)

	res1, res2, res3 := s.TourmindAdapter.PriceCheck(ctx, arg2, arg3, arg4, arg5)
	if res3 != nil {
		err, ok := reflect.ValueOf(res3).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.TourmindAdapter.PriceCheck failed")
			span.RecordError(err)
		}
	}
	return res1, res2, res3

}

func (s *tourmindAdapterTrace) SearchHotel(ctx context.Context, arg2 *domain.SearchHotelRequestData, arg3 string) (*domain.HotelSearchResult, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "tourmindAdapter.SearchHotel")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2 := s.TourmindAdapter.SearchHotel(ctx, arg2, arg3)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.TourmindAdapter.SearchHotel failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}
