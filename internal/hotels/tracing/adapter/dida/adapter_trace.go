// DO NOT EDIT: code generated from 'gen-tracing.go'
package client

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/dida"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"go.opentelemetry.io/otel/codes"
)

type adapterTrace struct {
	dida.Adapter
}

func NewAdapter(arg1 *config.Schema,
	arg2 repositories.RequestRepository,

) dida.Adapter {
	return &adapterTrace{
		Adapter: dida.NewAdapter(
			arg1,
			arg2,
		),
	}
}

func (s *adapterTrace) Book(ctx context.Context, arg2 *domain.HubBookReq, arg3 *domain.DidaSessionInfo, arg4 *domain.HubHotelOrder, arg5 string) (string, []*domain.HubRetrieveConfirmationID, enum.BookingStatus, string, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "adapter.Book")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3, arg4, arg5)

	res1, res2, res3, res4, res5 := s.Adapter.Book(ctx, arg2, arg3, arg4, arg5)
	if res5 != nil {
		err, ok := reflect.ValueOf(res5).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.Adapter.Book failed")
			span.RecordError(err)
		}
	}
	return res1, res2, res3, res4, res5

}

func (s *adapterTrace) Cancel(ctx context.Context, arg2 string, arg3 string, arg4 string) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "adapter.Cancel")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3, arg4)

	res1 := s.Adapter.Cancel(ctx, arg2, arg3, arg4)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.Adapter.Cancel failed")
			span.RecordError(err)
		}
	}
	return res1

}

func (s *adapterTrace) CheckAvailability(ctx context.Context, arg2 *domain.CheckAvaiAdapterReq) ([]*domain.HubRoom, string, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "adapter.CheckAvailability")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2, res3 := s.Adapter.CheckAvailability(ctx, arg2)
	if res3 != nil {
		err, ok := reflect.ValueOf(res3).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.Adapter.CheckAvailability failed")
			span.RecordError(err)
		}
	}
	return res1, res2, res3

}

func (s *adapterTrace) PreCancel(ctx context.Context, arg2 string, arg3 string) (string, float64, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "adapter.PreCancel")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2, res3 := s.Adapter.PreCancel(ctx, arg2, arg3)
	if res3 != nil {
		err, ok := reflect.ValueOf(res3).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.Adapter.PreCancel failed")
			span.RecordError(err)
		}
	}
	return res1, res2, res3

}

func (s *adapterTrace) PriceCheck(ctx context.Context, arg2 *domain.CacheCheckAvailabilityRequest, arg3 string, arg4 *domain.Address, arg5 *domain.HubRateData, arg6 string) (*domain.HubRateData, *domain.DidaSessionInfo, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "adapter.PriceCheck")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3, arg4, arg5, arg6)

	res1, res2, res3 := s.Adapter.PriceCheck(ctx, arg2, arg3, arg4, arg5, arg6)
	if res3 != nil {
		err, ok := reflect.ValueOf(res3).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.Adapter.PriceCheck failed")
			span.RecordError(err)
		}
	}
	return res1, res2, res3

}

func (s *adapterTrace) Retrieve(ctx context.Context, arg2 *domain.HubHotelOrder, arg3 string) ([]*domain.HubRetrieveConfirmationID, enum.BookingStatus, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "adapter.Retrieve")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2, res3 := s.Adapter.Retrieve(ctx, arg2, arg3)
	if res3 != nil {
		err, ok := reflect.ValueOf(res3).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.Adapter.Retrieve failed")
			span.RecordError(err)
		}
	}
	return res1, res2, res3

}

func (s *adapterTrace) RetrieveCancelStatus(ctx context.Context, arg2 string, arg3 float64, arg4 string) (bool, bool, *domain.RefundData, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "adapter.RetrieveCancelStatus")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3, arg4)

	res1, res2, res3, res4 := s.Adapter.RetrieveCancelStatus(ctx, arg2, arg3, arg4)
	if res4 != nil {
		err, ok := reflect.ValueOf(res4).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.Adapter.RetrieveCancelStatus failed")
			span.RecordError(err)
		}
	}
	return res1, res2, res3, res4

}

func (s *adapterTrace) SearchHotel(ctx context.Context, arg2 *domain.SearchAdapterReq, arg3 string) ([]*domain.HotelSummary, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "adapter.SearchHotel")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2 := s.Adapter.SearchHotel(ctx, arg2, arg3)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.Adapter.SearchHotel failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}
