// DO NOT EDIT: code generated from 'gen-tracing.go'
package client

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/adapter/elasticsearch"
	commonDomain "gitlab.deepgate.io/apps/common/domain"
	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/es/es_repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"go.opentelemetry.io/otel/codes"
)

type placeElasticRepositoryTrace struct {
	es_repositories.PlaceElasticRepository
}

func NewPlaceElasticRepository(arg1 elasticsearch.ES,

) es_repositories.PlaceElasticRepository {
	return &placeElasticRepositoryTrace{
		PlaceElasticRepository: es_repositories.NewPlaceElasticRepository(
			arg1,
		),
	}
}

func (s *placeElasticRepositoryTrace) Bulk(ctx context.Context, arg2 string, arg3 []uint8) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "placeElasticRepository.Bulk")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1 := s.PlaceElasticRepository.Bulk(ctx, arg2, arg3)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.PlaceElasticRepository.Bulk failed")
			span.RecordError(err)
		}
	}
	return res1

}

func (s *placeElasticRepositoryTrace) Search(ctx context.Context, arg2 *domain.SearchDestinationReq, arg3 string) ([]*domain.Place, *commonDomain.Pagination, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "placeElasticRepository.Search")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2, res3 := s.PlaceElasticRepository.Search(ctx, arg2, arg3)
	if res3 != nil {
		err, ok := reflect.ValueOf(res3).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.PlaceElasticRepository.Search failed")
			span.RecordError(err)
		}
	}
	return res1, res2, res3

}
