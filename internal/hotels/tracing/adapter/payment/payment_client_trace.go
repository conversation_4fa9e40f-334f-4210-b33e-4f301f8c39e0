// DO NOT EDIT: code generated from 'gen-tracing.go'
package payment_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/payment"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"go.opentelemetry.io/otel/codes"
)

type paymentClientTrace struct {
	payment.PaymentClient
}

func NewPaymentClient(arg1 *config.Schema,

) payment.PaymentClient {
	return &paymentClientTrace{
		PaymentClient: payment.NewPaymentClient(
			arg1,
		),
	}
}

func (s *paymentClientTrace) GetPaymentMethod(ctx context.Context, arg2 enum.PaymentMethod) (*domain.PaymentMethod, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "paymentClient.GetPaymentMethod")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.PaymentClient.GetPaymentMethod(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.PaymentClient.GetPaymentMethod failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}
