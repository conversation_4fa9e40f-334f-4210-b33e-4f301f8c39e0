// DO NOT EDIT: code generated from 'gen-tracing.go'
package notification_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/notification"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"go.opentelemetry.io/otel/codes"
)

type notificationServiceClientTrace struct {
	notification.NotificationServiceClient
}

func NewNotificationServiceClient(arg1 *config.Schema,

) notification.NotificationServiceClient {
	return &notificationServiceClientTrace{
		NotificationServiceClient: notification.NewNotificationServiceClient(
			arg1,
		),
	}
}

func (s *notificationServiceClientTrace) SendMessage(ctx context.Context, arg2 *notification.SendMessageRequest) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "notificationServiceClient.SendMessage")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.NotificationServiceClient.SendMessage(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.NotificationServiceClient.SendMessage failed")
			span.RecordError(err)
		}
	}
	return res1

}
