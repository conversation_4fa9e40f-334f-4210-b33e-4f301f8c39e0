// DO NOT EDIT: code generated from 'gen-tracing.go'
package repositories_tracing

import (
	"context"
	"reflect"

	commonMongoDb "gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"go.opentelemetry.io/otel/codes"
)

type searchHotelCachesRepositoryTrace struct {
	repositories.SearchHotelCachesRepository
}

func NewSearchHotelCachesRepository(arg1 commonMongoDb.DB,

) repositories.SearchHotelCachesRepository {
	return &searchHotelCachesRepositoryTrace{
		SearchHotelCachesRepository: repositories.NewSearchHotelCachesRepository(
			arg1,
		),
	}
}

func (s *searchHotelCachesRepositoryTrace) FindByParentID(ctx context.Context, arg2 string) ([]*domain.HotelSummary, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "searchHotelCachesRepository.FindByParentID")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.SearchHotelCachesRepository.FindByParentID(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.SearchHotelCachesRepository.FindByParentID failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *searchHotelCachesRepositoryTrace) FindParentIDByKey(ctx context.Context, arg2 string, arg3 []enum.HotelProvider) (map[enum.HotelProvider]string, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "searchHotelCachesRepository.FindParentIDByKey")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2 := s.SearchHotelCachesRepository.FindParentIDByKey(ctx, arg2, arg3)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.SearchHotelCachesRepository.FindParentIDByKey failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *searchHotelCachesRepositoryTrace) Insert(ctx context.Context, arg2 string, arg3 map[enum.HotelProvider][]*domain.HotelSummary) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "searchHotelCachesRepository.Insert")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1 := s.SearchHotelCachesRepository.Insert(ctx, arg2, arg3)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.SearchHotelCachesRepository.Insert failed")
			span.RecordError(err)
		}
	}
	return res1

}
