// DO NOT EDIT: code generated from 'gen-tracing.go'
package repositories_tracing

import (
	"context"
	"reflect"

	commonMongoDb "gitlab.deepgate.io/apps/common/adapter/mongodb"
	commonDomain "gitlab.deepgate.io/apps/common/domain"
	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"go.opentelemetry.io/otel/codes"
)

type orderRepositoryTrace struct {
	repositories.OrderRepository
}

func NewOrderRepository(arg1 commonMongoDb.DB,

) repositories.OrderRepository {
	return &orderRepositoryTrace{
		OrderRepository: repositories.NewOrderRepository(
			arg1,
		),
	}
}

func (s *orderRepositoryTrace) FindOneByOrderCode(ctx context.Context, arg2 string, arg3 string) (*domain.HubHotelOrder, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "orderRepository.FindOneByOrderCode")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2 := s.OrderRepository.FindOneByOrderCode(ctx, arg2, arg3)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.OrderRepository.FindOneByOrderCode failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *orderRepositoryTrace) FindOneByReservationCode(ctx context.Context, arg2 string, arg3 enum.HotelProvider) (*domain.HubHotelOrder, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "orderRepository.FindOneByReservationCode")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2 := s.OrderRepository.FindOneByReservationCode(ctx, arg2, arg3)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.OrderRepository.FindOneByReservationCode failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *orderRepositoryTrace) FindOrderByID(ctx context.Context, arg2 string) (*domain.HubHotelOrder, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "orderRepository.FindOrderByID")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.OrderRepository.FindOrderByID(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.OrderRepository.FindOrderByID failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *orderRepositoryTrace) FindOrderBySessionID(ctx context.Context, arg2 string, arg3 string) (*domain.HubHotelOrder, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "orderRepository.FindOrderBySessionID")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2 := s.OrderRepository.FindOrderBySessionID(ctx, arg2, arg3)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.OrderRepository.FindOrderBySessionID failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *orderRepositoryTrace) InsertOne(ctx context.Context, arg2 *domain.HubHotelOrder) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "orderRepository.InsertOne")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.OrderRepository.InsertOne(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.OrderRepository.InsertOne failed")
			span.RecordError(err)
		}
	}
	return res1

}

func (s *orderRepositoryTrace) ListCancelingOrder(ctx context.Context) ([]*domain.HubHotelOrder, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "orderRepository.ListCancelingOrder")
	defer span.End()
	tracing.AddAttributes(span, ctx)

	res1, res2 := s.OrderRepository.ListCancelingOrder(ctx)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.OrderRepository.ListCancelingOrder failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *orderRepositoryTrace) ListExpiredOrder(ctx context.Context) ([]*domain.HubHotelOrder, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "orderRepository.ListExpiredOrder")
	defer span.End()
	tracing.AddAttributes(span, ctx)

	res1, res2 := s.OrderRepository.ListExpiredOrder(ctx)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.OrderRepository.ListExpiredOrder failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *orderRepositoryTrace) ListOrder(ctx context.Context, arg2 *commonDomain.Pagination, arg3 *domain.ListBookingRequest) ([]*domain.HubHotelOrder, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "orderRepository.ListOrder")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2 := s.OrderRepository.ListOrder(ctx, arg2, arg3)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.OrderRepository.ListOrder failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *orderRepositoryTrace) ListOrderAggregate(ctx context.Context, arg2 *commonDomain.Pagination, arg3 *domain.ListOrderFilter) ([]*domain.HubHotelOrder, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "orderRepository.ListOrderAggregate")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2 := s.OrderRepository.ListOrderAggregate(ctx, arg2, arg3)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.OrderRepository.ListOrderAggregate failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *orderRepositoryTrace) ListOrderByFilter(ctx context.Context, arg2 *commonDomain.Pagination, arg3 *domain.ListOrderFilter) ([]*domain.HubHotelOrder, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "orderRepository.ListOrderByFilter")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2 := s.OrderRepository.ListOrderByFilter(ctx, arg2, arg3)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.OrderRepository.ListOrderByFilter failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *orderRepositoryTrace) UpdateOne(ctx context.Context, arg2 string, arg3 *domain.HubHotelOrder) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "orderRepository.UpdateOne")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1 := s.OrderRepository.UpdateOne(ctx, arg2, arg3)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.OrderRepository.UpdateOne failed")
			span.RecordError(err)
		}
	}
	return res1

}

func (s *orderRepositoryTrace) UpdateOneV2(ctx context.Context, arg2 string, arg3 *domain.HubOrderUpdate) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "orderRepository.UpdateOneV2")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1 := s.OrderRepository.UpdateOneV2(ctx, arg2, arg3)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.OrderRepository.UpdateOneV2 failed")
			span.RecordError(err)
		}
	}
	return res1

}

func (s *orderRepositoryTrace) WithTransaction(ctx context.Context, arg2 func(context.Context) (interface{}, error)) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "orderRepository.WithTransaction")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.OrderRepository.WithTransaction(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.OrderRepository.WithTransaction failed")
			span.RecordError(err)
		}
	}
	return res1

}
