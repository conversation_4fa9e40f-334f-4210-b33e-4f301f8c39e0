// DO NOT EDIT: code generated from 'gen-tracing.go'
package repositories_tracing

import (
	"context"
	"reflect"

	commonMongoDb "gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"go.opentelemetry.io/otel/codes"
)

type requestRepositoryTrace struct {
	repositories.RequestRepository
}

func NewRequestRepository(arg1 commonMongoDb.DB,
	arg2 *config.Schema,

) repositories.RequestRepository {
	return &requestRepositoryTrace{
		RequestRepository: repositories.NewRequestRepository(
			arg1,
			arg2,
		),
	}
}

func (s *requestRepositoryTrace) Create(ctx context.Context, arg2 *repositories.Request) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "requestRepository.Create")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.RequestRepository.Create(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.RequestRepository.Create failed")
			span.RecordError(err)
		}
	}
	return res1

}
