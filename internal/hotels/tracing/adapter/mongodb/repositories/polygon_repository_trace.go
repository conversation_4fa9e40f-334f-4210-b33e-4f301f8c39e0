// DO NOT EDIT: code generated from 'gen-tracing.go'
package repositories_tracing

import (
	"context"
	"reflect"

	commonMongoDb "gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"go.opentelemetry.io/otel/codes"
)

type polygonRepositoryTrace struct {
	repositories.PolygonRepository
}

func NewPolygonRepository(arg1 commonMongoDb.DB,
	arg2 *config.Schema,

) repositories.PolygonRepository {
	return &polygonRepositoryTrace{
		PolygonRepository: repositories.NewPolygonRepository(
			arg1,
			arg2,
		),
	}
}

func (s *polygonRepositoryTrace) Create(ctx context.Context, arg2 *domain.BoundingPolygon) (string, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "polygonRepository.Create")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.PolygonRepository.Create(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.PolygonRepository.Create failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *polygonRepositoryTrace) CreateMany(ctx context.Context, arg2 []*domain.BoundingPolygon) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "polygonRepository.CreateMany")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.PolygonRepository.CreateMany(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.PolygonRepository.CreateMany failed")
			span.RecordError(err)
		}
	}
	return res1

}

func (s *polygonRepositoryTrace) FindByPolygonID(ctx context.Context, arg2 string) (*domain.BoundingPolygon, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "polygonRepository.FindByPolygonID")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.PolygonRepository.FindByPolygonID(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.PolygonRepository.FindByPolygonID failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *polygonRepositoryTrace) WithTransaction(ctx context.Context, arg2 func(context.Context) (interface{}, error)) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "polygonRepository.WithTransaction")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.PolygonRepository.WithTransaction(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.PolygonRepository.WithTransaction failed")
			span.RecordError(err)
		}
	}
	return res1

}
