// DO NOT EDIT: code generated from 'gen-tracing.go'
package repositories_tracing

import (
	"context"
	"reflect"

	commonMongoDb "gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"go.opentelemetry.io/otel/codes"
)

type tAMappingsRepositoryTrace struct {
	repositories.TAMappingsRepository
}

func NewTAMappingsRepository(arg1 commonMongoDb.DB,

) repositories.TAMappingsRepository {
	return &tAMappingsRepositoryTrace{
		TAMappingsRepository: repositories.NewTAMappingsRepository(
			arg1,
		),
	}
}

func (s *tAMappingsRepositoryTrace) FindOneByExternalID(ctx context.Context, arg2 string) (*domain.TAMapping, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "tAMappingsRepository.FindOneByExternalID")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.TAMappingsRepository.FindOneByExternalID(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.TAMappingsRepository.FindOneByExternalID failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}
