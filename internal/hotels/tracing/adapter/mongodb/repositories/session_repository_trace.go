// DO NOT EDIT: code generated from 'gen-tracing.go'
package repositories_tracing

import (
	"context"
	"reflect"

	commonMongoDb "gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"go.opentelemetry.io/otel/codes"
)

type sessionRepositoryTrace struct {
	repositories.SessionRepository
}

func NewSessionRepository(arg1 commonMongoDb.DB,

) repositories.SessionRepository {
	return &sessionRepositoryTrace{
		SessionRepository: repositories.NewSessionRepository(
			arg1,
		),
	}
}

func (s *sessionRepositoryTrace) FindBySessionID(ctx context.Context, arg2 string, arg3 string) (*domain.HotelSession, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "sessionRepository.FindBySessionID")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2 := s.SessionRepository.FindBySessionID(ctx, arg2, arg3)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.SessionRepository.FindBySessionID failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *sessionRepositoryTrace) FindBySessionIDWithoutExpire(ctx context.Context, arg2 string, arg3 string) (*domain.HotelSession, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "sessionRepository.FindBySessionIDWithoutExpire")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2 := s.SessionRepository.FindBySessionIDWithoutExpire(ctx, arg2, arg3)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.SessionRepository.FindBySessionIDWithoutExpire failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *sessionRepositoryTrace) FindBySessionIDs(ctx context.Context, arg2 []string) ([]*domain.HotelSession, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "sessionRepository.FindBySessionIDs")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.SessionRepository.FindBySessionIDs(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.SessionRepository.FindBySessionIDs failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *sessionRepositoryTrace) InsertOne(ctx context.Context, arg2 *domain.HotelSession) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "sessionRepository.InsertOne")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.SessionRepository.InsertOne(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.SessionRepository.InsertOne failed")
			span.RecordError(err)
		}
	}
	return res1

}

func (s *sessionRepositoryTrace) UpdateExpireTime(ctx context.Context, arg2 string, arg3 int64) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "sessionRepository.UpdateExpireTime")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1 := s.SessionRepository.UpdateExpireTime(ctx, arg2, arg3)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.SessionRepository.UpdateExpireTime failed")
			span.RecordError(err)
		}
	}
	return res1

}

func (s *sessionRepositoryTrace) WithTransaction(ctx context.Context, arg2 func(context.Context) (interface{}, error)) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "sessionRepository.WithTransaction")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.SessionRepository.WithTransaction(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.SessionRepository.WithTransaction failed")
			span.RecordError(err)
		}
	}
	return res1

}
