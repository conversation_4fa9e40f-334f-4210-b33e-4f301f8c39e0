// DO NOT EDIT: code generated from 'gen-tracing.go'
package repositories_tracing

import (
	"context"
	"reflect"

	commonMongoDb "gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"go.opentelemetry.io/otel/codes"
)

type hotelAreaCacheRepositoryTrace struct {
	repositories.HotelAreaCacheRepository
}

func NewHotelAreaCacheRepository(arg1 commonMongoDb.DB,

) repositories.HotelAreaCacheRepository {
	return &hotelAreaCacheRepositoryTrace{
		HotelAreaCacheRepository: repositories.NewHotelAreaCacheRepository(
			arg1,
		),
	}
}

func (s *hotelAreaCacheRepositoryTrace) FindByArea(ctx context.Context, arg2 string, arg3 enum.PlaceType) (*domain.HotelAreaCacheItem, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "hotelAreaCacheRepository.FindByArea")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2 := s.HotelAreaCacheRepository.FindByArea(ctx, arg2, arg3)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.HotelAreaCacheRepository.FindByArea failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *hotelAreaCacheRepositoryTrace) InsertOne(ctx context.Context, arg2 *domain.HotelAreaCacheItem) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "hotelAreaCacheRepository.InsertOne")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.HotelAreaCacheRepository.InsertOne(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.HotelAreaCacheRepository.InsertOne failed")
			span.RecordError(err)
		}
	}
	return res1

}
