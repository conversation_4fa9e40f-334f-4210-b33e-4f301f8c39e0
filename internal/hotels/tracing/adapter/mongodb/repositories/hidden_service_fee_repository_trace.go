// DO NOT EDIT: code generated from 'gen-tracing.go'
package repositories_tracing

import (
	"context"
	"reflect"

	commonMongoDb "gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"go.opentelemetry.io/otel/codes"
)

type hiddenServiceFeeRepositoryTrace struct {
	repositories.HiddenServiceFeeRepository
}

func NewHiddenServiceFeeRepository(arg1 commonMongoDb.DB,

) repositories.HiddenServiceFeeRepository {
	return &hiddenServiceFeeRepositoryTrace{
		HiddenServiceFeeRepository: repositories.NewHiddenServiceFeeRepository(
			arg1,
		),
	}
}

func (s *hiddenServiceFeeRepositoryTrace) Create(ctx context.Context, arg2 *domain.HiddenServiceFee) (string, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "hiddenServiceFeeRepository.Create")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.HiddenServiceFeeRepository.Create(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.HiddenServiceFeeRepository.Create failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *hiddenServiceFeeRepositoryTrace) Delete(ctx context.Context, arg2 string, arg3 string) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "hiddenServiceFeeRepository.Delete")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1 := s.HiddenServiceFeeRepository.Delete(ctx, arg2, arg3)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.HiddenServiceFeeRepository.Delete failed")
			span.RecordError(err)
		}
	}
	return res1

}

func (s *hiddenServiceFeeRepositoryTrace) Deletes(ctx context.Context, arg2 []string, arg3 string) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "hiddenServiceFeeRepository.Deletes")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1 := s.HiddenServiceFeeRepository.Deletes(ctx, arg2, arg3)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.HiddenServiceFeeRepository.Deletes failed")
			span.RecordError(err)
		}
	}
	return res1

}

func (s *hiddenServiceFeeRepositoryTrace) FindAllByCondition(ctx context.Context, arg2 *domain.ListHiddenServiceFeeReq) ([]*domain.HiddenServiceFee, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "hiddenServiceFeeRepository.FindAllByCondition")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.HiddenServiceFeeRepository.FindAllByCondition(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.HiddenServiceFeeRepository.FindAllByCondition failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *hiddenServiceFeeRepositoryTrace) FindAllByConditionAndFilter(ctx context.Context, arg2 *domain.ListHiddenServiceFeeReq) ([]*domain.HiddenServiceFee, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "hiddenServiceFeeRepository.FindAllByConditionAndFilter")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.HiddenServiceFeeRepository.FindAllByConditionAndFilter(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.HiddenServiceFeeRepository.FindAllByConditionAndFilter failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *hiddenServiceFeeRepositoryTrace) FindByID(ctx context.Context, arg2 string) (*domain.HiddenServiceFee, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "hiddenServiceFeeRepository.FindByID")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.HiddenServiceFeeRepository.FindByID(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.HiddenServiceFeeRepository.FindByID failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *hiddenServiceFeeRepositoryTrace) FindOne(ctx context.Context, arg2 *domain.HiddenServiceFee) (*domain.HiddenServiceFee, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "hiddenServiceFeeRepository.FindOne")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.HiddenServiceFeeRepository.FindOne(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.HiddenServiceFeeRepository.FindOne failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *hiddenServiceFeeRepositoryTrace) Update(ctx context.Context, arg2 *domain.HiddenServiceFee) (*domain.HiddenServiceFee, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "hiddenServiceFeeRepository.Update")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.HiddenServiceFeeRepository.Update(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.HiddenServiceFeeRepository.Update failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}
