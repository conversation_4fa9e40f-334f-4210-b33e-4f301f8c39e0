// DO NOT EDIT: code generated from 'gen-tracing.go'
package repositories_tracing

import (
	"context"
	"reflect"

	commonMongoDb "gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"go.opentelemetry.io/otel/codes"
)

type currencyExchangeRepositoryTrace struct {
	repositories.CurrencyExchangeRepository
}

func NewCurrencyExchangeRepository(arg1 commonMongoDb.DB,

) repositories.CurrencyExchangeRepository {
	return &currencyExchangeRepositoryTrace{
		CurrencyExchangeRepository: repositories.NewCurrencyExchangeRepository(
			arg1,
		),
	}
}

func (s *currencyExchangeRepositoryTrace) Find(ctx context.Context, arg2 string) ([]*domain.CurrencyExchange, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "currencyExchangeRepository.Find")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.CurrencyExchangeRepository.Find(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.CurrencyExchangeRepository.Find failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}
