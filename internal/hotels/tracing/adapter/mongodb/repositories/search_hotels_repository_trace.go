// DO NOT EDIT: code generated from 'gen-tracing.go'
package repositories_tracing

import (
	"context"
	"reflect"

	commonMongoDb "gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"go.opentelemetry.io/otel/codes"
)

type searchHotelsRepositoryTrace struct {
	repositories.ProviderSearchHotelsRepository
}

func NewSearchHotelsRepository(arg1 commonMongoDb.DB,

) repositories.ProviderSearchHotelsRepository {
	return &searchHotelsRepositoryTrace{
		ProviderSearchHotelsRepository: repositories.NewProviderSearchHotelsRepository(
			arg1,
		),
	}
}

func (s *searchHotelsRepositoryTrace) FindByID(ctx context.Context, arg2 string, arg3 string, arg4 string, arg5 string) (*domain.HotelSearchResult, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "searchHotelsRepository.FindByID")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3, arg4, arg5)

	res1, res2 := s.ProviderSearchHotelsRepository.FindByID(ctx, arg2, arg3, arg4, arg5)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.SearchHotelsRepository.FindByID failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *searchHotelsRepositoryTrace) FindByKey(ctx context.Context, arg2 string, arg3 []enum.HotelProvider) ([]*domain.HotelSearchResult, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "searchHotelsRepository.FindByKey")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2 := s.ProviderSearchHotelsRepository.FindByKey(ctx, arg2, arg3)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.SearchHotelsRepository.FindByKey failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *searchHotelsRepositoryTrace) InsertMany(ctx context.Context, arg2 []*domain.HotelSearchResult) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "searchHotelsRepository.InsertMany")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.ProviderSearchHotelsRepository.InsertMany(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.SearchHotelsRepository.InsertMany failed")
			span.RecordError(err)
		}
	}
	return res1

}
