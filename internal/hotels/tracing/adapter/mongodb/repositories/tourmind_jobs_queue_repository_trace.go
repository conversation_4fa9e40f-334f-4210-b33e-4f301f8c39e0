// DO NOT EDIT: code generated from 'gen-tracing.go'
package repositories_tracing

import (
	"context"
	"reflect"

	commonMongoDb "gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"go.opentelemetry.io/otel/codes"
)

type tourmindJobsQueueRepositoryTrace struct {
	repositories.TourmindJobsQueueRepository
}

func NewTourmindJobsQueueRepository(arg1 commonMongoDb.DB,
	arg2 *config.Schema,

) repositories.TourmindJobsQueueRepository {
	return &tourmindJobsQueueRepositoryTrace{
		TourmindJobsQueueRepository: repositories.NewTourmindJobsQueueRepository(
			arg1,
			arg2,
		),
	}
}

func (s *tourmindJobsQueueRepositoryTrace) CreateMany(ctx context.Context, arg2 []*domain.TourmindJobsQueue) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "tourmindJobsQueueRepository.CreateMany")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.TourmindJobsQueueRepository.CreateMany(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.TourmindJobsQueueRepository.CreateMany failed")
			span.RecordError(err)
		}
	}
	return res1

}

func (s *tourmindJobsQueueRepositoryTrace) MarkJobAsCompleted(ctx context.Context, arg2 *domain.TourmindJobsQueue) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "tourmindJobsQueueRepository.MarkJobAsCompleted")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.TourmindJobsQueueRepository.MarkJobAsCompleted(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.TourmindJobsQueueRepository.MarkJobAsCompleted failed")
			span.RecordError(err)
		}
	}
	return res1

}

func (s *tourmindJobsQueueRepositoryTrace) RetrieveActiveJobs(ctx context.Context, arg2 string) ([]*domain.TourmindJobsQueue, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "tourmindJobsQueueRepository.RetrieveActiveJobs")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.TourmindJobsQueueRepository.RetrieveActiveJobs(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.TourmindJobsQueueRepository.RetrieveActiveJobs failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *tourmindJobsQueueRepositoryTrace) SoftDeleteMany(ctx context.Context, arg2 string) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "tourmindJobsQueueRepository.SoftDeleteMany")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.TourmindJobsQueueRepository.SoftDeleteMany(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.TourmindJobsQueueRepository.SoftDeleteMany failed")
			span.RecordError(err)
		}
	}
	return res1

}
