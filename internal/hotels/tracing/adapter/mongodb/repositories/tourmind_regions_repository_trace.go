// DO NOT EDIT: code generated from 'gen-tracing.go'
package repositories_tracing

import (
	"context"
	"reflect"

	commonMongoDb "gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"go.opentelemetry.io/otel/codes"
)

type tourmindRegionsRepositoryTrace struct {
	repositories.TourmindRegionsRepository
}

func NewTourmindRegionsRepository(arg1 commonMongoDb.DB,
	arg2 *config.Schema,

) repositories.TourmindRegionsRepository {
	return &tourmindRegionsRepositoryTrace{
		TourmindRegionsRepository: repositories.NewTourmindRegionsRepository(
			arg1,
			arg2,
		),
	}
}

func (s *tourmindRegionsRepositoryTrace) FindAll(ctx context.Context, arg2 bool) ([]*domain.TourmindRegion, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "tourmindRegionsRepository.FindAll")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.TourmindRegionsRepository.FindAll(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.TourmindRegionsRepository.FindAll failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *tourmindRegionsRepositoryTrace) MarkAsCompleted(ctx context.Context, arg2 string) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "tourmindRegionsRepository.MarkAsCompleted")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.TourmindRegionsRepository.MarkAsCompleted(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.TourmindRegionsRepository.MarkAsCompleted failed")
			span.RecordError(err)
		}
	}
	return res1

}
