// DO NOT EDIT: code generated from 'gen-tracing.go'
package repositories_tracing

import (
	"context"
	"reflect"

	commonMongoDb "gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"go.opentelemetry.io/otel/codes"
)

type regionRepositoryTrace struct {
	repositories.RegionRepository
}

func NewRegionRepository(arg1 commonMongoDb.DB,
	arg2 *config.Schema,

) repositories.RegionRepository {
	return &regionRepositoryTrace{
		RegionRepository: repositories.NewRegionRepository(
			arg1,
			arg2,
		),
	}
}

func (s *regionRepositoryTrace) Create(ctx context.Context, arg2 *domain.Region, arg3 string) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "regionRepository.Create")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1 := s.RegionRepository.Create(ctx, arg2, arg3)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.RegionRepository.Create failed")
			span.RecordError(err)
		}
	}
	return res1

}

func (s *regionRepositoryTrace) FindAllByCountryCode(ctx context.Context, arg2 string) ([]*domain.Region, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "regionRepository.FindAllByCountryCode")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.RegionRepository.FindAllByCountryCode(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.RegionRepository.FindAllByCountryCode failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *regionRepositoryTrace) FindByRegionID(ctx context.Context, arg2 string, arg3 string) (*domain.Region, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "regionRepository.FindByRegionID")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2 := s.RegionRepository.FindByRegionID(ctx, arg2, arg3)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.RegionRepository.FindByRegionID failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *regionRepositoryTrace) FindRegionsByRadiusAndTypes(ctx context.Context, arg2 float64, arg3 float64, arg4 float64, arg5 []string, arg6 string) ([]*domain.Region, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "regionRepository.FindRegionsByRadiusAndTypes")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3, arg4, arg5, arg6)

	res1, res2 := s.RegionRepository.FindRegionsByRadiusAndTypes(ctx, arg2, arg3, arg4, arg5, arg6)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.RegionRepository.FindRegionsByRadiusAndTypes failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *regionRepositoryTrace) Upsert(ctx context.Context, arg2 *domain.Region, arg3 string) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "regionRepository.Upsert")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1 := s.RegionRepository.Upsert(ctx, arg2, arg3)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.RegionRepository.Upsert failed")
			span.RecordError(err)
		}
	}
	return res1

}

func (s *regionRepositoryTrace) WithTransaction(ctx context.Context, arg2 func(context.Context) (interface{}, error)) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "regionRepository.WithTransaction")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.RegionRepository.WithTransaction(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.RegionRepository.WithTransaction failed")
			span.RecordError(err)
		}
	}
	return res1

}
