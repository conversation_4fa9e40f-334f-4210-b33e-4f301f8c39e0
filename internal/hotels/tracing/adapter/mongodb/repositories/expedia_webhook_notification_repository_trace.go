// DO NOT EDIT: code generated from 'gen-tracing.go'
package repositories_tracing

import (
	"context"
	"reflect"

	commonMongoDb "gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"go.opentelemetry.io/otel/codes"
)

type expediaWebhookNotificationRepositoryTrace struct {
	repositories.ExpediaWebhookNotificationRepository
}

func NewExpediaWebhookNotificationRepository(arg1 commonMongoDb.DB,

) repositories.ExpediaWebhookNotificationRepository {
	return &expediaWebhookNotificationRepositoryTrace{
		ExpediaWebhookNotificationRepository: repositories.NewExpediaWebhookNotificationRepository(
			arg1,
		),
	}
}

func (s *expediaWebhookNotificationRepositoryTrace) FindByKey(ctx context.Context, arg2 string) (*domain.ExpediaWebhookNotificationRequest, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "expediaWebhookNotificationRepository.FindByKey")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.ExpediaWebhookNotificationRepository.FindByKey(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.ExpediaWebhookNotificationRepository.FindByKey failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *expediaWebhookNotificationRepositoryTrace) Insert(ctx context.Context, arg2 *domain.ExpediaWebhookNotificationRequest) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "expediaWebhookNotificationRepository.Insert")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.ExpediaWebhookNotificationRepository.Insert(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.ExpediaWebhookNotificationRepository.Insert failed")
			span.RecordError(err)
		}
	}
	return res1

}
