// DO NOT EDIT: code generated from 'gen-tracing.go'
package client

import (
	"context"
	"reflect"

	commonRedis "gitlab.deepgate.io/apps/common/adapter/redis"
	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/ta_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"go.opentelemetry.io/otel/codes"
)

type tAAdapterTrace struct {
	ta_client.TAAdapter
}

func NewTAAdapter(arg1 *config.Schema,
	arg2 repositories.RequestRepository,
	arg3 commonRedis.IRedis,

) ta_client.TAAdapter {
	return &tAAdapterTrace{
		TAAdapter: ta_client.NewTAAdapter(
			arg1,
			arg2,
			arg3,
		),
	}
}

func (s *tAAdapterTrace) Book(ctx context.Context, arg2 *domain.HubHotelOrder, arg3 string) (string, []*domain.HubRetrieveConfirmationID, enum.BookingStatus, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "tAAdapter.Book")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2, res3, res4 := s.TAAdapter.Book(ctx, arg2, arg3)
	if res4 != nil {
		err, ok := reflect.ValueOf(res4).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.TAAdapter.Book failed")
			span.RecordError(err)
		}
	}
	return res1, res2, res3, res4

}

func (s *tAAdapterTrace) Cancel(ctx context.Context, arg2 string, arg3 string) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "tAAdapter.Cancel")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1 := s.TAAdapter.Cancel(ctx, arg2, arg3)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.TAAdapter.Cancel failed")
			span.RecordError(err)
		}
	}
	return res1

}

func (s *tAAdapterTrace) ConfirmBook(ctx context.Context, arg2 string, arg3 string) (float64, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "tAAdapter.ConfirmBook")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2 := s.TAAdapter.ConfirmBook(ctx, arg2, arg3)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.TAAdapter.ConfirmBook failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *tAAdapterTrace) HasPrice(ctx context.Context, arg2 *domain.CacheCheckAvailabilityRequest, arg3 string, arg4 string, arg5 string, arg6 *domain.HubRateData, arg7 string, arg8 string) (bool, float64, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "tAAdapter.HasPrice")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3, arg4, arg5, arg6, arg7, arg8)

	res1, res2, res3 := s.TAAdapter.HasPrice(ctx, arg2, arg3, arg4, arg5, arg6, arg7, arg8)
	if res3 != nil {
		err, ok := reflect.ValueOf(res3).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.TAAdapter.HasPrice failed")
			span.RecordError(err)
		}
	}
	return res1, res2, res3

}

func (s *tAAdapterTrace) Retrieve(ctx context.Context, arg2 *domain.HubHotelOrder, arg3 string) ([]*domain.HubRetrieveConfirmationID, enum.BookingStatus, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "tAAdapter.Retrieve")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2, res3 := s.TAAdapter.Retrieve(ctx, arg2, arg3)
	if res3 != nil {
		err, ok := reflect.ValueOf(res3).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.TAAdapter.Retrieve failed")
			span.RecordError(err)
		}
	}
	return res1, res2, res3

}
