package http

import (
	"time"

	"gitlab.deepgate.io/apps/common/adapter/redis"
	"gitlab.deepgate.io/apps/common/http_echo/routeutil"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/partner"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"

	"github.com/labstack/echo/v4"
)

func (s Server) Register(e *echo.Echo, cfg *config.Schema, partnerClient partner.PartnerClient, redis redis.IRedis) {
	headerAuth := Auth(partnerClient)

	authHotelGroup := e.Group("", headerAuth)

	routeutil.AddEndpoint(authHotelGroup, "POST", "/search-destinations", s.SearchDestinations, domain.SearchDestinationReq{}, domain.SearchDestinationRes{}, "", nil)
	routeutil.AddEndpoint(authHotelGroup, "POST", "/search-hotels", s.SearchHotels, domain.HubSearchHotelRequest{}, domain.HubSearchHotelResponse{}, "", nil)
	routeutil.AddEndpoint(authHotelGroup, "POST", "/search-ids", s.SearchHotelList, domain.HubSearchHotelRequest{}, domain.HubSearchHotelResponse{}, "", nil)
	routeutil.AddEndpoint(authHotelGroup, "POST", "/hotel-reviews", s.GetHotelReviews, domain.HubHotelReviewReq{}, domain.HubHotelReviewRes{}, "", nil)
	routeutil.AddEndpoint(authHotelGroup, "POST", "/check-availability", s.CheckAvailability, domain.HubCheckAvailabilityReq{}, domain.HubCheckAvailabilityRes{}, "", nil)

	routeutil.AddEndpoint(authHotelGroup, "POST", "/timeout_test", s.TimeoutTest, domain.TimeoutTestRequest{}, domain.TimeoutTestResponse{}, "", nil)
	routeutil.AddEndpoint(authHotelGroup, "POST", "/price-check", s.PriceCheckHotels, domain.HubPriceCheckReq{}, domain.HubPriceCheckRes{}, "", nil)
	routeutil.AddEndpoint(authHotelGroup, "POST", "/book", s.BookHotels, domain.HubBookReq{}, domain.HubBookRes{}, "", nil)
	routeutil.AddEndpoint(authHotelGroup, "GET", "/retrieve-booking", s.RetrieveBooking, domain.HubRetrieveBookingReq{}, domain.HubRetrieveBookingRes{}, "", nil)
	routeutil.AddEndpoint(authHotelGroup, "POST", "/retrieve-booking", s.RetrieveBooking, domain.HubRetrieveBookingReq{}, domain.HubRetrieveBookingRes{}, "", nil)
	routeutil.AddEndpoint(authHotelGroup, "POST", "/cancel-booking", s.CancelBooking, domain.HubCancelBookingReq{}, nil, "", nil)
	routeutil.AddEndpoint(authHotelGroup, "POST", "/check-booking-cancel", s.CheckBookingCancelStatus, domain.HubCancelBookingReq{}, nil, "", nil)
	routeutil.AddEndpoint(authHotelGroup, "POST", "/get-content-hotel", s.GetHotelDetail, domain.GetHotelDetailReq{}, domain.GetHotelDetailRes{}, "", nil, RateLimitMiddleware(redis, cfg.RateLimitMaxRequests, time.Duration(cfg.RateLimitExpire)*time.Second))

	internalGroup := e.Group("/internal")
	routeutil.AddEndpoint(internalGroup, "POST", "/aggregate-search-place", s.AggregateSearchPlace, nil, nil, "", nil)

	webhookGroup := e.Group("/webhook")
	routeutil.AddEndpoint(webhookGroup, "POST", "/platform/notifications", s.ExpediaWebhookNotification, domain.ExpediaWebhookNotificationRequest{}, domain.ErrorRes{}, "", nil)
	routeutil.AddEndpoint(webhookGroup, "POST", "/ta-booking", s.HandleTAWebhook, domain.TAWebhookMsg{}, domain.ErrorRes{}, "", nil)
}
