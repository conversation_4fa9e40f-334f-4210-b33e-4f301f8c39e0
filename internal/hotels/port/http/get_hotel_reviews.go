package http

import (
	"fmt"
	"net/http"

	"github.com/labstack/echo/v4"
	"gitlab.deepgate.io/apps/common/auth"
	"gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
)

func (s *Server) GetHotelReviews(c echo.Context) error {
	req := &domain.HubHotelReviewReq{
		OfficeID: c.Request().Header.Get(auth.OfficeIDHeaderKey),
		// EndUserIPAddress:    c.Request().Header.Get(constants.HeaderKeyClientIP),
		// EndUserBrowserAgent: c.Request().Header.Get(constants.HeaderKeyClientBrowserAgent),
	}

	// if val, ok := c.Get(constants.ContextEnabledProvidersKey).([]*domain.ProviderConfig); ok {
	// 	req.ProvidersConfig = val
	// }

	// if val, ok := c.Get(constants.ContextDefaultLanguage).(string); ok {
	// 	req.DefaultLanguage = val
	// }

	if err := c.Bind(req); err != nil {
		fmt.Println("bind", err)
		return s.handleErr(errors.WithMsg(errors.ErrInvalidInput, constants.ErrMsgInvalidPayload), c)
	}

	err := validate.Struct(req)
	if err != nil {
		fmt.Println("validate", err)
		return s.validateStructError(err, c)
	}

	res, err := s.app.Commands.GetHotelReview.Handle(c.Request().Context(), req)
	if err != nil {
		return s.handleErr(err, c)
	}

	return c.JSON(http.StatusOK, res)
}
