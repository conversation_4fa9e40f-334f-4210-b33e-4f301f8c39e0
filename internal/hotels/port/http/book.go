package http

import (
	"net/http"

	"github.com/labstack/echo/v4"
	"gitlab.deepgate.io/apps/common/auth"
	"gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/utils"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
)

func (s *Server) BookHotels(c echo.Context) error {
	req := &domain.HubBookReq{
		OfficeID:            s.getApi<PERSON>ey(c),
		HubOfficeID:         s.getOfficeID(c),
		EndUserIPAddress:    c.Request().Header.Get(constants.HeaderKeyClientIP),
		EndUserBrowserAgent: c.Request().Header.Get(constants.HeaderKeyClientBrowserAgent),
	}

	authUser, ok := auth.GetContextUserFromEcho(c)
	if !ok {
		return s.handleErr(errors.ErrPermissionDenied, c)
	}

	if req.EndUserIPAddress == "" {
		return s.handleErr(domain.ErrMissingClientHeaderInfo, c)
	}

	if err := c.Bind(req); err != nil {
		log.Error("Bind req err", log.Any("err", err), log.Any("req", req))
		return s.handleErr(errors.WithMsg(errors.ErrInvalidInput, constants.ErrMsgInvalidPayload), c)
	}

	err := validate.Struct(req)
	if err != nil {
		log.Error("validate err", log.Any("err", err), log.Any("req", req))
		return s.validateStructError(err, c)
	}

	req.AgentID = authUser.Id

	webhookCfg, ok := utils.GetWebhookCfgFromEcho(c)
	if !ok {
		log.Error("Office empty webhook error", log.String("officeID", req.OfficeID))
	}

	res, err := s.app.Commands.BookHotelHandler.Handle(c.Request().Context(), req, webhookCfg)
	if err != nil {
		return s.handleErr(err, c)
	}

	return c.JSON(http.StatusOK, res)
}
