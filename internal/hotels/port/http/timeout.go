package http

import (
	"context"
	"net/http"
	"sync"
	"time"

	"github.com/labstack/echo/v4"
	"gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/command"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
)

func (s *Server) TimeoutTest(c echo.Context) error {
	req := &domain.TimeoutTestRequest{}
	res := &domain.TimeoutTestResponse{
		Status:  "OKE",
		Message: "Success",
	}

	if err := c.Bind(req); err != nil {
		return s.handleErr(errors.WithMsg(errors.ErrInvalidInput, constants.ErrMsgInvalidPayload), c)
	}

	err := validate.Struct(req)
	if err != nil {
		return s.validateStructError(err, c)
	}

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	var wg sync.WaitGroup
	errChan := make(chan error, 1)
	semaphore := make(chan struct{}, 5)
	wg.Add(1)

	countryCodes := []string{
		"AF", "AL", "DZ", "AS", "AD", "AO", "AI", "AQ", "AG", "AR", "AM", "AW", "AU", "AT", "AZ",
		"BS", "BH", "BD", "BB", "BY", "BE", "BZ", "BJ", "BM", "BT", "BO", "BQ", "BA", "BW", "BV",
		"BR", "IO", "BN", "BG", "BF", "BI", "CV", "KH", "CM", "CA", "KY", "CF", "TD", "CL", "CN",
		"CX", "CC", "CO", "KM", "CD", "CG", "CK", "CR", "HR", "CU", "CW", "CY", "CZ", "CI", "DK",
		"DJ", "DM", "DO", "EC", "EG", "SV", "GQ", "ER", "EE", "SZ", "ET", "FK", "FO", "FJ", "FI",
		"FR", "GF", "PF", "TF", "GA", "GM", "GE", "DE", "GH", "GI", "GR", "GL", "GD", "GP", "GU",
		"GT", "GG", "GN", "GW", "GY", "HT", "HM", "VA", "HN", "HK", "HU", "IS", "IN", "ID", "IR",
		"IQ", "IE", "IM", "IL", "IT", "JM", "JP", "JE", "JO", "KZ", "KE", "KI", "KP", "KR", "KW",
		"KG", "LA", "LV", "LB", "LS", "LR", "LY", "LI", "LT", "LU", "MO", "MG", "MW", "MY", "MV",
		"ML", "MT", "MH", "MQ", "MR", "MU", "YT", "MX", "FM", "MD", "MC", "MN", "ME", "MS", "MA",
		"MZ", "MM", "NA", "NR", "NP", "NL", "NC", "NZ", "NI", "NE", "NG", "NU", "NF", "MP", "NO",
		"OM", "PK", "PW", "PS", "PA", "PG", "PY", "PE", "PH", "PN", "PL", "PT", "PR", "QA", "MK",
		"RO", "RU", "RW", "RE", "BL", "SH", "KN", "LC", "MF", "PM", "VC", "WS", "SM", "ST", "SA",
		"SN", "RS", "SC", "SL", "SG", "SX", "SK", "SI", "SB", "SO", "ZA", "GS", "SS", "ES", "LK",
		"SD", "SR", "SJ", "SE", "CH", "SY", "TW", "TJ", "TZ", "TH", "TL", "TG", "TK", "TO", "TT",
		"TN", "TR", "TM", "TC", "TV", "UG", "UA", "AE", "GB", "US", "UM", "UY", "UZ", "VU", "VE",
		"VG", "VI", "WF", "EH", "YE", "ZM", "ZW", "AX", "VN",
	}

	typeRegion := []string{
		"continent",
		"country",
		"province_state",
		"high_level_region",
		"multi_city_vicinity",
		"city",
		"neighborhood",
		"airport",
		"train_station",
		"metro_station",
		"bus_station",
		"point_of_interest",
	}

	reqCountry := req.CountryCodes
	if len(reqCountry) == 0 {
		reqCountry = countryCodes
	}

	for _, country := range reqCountry {
		wg.Add(1)
		semaphore <- struct{}{}
		go func(country string) {
			defer wg.Done()
			defer func() {
				log.Info("Done country", log.Any("country", country))
				<-semaphore
			}()
			for _, v := range typeRegion {
				err = s.app.Commands.AggregateRegionsContentHandler.Handle(ctx, &command.AggregateRegionsContentReq{
					Language:    "vi-VN",
					Include:     "details",
					CountryCode: country,
					Type: []string{
						v,
					},
				})
				if err != nil {
					errChan <- err
					log.Error("AggregateRegionsContentHandler err", log.Any("err", err))
					// return
				}

			}
		}(country)
	}
	wg.Wait()
	close(errChan)
	for err := range errChan {
		if err != nil {
			return err
		}
	}

	time.Sleep(time.Duration(req.DelayDuration) * time.Millisecond)
	log.Info("Aggregate Regions Done", log.Any("req", req))
	return c.JSON(http.StatusOK, res)
}
