package http

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"github.com/labstack/echo/v4"
	"gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/helpers"
)

func (s *Server) HandleTAWebhook(c echo.Context) error {
	raw, err := io.ReadAll(c.Request().Body)
	if err != nil {
		return c.String(http.StatusBadRequest, "Error reading body")
	}
	defer c.Request().Body.Close()

	req := &domain.TAWebhookMsg{}

	sig := c.Request().Header.Get("signature")
	if sig == "" {
		return s.handleErr(domain.ErrInvalidSignature, c)
	}

	ok, err := s.verifySignature(sig, string(raw))
	if err != nil {
		return s.handleErr(err, c)
	}

	if !ok {
		return s.handleErr(domain.ErrInvalidSignature, c)
	}

	if err := json.Unmarshal(raw, req); err != nil {
		return s.handleErr(errors.WithMsg(errors.ErrInvalidInput, constants.ErrMsgInvalidPayload), c)
	}

	err = s.app.Commands.TAWebHookHandler.Handle(c.Request().Context(), req)
	if err != nil {
		return s.handleErr(err, c)
	}

	return c.JSON(http.StatusOK, &domain.ErrorRes{
		IsSuccess: true,
		ErrorCode: "",
		ErrorMsg:  "",
	})
}

func (s *Server) verifySignature(sig string, rawBody string) (bool, error) {
	if s.cfg.Env == "local" {
		return true, nil
	}

	input := fmt.Sprintf("%s.%s", rawBody, s.cfg.TAWebHookKey)

	have := helpers.Md5Hex(input)

	if have != sig {
		log.Error("invalid signature", log.String("have", have), log.String("want", sig), log.String("body", rawBody))
		return false, nil
	}

	return true, nil
}
