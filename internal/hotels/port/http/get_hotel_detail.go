package http

import (
	"net/http"

	"github.com/labstack/echo/v4"
	"gitlab.deepgate.io/apps/common/auth"
	"gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/port/http/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
)

func (s *Server) GetHotelDetail(c echo.Context) error {
	req := &domain.GetHotelDetailReq{
		OfficeID: c.Request().Header.Get(auth.OfficeIDHeaderKey),
	}

	if err := c.Bind(req); err != nil {
		return s.handleErr(errors.WithMsg(errors.ErrInvalidInput, constants.ErrMsgInvalidPayload), c)
	}

	if err := validate.Struct(req); err != nil {
		return s.validateStructError(err, c)
	}

	detail, err := s.app.Queries.GetHotelDetailHandler.Handle(c.Request().Context(), req)
	if err != nil {
		return s.handleErr(err, c)
	}

	// convert rooms to room views
	if detail != nil && detail.Hotel != nil {
		detail.Hotel.DataRooms = converts.ToRoomViews(detail.Hotel.Rooms)
	}

	return c.JSON(http.StatusOK, detail)
}
