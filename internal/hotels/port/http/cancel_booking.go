package http

import (
	"net/http"

	"github.com/labstack/echo/v4"
	"gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/utils"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
)

func (s *Server) CancelBooking(c echo.Context) error {
	req := &domain.HubCancelBookingReq{
		OfficeID: s.getOfficeID(c),
	}

	if err := c.Bind(req); err != nil {
		return s.handleErr(errors.WithMsg(errors.ErrInvalidInput, constants.ErrMsgInvalidPayload), c)
	}

	err := validate.Struct(req)
	if err != nil {
		return s.validateStructError(err, c)
	}

	webhookCfg, ok := utils.GetWebhookCfgFromEcho(c)
	if !ok {
		log.Error("Office empty webhook error", log.String("officeID", req.OfficeID))
	}
	req.WebhookCfg = webhookCfg

	err = s.app.Commands.CancelBookingHandler.Handle(c.Request().Context(), req)
	if err != nil {
		return s.handleErr(err, c)
	}

	return c.JSON(http.StatusNoContent, nil)
}
