package http

import (
	"net/http"

	"github.com/labstack/echo/v4"
	commonDomain "gitlab.deepgate.io/apps/common/domain"
	"gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
)

func (s *Server) SearchDestinations(c echo.Context) error {
	req := &domain.SearchDestinationReq{
		OfficeID: s.getOfficeID(c),

		// EndUserIPAddress:    c.Request().Header.Get(constants.HeaderKeyClientIP),
		// EndUserBrowserAgent: c.Request().Header.Get(constants.HeaderKeyClientBrowserAgent),
	}

	if err := c.Bind(req); err != nil {
		return s.handleErr(errors.WithMsg(errors.ErrInvalidInput, constants.ErrMsgInvalidPayload), c)
	}

	if val, ok := c.Get(constants.ContextEnabledProvidersKey).([]enum.HotelProvider); ok {
		req.EnableProviders = val
	}

	if val, ok := c.Get(constants.ContextDefaultLanguage).(string); ok {
		req.DefaultLanguage = val
	}

	err := validate.Struct(req)
	if err != nil {
		return s.validateStructError(err, c)
	}

	if req.Pagination == nil {
		req.Pagination = &commonDomain.Pagination{
			PageLimit:   100,
			PageCurrent: 1,
		}
	}

	cmdReq := &domain.SearchDestinationReq{
		Query:      req.Query,
		Language:   req.Language,
		Pagination: req.Pagination,
		OrderBy:    req.OrderBy,
	}

	res, err := s.app.Commands.SearchDestinationHandler.Handle(c.Request().Context(), cmdReq)

	if err != nil {
		return s.handleErr(err, c)
	}

	return c.JSON(http.StatusOK, res)

}
