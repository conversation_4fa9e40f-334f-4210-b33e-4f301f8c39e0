package http

import (
	"fmt"
	"strconv"
	"time"

	"github.com/labstack/echo/v4"
	"github.com/samber/lo"
	"gitlab.deepgate.io/apps/common/adapter/redis"
	"gitlab.deepgate.io/apps/common/auth"
	"gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/partner"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
)

func Auth(partnerClient partner.PartnerClient) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			var err error
			officeID, apiKey, partnershipID := c.Request().Header.Get(auth.HubOfficeIDHeaderKey), c.Request().Header.Get(auth.HubAPIKeyHeader), c.Request().Header.Get(auth.PartnershipContextKey)
			if officeID != "" && apiKey != "" && partnershipID != "" {
				err = authMiddlewareV2(partnerClient, c)
			} else {
				err = authMiddlewareV1(partnerClient, c)
			}

			if c.Response().Committed {
				return nil
			}

			if err != nil {
				return err
			}

			return next(c)
		}

	}
}
func authMiddlewareV1(partnerClient partner.PartnerClient, c echo.Context) error {
	officeID, apiKey := c.Request().Header.Get(auth.OfficeIDHeaderKey), c.Request().Header.Get(auth.APIKeyHeader)
	if officeID == "" || apiKey == "" {
		return c.JSON(echo.ErrBadRequest.Code, domain.ErrorRes{
			IsSuccess: false,
			ErrorCode: errors.ErrPermissionDenied.Error(),
			ErrorMsg:  "Missing header authorization.",
		})
	}

	user, err := partnerClient.AuthByOffice(c.Request().Context(), &domain.LoginReq{
		OfficeID: officeID,
		APIKey:   apiKey,
	})
	if err != nil {
		return c.JSON(echo.ErrForbidden.Code, domain.ErrorRes{
			IsSuccess: false,
			ErrorCode: errors.ErrPermissionDenied.Error(),
		})
	}

	if user == nil {
		return c.JSON(echo.ErrForbidden.Code, domain.ErrorRes{
			IsSuccess: false,
			ErrorCode: errors.ErrPermissionDenied.Error(),
		})
	}

	partnerShop, err := partnerClient.GetOfficeInfo(c.Request().Context(), officeID, "")
	if err != nil {
		return c.JSON(echo.ErrNotFound.Code, domain.ErrorRes{
			IsSuccess: false,
			ErrorCode: errors.ErrNotFound.Error(),
		})
	}

	if partnerShop == nil {
		return c.JSON(echo.ErrNotFound.Code, domain.ErrorRes{
			IsSuccess: false,
			ErrorCode: errors.ErrNotFound.Error(),
		})
	}

	c = auth.SetContextUserFromEcho(c, auth.User{
		Id:            user.ID,
		Name:          user.Name,
		Email:         user.Email,
		PartnershipId: user.PartnershipID,
		PartnerShopId: user.PartnerShopID,
	})

	c.Set(constants.ContextWebhookKey, &domain.WebhookCfg{
		WebhookKey: user.WebhookCfg.WebhookKey,
		WebhookURLCfg: domain.WebhookURLCfg{
			Transaction: user.WebhookCfg.WebhookURLCfg.Transaction,
		},
	})

	if partnerShop.Hotel != nil {
		enableProviders := lo.Filter(partnerShop.Hotel.ProviderConfigs, func(item *domain.ProviderConfig, _ int) bool { return item.Enable })

		c.Set(constants.ContextEnabledProvidersKey, lo.Map(enableProviders, func(item *domain.ProviderConfig, _ int) enum.HotelProvider { return item.Provider }))
		c.Set(constants.ContextDefaultLanguage, partnerShop.Hotel.DefaultLanguage)

		if partnerShop.Hotel.PriceConfig == nil {
			return c.JSON(echo.ErrNotFound.Code, domain.ErrorRes{
				IsSuccess: false,
				ErrorCode: "MISSING_PARTNER_SHOP_CONFIG",
			})
		}

		c.Set(constants.ContextPriceConditionConfig, partnerShop.Hotel.PriceConfig)
	}

	return nil
}

func authMiddlewareV2(partnerClient partner.PartnerClient, c echo.Context) error {
	// AUTH Agent
	officeID, apiKey, partnershipID := c.Request().Header.Get(auth.OfficeIDHeaderKey), c.Request().Header.Get(auth.APIKeyHeader), c.Request().Header.Get(auth.PartnershipContextKey)
	if officeID == "" || apiKey == "" {
		return c.JSON(echo.ErrBadRequest.Code, domain.ErrorRes{
			IsSuccess: false,
			ErrorCode: errors.ErrPermissionDenied.Error(),
			ErrorMsg:  "Missing header authorization.",
		})
	}

	user, err := partnerClient.AuthByOffice(c.Request().Context(), &domain.LoginReq{
		OfficeID:      officeID,
		APIKey:        apiKey,
		PartnershipID: partnershipID,
	})
	if err != nil {
		return c.JSON(echo.ErrForbidden.Code, domain.ErrorRes{
			IsSuccess: false,
			ErrorCode: errors.ErrPermissionDenied.Error(),
		})
	}

	if user == nil {
		return c.JSON(echo.ErrForbidden.Code, domain.ErrorRes{
			IsSuccess: false,
			ErrorCode: errors.ErrPermissionDenied.Error(),
		})
	}

	subPartnerShop, err := partnerClient.GetOfficeInfo(c.Request().Context(), officeID, partnershipID)
	if err != nil {
		return c.JSON(echo.ErrNotFound.Code, domain.ErrorRes{
			IsSuccess: false,
			ErrorCode: errors.ErrNotFound.Error(),
		})
	}

	if subPartnerShop == nil {
		return c.JSON(echo.ErrNotFound.Code, domain.ErrorRes{
			IsSuccess: false,
			ErrorCode: errors.ErrNotFound.Error(),
		})
	}

	// c = auth.SetContextUserFromEcho(c, auth.User{
	// 	Id:            user.ID,
	// 	Name:          user.Name,
	// 	Email:         user.Email,
	// 	PartnershipId: user.PartnershipID,
	// 	PartnerShopId: user.PartnerShopID,
	// })

	// c.Set(constants.ContextWebhookKey, &domain.WebhookCfg{
	// 	WebhookKey: user.WebhookCfg.WebhookKey,
	// 	WebhookURLCfg: domain.WebhookURLCfg{
	// 		Transaction: user.WebhookCfg.WebhookURLCfg.Transaction,
	// 	},
	// })

	if subPartnerShop.Hotel != nil {
		// c.Set(constants.AgentEnabledProvidersKey, partnerShop.Hotel.ProviderConfigs)
		c.Set(constants.ContextDefaultLanguage, subPartnerShop.Hotel.DefaultLanguage)
	}

	// Auth For HUB
	hubOfficeID, hubApiKey := c.Request().Header.Get(auth.HubOfficeIDHeaderKey), c.Request().Header.Get(auth.HubAPIKeyHeader)
	if officeID == "" || apiKey == "" {
		return c.JSON(echo.ErrBadRequest.Code, domain.ErrorRes{
			IsSuccess: false,
			ErrorCode: errors.ErrPermissionDenied.Error(),
			ErrorMsg:  "Missing header authorization.",
		})
	}

	user, err = partnerClient.AuthByOffice(c.Request().Context(), &domain.LoginReq{
		OfficeID: hubOfficeID,
		APIKey:   hubApiKey,
	})
	if err != nil {
		return c.JSON(echo.ErrForbidden.Code, domain.ErrorRes{
			IsSuccess: false,
			ErrorCode: errors.ErrPermissionDenied.Error(),
		})
	}

	if user == nil {
		return c.JSON(echo.ErrForbidden.Code, domain.ErrorRes{
			IsSuccess: false,
			ErrorCode: errors.ErrPermissionDenied.Error(),
		})
	}

	hubPartnerShop, err := partnerClient.GetOfficeInfo(c.Request().Context(), hubOfficeID, "")
	if err != nil {
		return c.JSON(echo.ErrNotFound.Code, domain.ErrorRes{
			IsSuccess: false,
			ErrorCode: errors.ErrNotFound.Error(),
		})
	}

	if hubPartnerShop == nil {
		return c.JSON(echo.ErrNotFound.Code, domain.ErrorRes{
			IsSuccess: false,
			ErrorCode: errors.ErrNotFound.Error(),
		})
	}

	c = auth.SetContextUserFromEcho(c, auth.User{
		Id:            user.ID,
		Name:          user.Name,
		Email:         user.Email,
		PartnershipId: user.PartnershipID,
		PartnerShopId: user.PartnerShopID,
	})

	c.Set(constants.ContextWebhookKey, &domain.WebhookCfg{
		WebhookKey: user.WebhookCfg.WebhookKey,
		WebhookURLCfg: domain.WebhookURLCfg{
			Transaction: user.WebhookCfg.WebhookURLCfg.Transaction,
		},
	})

	if subPartnerShop.Hotel != nil {
		enableProviders := lo.Filter(subPartnerShop.Hotel.ProviderConfigs, func(item *domain.ProviderConfig, _ int) bool { return item.Enable })
		if hubPartnerShop.Hotel != nil {
			hubEnableProviders := lo.Filter(hubPartnerShop.Hotel.ProviderConfigs, func(item *domain.ProviderConfig, _ int) bool { return item.Enable })
			enableProviders = mergeProviderConfigs(enableProviders, hubEnableProviders)
		}
		c.Set(constants.ContextEnabledProvidersKey, lo.Map(enableProviders, func(item *domain.ProviderConfig, _ int) enum.HotelProvider { return item.Provider }))
		c.Set(constants.ContextDefaultLanguage, subPartnerShop.Hotel.DefaultLanguage)

		if subPartnerShop.Hotel.PriceConfig == nil {
			return c.JSON(echo.ErrNotFound.Code, domain.ErrorRes{
				IsSuccess: false,
				ErrorCode: "MISSING_PARTNER_SHOP_CONFIG",
			})
		}

		c.Set(constants.ContextPriceConditionConfig, subPartnerShop.Hotel.PriceConfig)
	}

	return nil
}
func mergeProviderConfigs(primary, secondary []*domain.ProviderConfig) []*domain.ProviderConfig {
	providerMap := make(map[enum.HotelProvider]*domain.ProviderConfig)

	for _, p := range primary {
		providerMap[p.Provider] = p
	}

	for _, p := range secondary {
		if _, exists := providerMap[p.Provider]; exists {
			providerMap[p.Provider] = p
		}
	}

	result := make([]*domain.ProviderConfig, 0, len(providerMap))
	for _, p := range primary {
		if val, exists := providerMap[p.Provider]; exists {
			result = append(result, val)
		}
	}

	return result
}

func RateLimitMiddleware(redis redis.IRedis, maxReqs int64, timeExpire time.Duration) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			officeID := c.Request().Header.Get(auth.OfficeIDHeaderKey)
			if officeID == "" {
				return c.JSON(echo.ErrBadRequest.Code, domain.ErrorRes{
					IsSuccess: false,
					ErrorCode: errors.ErrPermissionDenied.Error(),
					ErrorMsg:  "Missing header authorization.",
				})
			}

			var (
				key   = fmt.Sprintf("rate_limit:%s:%s", c.Path(), officeID)
				count = maxReqs - 1
			)

			ttl, err := redis.CMD().TTL(c.Request().Context(), key).Result()
			if err != nil {
				return c.JSON(echo.ErrInternalServerError.Code, domain.ErrorRes{
					IsSuccess: false,
					ErrorCode: errors.ErrInternalServer.Error(),
				})
			}

			if ttl <= 0 {
				err = redis.SetWithExpiration(key, maxReqs-1, timeExpire)
				if err != nil {
					return c.JSON(echo.ErrInternalServerError.Code, domain.ErrorRes{
						IsSuccess: false,
						ErrorCode: errors.ErrInternalServer.Error(),
					})
				}
				ttl = timeExpire
			} else {
				count, err = redis.CMD().Decr(c.Request().Context(), key).Result()
				if err != nil {
					return c.JSON(echo.ErrInternalServerError.Code, domain.ErrorRes{
						IsSuccess: false,
						ErrorCode: errors.ErrInternalServer.Error(),
					})
				}

				if count < 0 {
					resetTime := time.Now().Add(ttl).Unix() // Tính thời gian reset
					c.Response().Header().Set("x-RateLimit-Limit", strconv.Itoa(int(maxReqs)))
					c.Response().Header().Set("x-RateLimit-Remaining", strconv.Itoa(0))
					c.Response().Header().Set("x-RateLimit-Reset", strconv.FormatInt(resetTime, 10))
					return c.JSON(echo.ErrTooManyRequests.Code, domain.ErrorRes{
						IsSuccess: false,
						ErrorCode: domain.ErrTooManyRequests.Error(),
					})
				}
			}

			resetTime := time.Now().Add(ttl).Unix()

			c.Response().Header().Set("x-RateLimit-Limit", strconv.Itoa(int(maxReqs)))
			c.Response().Header().Set("x-RateLimit-Remaining", strconv.Itoa(int(count)))
			c.Response().Header().Set("x-RateLimit-Reset", strconv.FormatInt(resetTime, 10))

			return next(c)
		}
	}
}
