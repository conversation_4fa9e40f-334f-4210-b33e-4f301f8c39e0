package http

import (
	"fmt"
	"net/http"

	"github.com/go-playground/validator/v10"
	"github.com/labstack/echo/v4"
	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/auth"
	commonErrs "gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
)

var validate *validator.Validate

type Server struct {
	cfg *config.Schema
	app app.Application
}

func New(cfg *config.Schema, app app.Application) *Server {
	validate = validator.New(validator.WithRequiredStructEnabled())

	return &Server{cfg, app}
}

func (s Server) handleErr(err error, c echo.Context) error {
	if err == nil {
		return err
	}

	err = errors.Cause(err)

	if appErr, ok := err.(*commonErrs.Error); ok {
		msg := appErr.Msg()
		if msg == "" {
			msg = constants.AppErrMsg[appErr.Code()]
		}

		return c.JSON(int(appErr.Category()), &domain.ErrorRes{
			IsSuccess: false,
			ErrorCode: appErr.Code(),
			ErrorMsg:  msg,
		})
	}

	return c.JSON(http.StatusInternalServerError, &domain.ErrorRes{
		IsSuccess: false,
		ErrorCode: commonErrs.ErrSomethingOccurred.Error(),
	})
}

func (s Server) validateStructError(err error, c echo.Context) error {
	if _, ok := err.(*validator.InvalidValidationError); ok {
		return c.JSON(http.StatusInternalServerError, &domain.ErrorRes{
			IsSuccess: false,
			ErrorCode: commonErrs.ErrSomethingOccurred.Error(),
			ErrorMsg:  err.Error(),
		})
	}

	errs := err.(validator.ValidationErrors)
	if len(errs) > 0 {
		err := errs[0]

		strErr := fmt.Sprintf("%s %s", err.StructField(), err.Tag())
		msg := constants.AppErrMsg[strErr]
		if msg == "" {
			msg = strErr
		}

		return c.JSON(http.StatusBadRequest, &domain.ErrorRes{
			IsSuccess: false,
			ErrorCode: commonErrs.ErrInvalidInput.Error(),
			ErrorMsg:  msg,
		})
	}

	return c.JSON(http.StatusInternalServerError, &domain.ErrorRes{
		IsSuccess: false,
		ErrorCode: err.Error(),
	})
}

func (s Server) getOfficeID(c echo.Context) string {
	// get office id agent(hub) or subagent(service)
	officeID := c.Request().Header.Get(auth.HubOfficeIDHeaderKey)
	if officeID == "" {
		officeID = c.Request().Header.Get(auth.OfficeIDHeaderKey)
	}

	return officeID
}

func (s Server) getApiKey(c echo.Context) string {
	// get office id agent(hub) or subagent(service)
	apiKey := c.Request().Header.Get(auth.OfficeIDHeaderKey)
	if apiKey == "" {
		apiKey = c.Request().Header.Get(auth.HubAPIKeyHeader)
	}

	return apiKey
}
