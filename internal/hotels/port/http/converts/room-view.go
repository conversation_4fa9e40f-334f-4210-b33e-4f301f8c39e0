package converts

import "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"

// ToRoomViews converts []*domain.Room to []*domain.RoomView
func ToRoomViews(rooms []*domain.Room) []*domain.RoomView {
	var roomViews []*domain.RoomView
	for _, room := range rooms {
		if room == nil {
			continue
		}

		roomViews = append(roomViews, ToRoomView(room))
	}

	return roomViews
}

// ToRoomView converts *domain.Room to *domain.RoomView
func ToRoomView(room *domain.Room) *domain.RoomView {
	return &domain.RoomView{
		RoomID:       room.RoomID,
		Name:         room.Name,
		Descriptions: room.Descriptions,
		Amenities:    room.Amenities,
		Occupancy:    room.Occupancy,
		Area:         room.Area,
		Views:        room.Views,
		Images:       room.Images,
	}
}
