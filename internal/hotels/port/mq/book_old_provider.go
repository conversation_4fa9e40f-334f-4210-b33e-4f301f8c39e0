package mq

import (
	"context"
	"encoding/json"
	"time"

	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/apps/common/schemas"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

func (c *consumer) bookOldProvider(data []byte) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Minute*5)
	defer cancel()
	var msg *schemas.BookOldProviderMsg
	err := json.Unmarshal(data, &msg)
	if err != nil {
		log.Error("Consumer bookOldProvider json.Unmarshal", log.Any("error", err), log.Any("msg", msg))
		return
	}

	cmdReq := &domain.HubBookOldProviderReq{
		OrderCode: msg.OrderCode,
		ID:        "",
	}

	err = c.app.Commands.BookOldProviderHandler.Handle(ctx, cmdReq)
	if err != nil {
		log.Error("Consumer BookOldProviderHandler.Handle", log.Any("error", err), log.Any("msg", msg), log.Any("cmdReq", cmdReq))
		return
	}

	log.Info("Consumer bookOldProvider ok", log.Any("msg", msg))
}
