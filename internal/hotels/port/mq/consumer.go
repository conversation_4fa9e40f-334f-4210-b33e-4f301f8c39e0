package mq

import (
	"gitlab.deepgate.io/apps/common/adapter/rabbitmq"
	commonConstants "gitlab.deepgate.io/apps/common/constants"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app"
)

type consumer struct {
	app app.Application
}

type Consumer interface {
	GetHandlers() []rabbitmq.ConsumerHandler
}

func NewConsumer(application app.Application) Consumer {
	return &consumer{
		app: application,
	}
}

func (c *consumer) GetHandlers() []rabbitmq.ConsumerHandler {
	return []rabbitmq.ConsumerHandler{
		{
			RoutingKey: commonConstants.SkyhubHotelsBookOldProviderKey,
			Processor:  c.bookOldProvider,
		},
	}
}
