package background

import (
	"context"
	"time"

	"github.com/go-co-op/gocron"
	"gitlab.deepgate.io/apps/common/log"
)

func (c *cronjob) registerProcessCancelingBooking(s *gocron.Scheduler, scheduled string) {
	const jobTimeout = time.Minute * 2

	_, err := s.Cron(scheduled).Do(func() {
		ctx, cancel := context.WithTimeout(context.Background(), jobTimeout)
		defer cancel()

		c.processCancelingBooking(ctx)
	})
	if err != nil {
		log.Fatal("cronjob do error", log.Any("error", err))
		return
	}
}

func (c *cronjob) processCancelingBooking(ctx context.Context) {
	log.Info("[cronjob] start processCancelingBooking...")

	err := c.app.Commands.ProcessCancelingBookingHandler.Handle(ctx)
	if err != nil {
		log.Fatal("cronjob do error", log.Any("error", err))
		return
	}
	log.Info("[cronjob] end processCancelingBooking...")

}
