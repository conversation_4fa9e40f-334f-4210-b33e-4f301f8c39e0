package admin

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	"gitlab.deepgate.io/apps/api/gen/go/base"
	pb "gitlab.deepgate.io/apps/api/gen/go/skyhub/admin"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/command"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
)

type Range struct {
	From int `json:"from"`
	To   int `json:"to"`
}

func splitContent(arrayLength int) []Range {
	total := 20844335
	var ranges []Range

	// Calculate the step size
	step := total / arrayLength

	for i := 0; i < arrayLength; i++ {
		from := i * step
		to := from + step - 1

		// Ensure the last range does not exceed the total
		if i == arrayLength-1 {
			to = total
		}

		// Append the range to the slice
		ranges = append(ranges, struct {
			From int `json:"from"`
			To   int `json:"to"`
		}{From: from, To: to})
	}

	return ranges
}

// AggregateHotelContent nhận và xử lý dữ liệu JSON từ client
func (s *AdminServer) MigrateHotelNameContent(ctx context.Context, input *pb.MigrateHotelNameContentReq) (*base.BasicRes, error) {
	var wg sync.WaitGroup
	errChan := make(chan error, 1)
	concurrency := 100
	contentRange := splitContent(concurrency)
	// Semaphore giới hạn số lượng goroutine đồng thời
	semaphore := make(chan struct{}, concurrency)
	prefixPool := make(chan int, concurrency)
	for i, data := range contentRange {
		prefixPool <- i // Đưa các giá trị prefix vào pool

		cmdReq := &command.MigrateData{
			Page:           i + 1,
			From:           data.From,
			To:             data.To,
			TargetLanguage: input.TargetLanguage,
			SourceLanguage: input.SourceLanguage,
		}

		wg.Add(1)
		semaphore <- struct{}{} // Chặn khi số lượng goroutine đạt tối đa
		prefix := <-prefixPool
		// Truyền giá trị prefix (i) vào hàm Handle
		go func(cmdReq *command.MigrateData, prefix int) {
			defer wg.Done() // Đảm bảo wg.Done() được gọi khi goroutine hoàn thành
			defer func() {
				fmt.Println("prefix done")
				<-semaphore
				prefixPool <- prefix
			}() // Giải phóng semaphore khi xong việc
			ctx, ctxCancel := context.WithTimeout(context.Background(), 100*time.Minute)
			defer ctxCancel()

			// Truyền prefix vào Handle
			err := s.app.Commands.AggregateHotelContentHandler.Handle(ctx, &command.AggregateHotelContentReq{
				Migrate:  cmdReq,
				Provider: enum.HotelProvider(input.Provider),
			})
			if err != nil {
				log.Printf("Error processing property with prefix %d", prefix)
				// Gửi phản hồi lỗi cho stream
				errChan <- err
			}
		}(cmdReq, prefix) // Truyền i (prefix) vào đây
	}

	// Chờ tất cả goroutine hoàn thành
	wg.Wait()
	close(errChan)

	// Kiểm tra và trả về lỗi nếu có
	for err := range errChan {
		if err != nil {
			return nil, err
		}
	}

	return nil, nil

}
