package admin

import (
	"context"

	"gitlab.deepgate.io/apps/api/gen/go/base"
	pb "gitlab.deepgate.io/apps/api/gen/go/skyhub/admin"
	"gitlab.deepgate.io/apps/common/auth"
	"gitlab.deepgate.io/apps/common/errors"
)

func (s *AdminServer) AggregateHotelTransaction(ctx context.Context, req *pb.AggregateHotelTransactionReq) (*base.Empty, error) {
	pID, ok := auth.GetContextPartnershipId(ctx)
	if !ok {
		return nil, errors.ErrInvalidPartnership
	}

	err := s.app.Commands.AggregateHotelTransactionHandler.Handle(ctx, pID, req.OfficeId)
	if err != nil {
		return nil, err
	}

	return nil, nil
}
