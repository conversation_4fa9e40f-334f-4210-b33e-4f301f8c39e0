package admin

import (
	"gitlab.deepgate.io/apps/api/gen/go/skyhub/admin"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
)

type AdminServer struct {
	admin.UnimplementedHotelAdminServiceServer
	admin.UnimplementedAdminServiceServer
	app app.Application
	cfg *config.Schema
}

func NewAdminServer(application app.Application, cfg *config.Schema) *AdminServer {
	return &AdminServer{app: application, cfg: cfg}
}

// func (s *AdminServer) handleError(err error) string {
// 	if err == nil {
// 		return ""
// 	}

// 	err = errors.Cause(err)
// 	if appErr, ok := err.(*commonError.Error); ok {
// 		return appErr.Error()
// 	}

// 	return commonError.ErrSomethingOccurred.Error()
// }
