package backend

import (
	"context"

	"gitlab.deepgate.io/apps/api/gen/go/skyhub/backend"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/port/grpc/converts"
)

func (s *BackendServer) UpdateOrderStatus(ctx context.Context, req *backend.UpdateOrderStatusRequest) (*backend.UpdateOrderStatusResponse, error) {
	bookingStatus := converts.ConvertBookingStatus(req.Status)
	if bookingStatus == enum.BookingStatusNone {
		return &backend.UpdateOrderStatusResponse{
			IsSuccess: false,
			ErrorCode: domain.ErrInvalidBookingStatus.Error(),
		}, nil
	}

	cmdReq := &domain.UpdateOrderStatusRequest{
		OrderCode:      req.OrderCode,
		Status:         bookingStatus,
		OccupancyInfos: converts.FromDomainOccupancyUpdateInfos(req.OccupancyUpdates),
	}

	if err := s.app.Commands.UpdateOrderStatusHandler.Handle(ctx, cmdReq); err != nil {
		return &backend.UpdateOrderStatusResponse{
			IsSuccess: false,
			ErrorCode: s.handleError(err),
		}, nil
	}

	return &backend.UpdateOrderStatusResponse{
		IsSuccess: true,
	}, nil
}
