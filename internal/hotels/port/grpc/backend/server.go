package backend

import (
	"github.com/pkg/errors"
	commonError "gitlab.deepgate.io/apps/common/errors"

	"gitlab.deepgate.io/apps/api/gen/go/skyhub/backend"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
)

type BackendServer struct {
	backend.UnimplementedHotelServiceServer
	app app.Application
	cfg *config.Schema
}

func NewBackendServer(application app.Application, cfg *config.Schema) *BackendServer {
	return &BackendServer{app: application, cfg: cfg}
}

func (s *BackendServer) handleError(err error) string {
	if err == nil {
		return ""
	}

	err = errors.Cause(err)
	if appErr, ok := err.(*commonError.Error); ok {
		return appErr.Error()
	}

	return commonError.ErrSomethingOccurred.Error()
}
