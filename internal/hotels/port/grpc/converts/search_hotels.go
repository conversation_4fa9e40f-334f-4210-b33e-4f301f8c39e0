package converts

import (
	"gitlab.deepgate.io/apps/api/gen/go/skyhub"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

func ToHotelInfoProto(in []*domain.Place) []*skyhub.HotelInfo {
	result := make([]*skyhub.HotelInfo, 0, len(in))
	for _, place := range in {
		locationType := skyhub.HotelLocationType_HotelLocationTypeNone
		if place.CountryCode != "VN" {
			locationType = skyhub.HotelLocationType_International
		} else {
			locationType = skyhub.HotelLocationType_Domestic
		}

		result = append(result, &skyhub.HotelInfo{
			Id:          place.PlaceID,
			Name:        place.Name,
			Location:    locationType,
			Address:     place.Address,
			CountryCode: place.CountryCode,
		})
	}

	return result
}
