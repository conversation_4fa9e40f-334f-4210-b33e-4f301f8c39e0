package web_partnership

import (
	"context"

	"gitlab.deepgate.io/apps/api/gen/go/skyhub/web_partnership"
	"gitlab.deepgate.io/apps/common/auth"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/apps/common/errors"
	commonHelpers "gitlab.deepgate.io/apps/common/helpers"

	partnershipConverts "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/port/grpc/web_partnership/converts"
)

func (s *WebPartnershipServer) ExportOrders(ctx context.Context, req *web_partnership.ExportOrdersReq) (*web_partnership.ExportOrdersRes, error) {
	user, ok := auth.GetContextUser(ctx)
	if !ok {
		return nil, errors.ErrInvalidToken
	}

	if isContain := commonHelpers.Contains[string](user.Roles, commonEnum.UserRoleName[commonEnum.UserRoleOfficeManager]); !isContain {
		return nil, errors.ErrPermissionDenied
	}

	reqFilter := partnershipConverts.FromDomainListOrderReq(req.Filter)
	res, err := s.app.Queries.ListOrderByFilterHandler.Handle(ctx, reqFilter)
	if err != nil {
		return &web_partnership.ExportOrdersRes{
			IsSuccess: false,
			ErrorCode: err.Error(),
		}, nil
	}

	return &web_partnership.ExportOrdersRes{
		IsSuccess: true,
		Items:     partnershipConverts.ToProtoOrderBasicInfos(res),
	}, nil
}
