package web_partnership

import (
	"context"

	"gitlab.deepgate.io/apps/api/gen/go/skyhub/web_partnership"
	"gitlab.deepgate.io/apps/common/auth"
	commonDomain "gitlab.deepgate.io/apps/common/domain"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/apps/common/errors"
	commonHelpers "gitlab.deepgate.io/apps/common/helpers"

	partnershipConverts "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/port/grpc/web_partnership/converts"
)

func (s *WebPartnershipServer) ListHiddenFee(ctx context.Context, req *web_partnership.ListHiddenFeeReq) (*web_partnership.ListHiddenFeeRes, error) {
	user, ok := auth.GetContextUser(ctx)
	if !ok {
		return nil, errors.ErrInvalidToken
	}

	if isContain := commonHelpers.Contains[string](user.Roles, commonEnum.UserRoleName[commonEnum.UserRoleSkyAdmin]); !isContain {
		return nil, errors.ErrPermissionDenied
	}

	reqFilter := partnershipConverts.FromDomainListHiddenServiceFeeReq(req.Filter)
	reqFilter.Pagination = commonDomain.ToPagingDomain(req.Pagination)
	res, err := s.app.Queries.ListConfigHiddenFeeHandler.Handle(ctx, reqFilter)
	if err != nil {
		return &web_partnership.ListHiddenFeeRes{
			IsSuccess: false,
			ErrorCode: err.Error(),
		}, nil
	}

	return &web_partnership.ListHiddenFeeRes{
		IsSuccess:  true,
		HiddenFees: partnershipConverts.ToProtoListHiddenServiceFee(res),
		Pagination: commonDomain.ToPagingProto(reqFilter.Pagination),
	}, nil
}
