package web_partnership

import (
	"context"

	"gitlab.deepgate.io/apps/api/gen/go/base"
	"gitlab.deepgate.io/apps/api/gen/go/skyhub/web_partnership"
	"gitlab.deepgate.io/apps/common/auth"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/apps/common/errors"
	commonHelpers "gitlab.deepgate.io/apps/common/helpers"
)

func (s *WebPartnershipServer) DeleteHiddenFee(ctx context.Context, req *web_partnership.DeleteHiddenFeeReq) (*base.BasicRes, error) {
	user, ok := auth.GetContextUser(ctx)
	if !ok {
		return nil, errors.ErrInvalidToken
	}

	if isContain := commonHelpers.Contains[string](user.Roles, commonEnum.UserRoleName[commonEnum.UserRoleSkyAdmin]); !isContain {
		return nil, errors.ErrPermissionDenied
	}

	err := s.app.Commands.DeleteConfigHiddenFeeHandler.Handle(ctx, req.Ids, user.Id)
	if err != nil {
		return &base.BasicRes{
			IsSuccess: false,
			ErrorCode: err.Error(),
		}, nil
	}

	return &base.BasicRes{
		IsSuccess: true,
	}, nil
}
