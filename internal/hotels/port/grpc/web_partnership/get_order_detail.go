package web_partnership

import (
	"context"

	"gitlab.deepgate.io/apps/api/gen/go/skyhub/web_partnership"
	"gitlab.deepgate.io/apps/common/auth"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/apps/common/errors"
	commonHelpers "gitlab.deepgate.io/apps/common/helpers"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/port/grpc/web_partnership/converts"
)

func (s *WebPartnershipServer) GetOrderDetail(ctx context.Context, req *web_partnership.GetOrderDetailRequest) (*web_partnership.GetOrderDetailResponse, error) {
	user, ok := auth.GetContextUser(ctx)
	if !ok {
		return nil, errors.ErrInvalidToken
	}

	if isContain := commonHelpers.Contains[string](user.Roles, commonEnum.UserRoleName[commonEnum.UserRoleOfficeManager]); !isContain {
		return nil, errors.ErrPermissionDenied
	}

	res, hotelContent, err := s.app.Queries.GetDetailOrderByIDHandler.Handle(ctx, req.Id)
	if err != nil {
		return &web_partnership.GetOrderDetailResponse{
			IsSuccess: false,
			ErrorCode: err.Error(),
		}, nil
	}

	return &web_partnership.GetOrderDetailResponse{
		IsSuccess: true,
		Data:      converts.FromDomainOrderDetail(res, hotelContent),
	}, nil
}
