package converts

import (
	"github.com/samber/lo"
	"gitlab.deepgate.io/apps/api/gen/go/skyhub"
	"gitlab.deepgate.io/apps/api/gen/go/skyhub/web_partnership"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/helpers"
)

func FromHotelBookingStatuses(ins []skyhub.HotelBookingStatus) []enum.BookingStatus {
	return lo.Map(ins, func(item skyhub.HotelBookingStatus, _ int) enum.BookingStatus {
		return enum.BookingStatusIntMap[int32(item)]
	})
}

func FromDomainListOrderReq(info *web_partnership.ListOrdersFilter) *domain.ListOrderFilter {
	if info == nil {
		return nil
	}

	return &domain.ListOrderFilter{
		BookingStatuses: FromHotelBookingStatuses(info.BookingStatuses),
		From:            info.From,
		To:              info.To,
		OfficeID:        info.OfficeId,
		OrderCode:       info.OrderCode,
		NotInStatuses:   FromHotelBookingStatuses(info.NotInBookingStatuses),
	}
}

func ToProtoOrderBasicInfos(infos []*domain.HubHotelOrder) []*skyhub.OrderBasicInfo {
	if len(infos) == 0 {
		return nil
	}

	res := make([]*skyhub.OrderBasicInfo, 0)
	for _, item := range infos {
		res = append(res, ToHubOrderBasicInfo(item))
	}

	return res
}

func ToHubOrderBasicInfo(info *domain.HubHotelOrder) *skyhub.OrderBasicInfo {
	if info == nil {
		return nil
	}

	res := &skyhub.OrderBasicInfo{
		OrderCode: info.OrderCode,
		Id:        info.ID,
		Status:    skyhub.HotelBookingStatus(enum.BookingStatusNumber[info.BookingStatus]),
		AgentCode: info.AgentCode,
		Refunded:  info.Refunded,
		CreatedAt: info.CreatedAt,
	}

	if info.RefundData != nil {
		res.RefundAmount = info.RefundData.RefundAmount
	}

	if info.ExchangedRateDataCf != nil {
		res.Amount = info.ExchangedRateDataCf.PayNow
	}

	if info.Hotel != nil && len(info.Hotel.ListRooms) > 0 {
		res.ConfirmationId = info.Hotel.ListRooms[0].ConfirmationID
	}

	return res
}

func FromDomainOrderDetail(hubOrder *domain.HubHotelOrder, hotel *domain.Hotel) *skyhub.OrderDetail {
	if hubOrder == nil {
		return nil
	}

	res := &skyhub.OrderDetail{
		Id:        hubOrder.ID,
		OrderCode: hubOrder.OrderCode,
		CreatedAt: hubOrder.CreatedAt,
		Status:    skyhub.HotelBookingStatus(enum.BookingStatusNumber[hubOrder.BookingStatus]),
		RateCf:    FromDomainHubRateData(hubOrder.ExchangedRateDataCf),
	}

	if hubOrder.HotelSearchRequest != nil {
		res.TotalNights = int32(hubOrder.HotelSearchRequest.Stay.DayCount)
		res.CheckInDate = hubOrder.HotelSearchRequest.Stay.CheckIn
		res.CheckOutDate = hubOrder.HotelSearchRequest.Stay.CheckOut
	}

	if hotel != nil {
		if hotel.CheckIn != nil {
			res.CheckInTime = hotel.CheckIn.BeginTime
			res.CheckInInstructions = append(res.CheckInInstructions, helpers.SplitHTMLContentByTag(hotel.CheckIn.Instructions, helpers.TagLi)...)
			res.CheckInInstructions = append(res.CheckInInstructions, helpers.SplitIntoSentences(hotel.CheckIn.SpecialInstructions)...)
		}
		if hotel.Checkout != nil {
			res.CheckOutTime = hotel.Checkout.Time
		}

	}

	if hubOrder.ExchangedRateDataCf != nil || hubOrder.RateData != nil {
		rateData := hubOrder.ExchangedRateDataCf
		if rateData == nil {
			rateData = hubOrder.RateData
		}

		// checkInStr := helpers.ConvertToVietnameseDateFormat(fmt.Sprintf("%sT%s", res.CheckInDate, res.CheckInTime))
		// res.CancelPolicies = domain.MappingCancelPolicyToStrings(rateData, checkInStr)
		res.Amenities = rateData.GetAmenityNames()
	}

	if hubOrder.Hotel != nil {
		room := make([]*skyhub.BookingRoom, 0)

		for _, item := range hubOrder.Hotel.ListRooms {
			roomProto := &skyhub.BookingRoom{
				RoomId:         item.RoomID,
				Name:           item.Name,
				ConfirmationId: item.ConfirmationID,
				Holder:         FromDomainHolder(item),
			}
			if item.BedOption != nil {
				roomProto.BedOption = &skyhub.BedOption{
					OptionId: item.BedOption.OptionID,
					Name:     item.BedOption.Name,
				}
			}
			room = append(room, roomProto)
		}

		res.Rooms = room

		res.HotelInfo = &skyhub.HotelBasicInfo{
			Id:      hubOrder.Hotel.HotelID,
			Name:    hubOrder.Hotel.Name,
			Address: FromDomainAddress(hubOrder.Hotel.Address),
			Rating:  hubOrder.Hotel.Rating,
		}
	}
	return res
}

func FromDomainAddress(address *domain.Address) *skyhub.Address {
	if address == nil {
		return nil
	}
	return &skyhub.Address{
		City:        address.City,
		CountryCode: address.CountryCode,
		Line_1:      address.Line1,
	}
}

func FromDomainHolder(room *domain.HubOrderRoomItem) *skyhub.RoomHolder {
	if room == nil {
		return nil
	}

	return &skyhub.RoomHolder{
		GivenName: room.GivenName,
		Surname:   room.Surname,
		Email:     room.Email,
	}
}
func FromDomainHubRateData(rateData *domain.HubRateData) *skyhub.HubRateData {
	if rateData == nil {
		return nil
	}

	return &skyhub.HubRateData{
		RateId:          rateData.RateID,
		PayNow:          rateData.PayNow,
		TotalPayAtHotel: rateData.TotalPayAtHotel,
	}
}

func FromDomainPayAtHotel(domain *domain.PayAtHotel) *skyhub.PayAtHotel {
	if domain == nil {
		return nil
	}

	return &skyhub.PayAtHotel{
		Amount:      domain.Amount,
		Description: domain.Description,
		Currency:    domain.Currency,
	}
}

func FromDomainPayAtHotels(domains []*domain.PayAtHotel) []*skyhub.PayAtHotel {
	if len(domains) == 0 {
		return nil
	}

	res := make([]*skyhub.PayAtHotel, 0)
	for _, item := range domains {
		res = append(res, FromDomainPayAtHotel(item))
	}

	return res
}
