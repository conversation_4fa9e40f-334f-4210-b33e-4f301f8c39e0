package web_partnership

import (
	"github.com/pkg/errors"
	commonError "gitlab.deepgate.io/apps/common/errors"

	"gitlab.deepgate.io/apps/api/gen/go/skyhub/web_partnership"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
)

type WebPartnershipServer struct {
	web_partnership.UnimplementedHiddenFeeServiceServer
	web_partnership.UnimplementedHotelServiceServer
	app app.Application
	cfg *config.Schema
}

func NewWebPartnershipServer(application app.Application, cfg *config.Schema) *WebPartnershipServer {
	return &WebPartnershipServer{app: application, cfg: cfg}
}

func (s *WebPartnershipServer) handleError(err error) string {
	if err == nil {
		return ""
	}

	err = errors.Cause(err)
	if appErr, ok := err.(*commonError.Error); ok {
		return appErr.Error()
	}

	return commonError.ErrSomethingOccurred.Error()
}
