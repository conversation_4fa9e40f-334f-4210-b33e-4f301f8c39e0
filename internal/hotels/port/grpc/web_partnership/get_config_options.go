package web_partnership

import (
	"context"

	"gitlab.deepgate.io/apps/api/gen/go/base"
	"gitlab.deepgate.io/apps/api/gen/go/skyhub"
	"gitlab.deepgate.io/apps/api/gen/go/skyhub/web_partnership"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
)

func (s *WebPartnershipServer) GetConfigOptions(ctx context.Context, req *base.Empty) (*web_partnership.GetConfigOptionsRes, error) {
	return &web_partnership.GetConfigOptionsRes{
		IsSuccess:          true,
		Providers:          []*skyhub.HotelProviderHiddenFee{},
		AccommodationTypes: constants.AccommodationTypes,
	}, nil
}
