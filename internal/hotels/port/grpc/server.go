package port

import (
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/port/grpc/admin"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/port/grpc/backend"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/port/grpc/web_partnership"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
)

type Server struct {
	*admin.AdminServer
	*backend.BackendServer
	*web_partnership.WebPartnershipServer
}

func NewServer(application app.Application, cfg *config.Schema) *Server {
	return &Server{
		AdminServer:          admin.NewAdminServer(application, cfg),
		BackendServer:        backend.NewBackendServer(application, cfg),
		WebPartnershipServer: web_partnership.NewWebPartnershipServer(application, cfg),
	}
}
