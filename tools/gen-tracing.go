package main

import (
	"gitlab.deepgate.io/apps/common/tools/trace"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/dida"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/notification"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/partnership"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/command"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/query"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service"
)

func getMongoRepoTrace() []trace.Trace {
	return []trace.Trace{
		// MongoDB
		// trace.CreateTracingItem(repositories.NewRequestRepository, "repositories_tracing", "./internal/hotels/tracing/adapter/mongodb/repositories", ""),
		// trace.CreateTracingItem(repositories.NewCurrencyExchangeRepository, "repositories_tracing", "./internal/hotels/tracing/adapter/mongodb/repositories", ""),
		// trace.CreateTracingItem(repositories.NewHotelRepository, "repositories_tracing", "./internal/hotels/tracing/adapter/mongodb/repositories", ""),
		// trace.CreateTracingItem(repositories.NewRoomRepository, "repositories_tracing", "./internal/hotels/tracing/adapter/mongodb/repositories", ""),
		// trace.CreateTracingItem(repositories.NewPolygonRepository, "repositories_tracing", "./internal/hotels/tracing/adapter/mongodb/repositories", ""),
		// trace.CreateTracingItem(repositories.NewRegionRepository, "repositories_tracing", "./internal/hotels/tracing/adapter/mongodb/repositories", ""),
		// trace.CreateTracingItem(repositories.NewSearchHotelCachesRepository, "repositories_tracing", "./internal/hotels/tracing/adapter/mongodb/repositories", ""),
		// trace.CreateTracingItem(repositories.NewSessionRepository, "repositories_tracing", "./internal/hotels/tracing/adapter/mongodb/repositories", ""),
		// trace.CreateTracingItem(repositories.NewOrderRepository, "repositories_tracing", "./internal/hotels/tracing/adapter/mongodb/repositories", ""),
		// trace.CreateTracingItem(repositories.NewTourmindJobsQueueRepository, "repositories_tracing", "./internal/hotels/tracing/adapter/mongodb/repositories", ""),
		// trace.CreateTracingItem(repositories.NewTourmindRegionsRepository, "repositories_tracing", "./internal/hotels/tracing/adapter/mongodb/repositories", ""),
		trace.CreateTracingItem(repositories.NewProviderSearchHotelsRepository, "repositories_tracing", "./internal/hotels/tracing/adapter/mongodb/repositories", ""),
		// trace.CreateTracingItem(repositories.NewHotelAreaCacheRepository, "repositories_tracing", "./internal/hotels/tracing/adapter/mongodb/repositories", ""),
		// trace.CreateTracingItem(repositories.NewHiddenServiceFeeRepository, "repositories_tracing", "./internal/hotels/tracing/adapter/mongodb/repositories", ""),
		// trace.CreateTracingItem(repositories.NewExpediaWebhookNotificationRepository, "repositories_tracing", "./internal/hotels/tracing/adapter/mongodb/repositories", ""),

		// trace.CreateTracingItem(repositories.NewTAMappingsRepository, "repositories_tracing", "./internal/hotels/tracing/adapter/mongodb/repositories", ""),
		// Redis
		// Skip redis repo tracing
	}
}

func getCommandTrace() []trace.Trace {
	return []trace.Trace{
		// trace.CreateTracingItem(command.NewAggregateHotelContentHandler, "command_tracing", "./internal/hotels/tracing/app/command", ""),
		// trace.CreateTracingItem(command.NewAggregateRegionsContentHandler, "command_tracing", "./internal/hotels/tracing/app/command", ""),
		// trace.CreateTracingItem(command.NewPriceCheckHandler, "command_tracing", "./internal/hotels/tracing/app/command", ""),
		// trace.CreateTracingItem(command.NewBookHotelHandler, "command_tracing", "./internal/hotels/tracing/app/command", ""),
		// trace.CreateTracingItem(command.NewProcessPendingBookingHandler, "command_tracing", "./internal/hotels/tracing/app/command", ""),
		// trace.CreateTracingItem(command.NewAggregateTourmindContentHandler, "command_tracing", "./internal/hotels/tracing/app/command", ""),
		// trace.CreateTracingItem(command.NewCheckAvailabilityHandler, "command_tracing", "./internal/hotels/tracing/app/command", ""),
		// trace.CreateTracingItem(command.NewSearchHotelsHandler, "command_tracing", "./internal/hotels/tracing/app/command", ""),
		// trace.CreateTracingItem(command.NewUpdateOrderStatusHandler, "command_tracing", "./internal/hotels/tracing/app/command", ""),
		// trace.CreateTracingItem(command.NewSearchDestinationHandler, "command_tracing", "./internal/hotels/tracing/app/command", ""),
		// trace.CreateTracingItem(command.NewAggregateRegionContentHandler, "command_tracing", "./internal/hotels/tracing/app/command", ""),
		trace.CreateTracingItem(command.NewExpediaWebhookNotificationHandler, "command_tracing", "./internal/hotels/tracing/app/command", ""),
		// trace.CreateTracingItem(command.NewGetReviewHandler, "command_tracing", "./internal/hotels/tracing/app/command", ""),
		// trace.CreateTracingItem(command.NewUpdateConfigHiddenFeeHandler, "command_tracing", "./internal/hotels/tracing/app/command", ""),
		// trace.CreateTracingItem(command.NewCreateConfigHiddenFeeHandler, "command_tracing", "./internal/hotels/tracing/app/command", ""),
		// trace.CreateTracingItem(command.NewDeleteConfigHiddenFeeHandler, "command_tracing", "./internal/hotels/tracing/app/command", ""),
		// trace.CreateTracingItem(command.NewCancelBookingHandler, "command_tracing", "./internal/hotels/tracing/app/command", ""),
		// trace.CreateTracingItem(command.NewCheckBookingCancelStatusHandler, "command_tracing", "./internal/hotels/tracing/app/command", ""),
		// trace.CreateTracingItem(command.NewProcessCancelingBookingHandler, "command_tracing", "./internal/hotels/tracing/app/command", ""),
		// trace.CreateTracingItem(command.NewAggregateHotelTransactionHandler, "command_tracing", "./internal/hotels/tracing/app/command", ""),

		// command
	}
}

func getQueryTrace() []trace.Trace {
	return []trace.Trace{
		// query
		// trace.CreateTracingItem(query.NewGetHotelDetailHandler, "query_tracing", "./internal/hotels/tracing/app/query", ""),
		// trace.CreateTracingItem(query.NewRetrieveBookingHandler, "query_tracing", "./internal/hotels/tracing/app/query", ""),
		// trace.CreateTracingItem(query.NewListBookingHandler, "query_tracing", "./internal/hotels/tracing/app/query", ""),
		// trace.CreateTracingItem(query.NewListConfigHiddenFeeHandler, "query_tracing", "./internal/hotels/tracing/app/query", ""),
		// trace.CreateTracingItem(query.NewDetailConfigHiddenFeeHandler, "query_tracing", "./internal/hotels/tracing/app/query", ""),
		trace.CreateTracingItem(query.NewListOrderByFilterHandler, "query_tracing", "./internal/hotels/tracing/app/query", ""),
		trace.CreateTracingItem(query.NewGetDetailOrderByIDHandler, "query_tracing", "./internal/hotels/tracing/app/query", ""),
	}
}

func getServiceTrace() []trace.Trace {
	return []trace.Trace{
		// trace.CreateTracingItem(service.NewCurrencyExchangeService, "service_tracing", "./internal/hotels/tracing/app/service", ""),
		// trace.CreateTracingItem(service.NewCheckAvailabilityService, "service_tracing", "./internal/hotels/tracing/app/service", ""),
		// trace.CreateTracingItem(service.NewSearchHotelService, "service_tracing", "./internal/hotels/tracing/app/service", ""),
		// trace.CreateTracingItem(service.NewSessionService, "service_tracing", "./internal/hotels/tracing/app/service", ""),
		trace.CreateTracingItem(service.NewPriceCheckService, "service_tracing", "./internal/hotels/tracing/app/service", ""),
		trace.CreateTracingItem(service.NewBookingService, "service_tracing", "./internal/hotels/tracing/app/service", ""),
		// trace.CreateTracingItem(service.NewHiddenFeeService, "service_tracing", "./internal/hotels/tracing/app/service", ""),
		// trace.CreateTracingItem(service.NewWebhookService, "service_tracing", "./internal/hotels/tracing/app/service", ""),
		// trace.CreateTracingItem(service.NewGetReviewService, "service_tracing", "./internal/hotels/tracing/app/service", ""),
		// trace.CreateTracingItem(service.NewCancelBookingService, "service_tracing", "./internal/hotels/tracing/app/service", ""),
	}
}

func getGrpcClientTrace() []trace.Trace {
	return []trace.Trace{
		// Grpc clients
		// trace.CreateTracingItem(order.NewOrderServiceClient, "wallet_tracing", "./internal/hotels/tracing/adapter/order", ""),
		// trace.CreateTracingItem(wallet.NewWalletClient, "wallet_tracing", "./internal/hotels/tracing/adapter/wallet", ""),
		// trace.CreateTracingItem(payment.NewPaymentClient, "payment_tracing", "./internal/hotels/tracing/adapter/payment", ""),
		// trace.CreateTracingItem(webhook.NewWebhookAdapter, "webhook_tracing", "./internal/hotels/tracing/adapter/webhook", ""),
		// trace.CreateTracingItem(partner.NewPartnerClient, "partner_tracing", "./internal/hotels/tracing/adapter/partner", ""),
		// trace.CreateTracingItem(price.NewPriceClient, "price_tracing", "./internal/hotels/tracing/adapter/price", ""),
		trace.CreateTracingItem(notification.NewNotificationServiceClient, "notification_tracing", "./internal/hotels/tracing/adapter/notification", ""),
		trace.CreateTracingItem(partnership.NewPartnershipClient, "partnership_tracing", "./internal/hotels/tracing/adapter/partnership", ""),
	}
}

func getAdapterTrace() []trace.Trace {
	return []trace.Trace{
		// Adapter, client & other stuff
		// 	trace.CreateTracingItem(expedia_client.NewExpediaAdapter, "expedia_client_tracing", "./internal/hotels/tracing/adapter/expedia_client", ""),
		// 	trace.CreateTracingItem(tourmind_client.NewTourmindAdapter, "tourmind_client_tracing", "./internal/hotels/tracing/adapter/tourmind_client", ""),
		// 	trace.CreateTracingItem(telegram_bot.NewTelegramBotAdapter, "telegram_bot_tracing", "./internal/hotels/tracing/adapter/telegram_bot", ""),
		// 	trace.CreateTracingItem(esRepo.NewPlaceElasticRepository, "client", "./internal/hotels/tracing/adapter/elastic_search", ""),
		// 	trace.CreateTracingItem(rate_hawk_client.NewRateHawkAdapter, "client", "./internal/hotels/tracing/adapter/rate_hawk_client", ""),
		// 	trace.CreateTracingItem(btm_client.NewBTMAdapter, "client", "./internal/hotels/tracing/adapter/btm_client", ""),
		// 	trace.CreateTracingItem(taclient.NewTAAdapter, "client", "./internal/hotels/tracing/adapter/ta_client", ""),
		// 	trace.CreateTracingItem(mq.NewAdapter, "client", "./internal/hotels/tracing/adapter/mq", ""),
		trace.CreateTracingItem(dida.NewAdapter, "client", "./internal/hotels/tracing/adapter/dida", ""),
	}
}

func main() {
	items := []trace.Trace{}

	genMongoRepo := true
	genCommand := false
	genQuery := false
	genService := true
	genGrpcClient := false
	genAdapter := false

	if genMongoRepo {
		items = append(items, getMongoRepoTrace()...)
	}
	if genCommand {
		items = append(items, getCommandTrace()...)
	}
	if genQuery {
		items = append(items, getQueryTrace()...)
	}
	if genService {
		items = append(items, getServiceTrace()...)
	}
	if genGrpcClient {
		items = append(items, getGrpcClientTrace()...)
	}
	if genAdapter {
		items = append(items, getAdapterTrace()...)
	}

	trace.GenTrace(items)
}
